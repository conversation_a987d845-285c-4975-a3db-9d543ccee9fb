{"ignored_warnings": [{"warning_type": "Dangerous Eval", "warning_code": 13, "fingerprint": "0964d23d4f9dc825c1ec5be9a5dc275afb01d4d28702272205a470c07ac389ff", "check_name": "Evaluation", "message": "Dynamic string evaluated as code", "file": "config/initializers/factory_bot_associations.rb", "line": 128, "link": "https://brakemanscanner.org/docs/warning_types/dangerous_eval/", "code": "instance_eval(\"        #{\"trait :#{(\"with_#{name}\" or nil)} do\" if (\"with_#{name}\" or nil).present?}\\n\\n        transient do\\n          #{name}_args { nil }\\n        end\\n\\n        #{name} do\\n          traits, kwargs = factorify_args(\\n            #{name}_args,\\n            (defined? default_#{name}_args) ? default_#{name}_args : nil,\\n          )\\n\\n          association(:#{(factory or name)}, *traits, **kwargs)\\n        end\\n\\n        #{\"end\" if (\"with_#{name}\" or nil).present?}\\n\")", "render_path": null, "location": {"type": "method", "class": "DefinitionProxy", "method": "parent_with_nested_args"}, "user_input": null, "confidence": "Weak", "cwe_id": [913, 95], "note": "Helper just for tests"}, {"warning_type": "Mass Assignment", "warning_code": 70, "fingerprint": "0b828fc644310cbea993acd88dda33dde5b9de379fc7c56895d01def76301f1f", "check_name": "MassAssignment", "message": "Specify exact keys allowed for mass assignment instead of using `permit!` which allows any keys", "file": "app/controllers/site/syllabus_controller.rb", "line": 34, "link": "https://brakemanscanner.org/docs/warning_types/mass_assignment/", "code": "params.permit!", "render_path": null, "location": {"type": "method", "class": "Site::SyllabusController", "method": "redirect"}, "user_input": null, "confidence": "Medium", "cwe_id": [915], "note": "We are just passing params through the redirect, and the redirected controller will restrict param usage"}, {"warning_type": "SQL Injection", "warning_code": 0, "fingerprint": "10c47dcccf7e15923c8a6e83b7ea9fe497dfe5c127402001b49fed7bc6cd0e5f", "check_name": "SQL", "message": "Possible SQL injection", "file": "app/models/external_content/code.rb", "line": 50, "link": "https://brakemanscanner.org/docs/warning_types/sql_injection/", "code": "available(time).joins(\"LEFT JOIN external_content_code_uses ON external_content_code_uses.code_id = external_content_codes.id AND external_content_code_uses.status in (#{ExternalContent::CodeUse.statuses.values_at(*ExternalContent::CodeUse::ASSUMED_USED_STATUSES).join(\",\")})\")", "render_path": null, "location": {"type": "method", "class": "Code", "method": null}, "user_input": "ExternalContent::CodeUse.statuses.values_at(*ExternalContent::CodeUse::ASSUMED_USED_STATUSES)", "confidence": "Medium", "cwe_id": [89], "note": "The user_input is from code and not from website users so it can be trusted."}, {"warning_type": "Dangerous Eval", "warning_code": 13, "fingerprint": "1eca26522429239bc9d6706e7161381a9d7499845b0557cd3280bc68b15656c1", "check_name": "Evaluation", "message": "Dynamic string evaluated as code", "file": "config/initializers/factory_bot_associations.rb", "line": 237, "link": "https://brakemanscanner.org/docs/warning_types/dangerous_eval/", "code": "instance_eval(\"        transient do\\n          #{name}_count { (defined? default_#{name}_count) ? default_#{name}_count : 0 }\\n          common_#{name}_args { nil }\\n          mapped_#{name}_args {\\n            (defined? base_mapped_#{name}_args) ? base_mapped_#{name}_args : []\\n          }\\n        end\\n\\n        after(:create) do |model, evaluator|\\n          # when not using factory_bot_associations, the association is not loaded by default. Retain this behavior as some specs are dependent on it.\\n          model.association(:#{(name.to_s or name.to_s.pluralize)}).reset\\n        end\\n\\n        after(:build, :stub) do |model, evaluator|\\n          if (evaluator.mapped_#{name}_args.present? &&\\n            [0, evaluator.mapped_#{name}_args.size].exclude?(evaluator.#{name}_count)\\n          )\\n            raise ArgumentError.new(\\n              \\\"#{name}_count and length of mapped_#{name}_args must align if both are \\\" \\\\\\n                \\\"provided. #{name}_count: \\#{evaluator.#{name}_count}, \\\" \\\\\\n                \\\"mapped_#{name}_args length: \\#{evaluator.mapped_#{name}_args.size}.\\\",\\n            )\\n          end\\n\\n          begin\\n            # if association child/children were explicitly passed in, we don't want to\\n            # create any more, so if the association is not empty, exit\\n            next if model.#{(name.to_s or name.to_s.pluralize)}.#{(has == :one) ? (\"present?\") : (\"any?\")}\\n\\n            derived_count = [evaluator.mapped_#{name}_args.length, evaluator.#{name}_count].max\\n\\n            derived_association = #{as.present? ? (\":#{as}\") : (\"model.class.to_s.split(/::/).last.underscore\")}\\n\\n            derived_count.times do |i|\\n              traits, kwargs = factorify_args(\\n                evaluator.mapped_#{name}_args[i],\\n                evaluator.common_#{name}_args,\\n                evaluator.respond_to?(:default_common_#{name}_args) ?\\n                  evaluator.default_common_#{name}_args :\\n                  nil,\\n                derived_association.to_sym => model,\\n                strategy: :build,\\n              )\\n\\n              if #{(has == :one)}\\n                model.#{(name.to_s or name.to_s.pluralize)} = evaluator.association(:#{(factory or name)}, *traits, **kwargs)\\n              else\\n                model.#{(name.to_s or name.to_s.pluralize)} << evaluator.association(:#{(factory or name)}, *traits, **kwargs)\\n              end\\n            end\\n          rescue => e\\n            puts \\\"An exception was raised in child_with_nested_args: \\#{e.inspect}\\\"\\n            raise e\\n          end\\n        end\\n\")", "render_path": null, "location": {"type": "method", "class": "DefinitionProxy", "method": "child_with_nested_args"}, "user_input": null, "confidence": "Weak", "cwe_id": [913, 95], "note": ""}, {"warning_type": "Mass Assignment", "warning_code": 70, "fingerprint": "2ae3a791bc440354b21f8c84b2f8e3bcb1d254b2d9bc4e74ddee320562bf90a3", "check_name": "MassAssignment", "message": "Specify exact keys allowed for mass assignment instead of using `permit!` which allows any keys", "file": "app/controllers/site/syllabus_controller.rb", "line": 48, "link": "https://brakemanscanner.org/docs/warning_types/mass_assignment/", "code": "params.permit!", "render_path": null, "location": {"type": "method", "class": "Site::SyllabusController", "method": "redirect_to_alternate_partner"}, "user_input": null, "confidence": "Medium", "cwe_id": [915], "note": "We are just passing params through the redirect, and the redirected controller will restrict param usage"}, {"warning_type": "Mass Assignment", "warning_code": 70, "fingerprint": "41ddd880486569cab837d2bc421a2b9f01419cdab3f9e769f6c3de61901df9f0", "check_name": "MassAssignment", "message": "Specify exact keys allowed for mass assignment instead of using `permit!` which allows any keys", "file": "app/controllers/learning_delivery/employee/availabilities_controller.rb", "line": 45, "link": "https://brakemanscanner.org/docs/warning_types/mass_assignment/", "code": "params.require(:availabilities).permit!", "render_path": null, "location": {"type": "method", "class": "LearningDelivery::Employee::AvailabilitiesController", "method": "availability_params"}, "user_input": null, "confidence": "Medium", "cwe_id": [915], "note": "Needed since keys are dynamic.  Controller verifies that availability slots are valid before saving."}, {"warning_type": "Mass Assignment", "warning_code": 70, "fingerprint": "4706f6759e47d5a7b6f4eb06b38ccada24bcce197fd43002a3f9513946370bd0", "check_name": "MassAssignment", "message": "Specify exact keys allowed for mass assignment instead of using `permit!` which allows any keys", "file": "app/controllers/site/get_to_know_you_controller.rb", "line": 17, "link": "https://brakemanscanner.org/docs/warning_types/mass_assignment/", "code": "params.permit!", "render_path": null, "location": {"type": "method", "class": "Site::GetToKnowYouController", "method": "redirect_to_alternate_partner"}, "user_input": null, "confidence": "Medium", "cwe_id": [915], "note": "We are just passing params through the redirect, and the redirected controller will restrict param usage"}, {"warning_type": "SQL Injection", "warning_code": 0, "fingerprint": "50bd766bad1b273b1bcd98b7f0361e8bbb79e6f2060f78d12e7aa54db8ac4e6e", "check_name": "SQL", "message": "Possible SQL injection", "file": "app/jobs/postgres_vacuumer_job.rb", "line": 42, "link": "https://brakemanscanner.org/docs/warning_types/sql_injection/", "code": "ActiveRecord::Base.connection.execute(\"VACUUM ANALYZE#{\" #{quote_table_name(table)}\" if table}\")", "render_path": null, "location": {"type": "method", "class": "PostgresVacuumerJob", "method": "vacuum"}, "user_input": "\" #{quote_table_name(table)}\" if table", "confidence": "Medium", "cwe_id": [89], "note": "Input is being quoted via AR#quote_table_name before being passed to execute, bind parameters are not supported for vaccum"}, {"warning_type": "Mass Assignment", "warning_code": 70, "fingerprint": "54555d4482c2a0927d33786fff0cc7a16a2a03f8f003d3da2232b41bac81b38c", "check_name": "MassAssignment", "message": "Specify exact keys allowed for mass assignment instead of using `permit!` which allows any keys", "file": "app/controllers/site/reimbursement_controller.rb", "line": 35, "link": "https://brakemanscanner.org/docs/warning_types/mass_assignment/", "code": "params.permit!", "render_path": null, "location": {"type": "method", "class": "Site::ReimbursementController", "method": "redirect_to_alternate_partner"}, "user_input": null, "confidence": "Medium", "cwe_id": [915], "note": "We are just passing params through the redirect, and the redirected controller will restrict param usage"}, {"warning_type": "SQL Injection", "warning_code": 0, "fingerprint": "572e34925ce2ca49f886c34542a3b877e69c5f60bdf56965096c1e3efff9888b", "check_name": "SQL", "message": "Possible SQL injection", "file": "app/models/external_content/code.rb", "line": 50, "link": "https://brakemanscanner.org/docs/warning_types/sql_injection/", "code": "available(time).joins(\"LEFT JOIN external_content_code_uses ON external_content_code_uses.code_id = external_content_codes.id AND external_content_code_uses.status in (#{ExternalContent::CodeUse.statuses.values_at(*ExternalContent::CodeUse::ASSUMED_USED_STATUSES).join(\",\")})\")", "render_path": null, "location": {"type": "method", "class": "ExternalContent::Code", "method": "usable"}, "user_input": "ExternalContent::CodeUse.statuses.values_at(*ExternalContent::CodeUse::ASSUMED_USED_STATUSES)", "confidence": "High", "cwe_id": [89], "note": "The user_input is from code and not from website users so it can be trusted."}, {"warning_type": "Mass Assignment", "warning_code": 70, "fingerprint": "57fd8ebdc1846d60a4afcb824e09ce66875fdf5a3031fab6cae47f154bd3908c", "check_name": "MassAssignment", "message": "Specify exact keys allowed for mass assignment instead of using `permit!` which allows any keys", "file": "app/controllers/site/registrations_controller.rb", "line": 145, "link": "https://brakemanscanner.org/docs/warning_types/mass_assignment/", "code": "params.permit!", "render_path": null, "location": {"type": "method", "class": "Site::RegistrationsController", "method": "redirect_to_alternate_partner"}, "user_input": null, "confidence": "Medium", "cwe_id": [915], "note": "We are just passing params through the redirect, and the redirected controller will restrict param usage"}, {"warning_type": "Mass Assignment", "warning_code": 70, "fingerprint": "767cc4299de656c84ff52dcdf6f59a03a994772047bd7d878032cc2750f2b639", "check_name": "MassAssignment", "message": "Specify exact keys allowed for mass assignment instead of using `permit!` which allows any keys", "file": "app/controllers/site/squeeze_controller.rb", "line": 36, "link": "https://brakemanscanner.org/docs/warning_types/mass_assignment/", "code": "params.permit!", "render_path": null, "location": {"type": "method", "class": "Site::SqueezeController", "method": "redirect"}, "user_input": null, "confidence": "Medium", "cwe_id": [915], "note": "We are just passing params through the redirect, and the redirected controller will restrict param usage"}, {"warning_type": "Mass Assignment", "warning_code": 70, "fingerprint": "95c7513341f7a9d47450d5cf543b63b186d613111c124e343b630c3ed7ee7407", "check_name": "MassAssignment", "message": "Specify exact keys allowed for mass assignment instead of using `permit!` which allows any keys", "file": "app/controllers/site/reimbursement_controller.rb", "line": 21, "link": "https://brakemanscanner.org/docs/warning_types/mass_assignment/", "code": "params.permit!", "render_path": null, "location": {"type": "method", "class": "Site::ReimbursementController", "method": "redirect"}, "user_input": null, "confidence": "Medium", "cwe_id": [915], "note": "We are just passing params through the redirect, and the redirected controller will restrict param usage"}, {"warning_type": "Mass Assignment", "warning_code": 70, "fingerprint": "e6b74463d7e0321a26985ad405e2b10504086ec7331075a2e796c24a2513e266", "check_name": "MassAssignment", "message": "Specify exact keys allowed for mass assignment instead of using `permit!` which allows any keys", "file": "app/controllers/site/squeeze_controller.rb", "line": 53, "link": "https://brakemanscanner.org/docs/warning_types/mass_assignment/", "code": "params.permit!", "render_path": null, "location": {"type": "method", "class": "Site::SqueezeController", "method": "redirect_to_alternate_partner"}, "user_input": null, "confidence": "Medium", "cwe_id": [915], "note": "We are just passing params through the redirect, and the redirected controller will restrict param usage"}, {"warning_type": "Dangerous Eval", "warning_code": 13, "fingerprint": "f167789ed7b4b395bc07d6c45f0b9c788ceae6175466d1998d8aca6edf8239bc", "check_name": "Evaluation", "message": "Dynamic string evaluated as code", "file": "config/initializers/factory_bot_associations.rb", "line": 237, "link": "https://brakemanscanner.org/docs/warning_types/dangerous_eval/", "code": "instance_eval(\"        transient do\\n          #{name}_count { (defined? default_#{name}_count) ? default_#{name}_count : 0 }\\n          common_#{name}_args { nil }\\n          mapped_#{name}_args {\\n            (defined? base_mapped_#{name}_args) ? base_mapped_#{name}_args : []\\n          }\\n        end\\n\\n        after(:create) do |model, evaluator|\\n          # when not using factory_bot_associations, the association is not loaded by default. Retain this behavior as some specs are dependent on it.\\n          model.association(:#{(name.to_s or name.to_s.pluralize)}).reset if model.association(:#{(name.to_s or name.to_s.pluralize)}).loaded? && model.#{(name.to_s or name.to_s.pluralize)}.blank?\\n        end\\n\\n        after(:build, :stub) do |model, evaluator|\\n          if (evaluator.mapped_#{name}_args.present? &&\\n            [0, evaluator.mapped_#{name}_args.size].exclude?(evaluator.#{name}_count)\\n          )\\n            raise ArgumentError.new(\\n              \\\"#{name}_count and length of mapped_#{name}_args must align if both are \\\" \\\\\\n                \\\"provided. #{name}_count: \\#{evaluator.#{name}_count}, \\\" \\\\\\n                \\\"mapped_#{name}_args length: \\#{evaluator.mapped_#{name}_args.size}.\\\",\\n            )\\n          end\\n\\n          begin\\n            # if association child/children were explicitly passed in, we don't want to\\n            # create any more, so if the association is not empty, exit\\n            next if model.#{(name.to_s or name.to_s.pluralize)}.#{(has == :one) ? (\"present?\") : (\"any?\")}\\n\\n            derived_count = [evaluator.mapped_#{name}_args.length, evaluator.#{name}_count].max\\n\\n            derived_association = #{as.present? ? (\":#{as}\") : (\"model.class.to_s.split(/::/).last.underscore\")}\\n\\n            derived_count.times do |i|\\n              traits, kwargs = factorify_args(\\n                evaluator.mapped_#{name}_args[i],\\n                evaluator.common_#{name}_args,\\n                evaluator.respond_to?(:default_common_#{name}_args) ?\\n                  evaluator.default_common_#{name}_args :\\n                  nil,\\n                derived_association.to_sym => model,\\n                strategy: :build,\\n              )\\n\\n              if #{(has == :one)}\\n                model.#{(name.to_s or name.to_s.pluralize)} = evaluator.association(:#{(factory or name)}, *traits, **kwargs)\\n              else\\n                model.#{(name.to_s or name.to_s.pluralize)} << evaluator.association(:#{(factory or name)}, *traits, **kwargs)\\n              end\\n            end\\n          rescue => e\\n            puts \\\"An exception was raised in child_with_nested_args: \\#{e.inspect}\\\"\\n            raise e\\n          end\\n        end\\n\")", "render_path": null, "location": {"type": "method", "class": "DefinitionProxy", "method": "child_with_nested_args"}, "user_input": null, "confidence": "Weak", "cwe_id": [913, 95], "note": ""}, {"warning_type": "Mass Assignment", "warning_code": 70, "fingerprint": "f5fe3b999df82e9272038a54c28c281856987e4e5a10a2cfb3a2cfe6c09edc2d", "check_name": "MassAssignment", "message": "Specify exact keys allowed for mass assignment instead of using `permit!` which allows any keys", "file": "app/controllers/site/landing_controller.rb", "line": 44, "link": "https://brakemanscanner.org/docs/warning_types/mass_assignment/", "code": "params.permit!", "render_path": null, "location": {"type": "method", "class": "Site::LandingController", "method": "redirect_to_alternate_partner"}, "user_input": null, "confidence": "Medium", "cwe_id": [915], "note": "We are just passing params through the redirect, and the redirected controller will restrict param usage"}, {"warning_type": "Mass Assignment", "warning_code": 70, "fingerprint": "f9ea8b7504316a9f325c69ef42c25357290c9461fbee009b01db1789ff9947f1", "check_name": "MassAssignment", "message": "Specify exact keys allowed for mass assignment instead of using `permit!` which allows any keys", "file": "app/controllers/site/registrations_controller.rb", "line": 85, "link": "https://brakemanscanner.org/docs/warning_types/mass_assignment/", "code": "params.permit!", "render_path": null, "location": {"type": "method", "class": "Site::RegistrationsController", "method": "redirect"}, "user_input": null, "confidence": "Medium", "cwe_id": [915], "note": "We are just passing params through the redirect, and the redirected controller will restrict param usage"}], "brakeman_version": "7.0.2"}