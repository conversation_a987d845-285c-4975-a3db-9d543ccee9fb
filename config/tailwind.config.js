const defaultTheme = require('tailwindcss/defaultTheme')

module.exports = {
  content: [
    './public/*.html',
    './app/helpers/**/*.rb',
    './app/javascript/**/*.js',
    './app/presenters/**/*.rb',
    './app/views/**/*.{erb,haml,html,slim}',
    './app/admin/**/*.rb',
    './vendor/javascript/tailwindcss-stimulus-components.js'
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Barlow', ...defaultTheme.fontFamily.sans],
        zip: ['"IBM Plex Sans"', ...defaultTheme.fontFamily.sans],
        shadows: ['"Shadows Into Light"', 'cursive'],
      },
      colors: {
        'blue-zip': '#241451',
        'yellow-zip': '#DEE140',
        'green-zip': '#6EB77C',
        'red-zip': '#ED4634',
        'brand-blue-zip': '#1D4ED8',
        'extra-dark-grey-zip': '#404041',
        'extra-light-yellow-zip': '#FFFAE5',
        'dark-yellow-zip': '#CA8A04',
        'purple-zip': '#6B21A8',
        'dark-green-zip': '#166534',
        'secondary-zip': '#5E5E5E',
        'tertiary-black-zip': '#232323',
        'tertiary-grey-zip': '#BABCC0',
        'tertiary-light-grey-zip': '#E1DFE1',
        'orange-zip': '#FF9735',
        'black-zip': '#000000',
        'bg-secondary-grey-zip': '#F8F8F9',
        'bg-white-10-zip': '#FFFFFF1A',
        'text-disabled-grey-zip': '#9E9E9E',
        'brand-primary-gray-zip' : '#969796',
        'secondary-dark-yellow-zip' : '#CA8A0433',
        'secondary-dark-Purple-zip': '#6B21A833',
        'primary-light-green-zip' : '#6EB77C99',
        'light-yellow-zip':'#CA8A041A',
        'discount-green-zip':'#DCEBDD',
        'secondary-discount-green-zip':'#c3d4c4',
        'secondary-cricket-yellow-zip':'#FFCD00',
        'functional-extra-light-red-zip':'#FDF4F4',
        'functional-warning-red-zip':'#CF292A',
        'functional-extra-light-grey-zip': '#F6F6F6',
        'text-focus-zip':'#0000001F',
      }
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/aspect-ratio'),
    require('@tailwindcss/typography'),
    require('@tailwindcss/container-queries'),
  ]
}
