pg_dump_timeout_sec: 600
db_snapshotter_instance_types: 
  - r8g.large
  - r7g.large

# Note that obfuscation queries are run in parallel against the database.
# If you have queries that depend on each other, you can place multiple queries in a single list item.
# Queries within a list item will be run in order, but multiple list items will be run in parallel.
# NOTE: If you want to use SQL comments in a query, you must use a YAML literal block like so:
#  - |
#    -- SQL Comment
#    UPDATE ...
# Otherwise YAML does not preserve line breaks and the query will not run correctly
obfuscation_queries:
  - UPDATE certificate_themes SET
      administrator_name = (
        CASE WHEN administrator_name IS NOT NULL THEN 'xxxxx xxxxxxxx' END
      ),
      administrator_title = (
        CASE WHEN administrator_title IS NOT NULL THEN 'xxxxxxxxxx' END
      );

  - UPDATE ecom_orders SET 
      short_payment_url = (
        CASE WHEN short_payment_url IS NOT NULL THEN 'https://example.com/xxxxxx' END
      );
    
  - UPDATE emails SET 
      address = (
        CASE WHEN address is not null
        AND address NOT LIKE '%@ziplines.com'
        THEN CONCAT(id, '@example.com') ELSE address END
      );

  - UPDATE learners SET
      first_name = 'xxxxxxxxxx',
      last_name = 'xxxxxxxxxx',
      phone_standardized = '+15555555555';

  - UPDATE payments_events SET
      meta = '{}'::jsonb;

  - UPDATE payments_payers SET
      name = 'xxxxx xxxxxxxx',
      email = '<EMAIL>';

  - UPDATE payments_manual_payments SET payment_url = 'https://example.com/xxxxxx';

  - UPDATE remote_canvas_password_resets SET
      email = (
        CASE WHEN email is not null
        AND email NOT LIKE '%@ziplines.com'
        THEN CONCAT(id, '@example.com') ELSE email END
      ),
      reset_digest = NULL;

  - UPDATE syllabus_requests SET
      first_name = 'xxxxxxxxxx',
      phone_standardized = (
        CASE WHEN phone_standardized IS NOT NULL THEN '+15555555555' END
      );

  - DELETE from remote_resources where created_at < <%=snapshot_timestamp%> - INTERVAL '30 days';

  - <%= with_foreign_keys_disabled { "DELETE from syllabus_requests where created_at < #{snapshot_timestamp} - INTERVAL '30 days';" } %>
    UPDATE syllabus_requests SET 
      site_ad_tracking_id = NULL
    WHERE site_ad_tracking_id NOT IN (
      SELECT id 
      FROM site_ad_trackings 
      WHERE created_at >= <%=snapshot_timestamp%> - INTERVAL '30 days'
    );
    UPDATE syllabus_requests SET 
      deal_id = NULL
    WHERE deal_id NOT IN (
      SELECT id 
      FROM deals 
      WHERE created_at >= <%=snapshot_timestamp%> - INTERVAL '30 days'
    );

  - <%= with_foreign_keys_disabled { "DELETE from promos_cross_sells WHERE deal_id NOT IN (SELECT id FROM deals WHERE created_at >= #{snapshot_timestamp} - INTERVAL '30 days');" } %>

  - UPDATE registrations SET
      site_ad_tracking_id = NULL
    WHERE site_ad_tracking_id NOT IN (
      SELECT id 
      FROM site_ad_trackings 
      WHERE created_at >= <%=snapshot_timestamp%> - INTERVAL '30 days'
    );
    UPDATE registrations SET
      deal_id = NULL
    WHERE deal_id NOT IN (
      SELECT id 
      FROM deals 
      WHERE created_at >= <%=snapshot_timestamp%> - INTERVAL '30 days'
    );

  - <%= with_foreign_keys_disabled { "DELETE FROM lms_submissions WHERE created_at < #{snapshot_timestamp} - INTERVAL '105 days';" } %>

  - <%= with_foreign_keys_disabled { "DELETE FROM lms_submission_comments WHERE submission_id NOT IN (select id FROM lms_submissions WHERE created_at >= #{snapshot_timestamp} - INTERVAL '105 days');" } %>
  
  - <%= with_foreign_keys_disabled { "DELETE FROM lms_submission_reviews WHERE submission_id NOT IN (select id FROM lms_submissions WHERE created_at >= #{snapshot_timestamp} - INTERVAL '105 days');" } %>

  - <%= with_foreign_keys_disabled { "DELETE FROM lms_submission_exercise_reviews WHERE review_id NOT IN (select id FROM lms_submission_reviews WHERE submission_id IN (select id FROM lms_submissions WHERE created_at >= #{snapshot_timestamp} - INTERVAL '105 days'));" } %>

  - <%= with_foreign_keys_disabled { "DELETE FROM lms_submission_review_training_evaluations WHERE review_id NOT IN (select id FROM lms_submission_reviews WHERE submission_id IN (select id FROM lms_submissions WHERE created_at >= #{snapshot_timestamp} - INTERVAL '105 days'));" } %>

  - UPDATE enrollments SET
      deal_id = NULL
    WHERE deal_id NOT IN (
      SELECT id 
      FROM deals 
      WHERE created_at >= <%=snapshot_timestamp%> - INTERVAL '30 days'
    );

  - <%= with_foreign_keys_disabled { "DELETE FROM site_ad_trackings WHERE created_at < #{snapshot_timestamp} - INTERVAL '30 days';" } %>

  - <%= with_foreign_keys_disabled { "DELETE FROM deals WHERE created_at < #{snapshot_timestamp} - INTERVAL '30 days';" } %>
  
  - UPDATE active_storage_blobs set service_name = 'amazon_production_snapshot';

  - UPDATE learning_delivery_employees SET personal_email = CONCAT(id, '@example.com');

  - <%= fast_delete_table('versions', "SELECT * FROM versions WHERE item_type IN ('Lms::Submission::Review', 'Lms::Submission') AND created_at >= #{snapshot_timestamp} - INTERVAL '7 days'") %>

# These queries run after the snapshot is restored into the local database and allow for
# environment-specific adjustments to be made to the data. These queries are run in parallel.
# If you have queries that depend on each other, you can place multiple queries in a single list item.
# Queries within a list item will be run in order, but multiple list items will be run in parallel.
# NOTE: If you want to use SQL comments in a query, you must use a YAML literal block like so:
#  - |
#    -- SQL Comment
#    UPDATE ...
# Otherwise YAML does not preserve line breaks and the query will not run correctly
post_restore_queries:
  <% if settings.development? || (settings.staging? && ENV['STAGING_SERVER_NAME'] != 'dev') %>
  - UPDATE partners set dns_status = 1; # set all partners to pending
  <% end %>

  <% if settings.development? || (settings.staging? && ENV['STAGING_SERVER_NAME'] == 'dev') %>
  - UPDATE settings
    SET value = jsonb_set(COALESCE(value, '{}'::jsonb), '{zendesk_support_page}', 'true'::jsonb, true)
    WHERE key = 'development_flags';
  <% end %>

  <% if settings.staging? && ENV['STAGING_SERVER_NAME'] == 'dev' %>
  - UPDATE partners SET
    proxy_host = (
      CASE WHEN proxy_host IS NOT NULL THEN CONCAT(proxy_host, '.stg-proxy.ziplines.dev') END
    );

  - UPDATE site_pages SET
    url = regexp_replace(url, '(https?://[^/]+)', '\1.stg-proxy.ziplines.dev'),
    short_url = null;
  <% end %>

  - INSERT INTO admin_users(id, email, provider, first_name, last_name, created_at, updated_at, time_zone, status) 
      VALUES 
        (10000, '<EMAIL>', 'google', 'QA', 'Service Account', now(), now(), 'UTC', 1);
    INSERT INTO admin_user_roles(admin_user_id, role_id, created_at, updated_at)
      VALUES 
        (10000, 7, now(), now());
    INSERT INTO admin_user_permissions(admin_user_id, permission_id, role_id, created_at, updated_at)
    SELECT 
      10000, 
      p.id, 
      CASE 
        WHEN p.resource_type = 'Role' THEN p.resource_id 
        ELSE NULL 
      END, 
      now(), 
      now()
    FROM permissions p;