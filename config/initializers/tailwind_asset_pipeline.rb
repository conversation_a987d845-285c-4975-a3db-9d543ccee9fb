# frozen_string_literal: true

require 'sprockets/sass_compressor'
module Sprockets
  class SassCompressor
    TAILWIND_SEARCH = "--tw-"
    def call(*args)
      input = if defined?(data)
        data # sprockets 2.x
      else
        args[0][:data] # sprockets 3.x
      end

      return input if skip_compiling?(input) # added this line

      SassC::Engine.new(
        input,
        {
          style: :compressed,
        },
      ).render
    end

    def skip_compiling?(body)
      body.include?(TAILWIND_SEARCH)
    end

  end
end

# Override Tailwind's CSS compressor detection to fix minification in staging/production
#
# The original tailwindcss-ruby gem disables minification when ANY css_compressor is present,
# but the sassc-rails gem sets css_compressor to :sass in Rails config during initialization.
# This causes Tailwind CSS to skip minification entirely, resulting in unminified CSS builds.
#
# Note: tailwindcss-rails documentation recommends removing sassc-rails due to compatibility
# issues with modern CSS features, but since we still have it, this patch allows Tailwind
# to handle its own minification when the compressor is :sass.
#
# This patch should be safe to remove if sassc-rails is ever removed.
module Tailwindcss
  module Commands
    class << self
      def rails_css_compressor?
        defined?(Rails) && Rails.application&.config&.assets&.css_compressor.present? && Rails.application.config.assets.css_compressor != :sass
      end
    end
  end
end
