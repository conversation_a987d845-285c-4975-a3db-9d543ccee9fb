# frozen_string_literal: true

require 'ipaddr'

ROLLBAR_IP_RANGES_TO_IGNORE = [
  IPAddr.new('2600:0C02:1020:2111::/64'), # Qualys
  IPAddr.new('2600:0c02:1020:2881::/64'), # Qualys
  IPAddr.new('************/23'), # Qualys
  IPAddr.new('**********/20'), # Qualys
].freeze

ignore_pci_scans_handler = proc do |options|
  scope = options[:scope]
  request_data = scope[:request] || {}

  if request_data[:user_ip]
    user_ip = request_data[:user_ip]

    # Check if the user's IP is in any of the ignored IP ranges
    raise Rollbar::Ignore if ROLLBAR_IP_RANGES_TO_IGNORE.any? { |range| range.include?(user_ip) }

    # Don't open rollbars for any errors generated by our weekly Nuclei scanner
    if ENV.fetch('RUNS_ON_OUTBOUND_NAT_IPS', nil) && request_data[:headers] && request_data[:headers]['Ziplines-Nuclei-Scanner']
      outbound_nat_ips = ENV.fetch('RUNS_ON_OUTBOUND_NAT_IPS', nil).split(',').map(&:strip)
      raise Rollbar::Ignore if outbound_nat_ips.include?(user_ip)
    end
  end
end

Rollbar.configure do |config|
  # Without configuration, Rollbar is enabled in all environments.
  # To disable in specific environments, set config.enabled=false.

  config.access_token = Rails.application.credentials.ROLLBAR_ACCESS_TOKEN

  # Here we'll disable in 'test':
  config.enabled = false if !Rails.env.hosted?

  # By default, Rollbar will try to call the `current_user` controller method
  # to fetch the logged-in user object, and then call that object's `id`
  # method to fetch this property. To customize:
  # config.person_method = "my_current_user"
  # config.person_id_method = "my_id"
  config.person_method = "current_admin_user"


  # Additionally, you may specify the following:
  # config.person_username_method = "username"
  config.person_username_method = "full_name"
  config.person_email_method = "email"

  # If you want to attach custom data to all exception and message reports,
  # provide a lambda like the following. It should return a hash.
  # config.custom_data_method = lambda { {:some_key => "some_value" } }

  # Add exception class names to the exception_level_filters hash to
  # change the level that exception is reported at. Note that if an exception
  # has already been reported and logged the level will need to be changed
  # via the rollbar interface.
  # Valid levels: 'critical', 'error', 'warning', 'info', 'debug', 'ignore'
  # 'ignore' will cause the exception to not be reported at all.
  # config.exception_level_filters.merge!('MyCriticalException' => 'critical')
  #
  # You can also specify a callable, which will be called with the exception instance.
  # config.exception_level_filters.merge!('MyCriticalException' => lambda { |e| 'critical' })

  # Enable asynchronous reporting (uses girl_friday or Threading if girl_friday
  # is not installed)
  # config.use_async = true
  # Supply your own async handler:
  # config.async_handler = Proc.new { |payload|
  #  Thread.new { Rollbar.process_from_async_handler(payload) }
  # }

  # Enable asynchronous reporting (using sucker_punch)
  # config.use_sucker_punch

  # Enable delayed reporting (using Sidekiq)
  # config.use_sidekiq
  # You can supply custom Sidekiq options:
  # config.use_sidekiq 'queue' => 'default'

  # If your application runs behind a proxy server, you can set proxy parameters here.
  # If https_proxy is set in your environment, that will be used. Settings here have precedence.
  # The :host key is mandatory and must include the URL scheme (e.g. 'http://'), all other fields
  # are optional.
  #
  # config.proxy = {
  #   host: 'http://some.proxy.server',
  #   port: 80,
  #   user: 'username_if_auth_required',
  #   password: 'password_if_auth_required'
  # }

  # If you run your staging application instance in production environment then
  # you'll want to override the environment reported by `Rails.env` with an
  # environment variable like this: `ROLLBAR_ENV=staging`. This is a recommended
  # setup for Heroku. See:
  # https://devcenter.heroku.com/articles/deploying-to-a-custom-rails-environment
  config.environment = ENV['ROLLBAR_ENV'].presence || Rails.env
  config.code_version = begin
    Rails.root.join("REVISION").read.chomp
  rescue SystemCallError
    nil
  end

  config.before_process << ignore_pci_scans_handler

  if Rails.env.staging?
    config.custom_data_method = lambda {
      { staging_server_name: ENV.fetch('STAGING_SERVER_NAME', nil) }
    }
  end

  # By default Rollbar sets `ActiveRecord::RecordNotFound`, `AbstractController::ActionNotFound`,
  # and `ActionController::RoutingError` exceptions as warning level. This isn't desirable
  # or needed from sidekiq jobs, so we disable this behavior when running in a sidekiq job.
  config.exception_level_filters = if Sidekiq.server?
    {
      'Remote::PublishCommand::SilencedRetry' => 'ignore',
    }
  else
    {
      'ActionController::RoutingError' => 'ignore',
      'ActionController::UnknownFormat' => 'ignore',
      'ActionDispatch::Http::MimeNegotiation::InvalidType' => 'ignore',
    }
  end

  # This allows Rollbar.log calls from within a sidekiq job to be reported with the correct
  # framework and context. Otherwise these errors are incorrectly classified as Rails in the
  # Rollbar UI (when it should say Sidekiq).
  config.sidekiq_use_scoped_block = true

  config.user_ip_rack_env_key = ActionPack::CustomClientIpHeader::Request::CUSTOM_CLIENT_IP_HEADER
end
