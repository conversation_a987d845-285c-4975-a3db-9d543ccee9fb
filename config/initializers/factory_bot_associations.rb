# frozen_string_literal: true

#
# I would have liked to require this from rails_helper and from .irbrc for
# non-prod consoles rather than use a config/initializer, but there's a bit of
# craziness that prevents it.
#
# factory_bot_rails gem loads the factories with an after_initialize hook.
# That will fail without this file having been loaded, so rails_helper/.irbrc
# is too late. It is also not possible to do it before the factory bot gem
# is loaded, because the DefinitionProxy actually undefs all instance methods
# except a configured handful, so it will actually remove the added methods.
# Using config/initializers seems to be the happy medium that gets this file
# loaded after factory bot but before the after_initialize hook that loads
# factories.
#

return if Rails.env.production?
return unless defined? FactoryBot

# primary_args and args expected to be one of:
# - symbol (trait)
# - hash (attribute/value pairs)
# - array of above
#
# kwargs expected to be attribute/value paris.
#
# Since primary args is expected to be what was specified in the actual create
# call for a factory, duplicate hash keys will take precedence from left to right
# (e.g. primary_args first, args second, kwargs third).
#
# nil values are compacted out so that you can pass in 'x ? :trait : nil' easily.
#
# Usage Examples:
#
# traits, kwargs = factorify_args(foo_args, :trait_1, some_key: :some_value)
# traits, kwargs = factorify_args(foo_args, [:trait_1, some_key: :some_value])
# traits, kwargs = factorify_args(foo_args, if_something? ? :trait_1 : nil, :trait_2)
#
# and then:
#
# association(:foo, *traits, **kwargs)
#
# In a factory, this would be used to set up pass-through arguments, for instance:
#
# factory :child do
#   transient
#     parent_args { nil } # potentially overridden by spec
#     default_parent_args { { some_key: some_value } }
#   end
#
#   # defaults could be handled via default_parent_args or passed in explicitly
#   parent {
#     traits, kwargs = factorify_args(parent_args, default_parent_args, key: :default)
#     association(:factory_name, *traits, **kwargs)
#   }
# end

module FactoryBot
  module Syntax
    module Methods
      def factorify_args(primary_args, *args, **kwargs)
        all_args = (
          Array.wrap(primary_args) +
          Array.wrap(args.flatten) +
          Array.wrap(kwargs)
        ).compact

        all_kwargs, all_traits = all_args.partition { |arg| arg.is_a?(Hash) }
        merged_kwargs = all_kwargs.inject({}, &:reverse_merge)

        [all_traits, merged_kwargs]
      end
    end
  end

  class Evaluator
    def overridden?(attribute)
      @overrides.key?(attribute.to_sym)
    end
  end

  class DefinitionProxy
    def parent_with_nested_args(name, factory: nil, optional: false)
      validate_arg_for_instance_eval(name)
      validate_name_format(name)
      validate_arg_for_instance_eval(factory.to_s)

      #
      # for a belongs_to association called "parent", this is basically what we're
      # generating (if optional: true is passed, this is wrapped in a :with_parent trait):
      #
      # transient do
      #   parent_args { nil }
      # end
      #
      # parent do
      #   traits, kwargs = factorify_args(
      #     parent_args,
      #     (defined? default_parent_args) ? default_parent_args : nil,
      #   )
      #
      #   association(:#{factory || 'parent'}, *traits, **kwargs)
      # end
      #

      with_trait = optional ? "with_#{name}" : nil

      ruby_code_string = <<-RUBY
        #{"trait :#{with_trait} do" if with_trait.present?}

        transient do
          #{name}_args { nil }
        end

        #{name} do
          traits, kwargs = factorify_args(
            #{name}_args,
            (defined? default_#{name}_args) ? default_#{name}_args : nil,
          )

          association(:#{factory || name}, *traits, **kwargs)
        end

        #{'end' if with_trait.present?}
      RUBY

      instance_eval(ruby_code_string)
    end

    def child_with_nested_args(name, has: :many, factory: nil, as: nil)
      validate_arg_for_instance_eval(name)
      validate_name_format(name)
      validate_arg_for_instance_eval(factory.to_s)

      #
      # for a child association called "child", this is essentially what we're generating
      # - excludes some argument validation and a begin/rescue for unexpected errors
      # - has: :one case is slightly different but conceptually the same
      #
      # transient do
      #   child_count { defined? default_child_count ? default_child_count : 0 }
      #   common_child_args { nil }
      #   mapped_child_args {
      #     defined? base_mapped_child_args ? base_mapped_child_args : []
      #   }
      # end
      #
      # after(:build, :stub) do |model, evaluator|
      #   next if model.children.any?
      #
      #   derived_count = [evaluator.mapped_child_args.length, evaluator.child_count].max
      #
      #   derived_association = "model.class.to_s.split(/::/).last.underscore"
      #                  <OR, if :as is provided>
      #   derived_association = :<:as>
      #
      #   derived_count.times do |i|
      #     traits, kwargs = factorify_args(
      #       evaluator.mapped_child_args[i],
      #       evaluator.common_child_args,
      #       evaluator.respond_to?(:default_common_child_args) ?
      #         evaluator.default_common_child_args :
      #         nil,
      #       derived_association.to_sym => model,
      #       strategy: :build,
      #     )
      #
      #     evaluator.association(:#{factory || name}, *traits, **kwargs)
      #   end
      # end
      #

      has_one = has == :one
      association = has_one ? name.to_s : name.to_s.pluralize

      ruby_code_string = <<-RUBY
        transient do
          #{name}_count { (defined? default_#{name}_count) ? default_#{name}_count : 0 }
          common_#{name}_args { nil }
          mapped_#{name}_args {
            (defined? base_mapped_#{name}_args) ? base_mapped_#{name}_args : []
          }
        end

        after(:create) do |model, evaluator|
          # when not using factory_bot_associations, the association is not loaded by default. Retain this behavior as some specs are dependent on it.
          model.association(:#{association}).reset if model.association(:#{association}).loaded? && model.#{association}.blank?
        end

        after(:build, :stub) do |model, evaluator|
          if (evaluator.mapped_#{name}_args.present? &&
            [0, evaluator.mapped_#{name}_args.size].exclude?(evaluator.#{name}_count)
          )
            raise ArgumentError.new(
              "#{name}_count and length of mapped_#{name}_args must align if both are " \\
                "provided. #{name}_count: \#{evaluator.#{name}_count}, " \\
                "mapped_#{name}_args length: \#{evaluator.mapped_#{name}_args.size}.",
            )
          end

          begin
            # if association child/children were explicitly passed in, we don't want to
            # create any more, so if the association is not empty, exit
            next if model.#{association}.#{has_one ? 'present?' : 'any?'}

            derived_count = [evaluator.mapped_#{name}_args.length, evaluator.#{name}_count].max

            derived_association = #{
              as.present? ? ":#{as}" : 'model.class.to_s.split(/::/).last.underscore'
            }

            derived_count.times do |i|
              traits, kwargs = factorify_args(
                evaluator.mapped_#{name}_args[i],
                evaluator.common_#{name}_args,
                evaluator.respond_to?(:default_common_#{name}_args) ?
                  evaluator.default_common_#{name}_args :
                  nil,
                derived_association.to_sym => model,
                strategy: :build,
              )

              if #{has_one}
                model.#{association} = evaluator.association(:#{factory || name}, *traits, **kwargs)
              else
                model.#{association} << evaluator.association(:#{factory || name}, *traits, **kwargs)
              end
            end
          rescue => e
            puts "An exception was raised in child_with_nested_args: \#{e.inspect}"
            raise e
          end
        end
      RUBY

      instance_eval(ruby_code_string)
    end

    private

    # check that user-provided arg is safe for instance_eval
    def validate_arg_for_instance_eval(arg)
      return if /\A[a-z_]*\z/.match?(arg)

      raise ArgumentError.new(
        "invalid argument (cannot contain anything other that lowercase letters and " \
        "underscore): #{arg.inspect}",
      )
    end

    def validate_name_format(name)
      return if ActiveSupport::Inflector.singularize(name) == name.to_s

      raise ArgumentError.new(
        "child_with_nested_args and parent_with_nested_args expects the association " \
        "name to be the singular form and '#{name.inspect}' is not.",
      )
    end
  end
end
