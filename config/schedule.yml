cohort_set_inactive_status:
  cron: "0 6 * * *" # 11pm pst every day
  class: "Cohort::SetInactiveStatusJob"
  description: "Update cohorts to inactive if they qualify"

cohort_update_ending_statuses:
  cron: "0 17 * * *" # 10am pst every day
  class: "Cohort::UpdateStatusesJob"
  description: "Update the status of all cohorts that trigger off ending dates like ends_on"
  args:
    - ends

cohort_update_starting_statuses:
  cron: "0 10 * * *" # 3am pst every day
  class: "Cohort::UpdateStatusesJob"
  description: "Update the status of all cohorts that trigger off starting dates like starts_on"
  args:
    - starts

collections_notifications:
  cron: '0 17 * * *' # 10am pst every day
  class: "Collection::NotificationsJob"
  description: "Send Collections emails for Days 1, 10, 15"

collection_day7_job:
  cron: '0 4 * * *' # 9pm pst every day
  class: "Collection::Day7Job"
  description: "Send Collections emails for Day 7 after automatic withdrawal"

deal_create_from_alternate_channels:
  cron: '*/15 * * * *' # every 15 minutes
  class: "Deal::CreateFromAlternateChannelsJob"
  description: "Creates deals for contacts from alternate channels"

deal_import_probability_of_enrollment_job:
  cron: "0 0 * * *"
  class: "Deal::ImportProbabilityOfEnrollmentJob"
  description: "Imports probability of enrollment from the datalake and publishes the deal to hubspot"

deal_update_stale_status:
  cron: "15 5 * * *" # 1:15am est every day
  class: "Deal::UpdateStaleStatusJob"
  description: "Marks as stale if there are any deals that have been in the 'Open' stage for more than the configured days"

external_content_trigger_process_new_submissions_job:
  cron: "*/20 * * * *" # every 20 minutes
  class: "ExternalContent::Trigger::ProcessNewSubmissionsJob"
  description: "Checks google form submissions for requests for external content and processes them"

promos_discount_code_disable_invalid:
  cron: "0 7 * * *" # 3am pst every day
  class: "Promos::DiscountCode::DisableInvalidJob"
  description: "Disables invalid discount codes if max uses are exhausted or outside date range"

section_alert_missing_setup:
  cron: "0 11 * * *" # 7am est every day
  class: "Section::AlertMissingSetupJob"
  description: "Alert on sections missing setup and starting soon"

update_pending_failed_dns_status_job:
  cron: "*/5 * * * *" # every 5 mins
  class: "Partner::UpdatePendingFailedDnsStatusJob"
  description: "Update the status of all pending / failed partner dns"

update_connected_dns_status_job:
  cron: "58 * * * *" # hourly at minute 58
  class: "Partner::UpdateConnectedDnsStatusJob"
  description: "Update the status of all connected partner dns"

update_course_risk_job:
  cron: "0 8 * * TUE" # 4am est every Tuesday
  class: "Enrollment::UpdateCourseRiskJob"
  description: "Update the course_risk for all active enrollments"

update_pass_status_job:
  cron: "0 * * * *" # every hour
  class: "Enrollment::UpdatePassStatusJob"
  description: "Update the pass status for passable active enrollments"

process_certificates_job:
  cron: "20 * * * *" # hourly at minute 20
  class: "Enrollment::ProcessCertificatesJob"
  description: "Issue Certificates and Update the certificate_issued status for enrollments that have passed"

update_no_pass_status_job:
  cron: "30 * * * *" # hourly at minute 30
  class: "Enrollment::UpdateNoPassStatusJob"
  description: "Update the fail status for enrollments that haven't passed after extensions have ended"

sidekiq_long_running_job_slack_alert_job:
  cron: "*/5 * * * *" # every 5 minutes
  class: "SidekiqLongRunningJobSlackAlertJob"
  description: "Send slack alerts for any long running sidekiq jobs"

postgres_vacuumer_low_visibility:
  cron: "7 0-9,11-23 * * *" # every hour except 3am pst (when the postgres_vacuumer_all job runs)
  class: "PostgresVacuumerJob"
  description: "Vacuum low visibility tables"
  args:
    - low_visibility

postgres_vacuumer_all:
  cron: "7 10 * * *" # 3:07am pst
  class: "PostgresVacuumerJob"
  description: "Vacuum all tables"
  args:
    - all

purge_unattached_active_storage_blobs:
  cron: "0 10 * * 7" # 3am pst every Sunday
  class: "PurgeUnattachedActiveStorageBlobsJob"
  description: "Purge unattached active storage blobs"

promos_cross_sell_list_create_cross_sells_job:
  cron: "10 * * * *" # every hour
  class: "Promos::CrossSellList::CreateCrossSellsJob"
  description: "Create cross sells (and deals) for active cross sell lists"

partner_alert_missing_certificate_theme_job:
  cron: "0 19 * * *" # 11am pst every day
  class: "Partner::AlertMissingCertificateThemeJob"
  description: "Alert on partners missing certificate themes"

partner_program_alert_missing_certificate_badge_job:
  cron: "0 21 * * *" # 1pm pst every day
  class: "PartnerProgram::AlertMissingCertificateBadgeJob"
  description: "Alert on partner programs missing certificate badges"

risk_assessment_job:
  cron: "35 * * * *" # hourly at minute 35
  class: "LearningDelivery::RiskAssessment::AssessJob"
  description: "Compute and update the overall risk level on enrollments"

fetch_submissions_unsubmitted:
  cron: "0 5 * * * America/Los_Angeles" # nightly at 5am Pacific
  class: "Section::FetchSubmissionsJob"
  description: "Sync unsubmitted submissions for all sections"
  args:
    - ["unsubmitted"]

fetch_submissions_submitted:
  cron: "15 * * * *" # hourly at 15 minutes past the hour
  class: "Section::FetchSubmissionsJob"
  description: "Sync submitted submissions for all sections"
  args:
    - ["submitted"]

fetch_submissions_graded:
  cron: "45 * * * *" # hourly at 45 minutes past the hour
  class: "Section::FetchSubmissionsJob"
  description: "Sync graded submissions for all sections"
  args:
    - ["graded"]

mark_outdated_reviews_as_stale_job:
  cron: "0 * * * *" # every hour
  class: "Lms::Submission::Review::MarkOutdatedReviewsAsStaleJob"
  description: "Mark reviews that are not current as stale"

lms_submission_batch_auto_grade:
  cron: "25 * * * *" # hourly at minute 25
  class: "Lms::Submission::BatchAutoGradeJob"
  description: "Auto-grades submissions from active sections with grader assignments"

lms_submission_batch_publish_job:
  cron: "45 * * * *" # hourly at minute 45
  class: "Lms::Submission::BatchPublishJob"
  description: "Publishes all graded submission reviews that haven't been published yet"

lms_submission_review_process_auto_graded_reviews:
  cron: "*/10 * * * *" # every 10 minutes
  class: "Lms::Submission::Review::ProcessAutoGradedReviewsJob"
  description: "Processes all auto-graded reviews by applying rules to determine if they need manual review"

lms_submission_inactive_review_slack_alert:
  cron: "*/30 * * * *" # every 30 minutes
  class: "Lms::Submission::InactiveReviewSlackAlertJob"
  description: "Send Slack alerts for submission reviews stuck in non-terminal states for over 4 hours"

enrollment_create_tasks_for_merged_remote_contacts:
  cron: "0 15 * * *" # 8am pst every day
  class: "Enrollment::CreateTasksForMergedRemoteContactsJob"
  description: "Create tasks for enrollments with different core learners but same Hubspot Contact"

<% if Rails.env.production? %>
create_db_snapshot_job:
  cron: "0 9 * * * US/Pacific"
  class: "CreateDbSnapshotJob"
  description: "Create an obfuscated snapshot of the production database for use by the db-snapshot tool"
<% end %>

live_session_review_import_job:
  cron: "40 * * * *" # hourly at minute 40
  class: "LiveSession::Review::ImportJob"
  description: "Imports live session reviews hourly"

live_session_create_cloud_calendar_events_job:
  cron: "0 12 * * *" # 5am pst every day
  class: "LiveSession::CreateCloudCalendarEventsJob"
  description: "Creates Google Calendar events for live sessions that are approaching their cohort start date"

learning_delivery_employee_cohort_notification_job:
  cron: "0 12 * * MON,WED" # 5am pst every Monday and Wednesday
  class: "LearningDelivery::Employee::CohortNotificationJob"
  description: "Sends notifications to employees about their cohort"

enrollment_audit_lms_activity_active_cohorts:
  cron: "15 * * * *" # hourly at minute 15
  class: "Enrollment::AuditLmsActivityByCohortJob"
  args:
    - [opened, started, ended]
  description: "Updates last_lms_activity_at for learners in active cohorts (opened, started, ended) hourly"

enrollment_audit_lms_activity_extension_period_ended:
  cron: "30 3 * * * America/Los_Angeles" # daily at 3:30 AM Pacific
  class: "Enrollment::AuditLmsActivityByCohortJob"
  args:
    - extension_period_ended
  description: "Updates last_lms_activity_at for learners in extension_period_ended cohorts daily"

enrollment_audit_lms_activity_inactive_cohorts:
  cron: "45 2 * * 0 America/Los_Angeles" # weekly on Sunday at 2:45 AM Pacific
  class: "Enrollment::AuditLmsActivityByCohortJob"
  args:
    - inactive
  description: "Updates last_lms_activity_at for learners in inactive cohorts (archived, canceled) weekly"


registration_send_manual_registration_reminders:
  cron: "0 16 * * *" # 9am pst every day
  class: "Registration::SendManualRegistrationRemindersJob"
  description: "Send manual registration reminder emails to learners who haven't confirmed their registration"

cohort_sync_lms_resources:
  cron: "0 4 * * * America/Los_Angeles" # 4am Pacific daily
  class: "Cohort::SyncLmsResourcesJob"
  description: "Daily refresh of LMS module and assignment data from Canvas for all active cohorts"
