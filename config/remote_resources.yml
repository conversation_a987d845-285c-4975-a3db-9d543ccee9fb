# https://guides.rubyonrails.org/configuring.html#custom-configuration
shared:
  hubspot:
    api_origin: https://api.hubspot.com
    app_origin: https://app.hubspot.com
    portal_time_zone: 'Pacific Time (US & Canada)'
    contact:
      type_id: 0-1
    deal:
      type_id: 0-3
    product:
      type_id: 0-7
    line_item:
      type_id: 0-8
    form_submission:
      api_origin: https://api.hsforms.com
  tiny_url:
    api_origin: https://api.tinyurl.com
  everest_validity:
    api_origin: https://api.everest.validity.com
  zendesk:
    api_origin: https://ziplineseducation.zendesk.com

production:
  accredible:
    api_origin: https://api.accredible.com
    app_origin: https://dashboard.accredible.com
    department_id: 57973
  canvas:
    api_origin: https://learn.ziplines.com
    app_origin: https://learn.ziplines.com
  hubspot:
    portal: 3461273
    deal: &deal_config
      student_pipeline:
        id: caf123b0-9451-4172-a46b-f27c47c45024 # B2C Pipeline
        days_to_stale: <%= ENV.fetch('HUBSPOT_DAYS_TO_STALE', 120).to_i %>
        stages: # this is used to determine the values to be used for hubspot deal status, and pipeline stage when syncing to deals
          # Note that the multiple statuses can map to the same pipeline stage
          # Multiple lifecycle stages can map to the same pipeline stage
          lead:
            status_key: "Referral"
            stage_label: "Lead"
            stage_key: "171375651"
          prospect:
            status_key: "Form (Syllabus)"
            stage_label: "Prospect"
            stage_key: "171375652"
          registrant:
            status_key: "Registered"
            stage_label: "Registrant"
            stage_key: "171375653"
          enrollee:
            status_key: "Enrolled"
            stage_label: "Enrollee"
            stage_key: "171375654"
          learner:
            status_key: "Enrolled"
            stage_label: "Learner"
            stage_key: "171375655"
          post_learner:
            status_key: "Enrolled"
            stage_label: "Post Learner"
            stage_key: "171375656"
          other:
            status_key: "Drop / Unenroll / Withdraw"
            stage_label: "Other"
            stage_key: "171375657"
          suspect:
            status_key: "Stale - No enrollment within 120 days"
            stage_label: "Suspect"
            stage_key: "171375650"
    form_submission:
      forms:
        syllabus_request:
          form_guid: f361da07-851e-4a36-92d6-39e8c0d70219
          form_fields: &tracked_fields
            - email
            - phone
            - contact_text_form_opt_in
            - gclid
            - fbclid
            - utm_source
            - utm_medium
            - utm_campaign
            - utm_term
            - utm_content
        registration:
          form_guid: 5aa07dde-e6bb-4a06-996a-7c3f14f39619
          form_fields: *tracked_fields
    list:
      contacts:
        processed_from_alternate_channels_id: 25019
        unprocessed_from_alternate_channels_id: 17414

staging:
  accredible: &staging_accredible
    api_origin: https://sandbox.api.accredible.com
    app_origin: https://sandbox.dashboard.accredible.com
    department_id: 2319 # on sandbox we do not have departments. this is the account id
  canvas: &staging_canvas
    api_origin: https://learn-staging.ziplines.com
    app_origin: https://learn-staging.ziplines.com
  hubspot: &staging_hubspot
    portal: ********
    deal:
      <<: *deal_config
    form_submission:
      forms:
        syllabus_request:
          form_guid: 13bc7c78-5999-4a36-8daf-1a8d250b7b3e
          form_fields: *tracked_fields
        registration:
          form_guid: fe7eac52-cc66-41cb-a248-99c3cc6fbded
          form_fields: *tracked_fields
    list:
      contacts:
        processed_from_alternate_channels_id: 8702
        unprocessed_from_alternate_channels_id: 1456

development: &development
  accredible:
    <<: *staging_accredible
  canvas:
    <<: *staging_canvas
  hubspot:
    <<: *staging_hubspot

test:
  <<: *development
