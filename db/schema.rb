# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source <PERSON>s uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_07_21_045600) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_catalog.plpgsql"

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "addresses", force: :cascade do |t|
    t.string "street_address_1", null: false
    t.string "street_address_2"
    t.string "state", null: false
    t.string "city", null: false
    t.string "zip", null: false
    t.string "country", null: false
    t.decimal "latitude"
    t.decimal "longitude"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "admin_notes", force: :cascade do |t|
    t.bigint "admin_user_id", null: false
    t.string "resource_type", null: false
    t.bigint "resource_id", null: false
    t.string "uid", null: false
    t.text "body", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["admin_user_id"], name: "index_admin_notes_on_admin_user_id"
    t.index ["resource_type", "resource_id"], name: "index_admin_notes_on_resource"
    t.index ["uid"], name: "index_admin_notes_on_uid", unique: true
  end

  create_table "admin_user_permissions", force: :cascade do |t|
    t.bigint "admin_user_id", null: false
    t.bigint "permission_id", null: false
    t.bigint "role_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["admin_user_id", "permission_id", "role_id"], name: "idx_on_admin_user_id_permission_id_role_id_6a2acbcf4a", unique: true
    t.index ["permission_id"], name: "index_admin_user_permissions_on_permission_id"
    t.index ["role_id"], name: "index_admin_user_permissions_on_role_id"
  end

  create_table "admin_user_roles", force: :cascade do |t|
    t.bigint "admin_user_id", null: false
    t.bigint "role_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["admin_user_id"], name: "index_admin_user_roles_on_admin_user_id"
    t.index ["role_id"], name: "index_admin_user_roles_on_role_id"
  end

  create_table "admin_users", force: :cascade do |t|
    t.string "email", null: false
    t.string "first_name", null: false
    t.string "last_name", null: false
    t.datetime "remember_created_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "provider"
    t.string "uid"
    t.string "time_zone", default: "UTC", null: false
    t.string "slack_member_id"
    t.integer "status", default: 1, null: false
    t.index ["email"], name: "index_admin_users_on_email", unique: true
    t.index ["status"], name: "index_admin_users_on_status"
  end

  create_table "alert_status_changes", force: :cascade do |t|
    t.bigint "alert_id", null: false
    t.bigint "admin_user_id"
    t.text "notes"
    t.integer "status_was"
    t.integer "status_became"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["admin_user_id"], name: "index_alert_status_changes_on_admin_user_id"
    t.index ["alert_id"], name: "index_alert_status_changes_on_alert_id"
  end

  create_table "alerts", force: :cascade do |t|
    t.bigint "owner_id"
    t.string "resource_type"
    t.bigint "resource_id"
    t.string "key", null: false
    t.integer "status", default: 0, null: false
    t.string "title", null: false
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["key"], name: "index_alerts_on_key", unique: true
    t.index ["owner_id"], name: "index_alerts_on_owner_id"
    t.index ["resource_type", "resource_id"], name: "index_alerts_on_resource"
    t.index ["status"], name: "index_alerts_on_status"
  end

  create_table "certificate_themes", force: :cascade do |t|
    t.bigint "partner_id"
    t.bigint "partner_program_id"
    t.string "administrator_name"
    t.string "administrator_title"
    t.string "partner_name"
    t.integer "certificate_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["partner_id"], name: "index_certificate_themes_on_partner_id"
    t.index ["partner_program_id"], name: "index_certificate_themes_on_partner_program_id"
  end

  create_table "cohort_weeks", force: :cascade do |t|
    t.string "uid", null: false
    t.integer "number", null: false
    t.bigint "cohort_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["cohort_id", "number"], name: "index_cohort_weeks_on_cohort_id_and_number", unique: true
    t.index ["uid"], name: "index_cohort_weeks_on_uid", unique: true
  end

  create_table "cohorts", force: :cascade do |t|
    t.bigint "program_id", null: false
    t.string "key", null: false
    t.integer "status", default: 0, null: false
    t.date "lms_opens_on", null: false
    t.date "lms_closes_on", null: false
    t.date "starts_on", null: false
    t.date "ends_on", null: false
    t.date "add_period_starts_on"
    t.date "add_period_ends_on", null: false
    t.date "drop_period_ends_on", null: false
    t.date "extension_period_ends_on", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.date "transfer_period_ends_on", null: false
    t.string "name", null: false
    t.index ["key"], name: "index_cohorts_on_key", unique: true
    t.index ["name"], name: "index_cohorts_on_name"
    t.index ["program_id"], name: "index_cohorts_on_program_id"
    t.index ["status"], name: "index_cohorts_on_status"
  end

  create_table "crm_events", force: :cascade do |t|
    t.string "event_type", null: false
    t.jsonb "event_data", default: {}, null: false
    t.datetime "processed_at"
    t.text "ignored_reason"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "deals", force: :cascade do |t|
    t.string "name"
    t.integer "status"
    t.integer "stage"
    t.datetime "closed_at"
    t.bigint "email_id", null: false
    t.bigint "partner_program_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "clas_key"
    t.integer "source", default: 0, null: false
    t.datetime "revived_at"
    t.decimal "probability_of_enrollment_after_7_days"
    t.datetime "last_became_prospect_at"
    t.datetime "last_became_registrant_at"
    t.index ["clas_key"], name: "index_deals_on_clas_key", unique: true
    t.index ["email_id", "partner_program_id"], name: "index_deals_on_email_id_and_partner_program_id", unique: true, where: "(status = 0)"
    t.index ["email_id"], name: "index_deals_on_email_id"
    t.index ["partner_program_id"], name: "index_deals_on_partner_program_id"
  end

  create_table "ecom_order_items", force: :cascade do |t|
    t.integer "price_cents", default: 0, null: false
    t.bigint "order_id", null: false
    t.bigint "variant_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "registration_id"
    t.bigint "finance_contract_id"
    t.string "uid"
    t.string "currency_code", default: "USD", null: false
    t.index ["finance_contract_id"], name: "index_ecom_order_items_on_finance_contract_id"
    t.index ["order_id"], name: "index_ecom_order_items_on_order_id"
    t.index ["registration_id"], name: "index_ecom_order_items_on_registration_id"
    t.index ["uid"], name: "index_ecom_order_items_on_uid", unique: true
    t.index ["variant_id"], name: "index_ecom_order_items_on_variant_id"
  end

  create_table "ecom_orders", force: :cascade do |t|
    t.string "uid", null: false
    t.bigint "partner_id", null: false
    t.bigint "learner_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "status", default: 0, null: false
    t.string "short_payment_url"
    t.integer "purpose", default: 0, null: false
    t.index ["learner_id"], name: "index_ecom_orders_on_learner_id"
    t.index ["partner_id"], name: "index_ecom_orders_on_partner_id"
    t.index ["uid"], name: "index_ecom_orders_on_uid", unique: true
  end

  create_table "ecom_payment_methods", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "kind", default: 0, null: false
    t.boolean "offer_on_site", default: false, null: false
    t.boolean "offer_on_manual_create", default: false, null: false
    t.index ["kind"], name: "index_ecom_payment_methods_on_kind", unique: true
    t.index ["offer_on_manual_create"], name: "index_ecom_payment_methods_on_offer_on_manual_create"
    t.index ["offer_on_site"], name: "index_ecom_payment_methods_on_offer_on_site"
  end

  create_table "ecom_payments", force: :cascade do |t|
    t.integer "total_cents", default: 0, null: false
    t.integer "status", default: 0, null: false
    t.bigint "order_id", null: false
    t.bigint "payment_method_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "upfront_discount_opt_out_cents", default: 0, null: false
    t.integer "subtotal_cents", default: 0, null: false
    t.integer "discount_cents", default: 0, null: false
    t.string "currency_code", default: "USD", null: false
    t.index ["order_id"], name: "index_ecom_payments_on_order_id"
    t.index ["payment_method_id"], name: "index_ecom_payments_on_payment_method_id"
  end

  create_table "ecom_products", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "program_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "list_cents", default: 0, null: false
    t.integer "standard_discount_cents", null: false
    t.integer "upfront_discount_cents", null: false
    t.bigint "partner_id"
    t.boolean "active", default: true, null: false
    t.string "currency_code", default: "USD", null: false
    t.index ["partner_id"], name: "index_ecom_products_on_partner_id"
    t.index ["program_id"], name: "index_ecom_products_on_program_id"
  end

  create_table "ecom_refunds", force: :cascade do |t|
    t.integer "status", default: 0, null: false
    t.integer "refund_cents", default: 0, null: false
    t.text "notes"
    t.bigint "payment_id", null: false
    t.bigint "admin_user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "payments_transaction_charge_id", null: false
    t.bigint "payments_transaction_refund_id"
    t.integer "reason", default: 50, null: false
    t.boolean "affects_total_price", default: true, null: false
    t.string "currency_code", default: "USD", null: false
    t.index ["admin_user_id"], name: "index_ecom_refunds_on_admin_user_id"
    t.index ["payment_id"], name: "index_ecom_refunds_on_payment_id"
    t.index ["payments_transaction_charge_id"], name: "index_ecom_refunds_on_payments_transaction_charge_id"
    t.index ["payments_transaction_refund_id"], name: "index_ecom_refunds_on_payments_transaction_refund_id"
  end

  create_table "ecom_variants", force: :cascade do |t|
    t.string "sku", null: false
    t.string "name", null: false
    t.bigint "product_id", null: false
    t.bigint "section_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "list_cents", default: 0, null: false
    t.integer "standard_discount_cents", null: false
    t.integer "upfront_discount_cents", null: false
    t.bigint "partner_id"
    t.string "currency_code", default: "USD", null: false
    t.index ["partner_id"], name: "index_ecom_variants_on_partner_id"
    t.index ["product_id"], name: "index_ecom_variants_on_product_id"
    t.index ["section_id", "partner_id"], name: "index_ecom_variants_on_section_id_and_partner_id", unique: true
    t.index ["sku"], name: "index_ecom_variants_on_sku", unique: true
  end

  create_table "email_validation_results", force: :cascade do |t|
    t.integer "category", null: false
    t.string "status", null: false
    t.string "risk", null: false
    t.string "recommendation", null: false
    t.bigint "email_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email_id"], name: "index_email_validation_results_on_email_id"
  end

  create_table "emails", force: :cascade do |t|
    t.bigint "learner_id"
    t.boolean "primary", default: false, null: false
    t.string "address", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["address"], name: "index_emails_on_address", unique: true
    t.index ["learner_id"], name: "index_emails_on_learner_id"
  end

  create_table "enabled_payment_methods", force: :cascade do |t|
    t.bigint "ecom_payment_method_id", null: false
    t.bigint "partner_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["ecom_payment_method_id"], name: "index_enabled_payment_methods_on_ecom_payment_method_id"
    t.index ["partner_id"], name: "index_enabled_payment_methods_on_partner_id"
  end

  create_table "enrollment_status_changes", force: :cascade do |t|
    t.bigint "enrollment_id", null: false
    t.bigint "admin_user_id"
    t.integer "status_was"
    t.integer "status_became", null: false
    t.string "reason"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "notes"
    t.index ["admin_user_id"], name: "index_enrollment_status_changes_on_admin_user_id"
    t.index ["enrollment_id"], name: "index_enrollment_status_changes_on_enrollment_id"
  end

  create_table "enrollment_transfers", force: :cascade do |t|
    t.bigint "registration_id", null: false
    t.bigint "transferred_from_id", null: false
    t.bigint "transferred_to_id", null: false
    t.bigint "admin_user_id"
    t.boolean "limited", default: false, null: false
    t.boolean "before_limited_threshold", default: false, null: false
    t.boolean "same_cohort", default: false, null: false
    t.text "reason"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "transferred_at", null: false
    t.index ["admin_user_id"], name: "index_enrollment_transfers_on_admin_user_id"
    t.index ["registration_id"], name: "index_enrollment_transfers_on_registration_id"
    t.index ["transferred_at"], name: "index_enrollment_transfers_on_transferred_at"
    t.index ["transferred_from_id"], name: "index_enrollment_transfers_on_transferred_from_id", unique: true
    t.index ["transferred_to_id"], name: "index_enrollment_transfers_on_transferred_to_id", unique: true
  end

  create_table "enrollments", force: :cascade do |t|
    t.string "uid", null: false
    t.bigint "ecom_order_item_id", null: false
    t.bigint "learner_id", null: false
    t.bigint "partner_program_id", null: false
    t.bigint "registration_id", null: false
    t.bigint "section_id", null: false
    t.integer "status", default: 0, null: false
    t.date "extended_until"
    t.boolean "primary", default: true, null: false
    t.integer "certifier", default: 0, null: false
    t.string "certificate_url"
    t.datetime "certificate_issued_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "clas_cohort_admission_key"
    t.bigint "deal_id"
    t.integer "course_risk"
    t.datetime "course_risk_reviewed_at"
    t.bigint "extended_by_id"
    t.string "extension_reason"
    t.date "exit_requested_on"
    t.integer "risk_level", default: 0, null: false
    t.datetime "last_lms_activity_at"
    t.index ["clas_cohort_admission_key"], name: "index_enrollments_on_clas_cohort_admission_key", unique: true
    t.index ["deal_id"], name: "index_enrollments_on_deal_id"
    t.index ["ecom_order_item_id"], name: "index_enrollments_on_ecom_order_item_id"
    t.index ["extended_by_id"], name: "index_enrollments_on_extended_by_id"
    t.index ["learner_id"], name: "index_enrollments_on_learner_id"
    t.index ["partner_program_id"], name: "index_enrollments_on_partner_program_id"
    t.index ["registration_id"], name: "index_enrollments_on_registration_id"
    t.index ["section_id"], name: "index_enrollments_on_section_id"
    t.index ["status"], name: "index_enrollments_on_status"
    t.index ["uid"], name: "index_enrollments_on_uid", unique: true
  end

  create_table "external_content_code_uploads", force: :cascade do |t|
    t.string "uid", null: false
    t.bigint "material_id", null: false
    t.bigint "admin_user_id", null: false
    t.integer "rows_processed"
    t.integer "rows_with_errors"
    t.integer "status", default: 0, null: false
    t.json "results", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["admin_user_id"], name: "index_external_content_code_uploads_on_admin_user_id"
    t.index ["material_id"], name: "index_external_content_code_uploads_on_material_id"
    t.index ["uid"], name: "index_external_content_code_uploads_on_uid", unique: true
  end

  create_table "external_content_code_uses", force: :cascade do |t|
    t.string "uid", null: false
    t.bigint "code_id", null: false
    t.bigint "enrollment_id", null: false
    t.integer "status", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "material_id"
    t.bigint "material_set_id", null: false
    t.index ["code_id"], name: "index_eccu_on_code_id"
    t.index ["enrollment_id"], name: "index_external_content_code_uses_on_enrollment_id"
    t.index ["material_id", "enrollment_id"], name: "idx_on_material_id_enrollment_id_c0498720b5", unique: true
    t.index ["material_set_id"], name: "index_external_content_code_uses_on_material_set_id"
    t.index ["uid"], name: "index_external_content_code_uses_on_uid", unique: true
  end

  create_table "external_content_codes", force: :cascade do |t|
    t.string "uid", null: false
    t.string "code", null: false
    t.integer "max_uses", null: false
    t.datetime "available_from"
    t.datetime "available_until"
    t.bigint "material_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "code_upload_id"
    t.index ["code", "material_id"], name: "index_external_content_codes_on_code_and_material_id"
    t.index ["code_upload_id"], name: "index_external_content_codes_on_code_upload_id"
    t.index ["material_id"], name: "index_ecc_on_material_id"
    t.index ["uid"], name: "index_external_content_codes_on_uid", unique: true
  end

  create_table "external_content_material_set_items", force: :cascade do |t|
    t.bigint "material_set_id", null: false
    t.bigint "material_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["material_id", "material_set_id"], name: "index_material_set_items_on_material_and_material_set", unique: true
    t.index ["material_set_id"], name: "index_external_content_material_set_items_on_material_set_id"
  end

  create_table "external_content_material_sets", force: :cascade do |t|
    t.text "email_template"
    t.bigint "program_id", null: false
    t.string "uid", null: false
    t.string "name", null: false
    t.integer "status", default: 1, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["program_id"], name: "index_external_content_material_sets_on_program_id"
    t.index ["status"], name: "index_external_content_material_sets_on_status"
    t.index ["uid"], name: "index_external_content_material_sets_on_uid", unique: true
  end

  create_table "external_content_materials", force: :cascade do |t|
    t.string "name", null: false
    t.string "variable_name", null: false
    t.string "uid", null: false
    t.bigint "material_set_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["material_set_id"], name: "index_ecm_on_material_set_id"
    t.index ["uid"], name: "index_external_content_materials_on_uid", unique: true
    t.index ["variable_name"], name: "index_external_content_materials_on_variable_name", unique: true
  end

  create_table "external_content_triggers", force: :cascade do |t|
    t.string "uid", null: false
    t.string "name", null: false
    t.bigint "material_set_id", null: false
    t.string "form_key", null: false
    t.string "email_question_label", null: false
    t.string "email_question_key"
    t.string "conditional_question_label"
    t.string "conditional_question_key"
    t.string "conditional_answer"
    t.datetime "last_checked_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["form_key"], name: "index_ect_on_form_key"
    t.index ["material_set_id"], name: "index_ect_on_external_content_material_set_id"
    t.index ["uid"], name: "index_external_content_triggers_on_uid", unique: true
  end

  create_table "finance_bookings", force: :cascade do |t|
    t.string "type", null: false
    t.datetime "booked_at", null: false
    t.integer "product_cents", null: false
    t.integer "upfront_discount_opt_out_cents", null: false
    t.integer "processing_fee_carve_out_cents", null: false
    t.integer "principal_share_cents", null: false
    t.integer "partner_share_cents", null: false
    t.bigint "ecom_order_item_id"
    t.bigint "ecom_payment_id"
    t.string "uid", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "discount_cents", default: 0, null: false
    t.bigint "ecom_refund_id"
    t.string "currency_code", default: "USD", null: false
    t.integer "product_discount_cents", default: 0, null: false
    t.index ["booked_at"], name: "index_finance_bookings_on_booked_at"
    t.index ["ecom_order_item_id"], name: "index_finance_bookings_on_ecom_order_item_id"
    t.index ["ecom_payment_id"], name: "index_finance_bookings_on_ecom_payment_id"
    t.index ["ecom_refund_id"], name: "index_finance_bookings_on_ecom_refund_id"
    t.index ["type"], name: "index_finance_bookings_on_type"
    t.index ["uid"], name: "index_finance_bookings_on_uid", unique: true
  end

  create_table "finance_contracts", force: :cascade do |t|
    t.bigint "relationship_id", null: false
    t.date "starts_on", null: false
    t.integer "per_unit_revenue_cents"
    t.decimal "fee_rate"
    t.decimal "processing_fee_carve_out_rate", default: "0.0", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "currency_code", default: "USD", null: false
    t.index ["relationship_id"], name: "index_finance_contracts_on_relationship_id"
    t.index ["starts_on", "relationship_id"], name: "index_finance_contracts_on_starts_on_and_relationship_id", unique: true
  end

  create_table "finance_receipts", force: :cascade do |t|
    t.bigint "booking_id", null: false
    t.bigint "payments_transaction_id", null: false
    t.datetime "received_at", null: false
    t.integer "total_cents", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "currency_code", default: "USD", null: false
    t.index ["booking_id"], name: "index_finance_receipts_on_booking_id"
    t.index ["payments_transaction_id"], name: "index_finance_receipts_on_payments_transaction_id"
  end

  create_table "finance_relationships", force: :cascade do |t|
    t.bigint "partner_id", null: false
    t.string "name", null: false
    t.boolean "default", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "uid", null: false
    t.index ["partner_id", "default"], name: "index_finance_relationships_on_partner_id_and_default"
    t.index ["uid"], name: "index_finance_relationships_on_uid", unique: true
  end

  create_table "learners", force: :cascade do |t|
    t.string "first_name", null: false
    t.string "last_name", null: false
    t.string "phone_standardized", null: false
    t.string "time_zone", default: "UTC", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "uid", null: false
    t.string "sis_key", null: false
    t.string "canvas_registration_url"
    t.index ["sis_key"], name: "index_learners_on_sis_key", unique: true
    t.index ["uid"], name: "index_learners_on_uid", unique: true
  end

  create_table "learning_delivery_activities", force: :cascade do |t|
    t.integer "activity_type", null: false
    t.string "title", null: false
    t.text "description"
    t.json "metadata"
    t.bigint "employee_id", null: false
    t.string "target_type"
    t.bigint "target_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["activity_type", "employee_id"], name: "idx_on_activity_type_employee_id_783bac81e2"
    t.index ["employee_id", "created_at"], name: "idx_on_employee_id_created_at_b1ebf93a5d"
    t.index ["employee_id"], name: "index_learning_delivery_activities_on_employee_id"
    t.index ["target_type", "target_id"], name: "index_learning_delivery_activities_on_target"
  end

  create_table "learning_delivery_availabilities", force: :cascade do |t|
    t.bigint "availability_slot_id", null: false
    t.bigint "employee_id", null: false
    t.integer "status", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["availability_slot_id", "employee_id"], name: "index_ld_availabilities_on_se", unique: true
    t.index ["employee_id"], name: "index_learning_delivery_availabilities_on_employee_id"
  end

  create_table "learning_delivery_availability_slots", force: :cascade do |t|
    t.date "date", null: false
    t.time "start_time", null: false
    t.time "end_time", null: false
    t.integer "day_of_the_week", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["date", "day_of_the_week", "start_time", "end_time"], name: "index_ld_availability_slots_on_date_and_time", unique: true
  end

  create_table "learning_delivery_competencies", force: :cascade do |t|
    t.bigint "program_id", null: false
    t.bigint "employee_id", null: false
    t.integer "level", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["employee_id", "program_id"], name: "idx_ld_competencies_on_employee_and_program", unique: true
    t.index ["employee_id"], name: "index_learning_delivery_competencies_on_employee_id"
    t.index ["program_id"], name: "index_learning_delivery_competencies_on_program_id"
  end

  create_table "learning_delivery_employee_roles", force: :cascade do |t|
    t.bigint "employee_id", null: false
    t.bigint "role_id", null: false
    t.string "uid"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["employee_id", "role_id"], name: "index_learning_delivery_employee_roles_on_employee_and_role", unique: true
    t.index ["employee_id"], name: "index_learning_delivery_employee_roles_on_employee_id"
    t.index ["role_id"], name: "index_learning_delivery_employee_roles_on_role_id"
    t.index ["uid"], name: "index_learning_delivery_employee_roles_on_uid", unique: true
  end

  create_table "learning_delivery_employees", force: :cascade do |t|
    t.string "uid", null: false
    t.string "first_name", null: false
    t.string "last_name", null: false
    t.string "personal_email", null: false
    t.string "company_email"
    t.string "linked_in_url"
    t.string "company_profile_url"
    t.date "joined_on"
    t.integer "status", default: 0, null: false
    t.text "notes"
    t.boolean "on_website", default: false, null: false
    t.string "time_zone", null: false
    t.text "availability_notes"
    t.string "type"
    t.bigint "admin_user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "sis_key"
    t.integer "max_concurrent_sections"
    t.string "canvas_registration_url"
    t.index ["admin_user_id"], name: "index_learning_delivery_employees_on_admin_user_id"
    t.index ["company_email"], name: "index_learning_delivery_employees_on_company_email", unique: true
    t.index ["personal_email"], name: "index_learning_delivery_employees_on_personal_email", unique: true
    t.index ["sis_key"], name: "index_learning_delivery_employees_on_sis_key", unique: true
    t.index ["status"], name: "index_learning_delivery_employees_on_status"
    t.index ["type"], name: "index_learning_delivery_employees_on_type"
    t.index ["uid"], name: "index_learning_delivery_employees_on_uid", unique: true
  end

  create_table "learning_delivery_risk_assessments", force: :cascade do |t|
    t.bigint "enrollment_id", null: false
    t.integer "risk_type", null: false
    t.integer "risk_level", default: 0, null: false
    t.jsonb "risk_details", default: {}, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "assessed_at", precision: nil, null: false
    t.index ["enrollment_id", "risk_type"], name: "idx_learning_delivery_risk_assessments_on_enrollment_and_type", unique: true
    t.index ["enrollment_id"], name: "index_learning_delivery_risk_assessments_on_enrollment_id"
  end

  create_table "learning_delivery_roles", force: :cascade do |t|
    t.string "uid", null: false
    t.string "name", null: false
    t.string "abbreviation", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["abbreviation"], name: "index_learning_delivery_roles_on_abbreviation", unique: true
    t.index ["name"], name: "index_learning_delivery_roles_on_name", unique: true
    t.index ["uid"], name: "index_learning_delivery_roles_on_uid", unique: true
  end

  create_table "learning_delivery_section_availability_slots", force: :cascade do |t|
    t.bigint "section_id", null: false
    t.bigint "availability_slot_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["availability_slot_id"], name: "index_ld_section_availability_slots_on_a"
    t.index ["section_id", "availability_slot_id"], name: "index_ld_section_availability_slots_on_ss", unique: true
  end

  create_table "learning_delivery_task_comments", force: :cascade do |t|
    t.bigint "task_id", null: false
    t.bigint "author_id", null: false
    t.text "text", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["author_id"], name: "index_learning_delivery_task_comments_on_author_id"
    t.index ["task_id", "created_at"], name: "idx_on_task_id_created_at_3c93ec242d"
    t.index ["task_id"], name: "index_learning_delivery_task_comments_on_task_id"
  end

  create_table "learning_delivery_task_groups", force: :cascade do |t|
    t.string "uid", null: false
    t.string "title", null: false
    t.text "description"
    t.integer "status", default: 0, null: false
    t.bigint "created_by_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_by_id"], name: "index_learning_delivery_task_groups_on_created_by_id"
    t.index ["status"], name: "index_learning_delivery_task_groups_on_status"
    t.index ["uid"], name: "index_learning_delivery_task_groups_on_uid", unique: true
  end

  create_table "learning_delivery_task_templates", force: :cascade do |t|
    t.string "uid"
    t.integer "task_type", default: 0, null: false
    t.string "sub_type"
    t.string "title"
    t.text "description"
    t.text "reason"
    t.text "recommendation"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["task_type", "sub_type"], name: "index_task_templates_on_task_type_and_sub_type", unique: true
  end

  create_table "learning_delivery_tasks", force: :cascade do |t|
    t.string "uid", null: false
    t.string "type", null: false
    t.string "sub_type"
    t.integer "status", default: 0, null: false
    t.string "title", null: false
    t.text "description"
    t.string "reason"
    t.text "recommendation"
    t.datetime "due_at"
    t.datetime "assigned_at"
    t.datetime "viewed_at"
    t.datetime "completed_at"
    t.bigint "owner_id"
    t.bigint "assigned_by_id"
    t.string "resource_type"
    t.bigint "resource_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "task_group_id"
    t.index ["assigned_by_id"], name: "index_learning_delivery_tasks_on_assigned_by_id"
    t.index ["owner_id"], name: "index_learning_delivery_tasks_on_owner_id"
    t.index ["resource_type", "resource_id"], name: "index_learning_delivery_tasks_on_resource"
    t.index ["task_group_id"], name: "index_learning_delivery_tasks_on_task_group_id"
    t.index ["uid"], name: "index_learning_delivery_tasks_on_uid", unique: true
  end

  create_table "live_session_reviews", force: :cascade do |t|
    t.bigint "live_session_id", null: false
    t.bigint "enrollment_id", null: false
    t.integer "score", null: false
    t.text "comments"
    t.integer "delivery_rating"
    t.integer "format_rating"
    t.integer "content_rating"
    t.datetime "submitted_at", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["enrollment_id"], name: "index_live_session_reviews_on_enrollment_id"
    t.index ["live_session_id", "enrollment_id"], name: "index_live_session_reviews_on_live_session_and_enrollment", unique: true
  end

  create_table "live_sessions", force: :cascade do |t|
    t.string "uid", null: false
    t.datetime "starts_at", null: false
    t.datetime "ends_at", null: false
    t.bigint "section_week_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "leader_id"
    t.bigint "moderator_id"
    t.string "cloud_calendar_event_key"
    t.index ["leader_id"], name: "index_live_sessions_on_leader_id"
    t.index ["moderator_id"], name: "index_live_sessions_on_moderator_id"
    t.index ["section_week_id"], name: "index_live_sessions_on_section_week_id"
    t.index ["starts_at"], name: "index_live_sessions_on_starts_at"
    t.index ["uid"], name: "index_live_sessions_on_uid", unique: true
  end

  create_table "lms_assignment_groups", force: :cascade do |t|
    t.string "name", null: false
    t.integer "position", null: false
    t.bigint "cohort_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "short_name", null: false
    t.index ["cohort_id"], name: "index_lms_assignment_groups_on_cohort_id"
  end

  create_table "lms_assignment_templates", force: :cascade do |t|
    t.string "uid", null: false
    t.string "name", null: false
    t.bigint "grading_config_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "module_template_id"
    t.index ["grading_config_id"], name: "index_lms_assignment_templates_on_grading_config_id"
    t.index ["module_template_id"], name: "index_lms_assignment_templates_on_module_template_id"
    t.index ["name", "module_template_id"], name: "index_unique_name_module_template", unique: true
    t.index ["uid"], name: "index_lms_assignment_templates_on_uid", unique: true
  end

  create_table "lms_assignments", force: :cascade do |t|
    t.string "name", null: false
    t.integer "grading_type", null: false
    t.boolean "required", default: false, null: false
    t.boolean "published", default: false, null: false
    t.integer "status"
    t.datetime "due_at"
    t.datetime "unlock_at"
    t.datetime "lock_at"
    t.text "html_url"
    t.bigint "lms_assignment_group_id"
    t.bigint "lms_module_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "assignment_template_id"
    t.index ["assignment_template_id"], name: "index_lms_assignments_on_assignment_template_id"
    t.index ["lms_assignment_group_id"], name: "index_lms_assignments_on_lms_assignment_group_id"
    t.index ["lms_module_id"], name: "index_lms_assignments_on_lms_module_id"
  end

  create_table "lms_exercise_configs", force: :cascade do |t|
    t.string "title", null: false
    t.text "grading_instructions"
    t.string "uid", null: false
    t.bigint "grading_config_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "requires_manual_review", default: false, null: false
    t.integer "order", default: 0, null: false
    t.index ["grading_config_id"], name: "index_lms_exercise_configs_on_grading_config_id"
    t.index ["uid"], name: "index_lms_exercise_configs_on_uid", unique: true
  end

  create_table "lms_grading_assistants", force: :cascade do |t|
    t.string "uid", null: false
    t.string "name", null: false
    t.string "ai_key", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["ai_key"], name: "index_lms_grading_assistants_on_ai_key", unique: true
    t.index ["uid"], name: "index_lms_grading_assistants_on_uid", unique: true
  end

  create_table "lms_grading_configs", force: :cascade do |t|
    t.string "uid", null: false
    t.bigint "grading_assistant_id", null: false
    t.string "ai_blank_template_file_key"
    t.string "ai_blank_template_vector_store_file_key"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "ai_vector_store_key"
    t.index ["grading_assistant_id"], name: "index_lms_grading_configs_on_grading_assistant_id"
    t.index ["uid"], name: "index_lms_grading_configs_on_uid", unique: true
  end

  create_table "lms_module_templates", force: :cascade do |t|
    t.string "title", null: false
    t.string "title_prefix", null: false
    t.integer "week_number"
    t.string "uid", null: false
    t.bigint "program_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["program_id"], name: "index_lms_module_templates_on_program_id"
    t.index ["title", "title_prefix", "week_number", "program_id"], name: "index_lms_module_templates_on_title_prefix_week_program", unique: true
    t.index ["uid"], name: "index_lms_module_templates_on_uid", unique: true
  end

  create_table "lms_modules", force: :cascade do |t|
    t.string "title", null: false
    t.string "title_prefix", null: false
    t.datetime "unlock_at"
    t.string "week_type"
    t.bigint "week_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "cohort_id"
    t.integer "position", default: 0, null: false
    t.bigint "module_template_id"
    t.index ["cohort_id"], name: "index_lms_modules_on_cohort_id"
    t.index ["module_template_id"], name: "index_lms_modules_on_module_template_id"
    t.index ["week_type", "week_id"], name: "index_lms_modules_on_week"
  end

  create_table "lms_section_assignments", force: :cascade do |t|
    t.bigint "section_id"
    t.bigint "lms_assignment_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["lms_assignment_id"], name: "index_lms_section_assignments_on_lms_assignment_id"
    t.index ["section_id", "lms_assignment_id"], name: "idx_on_section_id_lms_assignment_id_f88fe0bd4f", unique: true
  end

  create_table "lms_submission_accepted_types", force: :cascade do |t|
    t.integer "name", null: false
    t.bigint "assignment_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["assignment_id", "name"], name: "index_lms_submission_accepted_types_on_assignment_and_name", unique: true
    t.index ["name"], name: "index_lms_submission_accepted_types_on_name"
  end

  create_table "lms_submission_comments", force: :cascade do |t|
    t.bigint "submission_id", null: false
    t.string "author_type"
    t.bigint "author_id"
    t.integer "attempt"
    t.text "text"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "read_at"
    t.index ["author_type", "author_id"], name: "index_lms_submission_comments_on_author"
    t.index ["read_at"], name: "index_lms_submission_comments_on_read_at"
    t.index ["submission_id"], name: "index_lms_submission_comments_on_submission_id"
  end

  create_table "lms_submission_exercise_reviews", force: :cascade do |t|
    t.bigint "review_id", null: false
    t.integer "state", default: 0, null: false
    t.string "title"
    t.integer "grade"
    t.integer "autograder_confidence"
    t.datetime "manually_reviewed_at"
    t.bigint "manually_reviewed_by_id", comment: "References admin_users"
    t.datetime "auto_graded_at", null: false
    t.text "comment"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "extracted_work"
    t.index ["manually_reviewed_by_id"], name: "idx_on_manually_reviewed_by_id_b37a61481e"
    t.index ["review_id"], name: "index_lms_submission_exercise_reviews_on_review_id"
  end

  create_table "lms_submission_review_training_evaluations", force: :cascade do |t|
    t.bigint "review_id", null: false
    t.integer "status", default: 0, null: false
    t.integer "result"
    t.text "notes"
    t.datetime "evaluated_at", precision: nil
    t.bigint "evaluated_by_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["evaluated_by_id"], name: "index_training_evaluations_on_evaluated_by_id"
    t.index ["result"], name: "index_lms_submission_review_training_evaluations_on_result"
    t.index ["review_id"], name: "index_training_evaluations_on_review_id", unique: true
    t.index ["status"], name: "index_lms_submission_review_training_evaluations_on_status"
  end

  create_table "lms_submission_reviews", force: :cascade do |t|
    t.bigint "submission_id", null: false
    t.integer "attempt", default: 0
    t.integer "state", default: 0, null: false
    t.integer "grade"
    t.integer "score"
    t.datetime "pdf_generated_at"
    t.datetime "auto_graded_at"
    t.datetime "manually_reviewed_at"
    t.bigint "manually_reviewed_by_id", comment: "References admin_users"
    t.datetime "graded_at"
    t.text "comment"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "uid", null: false
    t.datetime "published_at"
    t.boolean "training", default: false, null: false
    t.integer "auto_grader_grade", comment: "Grade assigned by the AI Grader"
    t.index ["manually_reviewed_by_id"], name: "index_lms_submission_reviews_on_manually_reviewed_by_id"
    t.index ["state"], name: "index_lms_submission_reviews_on_state"
    t.index ["submission_id", "attempt"], name: "index_lms_submission_reviews_on_submission_id_and_attempt", unique: true
    t.index ["training"], name: "index_lms_submission_reviews_on_training"
    t.index ["uid"], name: "index_lms_submission_reviews_on_uid", unique: true
  end

  create_table "lms_submissions", force: :cascade do |t|
    t.decimal "score"
    t.string "grade"
    t.bigint "graded_by_id"
    t.datetime "submitted_at"
    t.datetime "graded_at"
    t.integer "state", default: 0, null: false
    t.integer "submission_type"
    t.text "url"
    t.bigint "enrollment_id", null: false
    t.bigint "lms_assignment_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "attempt"
    t.integer "seconds_late"
    t.index ["enrollment_id", "lms_assignment_id"], name: "index_lms_submissions_on_assignment_and_enrollment", unique: true
    t.index ["graded_by_id"], name: "index_lms_submissions_on_graded_by_id"
    t.index ["lms_assignment_id"], name: "index_lms_submissions_on_lms_assignment_id"
  end

  create_table "partner_programs", force: :cascade do |t|
    t.bigint "partner_id", null: false
    t.bigint "program_id", null: false
    t.string "custom_program_name"
    t.boolean "default", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "uid", null: false
    t.integer "status", default: 0, null: false
    t.string "custom_program_short_name"
    t.index ["partner_id", "program_id"], name: "index_partner_programs_on_partner_id_and_program_id", unique: true
    t.index ["program_id"], name: "index_partner_programs_on_program_id"
    t.index ["uid"], name: "index_partner_programs_on_uid", unique: true
  end

  create_table "partner_themes", force: :cascade do |t|
    t.bigint "partner_id", null: false
    t.string "primary_color", null: false
    t.string "secondary_color", null: false
    t.string "font_color_on_primary_background", null: false
    t.string "font_color_on_secondary_background", null: false
    t.string "course_nomenclature", null: false
    t.string "from_email", null: false
    t.string "reply_to_email", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "certificate_nomenclature", null: false
    t.string "cobranding_text"
    t.index ["partner_id"], name: "index_partner_themes_on_partner_id", unique: true
  end

  create_table "partners", force: :cascade do |t|
    t.string "name", null: false
    t.string "slug", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "certifier", default: 0, null: false
    t.string "time_zone", default: "UTC", null: false
    t.string "proxy_host"
    t.integer "status", default: 1, null: false
    t.bigint "promos_eva_discount_code_id"
    t.string "short_name", null: false
    t.string "formal_name", null: false
    t.integer "dns_status", default: 0, null: false
    t.string "uid", null: false
    t.integer "section_style", default: 0, null: false
    t.bigint "address_id"
    t.string "currency_code", default: "USD", null: false
    t.string "ecom_payment_providers", default: ["APPLE_PAY", "GOOGLE_PAY"], null: false, array: true
    t.index ["address_id"], name: "index_partners_on_address_id"
    t.index ["name"], name: "index_partners_on_name", unique: true
    t.index ["promos_eva_discount_code_id"], name: "index_partners_on_promos_eva_discount_code_id"
    t.index ["proxy_host"], name: "index_partners_on_proxy_host"
    t.index ["slug"], name: "index_partners_on_slug", unique: true
    t.index ["uid"], name: "index_partners_on_uid", unique: true
  end

  create_table "payments_events", force: :cascade do |t|
    t.jsonb "meta", default: {}, null: false
    t.integer "processor", null: false
    t.bigint "transaction_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "processed_at"
    t.text "ignored_reason"
    t.string "processor_event_type", default: "unknown", null: false
    t.index ["created_at"], name: "index_payments_events_on_created_at"
    t.index ["processed_at"], name: "index_payments_events_on_processed_at"
    t.index ["processor_event_type"], name: "index_payments_events_on_processor_event_type"
    t.index ["transaction_id"], name: "index_payments_events_on_transaction_id"
  end

  create_table "payments_installment_plans", force: :cascade do |t|
    t.integer "status", default: 0, null: false
    t.integer "processor", null: false
    t.string "processor_key", null: false
    t.bigint "ecom_payment_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "canceled_at", precision: nil
    t.bigint "canceled_by_admin_user_id"
    t.index ["canceled_by_admin_user_id"], name: "index_payments_installment_plans_on_canceled_by_admin_user_id"
    t.index ["ecom_payment_id"], name: "index_payments_installment_plans_on_ecom_payment_id"
    t.index ["processor", "processor_key"], name: "idx_on_processor_processor_key_4266591ed9", unique: true
  end

  create_table "payments_installments", force: :cascade do |t|
    t.integer "status", default: 0, null: false
    t.integer "processor", null: false
    t.string "processor_key", null: false
    t.datetime "due_at", precision: nil, null: false
    t.bigint "installment_plan_id", null: false
    t.bigint "transaction_charge_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["installment_plan_id"], name: "index_payments_installments_on_installment_plan_id"
    t.index ["transaction_charge_id"], name: "index_payments_installments_on_transaction_charge_id"
  end

  create_table "payments_invoice_payments", force: :cascade do |t|
    t.bigint "ecom_payment_id", null: false
    t.bigint "admin_user_id", null: false
    t.bigint "transaction_invoice_id"
    t.bigint "payer_id", null: false
    t.string "uid", null: false
    t.integer "amount_cents", default: 0, null: false
    t.string "invoice_number"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "currency_code", default: "USD", null: false
    t.index ["admin_user_id"], name: "index_payments_invoice_payments_on_admin_user_id"
    t.index ["ecom_payment_id"], name: "index_payments_invoice_payments_on_ecom_payment_id"
    t.index ["payer_id"], name: "index_payments_invoice_payments_on_payer_id"
    t.index ["transaction_invoice_id"], name: "index_payments_invoice_payments_on_transaction_invoice_id"
    t.index ["uid"], name: "index_payments_invoice_payments_on_uid", unique: true
  end

  create_table "payments_manual_payments", force: :cascade do |t|
    t.bigint "ecom_payment_id", null: false
    t.bigint "admin_user_id"
    t.bigint "transaction_charge_id"
    t.bigint "payer_id", null: false
    t.string "uid", null: false
    t.integer "status", default: 0, null: false
    t.integer "processor", null: false
    t.string "processor_key", null: false
    t.integer "amount_cents", default: 0, null: false
    t.string "payment_url", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "currency_code", default: "USD", null: false
    t.index ["admin_user_id"], name: "index_payments_manual_payments_on_admin_user_id"
    t.index ["ecom_payment_id"], name: "index_payments_manual_payments_on_ecom_payment_id"
    t.index ["payer_id"], name: "index_payments_manual_payments_on_payer_id"
    t.index ["transaction_charge_id"], name: "index_payments_manual_payments_on_transaction_charge_id"
    t.index ["uid"], name: "index_payments_manual_payments_on_uid", unique: true
  end

  create_table "payments_payers", force: :cascade do |t|
    t.string "name", null: false
    t.string "email", null: false
    t.string "processor_key"
    t.integer "processor"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["processor", "processor_key"], name: "index_payments_payers_on_processor_and_processor_key", unique: true
  end

  create_table "payments_transactions", force: :cascade do |t|
    t.string "type", null: false
    t.integer "amount_cents", default: 0, null: false
    t.string "processor_key"
    t.integer "processor"
    t.bigint "payer_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "ecom_payment_id"
    t.datetime "transacted_at", null: false
    t.string "clas_receipt_number"
    t.string "uid", null: false
    t.string "currency_code", default: "USD", null: false
    t.index ["clas_receipt_number"], name: "index_payments_transactions_on_clas_receipt_number"
    t.index ["ecom_payment_id"], name: "index_payments_transactions_on_ecom_payment_id"
    t.index ["payer_id"], name: "index_payments_transactions_on_payer_id"
    t.index ["processor", "processor_key"], name: "index_payments_transactions_on_processor_and_processor_key", unique: true
    t.index ["transacted_at"], name: "index_payments_transactions_on_transacted_at"
    t.index ["uid"], name: "index_payments_transactions_on_uid", unique: true
  end

  create_table "permissions", force: :cascade do |t|
    t.string "name", null: false
    t.string "resource_group", null: false
    t.integer "level", null: false
    t.integer "resource_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "resource_type"
    t.index ["resource_group", "resource_type", "resource_id"], name: "idx_on_resource_group_resource_type_resource_id_70b222d81b", unique: true
  end

  create_table "program_themes", force: :cascade do |t|
    t.string "primary_color", null: false
    t.bigint "program_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["program_id"], name: "index_program_themes_on_program_id", unique: true
  end

  create_table "programs", force: :cascade do |t|
    t.string "name", null: false
    t.string "short_name", null: false
    t.string "slug", null: false
    t.string "career_title"
    t.string "admissions_schedule_url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "certificate_key"
    t.string "award_key"
    t.integer "duration", default: 6048000, null: false
    t.string "key", null: false
    t.integer "status", default: 1, null: false
    t.string "career_industry"
    t.string "abbreviation", null: false
    t.string "cobranded_certificate_key"
    t.bigint "lms_grading_assistant_id"
    t.index ["abbreviation"], name: "index_programs_on_abbreviation", unique: true
    t.index ["key"], name: "index_programs_on_key", unique: true
    t.index ["lms_grading_assistant_id"], name: "index_programs_on_lms_grading_assistant_id"
    t.index ["name"], name: "index_programs_on_name", unique: true
    t.index ["short_name"], name: "index_programs_on_short_name", unique: true
    t.index ["slug"], name: "index_programs_on_slug", unique: true
  end

  create_table "promos_cross_sell_lists", force: :cascade do |t|
    t.string "crm_list_key", null: false
    t.bigint "program_id", null: false
    t.string "cross_sell_type", null: false
    t.integer "status", default: 1, null: false
    t.datetime "fetched_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["fetched_at"], name: "index_promos_cross_sell_lists_on_fetched_at"
    t.index ["program_id"], name: "index_promos_cross_sell_lists_on_program_id"
  end

  create_table "promos_cross_sells", force: :cascade do |t|
    t.string "type", null: false
    t.bigint "learner_id"
    t.bigint "cross_sell_list_id", null: false
    t.bigint "deal_id", null: false
    t.bigint "enrollment_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "email_id"
    t.index ["cross_sell_list_id"], name: "index_promos_cross_sells_on_cross_sell_list_id"
    t.index ["deal_id"], name: "index_promos_cross_sells_on_deal_id"
    t.index ["email_id"], name: "index_promos_cross_sells_on_email_id"
    t.index ["enrollment_id"], name: "index_promos_cross_sells_on_enrollment_id"
    t.index ["learner_id"], name: "index_promos_cross_sells_on_learner_id"
    t.index ["type", "learner_id"], name: "index_promos_cross_sells_on_type_and_learner_id"
  end

  create_table "promos_discount_applications", force: :cascade do |t|
    t.bigint "ecom_order_id", null: false
    t.bigint "discount_code_id", null: false
    t.integer "discount_cents", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "currency_code", default: "USD", null: false
    t.index ["discount_code_id"], name: "index_promos_discount_applications_on_discount_code_id"
    t.index ["ecom_order_id", "discount_code_id"], name: "idx_on_ecom_order_id_discount_code_id_c2df7da68b", unique: true
  end

  create_table "promos_discount_codes", force: :cascade do |t|
    t.bigint "discount_id", null: false
    t.bigint "admin_user_id"
    t.string "humanized_code", null: false
    t.string "code", null: false
    t.string "description", null: false
    t.boolean "enabled", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "viewable_code", default: true, null: false
    t.string "label"
    t.index ["admin_user_id"], name: "index_promos_discount_codes_on_admin_user_id"
    t.index ["code", "enabled"], name: "index_promos_discount_codes_on_code_and_enabled"
    t.index ["discount_id"], name: "index_promos_discount_codes_on_discount_id"
  end

  create_table "promos_discount_condition_applications", force: :cascade do |t|
    t.bigint "discount_condition_id", null: false
    t.bigint "discount_code_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discount_code_id"], name: "idx_on_discount_code_id_b5553c7e96"
    t.index ["discount_condition_id"], name: "idx_on_discount_condition_id_be81e64c12"
  end

  create_table "promos_discount_conditions", force: :cascade do |t|
    t.string "type", null: false
    t.string "name", null: false
    t.bigint "partner_id"
    t.bigint "program_id"
    t.bigint "email_id"
    t.integer "max_uses"
    t.date "starts_on"
    t.date "ends_on", comment: "Inclusive"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "config", default: {}
    t.index ["config"], name: "index_promos_discount_conditions_on_config", using: :gin
    t.index ["email_id"], name: "index_promos_discount_conditions_on_email_id"
    t.index ["name"], name: "index_promos_discount_conditions_on_name", unique: true
    t.index ["partner_id"], name: "index_promos_discount_conditions_on_partner_id"
    t.index ["program_id"], name: "index_promos_discount_conditions_on_program_id"
  end

  create_table "promos_discounts", force: :cascade do |t|
    t.string "type"
    t.string "name", null: false
    t.decimal "rate_off", precision: 5, scale: 2
    t.integer "cents_off"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "currency_code", default: "USD", null: false
    t.index ["name"], name: "index_promos_discounts_on_name", unique: true
  end

  create_table "promos_referrers", force: :cascade do |t|
    t.string "uid", null: false
    t.string "code", null: false
    t.bigint "enrollment_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["code"], name: "index_promos_referrers_on_code", unique: true
    t.index ["enrollment_id"], name: "index_promos_referrers_on_enrollment_id"
    t.index ["uid"], name: "index_promos_referrers_on_uid", unique: true
  end

  create_table "registrations", force: :cascade do |t|
    t.bigint "section_id", null: false
    t.bigint "partner_program_id", null: false
    t.bigint "email_id", null: false
    t.integer "experience_level"
    t.integer "aspiration"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "admin_user_id"
    t.bigint "learner_id", null: false
    t.integer "clas_key"
    t.bigint "finance_relationship_id"
    t.bigint "site_ad_tracking_id"
    t.integer "eva_type"
    t.string "third_party_entity"
    t.bigint "deal_id"
    t.string "uid", null: false
    t.bigint "reenrolled_from_id"
    t.text "notes"
    t.string "clas_external_key"
    t.bigint "promos_referrer_id"
    t.string "tracking_key"
    t.integer "status", default: 1, null: false
    t.index ["admin_user_id"], name: "index_registrations_on_admin_user_id"
    t.index ["clas_external_key"], name: "index_registrations_on_clas_external_key", unique: true
    t.index ["clas_key"], name: "index_registrations_on_clas_key", unique: true
    t.index ["deal_id"], name: "index_registrations_on_deal_id"
    t.index ["email_id"], name: "index_registrations_on_email_id"
    t.index ["finance_relationship_id"], name: "index_registrations_on_finance_relationship_id"
    t.index ["learner_id"], name: "index_registrations_on_learner_id"
    t.index ["partner_program_id"], name: "index_registrations_on_partner_program_id"
    t.index ["promos_referrer_id"], name: "index_registrations_on_promos_referrer_id"
    t.index ["reenrolled_from_id"], name: "index_registrations_on_reenrolled_from_id"
    t.index ["section_id"], name: "index_registrations_on_section_id"
    t.index ["site_ad_tracking_id"], name: "index_registrations_on_site_ad_tracking_id"
    t.index ["status"], name: "index_registrations_on_status"
    t.index ["uid"], name: "index_registrations_on_uid", unique: true
  end

  create_table "remote_canvas_password_resets", force: :cascade do |t|
    t.integer "canvas_user_key", default: 0
    t.string "email", default: ""
    t.string "reset_digest", default: ""
    t.datetime "reset_sent_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email"], name: "index_remote_canvas_password_resets_on_email", unique: true
  end

  create_table "remote_resources", force: :cascade do |t|
    t.string "core_record_type", null: false
    t.bigint "core_record_id", null: false
    t.string "type", null: false
    t.string "key", default: "", null: false
    t.datetime "fetched_at", precision: nil
    t.datetime "published_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["core_record_type", "core_record_id"], name: "index_remote_resources_on_core_record"
    t.index ["type", "key", "core_record_type", "core_record_id"], name: "index_remote_resources_on_type_and_key_and_core_record", unique: true
  end

  create_table "role_permissions", force: :cascade do |t|
    t.bigint "role_id", null: false
    t.bigint "permission_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["permission_id"], name: "index_role_permissions_on_permission_id"
    t.index ["role_id"], name: "index_role_permissions_on_role_id"
  end

  create_table "roles", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_roles_on_name", unique: true
  end

  create_table "section_weeks", force: :cascade do |t|
    t.string "uid", null: false
    t.bigint "section_id", null: false
    t.date "starts_on", null: false
    t.date "ends_on", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "cohort_week_id", null: false
    t.index ["cohort_week_id"], name: "index_section_weeks_on_cohort_week_id"
    t.index ["section_id"], name: "index_section_weeks_on_section_id"
    t.index ["uid"], name: "index_section_weeks_on_uid", unique: true
  end

  create_table "sections", force: :cascade do |t|
    t.bigint "cohort_id", null: false
    t.string "suffix", null: false
    t.string "chat_workspace_key"
    t.string "conferencing_url"
    t.string "chat_join_url"
    t.integer "live_day_of_the_week", null: false
    t.time "live_start_time", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "uid", null: false
    t.time "live_end_time", null: false
    t.bigint "partner_id"
    t.bigint "instructor_id"
    t.bigint "advocate_id"
    t.bigint "grader_id"
    t.index ["advocate_id"], name: "index_sections_on_advocate_id"
    t.index ["chat_workspace_key"], name: "index_sections_on_chat_workspace_key"
    t.index ["cohort_id"], name: "index_sections_on_cohort_id"
    t.index ["grader_id"], name: "index_sections_on_grader_id"
    t.index ["instructor_id"], name: "index_sections_on_instructor_id"
    t.index ["partner_id"], name: "index_sections_on_partner_id"
    t.index ["uid"], name: "index_sections_on_uid", unique: true
  end

  create_table "settings", force: :cascade do |t|
    t.string "name", null: false
    t.string "key", null: false
    t.jsonb "value", default: {}, null: false
    t.string "uid", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["key"], name: "index_settings_on_key", unique: true
    t.index ["uid"], name: "index_settings_on_uid", unique: true
  end

  create_table "site_ad_trackings", force: :cascade do |t|
    t.string "utm_source"
    t.string "utm_medium"
    t.string "utm_campaign"
    t.string "utm_term"
    t.string "utm_content"
    t.string "gclid"
    t.string "fbclid"
    t.string "referrer"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "hutk_cookie_id"
    t.string "first_referrer"
    t.string "hsa_kw"
    t.string "hsa_grp"
    t.string "hsa_mt"
    t.string "hsa_cam"
    t.string "abt_id"
    t.string "abt_name"
    t.string "abt_platform"
    t.string "abt_variant_name"
    t.string "abt_variant_id"
  end

  create_table "site_applied_technologies", force: :cascade do |t|
    t.bigint "program_dataset_id", null: false
    t.string "name", null: false
    t.integer "order", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "logo_css"
    t.string "logo_css_large"
    t.boolean "credential", default: true, null: false
    t.index ["program_dataset_id", "order"], name: "idx_on_program_dataset_id_order_ef9d13c7ec", unique: true
    t.index ["program_dataset_id"], name: "index_site_applied_technologies_on_program_dataset_id"
  end

  create_table "site_career_option_images", force: :cascade do |t|
    t.bigint "squeeze_page_template_id", null: false
    t.string "job_title"
    t.string "company_name"
    t.string "salary_range"
    t.string "order", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["squeeze_page_template_id", "order"], name: "idx_on_squeeze_page_template_id_order_8750009051", unique: true
  end

  create_table "site_company_brands", force: :cascade do |t|
    t.string "company_name"
    t.integer "order", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "logo_css"
  end

  create_table "site_global_datasets", force: :cascade do |t|
    t.jsonb "faqs", default: [], null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "settings", default: {}, null: false
  end

  create_table "site_instructors", force: :cascade do |t|
    t.bigint "program_dataset_id", null: false
    t.string "name", null: false
    t.string "title", null: false
    t.integer "order", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "logo_css"
    t.index ["program_dataset_id"], name: "index_site_instructors_on_program_dataset_id"
  end

  create_table "site_landing_page_templates", force: :cascade do |t|
    t.bigint "program_id", null: false
    t.string "hero_subtext", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "key_points", default: {}
    t.text "secondary_headline"
    t.text "explore_options"
    t.text "instructor_headline"
    t.string "custom_hero_text"
    t.string "step_2_title"
    t.text "step_2_body"
    t.string "meta_title"
    t.text "meta_description"
    t.text "expandable_career_blurb"
    t.string "banner_cta_header_text"
    t.string "step_3_title", default: "Advance your career"
    t.string "step_1_heading", null: false
    t.text "step_1_blurb", null: false
    t.index ["program_id"], name: "index_site_landing_page_templates_on_program_id", unique: true
  end

  create_table "site_pages", force: :cascade do |t|
    t.bigint "partner_program_id", null: false
    t.integer "template", null: false
    t.string "url", null: false
    t.string "short_url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["partner_program_id"], name: "index_site_pages_on_partner_program_id"
    t.index ["template", "partner_program_id"], name: "index_site_pages_on_template_and_partner_program_id", unique: true
  end

  create_table "site_partner_datasets", force: :cascade do |t|
    t.bigint "partner_id", null: false
    t.string "home_url"
    t.jsonb "faqs", default: [], null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "brand_logos_allowed", default: true, null: false
    t.text "custom_disclaimer"
    t.string "custom_eva_banner_text"
    t.text "custom_footer_text"
    t.string "cobranding_css"
    t.string "back_to_name"
    t.string "certificate_name"
    t.jsonb "overrides", default: {}, null: false
    t.string "squeeze_page_cobranding_css"
    t.string "squeeze_page_logo_css"
    t.string "logo_css"
    t.text "ab_testing_html"
    t.index ["partner_id"], name: "index_site_partner_datasets_on_partner_id", unique: true
  end

  create_table "site_partner_program_datasets", force: :cascade do |t|
    t.bigint "partner_program_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "home_url"
    t.jsonb "faqs", default: [], null: false
    t.jsonb "overrides", default: {}, null: false
    t.index ["partner_program_id"], name: "index_site_partner_program_datasets_on_partner_program_id", unique: true
  end

  create_table "site_program_datasets", force: :cascade do |t|
    t.bigint "program_id", null: false
    t.string "preview_video_url", null: false
    t.jsonb "expandable_skill_map", null: false
    t.jsonb "expandable_career_map", null: false
    t.jsonb "expandable_so_you_can_map"
    t.jsonb "faqs", default: [], null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "certificate_details"
    t.string "career_quote_alt_text"
    t.string "instructors_headline"
    t.text "instructors_blurb"
    t.jsonb "overrides", default: {}, null: false
    t.index ["program_id"], name: "index_site_program_datasets_on_program_id", unique: true
  end

  create_table "site_reimbursement_page_templates", force: :cascade do |t|
    t.text "email_template_html"
    t.bigint "program_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "meta_title"
    t.text "meta_description"
    t.index ["program_id"], name: "index_site_reimbursement_page_templates_on_program_id", unique: true
  end

  create_table "site_squeeze_page_templates", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "program_id"
    t.jsonb "skills", default: {}, null: false
    t.jsonb "services", default: {}, null: false
    t.jsonb "points_info", default: [], null: false
    t.string "sneak_peek_blurb", null: false
    t.string "custom_hero_text"
    t.string "meta_title"
    t.text "meta_description"
    t.integer "applied_technology_position", default: 0, null: false
    t.jsonb "hero_blurb_jsonb", default: {}, null: false
    t.string "custom_hero_text_v_1"
    t.jsonb "hero_blurb_jsonb_v_1", default: {}, null: false
    t.string "custom_hero_title_v_1"
    t.index ["program_id"], name: "index_site_squeeze_page_templates_on_program_id"
  end

  create_table "site_syllabus_page_templates", force: :cascade do |t|
    t.bigint "program_id", null: false
    t.string "headline_blurb", null: false
    t.string "preview_video_blurb", null: false
    t.jsonb "what_you_earn", default: {}, null: false
    t.jsonb "expandable_learnings", default: {}, null: false
    t.jsonb "our_students", default: {}, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "expandable_learnings_blurb", null: false
    t.string "instructors_blurb", null: false
    t.text "our_learners_blurb", null: false
    t.text "your_time_blurb", null: false
    t.string "meta_title"
    t.text "meta_description"
    t.string "custom_hero_text"
    t.string "preview_video_headline"
    t.index ["program_id"], name: "index_site_syllabus_page_templates_on_program_id", unique: true
  end

  create_table "site_testimonials", force: :cascade do |t|
    t.bigint "program_dataset_id", null: false
    t.string "name", null: false
    t.string "quote", null: false
    t.string "video_url", null: false
    t.integer "order", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "mentions_certificate", default: false, null: false
    t.index ["program_dataset_id"], name: "index_site_testimonials_on_program_dataset_id"
  end

  create_table "syllabus_requests", force: :cascade do |t|
    t.bigint "partner_program_id", null: false
    t.bigint "email_id", null: false
    t.string "first_name", null: false
    t.string "phone_standardized"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "site_ad_tracking_id"
    t.bigint "deal_id"
    t.boolean "contact_opt_in", default: false, null: false
    t.bigint "promos_referrer_id"
    t.string "tracking_key"
    t.index ["deal_id"], name: "index_syllabus_requests_on_deal_id"
    t.index ["email_id"], name: "index_syllabus_requests_on_email_id"
    t.index ["partner_program_id"], name: "index_syllabus_requests_on_partner_program_id"
    t.index ["promos_referrer_id"], name: "index_syllabus_requests_on_promos_referrer_id"
    t.index ["site_ad_tracking_id"], name: "index_syllabus_requests_on_site_ad_tracking_id"
  end

  create_table "versions", force: :cascade do |t|
    t.string "whodunnit"
    t.datetime "created_at"
    t.bigint "item_id", null: false
    t.string "item_type", null: false
    t.string "event", null: false
    t.text "object"
    t.text "object_changes"
    t.index ["item_type", "item_id"], name: "index_versions_on_item_type_and_item_id"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "admin_notes", "admin_users"
  add_foreign_key "admin_user_permissions", "admin_users"
  add_foreign_key "admin_user_permissions", "permissions"
  add_foreign_key "admin_user_permissions", "roles"
  add_foreign_key "admin_user_roles", "admin_users"
  add_foreign_key "admin_user_roles", "roles"
  add_foreign_key "alert_status_changes", "admin_users"
  add_foreign_key "alert_status_changes", "alerts", on_delete: :cascade
  add_foreign_key "alerts", "admin_users", column: "owner_id"
  add_foreign_key "certificate_themes", "partner_programs", on_delete: :cascade
  add_foreign_key "certificate_themes", "partners", on_delete: :cascade
  add_foreign_key "cohort_weeks", "cohorts"
  add_foreign_key "cohorts", "programs", on_delete: :cascade
  add_foreign_key "deals", "emails"
  add_foreign_key "deals", "partner_programs"
  add_foreign_key "ecom_order_items", "ecom_orders", column: "order_id", on_delete: :cascade
  add_foreign_key "ecom_order_items", "ecom_variants", column: "variant_id", on_delete: :cascade
  add_foreign_key "ecom_order_items", "finance_contracts"
  add_foreign_key "ecom_order_items", "registrations", on_delete: :cascade
  add_foreign_key "ecom_orders", "learners", on_delete: :cascade
  add_foreign_key "ecom_orders", "partners", on_delete: :cascade
  add_foreign_key "ecom_payments", "ecom_orders", column: "order_id", on_delete: :cascade
  add_foreign_key "ecom_payments", "ecom_payment_methods", column: "payment_method_id", on_delete: :cascade
  add_foreign_key "ecom_products", "partners"
  add_foreign_key "ecom_products", "programs", on_delete: :cascade
  add_foreign_key "ecom_refunds", "admin_users"
  add_foreign_key "ecom_refunds", "ecom_payments", column: "payment_id"
  add_foreign_key "ecom_refunds", "payments_transactions", column: "payments_transaction_charge_id", on_delete: :cascade
  add_foreign_key "ecom_refunds", "payments_transactions", column: "payments_transaction_refund_id", on_delete: :cascade
  add_foreign_key "ecom_variants", "ecom_products", column: "product_id", on_delete: :cascade
  add_foreign_key "ecom_variants", "partners"
  add_foreign_key "ecom_variants", "sections", on_delete: :cascade
  add_foreign_key "email_validation_results", "emails"
  add_foreign_key "emails", "learners"
  add_foreign_key "enabled_payment_methods", "ecom_payment_methods"
  add_foreign_key "enabled_payment_methods", "partners"
  add_foreign_key "enrollment_status_changes", "admin_users"
  add_foreign_key "enrollment_status_changes", "enrollments", on_delete: :cascade
  add_foreign_key "enrollment_transfers", "admin_users"
  add_foreign_key "enrollment_transfers", "enrollments", column: "transferred_from_id", on_delete: :cascade
  add_foreign_key "enrollment_transfers", "enrollments", column: "transferred_to_id", on_delete: :cascade
  add_foreign_key "enrollment_transfers", "registrations", on_delete: :cascade
  add_foreign_key "enrollments", "admin_users", column: "extended_by_id"
  add_foreign_key "enrollments", "deals"
  add_foreign_key "enrollments", "ecom_order_items"
  add_foreign_key "enrollments", "learners"
  add_foreign_key "enrollments", "partner_programs"
  add_foreign_key "enrollments", "registrations"
  add_foreign_key "enrollments", "sections"
  add_foreign_key "external_content_code_uploads", "admin_users"
  add_foreign_key "external_content_code_uploads", "external_content_materials", column: "material_id"
  add_foreign_key "external_content_code_uses", "enrollments"
  add_foreign_key "external_content_code_uses", "external_content_codes", column: "code_id", on_delete: :cascade
  add_foreign_key "external_content_code_uses", "external_content_material_sets", column: "material_set_id"
  add_foreign_key "external_content_code_uses", "external_content_materials", column: "material_id"
  add_foreign_key "external_content_codes", "external_content_code_uploads", column: "code_upload_id"
  add_foreign_key "external_content_codes", "external_content_materials", column: "material_id"
  add_foreign_key "external_content_material_set_items", "external_content_material_sets", column: "material_set_id"
  add_foreign_key "external_content_material_set_items", "external_content_materials", column: "material_id"
  add_foreign_key "external_content_material_sets", "programs"
  add_foreign_key "external_content_materials", "external_content_material_sets", column: "material_set_id"
  add_foreign_key "external_content_triggers", "external_content_material_sets", column: "material_set_id", on_delete: :cascade
  add_foreign_key "finance_bookings", "ecom_order_items", on_delete: :cascade
  add_foreign_key "finance_bookings", "ecom_payments", on_delete: :cascade
  add_foreign_key "finance_bookings", "ecom_refunds"
  add_foreign_key "finance_contracts", "finance_relationships", column: "relationship_id", on_delete: :cascade
  add_foreign_key "finance_receipts", "finance_bookings", column: "booking_id"
  add_foreign_key "finance_receipts", "payments_transactions"
  add_foreign_key "finance_relationships", "partners", on_delete: :cascade
  add_foreign_key "learning_delivery_activities", "learning_delivery_employees", column: "employee_id"
  add_foreign_key "learning_delivery_availabilities", "learning_delivery_availability_slots", column: "availability_slot_id"
  add_foreign_key "learning_delivery_availabilities", "learning_delivery_employees", column: "employee_id"
  add_foreign_key "learning_delivery_competencies", "learning_delivery_employees", column: "employee_id", on_delete: :cascade
  add_foreign_key "learning_delivery_competencies", "programs", on_delete: :cascade
  add_foreign_key "learning_delivery_employee_roles", "learning_delivery_employees", column: "employee_id"
  add_foreign_key "learning_delivery_employee_roles", "learning_delivery_roles", column: "role_id"
  add_foreign_key "learning_delivery_employees", "admin_users"
  add_foreign_key "learning_delivery_risk_assessments", "enrollments", on_delete: :cascade
  add_foreign_key "learning_delivery_section_availability_slots", "learning_delivery_availability_slots", column: "availability_slot_id"
  add_foreign_key "learning_delivery_section_availability_slots", "sections"
  add_foreign_key "learning_delivery_task_comments", "admin_users", column: "author_id"
  add_foreign_key "learning_delivery_task_comments", "learning_delivery_tasks", column: "task_id"
  add_foreign_key "learning_delivery_task_groups", "admin_users", column: "created_by_id"
  add_foreign_key "learning_delivery_tasks", "admin_users", column: "assigned_by_id"
  add_foreign_key "learning_delivery_tasks", "admin_users", column: "owner_id"
  add_foreign_key "learning_delivery_tasks", "learning_delivery_task_groups", column: "task_group_id"
  add_foreign_key "live_session_reviews", "enrollments"
  add_foreign_key "live_session_reviews", "live_sessions"
  add_foreign_key "live_sessions", "learning_delivery_employees", column: "leader_id"
  add_foreign_key "live_sessions", "learning_delivery_employees", column: "moderator_id"
  add_foreign_key "live_sessions", "section_weeks"
  add_foreign_key "lms_assignment_groups", "cohorts"
  add_foreign_key "lms_assignment_templates", "lms_grading_configs", column: "grading_config_id"
  add_foreign_key "lms_assignment_templates", "lms_module_templates", column: "module_template_id"
  add_foreign_key "lms_assignments", "lms_assignment_groups"
  add_foreign_key "lms_assignments", "lms_modules"
  add_foreign_key "lms_exercise_configs", "lms_grading_configs", column: "grading_config_id"
  add_foreign_key "lms_grading_configs", "lms_grading_assistants", column: "grading_assistant_id"
  add_foreign_key "lms_module_templates", "programs"
  add_foreign_key "lms_modules", "lms_module_templates", column: "module_template_id"
  add_foreign_key "lms_section_assignments", "lms_assignments"
  add_foreign_key "lms_section_assignments", "sections"
  add_foreign_key "lms_submission_accepted_types", "lms_assignments", column: "assignment_id", on_delete: :cascade
  add_foreign_key "lms_submission_comments", "lms_submissions", column: "submission_id"
  add_foreign_key "lms_submission_exercise_reviews", "admin_users", column: "manually_reviewed_by_id"
  add_foreign_key "lms_submission_exercise_reviews", "lms_submission_reviews", column: "review_id"
  add_foreign_key "lms_submission_review_training_evaluations", "admin_users", column: "evaluated_by_id"
  add_foreign_key "lms_submission_review_training_evaluations", "lms_submission_reviews", column: "review_id"
  add_foreign_key "lms_submission_reviews", "admin_users", column: "manually_reviewed_by_id"
  add_foreign_key "lms_submission_reviews", "lms_submissions", column: "submission_id"
  add_foreign_key "lms_submissions", "enrollments"
  add_foreign_key "lms_submissions", "learning_delivery_employees", column: "graded_by_id"
  add_foreign_key "lms_submissions", "lms_assignments"
  add_foreign_key "partner_programs", "partners", on_delete: :cascade
  add_foreign_key "partner_programs", "programs", on_delete: :cascade
  add_foreign_key "partner_themes", "partners", on_delete: :cascade
  add_foreign_key "partners", "addresses"
  add_foreign_key "partners", "promos_discount_codes", column: "promos_eva_discount_code_id"
  add_foreign_key "payments_events", "payments_transactions", column: "transaction_id"
  add_foreign_key "payments_installment_plans", "admin_users", column: "canceled_by_admin_user_id"
  add_foreign_key "payments_installment_plans", "ecom_payments"
  add_foreign_key "payments_installments", "payments_installment_plans", column: "installment_plan_id", on_delete: :cascade
  add_foreign_key "payments_installments", "payments_transactions", column: "transaction_charge_id", on_delete: :cascade
  add_foreign_key "payments_invoice_payments", "admin_users", on_delete: :cascade
  add_foreign_key "payments_invoice_payments", "ecom_payments", on_delete: :cascade
  add_foreign_key "payments_invoice_payments", "payments_payers", column: "payer_id", on_delete: :cascade
  add_foreign_key "payments_invoice_payments", "payments_transactions", column: "transaction_invoice_id", on_delete: :cascade
  add_foreign_key "payments_manual_payments", "admin_users", on_delete: :cascade
  add_foreign_key "payments_manual_payments", "ecom_payments", on_delete: :cascade
  add_foreign_key "payments_manual_payments", "payments_payers", column: "payer_id", on_delete: :cascade
  add_foreign_key "payments_manual_payments", "payments_transactions", column: "transaction_charge_id", on_delete: :cascade
  add_foreign_key "payments_transactions", "ecom_payments", on_delete: :cascade
  add_foreign_key "payments_transactions", "payments_payers", column: "payer_id"
  add_foreign_key "program_themes", "programs", on_delete: :cascade
  add_foreign_key "programs", "lms_grading_assistants"
  add_foreign_key "promos_cross_sell_lists", "programs", on_delete: :cascade
  add_foreign_key "promos_cross_sells", "deals", on_delete: :cascade
  add_foreign_key "promos_cross_sells", "emails"
  add_foreign_key "promos_cross_sells", "enrollments", on_delete: :cascade
  add_foreign_key "promos_cross_sells", "learners", on_delete: :cascade
  add_foreign_key "promos_cross_sells", "promos_cross_sell_lists", column: "cross_sell_list_id", on_delete: :cascade
  add_foreign_key "promos_discount_applications", "ecom_orders"
  add_foreign_key "promos_discount_applications", "promos_discount_codes", column: "discount_code_id"
  add_foreign_key "promos_discount_codes", "admin_users"
  add_foreign_key "promos_discount_codes", "promos_discounts", column: "discount_id", on_delete: :cascade
  add_foreign_key "promos_discount_condition_applications", "promos_discount_codes", column: "discount_code_id", on_delete: :cascade
  add_foreign_key "promos_discount_condition_applications", "promos_discount_conditions", column: "discount_condition_id", on_delete: :cascade
  add_foreign_key "promos_discount_conditions", "emails", on_delete: :cascade
  add_foreign_key "promos_discount_conditions", "partners", on_delete: :cascade
  add_foreign_key "promos_discount_conditions", "programs", on_delete: :cascade
  add_foreign_key "promos_referrers", "enrollments"
  add_foreign_key "registrations", "admin_users"
  add_foreign_key "registrations", "deals"
  add_foreign_key "registrations", "emails"
  add_foreign_key "registrations", "enrollments", column: "reenrolled_from_id"
  add_foreign_key "registrations", "finance_relationships"
  add_foreign_key "registrations", "learners"
  add_foreign_key "registrations", "partner_programs"
  add_foreign_key "registrations", "promos_referrers"
  add_foreign_key "registrations", "sections"
  add_foreign_key "registrations", "site_ad_trackings"
  add_foreign_key "role_permissions", "permissions"
  add_foreign_key "role_permissions", "roles"
  add_foreign_key "section_weeks", "cohort_weeks"
  add_foreign_key "section_weeks", "sections"
  add_foreign_key "sections", "cohorts", on_delete: :cascade
  add_foreign_key "sections", "learning_delivery_employees", column: "advocate_id"
  add_foreign_key "sections", "learning_delivery_employees", column: "grader_id"
  add_foreign_key "sections", "learning_delivery_employees", column: "instructor_id"
  add_foreign_key "sections", "partners"
  add_foreign_key "site_applied_technologies", "site_program_datasets", column: "program_dataset_id", on_delete: :cascade
  add_foreign_key "site_career_option_images", "site_squeeze_page_templates", column: "squeeze_page_template_id", on_delete: :cascade
  add_foreign_key "site_instructors", "site_program_datasets", column: "program_dataset_id", on_delete: :cascade
  add_foreign_key "site_landing_page_templates", "programs", on_delete: :cascade
  add_foreign_key "site_pages", "partner_programs", on_delete: :cascade
  add_foreign_key "site_partner_datasets", "partners", on_delete: :cascade
  add_foreign_key "site_partner_program_datasets", "partner_programs", on_delete: :cascade
  add_foreign_key "site_program_datasets", "programs", on_delete: :cascade
  add_foreign_key "site_reimbursement_page_templates", "programs", on_delete: :cascade
  add_foreign_key "site_squeeze_page_templates", "programs"
  add_foreign_key "site_syllabus_page_templates", "programs", on_delete: :cascade
  add_foreign_key "site_testimonials", "site_program_datasets", column: "program_dataset_id", on_delete: :cascade
  add_foreign_key "syllabus_requests", "deals"
  add_foreign_key "syllabus_requests", "emails"
  add_foreign_key "syllabus_requests", "partner_programs", on_delete: :cascade
  add_foreign_key "syllabus_requests", "promos_referrers"
  add_foreign_key "syllabus_requests", "site_ad_trackings"
end
