# frozen_string_literal: true

class AddIndexesForGraderQueryPerformance < ActiveRecord::Migration[8.0]
  disable_ddl_transaction!

  def change
    add_index :lms_assignments, [:due_at, :id],
      name: 'idx_assignments_sorting',
      comment: 'Optimizes ORDER BY lms_assignments.due_at, lms_assignments.id',
      algorithm: :concurrently

    remove_index :enrollments, :section_id, algorithm: :concurrently
    add_index :enrollments, [:section_id, :id],
      name: 'idx_enrollments_section_join',
      comment: 'Optimizes section filtering and JOIN back to submissions',
      algorithm: :concurrently
  end
end
