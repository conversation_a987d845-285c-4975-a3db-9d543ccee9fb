# frozen_string_literal: true

module Dev
  module Seeds
    class SeedRole < ApplicationCommand

      ROLES_WITH_PERMISSIONS = [
        { name: 'Site Builder', permissions: [
          { resource_group: 'Base', level: 'r' },
          { resource_group: 'Customers', level: 'r' },
          { resource_group: 'Order Management', level: 'r' },
          { resource_group: 'Product Catalog', level: 'r' },
          { resource_group: 'Promos', level: 'r' },
          { resource_group: 'Site', level: 'rwd' },
        ], },
        { name: 'Site Builder Manager', permissions: [
          { resource_group: 'Admin', level: 'rw' },
          { resource_group: 'Base', level: 'r' },
          { resource_group: 'Customers', level: 'r' },
          { resource_group: 'Order Management', level: 'r' },
          { resource_group: 'Product Catalog', level: 'r' },
          { resource_group: 'Promos', level: 'r' },
          { resource_group: 'Site', level: 'rwd' },
        ], },
        { name: 'LSA', permissions: [
        ], },
        { name: 'Learning Delivery Manager', permissions: [
          { resource_group: 'Admin', level: 'rw' },
          { resource_group: 'Other Platforms', level: 'r' },
          { resource_group: 'Base', level: 'r' },
          { resource_group: 'Customers', level: 'rw' },
          { resource_group: 'Product Catalog', level: 'r' },
          { resource_group: 'External Content', level: 'rwd' },
          { resource_group: 'Learning Delivery', level: 'rwd' },
          { resource_group: 'Learning Delivery - Management', level: 'rwd' },
          { resource_group: 'LSA', level: 'rwd' },
        ], },
        { name: 'Admissions', permissions: [
          { resource_group: 'Other Platforms', level: 'r' },
          { resource_group: 'Base', level: 'r' },
          { resource_group: 'Customers', level: 'rw' },
          { resource_group: 'Order Management', level: 'rw' },
          { resource_group: 'Product Catalog', level: 'r' },
          { resource_group: 'Promos', level: 'r' },
        ], },
        { name: 'Admissions Manager', permissions: [
          { resource_group: 'Admin', level: 'rw' },
          { resource_group: 'Other Platforms', level: 'r' },
          { resource_group: 'Base', level: 'r' },
          { resource_group: 'Customers', level: 'rw' },
          { resource_group: 'Order Management', level: 'rw' },
          { resource_group: 'Partner Contracts', level: 'r' },
          { resource_group: 'Product Catalog', level: 'r' },
          { resource_group: 'Promos', level: 'rwd' },
        ], },
        { name: 'Engineering', permissions: [
          { resource_group: 'Admin', level: 'r' },
          { resource_group: 'Alerts', level: 'rw' },
          { resource_group: 'Base', level: 'r' },
          { resource_group: 'Customers', level: 'r' },
          { resource_group: 'Development', level: 'r' },
          { resource_group: 'Order Management', level: 'r' },
          { resource_group: 'Other Platforms', level: 'r' },
          { resource_group: 'Partner Contracts', level: 'r' },
          { resource_group: 'Product Catalog', level: 'r' },
          { resource_group: 'Promos', level: 'r' },
          { resource_group: 'Site', level: 'r' },
        ], },
        { name: 'Engineering Manager', permissions: [
          { resource_group: 'Admin', level: 'rw' },
          { resource_group: 'Admin - Impersonation', level: 'rw' },
          { resource_group: 'Alerts', level: 'rw' },
          { resource_group: 'Base', level: 'rw' },
          { resource_group: 'Customers', level: 'rw' },
          { resource_group: 'Development', level: 'rw' },
          { resource_group: 'Finance', level: 'r' },
          { resource_group: 'Operational Finance', level: 'r' },
          { resource_group: 'Order Management', level: 'rw' },
          { resource_group: 'Other Platforms', level: 'rw' },
          { resource_group: 'Partner Contracts', level: 'r' },
          { resource_group: 'Product Catalog', level: 'r' },
          { resource_group: 'Promos', level: 'r' },
          { resource_group: 'Site', level: 'r' },
          { resource_group: 'External Content', level: 'rwd' },
          { resource_group: 'Learning Delivery', level: 'rw' },
          { resource_group: 'Learning Delivery - Impersonation', level: 'rw' },
        ], },
        { name: 'Operations', permissions: [
          { resource_group: 'Development', level: 'r' },
          { resource_group: 'Other Platforms', level: 'r' },
          { resource_group: 'Base', level: 'rw' },
          { resource_group: 'Customers', level: 'rw' },
          { resource_group: 'Order Management', level: 'rw' },
          { resource_group: 'Product Catalog', level: 'r' },
          { resource_group: 'Promos', level: 'rw' },
          { resource_group: 'Site', level: 'r' },
          { resource_group: 'Alerts', level: 'rw' },
          { resource_group: 'External Content', level: 'rw' },
        ], },
        { name: 'Operations Manager', permissions: [
          { resource_group: 'Admin', level: 'rwd' },
          { resource_group: 'Development', level: 'r' },
          { resource_group: 'Other Platforms', level: 'r' },
          { resource_group: 'Base', level: 'rwd' },
          { resource_group: 'Customers', level: 'rwd' },
          { resource_group: 'Order Management', level: 'rwd' },
          { resource_group: 'Product Catalog', level: 'rw' },
          { resource_group: 'Finance', level: 'r' },
          { resource_group: 'Promos', level: 'rwd' },
          { resource_group: 'Site', level: 'rwd' },
          { resource_group: 'Alerts', level: 'rw' },
          { resource_group: 'Manage Roles', level: 'rwd' },
          { resource_group: 'Operational Finance', level: 'r' },
          { resource_group: 'External Content', level: 'rwd' },
        ], },
        { name: 'Finance', permissions: [
          { resource_group: 'Other Platforms', level: 'r' },
          { resource_group: 'Base', level: 'r' },
          { resource_group: 'Customers', level: 'r' },
          { resource_group: 'Finance', level: 'r' },
          { resource_group: 'Order Management', level: 'r' },
          { resource_group: 'Product Catalog', level: 'r' },
          { resource_group: 'Promos', level: 'r' },
          { resource_group: 'Partner Contracts', level: 'r' },
        ], },
        { name: 'Finance Manager', permissions: [
          { resource_group: 'Admin', level: 'rw' },
          { resource_group: 'Other Platforms', level: 'r' },
          { resource_group: 'Base', level: 'r' },
          { resource_group: 'Customers', level: 'r' },
          { resource_group: 'Finance', level: 'rw' },
          { resource_group: 'Order Management', level: 'rw' },
          { resource_group: 'Product Catalog', level: 'rw' },
          { resource_group: 'Promos', level: 'r' },
          { resource_group: 'Partner Contracts', level: 'rw' },
        ], },
        { name: 'Marketing', permissions: [
          { resource_group: 'Other Platforms', level: 'r' },
          { resource_group: 'Base', level: 'r' },
          { resource_group: 'Customers', level: 'r' },
          { resource_group: 'Order Management', level: 'r' },
          { resource_group: 'Product Catalog', level: 'r' },
          { resource_group: 'Promos', level: 'rw' },
          { resource_group: 'Site', level: 'r' },
        ], },
        { name: 'Marketing Manager', permissions: [
          { resource_group: 'Admin', level: 'rw' },
          { resource_group: 'Other Platforms', level: 'r' },
          { resource_group: 'Base', level: 'r' },
          { resource_group: 'Customers', level: 'r' },
          { resource_group: 'Order Management', level: 'r' },
          { resource_group: 'Product Catalog', level: 'r' },
          { resource_group: 'Promos', level: 'rwd' },
          { resource_group: 'Site', level: 'r' },
        ], },
        { name: 'Partnerships', permissions: [
          { resource_group: 'Other Platforms', level: 'r' },
          { resource_group: 'Base', level: 'r' },
          { resource_group: 'Customers', level: 'r' },
          { resource_group: 'Order Management', level: 'r' },
          { resource_group: 'Product Catalog', level: 'r' },
          { resource_group: 'Finance', level: 'r' },
          { resource_group: 'Promos', level: 'r' },
          { resource_group: 'Site', level: 'r' },
          { resource_group: 'Partner Contracts', level: 'rw' },
        ], },
        { name: 'Partnerships Manager', permissions: [
          { resource_group: 'Admin', level: 'rw' },
          { resource_group: 'Other Platforms', level: 'r' },
          { resource_group: 'Base', level: 'r' },
          { resource_group: 'Customers', level: 'r' },
          { resource_group: 'Order Management', level: 'r' },
          { resource_group: 'Product Catalog', level: 'r' },
          { resource_group: 'Finance', level: 'r' },
          { resource_group: 'Promos', level: 'r' },
          { resource_group: 'Site', level: 'r' },
          { resource_group: 'Partner Contracts', level: 'rw' },
        ], },
      ].freeze

      MANAGER_ROLES_MAPPING = [
        { manager_role: 'Site Builder Manager', grant_role: 'Site Builder Manager' },
        { manager_role: 'Site Builder Manager', grant_role: 'Site Builder' },
        { manager_role: 'Learning Delivery Manager', grant_role: 'Learning Delivery Manager' },
        { manager_role: 'Learning Delivery Manager', grant_role: 'LSA' },
        { manager_role: 'Admissions Manager', grant_role: 'Admissions Manager' },
        { manager_role: 'Admissions Manager', grant_role: 'Admissions' },
        { manager_role: 'Engineering Manager', grant_role: 'Engineering Manager' },
        { manager_role: 'Engineering Manager', grant_role: 'Engineering' },
        { manager_role: 'Operations Manager', grant_role: 'Operations Manager' },
        { manager_role: 'Operations Manager', grant_role: 'Operations' },
        { manager_role: 'Finance Manager', grant_role: 'Finance Manager' },
        { manager_role: 'Finance Manager', grant_role: 'Finance' },
        { manager_role: 'Marketing Manager', grant_role: 'Marketing Manager' },
        { manager_role: 'Marketing Manager', grant_role: 'Marketing' },
        { manager_role: 'Partnerships Manager', grant_role: 'Partnerships Manager' },
        { manager_role: 'Partnerships Manager', grant_role: 'Partnerships' },
      ].freeze

      def call!
        create_roles_with_permissions
        add_grant_permissions
      end

      private

      def create_roles_with_permissions
        ROLES_WITH_PERMISSIONS.each do |role_data|
          role = Role.find_or_create_by!(name: role_data[:name])

          role_data[:permissions].each do |permission_data|
            permission = Permission.find_or_create_by!(resource_group: permission_data[:resource_group], level: permission_data[:level].to_sym)

            role.permissions << permission unless role.permissions.include?(permission)
          end
        end
      end

      def add_grant_permissions
        MANAGER_ROLES_MAPPING.each do |mapping|
          manager_role = Role.find_by(name: mapping[:manager_role])
          grant_role = Role.find_by(name: mapping[:grant_role])
          grant_permission = Permission.find_or_create_by(resource_group: 'Grant', level: 'rw', resource_type: 'Role', resource_id: grant_role.id)

          manager_role.permissions << grant_permission unless manager_role.permissions.include?(grant_permission)
        end
      end
    end
  end
end
