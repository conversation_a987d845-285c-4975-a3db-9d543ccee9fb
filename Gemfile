# frozen_string_literal: true

source 'https://rubygems.org'

# NOTE: Would prefer to use `file: '.ruby-version'` here but renovate isn't able
# to handle this (it forgets to run bundle install when bumping the ruby
# version), specifying the exact version does not suffer from this bug.
# In practice, this shouldn't be a big issue since renovate should keep it in sync
# with the .ruby-version file
ruby '3.4.4'

# Bundle edge Rails instead: gem "rails", github: "rails/rails", branch: "main"
gem 'rails', '~> 8.0.0', '>= 8.0.1'

# The original asset pipeline for Rails [https://github.com/rails/sprockets-rails]
gem "sprockets-rails", "~> 3.5"

# Use postgresql as the database for Active Record
gem 'pg', '~> 1.5'

# Use the Puma web server [https://github.com/puma/puma]
gem 'puma', '~> 6.4'
gem 'puma-cloudwatch', git: 'https://github.com/cswilliams/puma-cloudwatch.git', tag: 'v0.5.3'

# Use JavaScript with ESM import maps [https://github.com/rails/importmap-rails]
gem "importmap-rails", "~> 2.0"

# Hotwire's SPA-like page accelerator [https://turbo.hotwired.dev]
gem "turbo-rails", "~> 2.0"

# Hotwire's modest JavaScript framework [https://stimulus.hotwired.dev]
gem "stimulus-rails", "~> 1.3"

# Build JSON APIs with ease [https://github.com/rails/jbuilder]
gem "jbuilder", "~> 2.12"

# Use redis for session storage and sidekiq
gem "hiredis", "~> 0.6.3"
gem "hiredis-client", "~> 0.25.0"
gem "redis", "~> 5.2"
gem "redis-session-store", "~> 0.11.5"

# Sidekiq for background jobs
gem 'sidekiq', '~> 8.0'
gem 'sidekiq-cron', '~> 2.0'
gem 'sidekiq-unique-jobs', '~> 8.0'

# HTTP library
gem 'httpx', '~> 1.3'

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem 'tzinfo-data', "~> 1.2024", platforms: %i[windows jruby]

# Reduces boot times through caching; required in config/boot.rb
gem 'bootsnap', "~> 1.18", require: false

# Use Active Storage helpers and variant support [https://guides.rubyonrails.org/active_storage_overview.html#transforming-images]
gem "aws-sdk-cloudfront", "~> 1.96"
gem "aws-sdk-s3", "~> 1.151"
gem "down", "~> 5.4"
gem "image_processing", "~> 1.2"
gem "mini_magick", "~> 5.0"

# Active Admin
gem "activeadmin", "~> 3.2"
gem "activeadmin_addons", "~> 1.10"
gem "activeadmin_json_editor", "~> 0.0.10"

# Pagination
gem "pagy", "~> 9.0"

# Model Helpers
gem "active_storage_validations", "~> 2.0"
gem "nanoid", "~> 2.0"
gem "paper_trail", "~> 16.0"
gem "phonelib", "~> 0.10.0"
gem "public_uid", "~> 2.2"
gem "rich_enums", "~> 0.2.0"
gem "set_as_primary", "~> 0.1.4"
gem "strip_attributes", "~> 2.0"
gem "tod", "~> 3.1"

# Auth
gem 'bcrypt', '~> 3.1.20'
gem "devise", "~> 4.9"
gem "omniauth", "~> 2.1"
gem "omniauth-google-oauth2", "~> 1.1"
gem "omniauth-rails_csrf_protection", "~> 1.0"

# Front End
gem "chartkick", "~> 5.1"
gem "font-awesome-sass", "~> 6.5"
gem "html_to_plain_text", "~> 1.0"
gem "rails_charts", "~> 0.0.6"
gem "slim", "~> 5.2"
gem "tailwindcss-rails", "~> 3.0"
gem "tailwind_merge", "~> 0.16.0"

# Logging, Monitoring, and Error Reporting
gem "lograge", "~> 0.14.0"
gem "rollbar", "~> 3.5"
gem 'scout_apm', "~> 5.6.0"
gem 'slack-notifier', "~> 2.4"

# Debugging (see lib/sigdump_wrapper.rb)
gem "sigdump", "~> 0.2.5", require: false

# Load ENV vars and secrets from AWS Parameter Store
gem 'parameter-store-rails', git: 'https://github.com/zip-learning/parameter-store-rails.git', tag: 'v0.0.10'

# Remotely access rails console and DB in staging and production
gem 'rails-remote-access', git: 'https://github.com/zip-learning/rails-remote-access.git', tag: 'v0.0.18', require: false, group: :rails_remote_access

# Gem for creating and restoring obfuscated database snapshots of production
gem 'db-snapshot', git: 'https://github.com/zip-learning/db-snapshot.git', tag: 'v1.1.4', require: 'db_snapshot/snapshot_metadata'

# Middleware
gem "rack-cors", "~> 3.0"

# Timeout, locking
gem "pg_lock", "~> 1.0"
gem "rack-attack", "~> 6.7"
gem "rack-timeout", "~> 0.7.0"

# Stripe Payments
gem 'stripe', '~> 13.3'

# Affirm Payments
gem 'affirm-ruby', '~> 1.2.0', require: 'affirm'

# Permission based authorization
gem 'cancancan', '~> 3.6'

# Utility extensions
gem "indefinite_article", "~> 0.2.5"

# Misc Utilities
gem "resolv", "~> 0.6.0"

# Catch unsafe migrations in development
gem "strong_migrations", "~> 2.4.0"

# Address Geocoding
gem 'geocoder', '~> 1.8'

# Google API Gems
gem "google-apis-calendar_v3", "~> 0.47.0"
gem 'google-apis-drive_v3', '~> 0.65.0'
gem "google-apis-forms_v1", "~> 0.18.0"
gem "google-cloud-bigquery", "~> 1.0"

# Money for USD/CAD support
gem 'money', '~> 6.19'

# AI Integrations
gem "ruby-openai", "~> 8.1.0"

group :development, :test do
  gem "annotaterb", "~> 4.13"

  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem "debug", "~> 1.9", platforms: %i[ mri windows ]
  gem "dotenv-rails", "~> 3.1"

  gem "factory_bot_rails", "~> 6.4"
  gem "ffaker", "~> 2.23"
  gem "pry", "~> 0.15.0"
  gem 'pry-byebug', "~> 3.10" # allows you to use continue, next, step

  gem 'letter_opener', '~> 1.10'

  gem "parallel_tests", "~> 5.0"
end

group :rubocop, :development, :test do
  gem "rubocop-capybara", "~> 2.21", require: false
  gem "rubocop-factory_bot", "~> 2.26", require: false
  gem "rubocop-performance", "~> 1.21", require: false
  gem "rubocop-rails", "~> 2.25", require: false
  gem "rubocop-rspec", "~> 3.0", require: false
  gem "rubocop-rspec_rails", "~> 2.30", require: false
end

group :development do
  gem 'bullet', '~> 8.0'

  # Use console on exceptions pages [https://github.com/rails/web-console]
  gem "web-console", "~> 4.2"

  # Add speed badges [https://github.com/MiniProfiler/rack-mini-profiler]
  # gem "rack-mini-profiler"

  # Speed up commands on slow machines / big apps [https://github.com/rails/spring]
  # gem "spring"
end

group :test do
  # Use system testing [https://guides.rubyonrails.org/testing.html#system-testing]
  gem "capybara", "~> 3.40"
  gem "launchy", "~> 3.0"
  gem "rails-controller-testing", "~> 1.0"
  gem "rspec-collection_matchers", "~> 1.2"
  gem 'rspec-github', "~> 3.0", require: false
  gem "rspec-rails", "~> 8.0"
  gem "selenium-webdriver", "~> 4.23"
  gem 'shoulda-matchers', '~> 6.0'
  gem "temping", "~> 4.1"
  gem "timecop", "~> 0.9.10"
  gem "vcr", "~> 6.2"
  gem "webmock", "~> 3.23"
end
