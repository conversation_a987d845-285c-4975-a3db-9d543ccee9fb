---
http_interactions:
- request:
    method: get
    uri: https://ziplineseducation.zendesk.com/api/v2/help_center/articles/search?per_page=1&query=test
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept:
      - application/json
      Content-Type:
      - application/json
      Authorization:
      - Basic <BASIC_AUTH_TOKEN>
      User-Agent:
      - httpx.rb/1.5.1
      Accept-Encoding:
      - gzip, deflate
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 04 Aug 2025 07:47:58 GMT
      Content-Type:
      - application/json; charset=utf-8
      X-Ua-Compatible:
      - IE=edge
      X-Zendesk-Api-Version:
      - v2
      X-Xss-Protection:
      - 1; mode=block
      X-Frame-Options:
      - SAMEORIGIN
      Etag:
      - W/"8ffe5448ff3b35a1c54f418c5a271f55"
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Expose-Headers:
      - X-Zendesk-API-Warn
      Cache-Control:
      - max-age=0, private, must-revalidate
      X-Rate-Limit:
      - '400'
      X-Rate-Limit-Remaining:
      - '398'
      Strict-Transport-Security:
      - max-age=31536000; includeSubDomains
      X-Zendesk-Origin-Server:
      - app-server-645f59b588-rffw4
      X-Request-Id:
      - 969c731badd53a1a-BOM
      X-Runtime:
      - '0.193388'
      X-Envoy-Upstream-Service-Time:
      - '197'
      Zendesk-Service:
      - help-center
      X-Zendesk-Api-Gateway:
      - 'yes'
      X-Zendesk-Zorg:
      - 'yes'
      Protocol:
      - HTTP/1.1 always
      Vary:
      - Accept-Encoding
      Via:
      - zorg
      X-Envoy-Decorator-Operation:
      - "/api/v2/help_center/"
      Cf-Cache-Status:
      - BYPASS
      Set-Cookie:
      - __cf_bm=dQMax.3IQbFZxskcj77cwIO9GKwKvgSY6JYHt0XBqEY-1754293678-*******-KZCIAK_FS177BH9JQJPygnZgdipsSl3c.5agVGIrdpPrVSKP210R7MGarrO.kAXggRM7QSP97wRNSjPd2zYngF05k3dzBOTo7saM6ZtoEHg;
        path=/; expires=Mon, 04-Aug-25 08:17:58 GMT; domain=.ziplineseducation.zendesk.com;
        HttpOnly; Secure; SameSite=None, _cfuvid=_GSBw8kldqOSG4WTTElGE_X1ADl_iBGgqV5M2RkEZhE-1754293678163-*******-604800000;
        path=/; domain=.ziplineseducation.zendesk.com; HttpOnly; Secure; SameSite=None
      Report-To:
      - '{"endpoints":[{"url":"https:\/\/a.nel.cloudflare.com\/report\/v4?s=%2Bzq75yy0LYpFBKiXsXK6xz6z%2FMeHJo9tWNNwv%2BCmNm%2B3FBZ7HtnZdxLSmO8yTLLzps%2F3SbVaDPHJvXwWwJXxsojYESmsDUc1DgAvvlx1ArQQ4lXJ1T%2FVA6LvbW21YUm%2F688BPyE4W3JP%2BmfJAmHo"}],"group":"cf-nel","max_age":604800}'
      Nel:
      - '{"success_fraction":0.01,"report_to":"cf-nel","max_age":604800}'
      Server:
      - cloudflare
      Cf-Ray:
      - 969c731badd53a1a-BOM
      Content-Encoding:
      - gzip
    body:
      encoding: UTF-8
      string: '{"count":1,"next_page":null,"page":1,"page_count":1,"per_page":1,"previous_page":null,"results":[{"id":39431299473691,"url":"https://ziplineseducation.zendesk.com/api/v2/help_center/en-us/articles/39431299473691.json","html_url":"https://ziplineseducation.zendesk.com/hc/en-us/articles/39431299473691-TEST-Start-Dates","author_id":37150245325979,"comments_disabled":false,"draft":false,"promoted":false,"position":0,"vote_sum":0,"vote_count":0,"section_id":39430406757275,"created_at":"2025-07-28T23:32:46Z","updated_at":"2025-07-28T23:32:46Z","name":"TEST
        Start Dates","title":"TEST Start Dates","source_locale":"en-us","locale":"en-us","outdated":false,"outdated_locales":[],"edited_at":"2025-07-28T23:32:46Z","user_segment_id":37202369253275,"permission_group_id":37202369273755,"content_tag_ids":[],"label_names":[],"body":null,"snippet":"","result_type":"article"}]}'
  recorded_at: Mon, 04 Aug 2025 07:47:58 GMT
recorded_with: VCR 6.3.1
