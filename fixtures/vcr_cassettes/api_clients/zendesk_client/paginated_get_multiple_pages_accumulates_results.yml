---
http_interactions:
- request:
    method: get
    uri: https://ziplineseducation.zendesk.com/api/v2/help_center/articles?per_page=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept:
      - application/json
      Content-Type:
      - application/json
      Authorization:
      - Basic <BASIC_AUTH_TOKEN>
      User-Agent:
      - httpx.rb/1.5.1
      Accept-Encoding:
      - gzip, deflate
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Tue, 12 Aug 2025 22:05:23 GMT
      Content-Type:
      - application/json; charset=utf-8
      X-Zendesk-Api-Version:
      - v2
      Cache-Control:
      - max-age=0, private, must-revalidate
    body:
      encoding: UTF-8
      string: '{"count":109,"next_page":"https://ziplineseducation.zendesk.com/api/v2/help_center/articles.json?page=2\u0026per_page=1","page":1,"page_count":109,"per_page":1,"previous_page":null,"articles":[{"id":39431299473691,"title":"TEST Start Dates","name":"TEST Start Dates"}],"sort_by":"position","sort_order":"asc"}'
  recorded_at: Tue, 12 Aug 2025 22:05:23 GMT
- request:
    method: get
    uri: https://ziplineseducation.zendesk.com/api/v2/help_center/articles.json?page=2&per_page=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept:
      - application/json
      Content-Type:
      - application/json
      Authorization:
      - Basic <BASIC_AUTH_TOKEN>
      User-Agent:
      - httpx.rb/1.5.1
      Accept-Encoding:
      - gzip, deflate
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Tue, 12 Aug 2025 22:05:24 GMT
      Content-Type:
      - application/json; charset=utf-8
      X-Zendesk-Api-Version:
      - v2
      Cache-Control:
      - max-age=0, private, must-revalidate
    body:
      encoding: UTF-8
      string: '{"count":109,"next_page":"https://ziplineseducation.zendesk.com/api/v2/help_center/articles.json?page=3\u0026per_page=1","page":2,"page_count":109,"per_page":1,"previous_page":"https://ziplineseducation.zendesk.com/api/v2/help_center/articles.json?page=1\u0026per_page=1","articles":[{"id":39261851772443,"title":"How do I change my name and password?","name":"How do I change my name and password?"}],"sort_by":"position","sort_order":"asc"}'
  recorded_at: Tue, 12 Aug 2025 22:05:24 GMT
- request:
    method: get
    uri: https://ziplineseducation.zendesk.com/api/v2/help_center/articles.json?page=3&per_page=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept:
      - application/json
      Content-Type:
      - application/json
      Authorization:
      - Basic <BASIC_AUTH_TOKEN>
      User-Agent:
      - httpx.rb/1.5.1
      Accept-Encoding:
      - gzip, deflate
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Tue, 12 Aug 2025 22:05:25 GMT
      Content-Type:
      - application/json; charset=utf-8
      X-Zendesk-Api-Version:
      - v2
      Cache-Control:
      - max-age=0, private, must-revalidate
    body:
      encoding: UTF-8
      string: '{"count":109,"next_page":null,"page":3,"page_count":109,"per_page":1,"previous_page":"https://ziplineseducation.zendesk.com/api/v2/help_center/articles.json?page=2\u0026per_page=1","articles":[{"id":39259121494171,"title":"How to manage Zendesk Knowledge Base?","name":"How to manage Zendesk Knowledge Base?"}],"sort_by":"position","sort_order":"asc"}'
  recorded_at: Tue, 12 Aug 2025 22:05:25 GMT
recorded_with: VCR 6.3.1