---
http_interactions:
- request:
    method: get
    uri: https://ziplineseducation.zendesk.com/api/v2/help_center/articles?per_page=1
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept:
      - application/json
      Content-Type:
      - application/json
      Authorization:
      - Basic <BASIC_AUTH_TOKEN>
      User-Agent:
      - httpx.rb/1.5.1
      Accept-Encoding:
      - gzip, deflate
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Mon, 04 Aug 2025 07:47:56 GMT
      Content-Type:
      - application/json; charset=utf-8
      X-Ua-Compatible:
      - IE=edge
      X-Zendesk-Api-Version:
      - v2
      X-Xss-Protection:
      - 1; mode=block
      X-Frame-Options:
      - SAMEORIGIN
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Expose-Headers:
      - X-Zendesk-API-Warn
      X-Rate-Limit:
      - '400'
      X-Rate-Limit-Remaining:
      - '399'
      Strict-Transport-Security:
      - max-age=31536000; includeSubDomains
      X-Zendesk-Origin-Server:
      - app-server-645f59b588-8cth2
      Etag:
      - W/"09b2b52744ed1fe9c729b6aef92a5925"
      Cache-Control:
      - max-age=0, private, must-revalidate
      X-Request-Id:
      - 969c7314dab246ea-BOM
      X-Runtime:
      - '0.091044'
      X-Envoy-Upstream-Service-Time:
      - '95'
      Zendesk-Service:
      - help-center
      X-Zendesk-Api-Gateway:
      - 'yes'
      X-Zendesk-Zorg:
      - 'yes'
      Protocol:
      - HTTP/1.1 always
      Vary:
      - Accept-Encoding
      Via:
      - zorg
      X-Envoy-Decorator-Operation:
      - "/api/v2/help_center/"
      Cf-Cache-Status:
      - BYPASS
      Set-Cookie:
      - __cf_bm=NMHYGoiKRIEBPAAxA_bjjSuE3wTblVUWwrOIobKgMYg-1754293676-*******-jeXjikjKDjvNtYgGJh69jga6TL8fCKiIHdMEF1neEH0JzB33E_GwrGDC332kIayGfQRyBy3oU7_xXiaQ_5veIVQCJDU6dkVoG6nw6uA5HRE;
        path=/; expires=Mon, 04-Aug-25 08:17:56 GMT; domain=.ziplineseducation.zendesk.com;
        HttpOnly; Secure; SameSite=None, _cfuvid=AdfrIBpBHPMKs3Jy6dzDnxgwEg5h1u0.S6po.TxJ_ow-1754293676955-*******-604800000;
        path=/; domain=.ziplineseducation.zendesk.com; HttpOnly; Secure; SameSite=None
      Report-To:
      - '{"endpoints":[{"url":"https:\/\/a.nel.cloudflare.com\/report\/v4?s=GJePOBXR9VVnfZiWGE4%2BBXQmYOFeFBerHbtyxubhAvDAibw9yDo6H6K%2Bg3q8GKsm%2F49goNMA%2BEK88ArPLZvIFha13sSaUKo9GKlO23XgDD65gQsvS%2FWgienxSucD4z5OdJgtADowO6Hc9Oom1pNB"}],"group":"cf-nel","max_age":604800}'
      Nel:
      - '{"success_fraction":0.01,"report_to":"cf-nel","max_age":604800}'
      Server:
      - cloudflare
      Cf-Ray:
      - 969c7314dab246ea-BOM
      Content-Encoding:
      - gzip
    body:
      encoding: UTF-8
      string: '{"count":81,"next_page":"https://ziplineseducation.zendesk.com/api/v2/help_center/articles.json?page=2\u0026per_page=1","page":1,"page_count":81,"per_page":1,"previous_page":null,"articles":[{"id":39431299473691,"url":"https://ziplineseducation.zendesk.com/api/v2/help_center/en-us/articles/39431299473691.json","html_url":"https://ziplineseducation.zendesk.com/hc/en-us/articles/39431299473691-TEST-Start-Dates","author_id":37150245325979,"comments_disabled":false,"draft":false,"promoted":false,"position":0,"vote_sum":0,"vote_count":0,"section_id":39430406757275,"created_at":"2025-07-28T23:32:46Z","updated_at":"2025-07-28T23:32:46Z","name":"TEST
        Start Dates","title":"TEST Start Dates","source_locale":"en-us","locale":"en-us","outdated":false,"outdated_locales":[],"edited_at":"2025-07-28T23:32:46Z","user_segment_id":37202369253275,"permission_group_id":37202369273755,"content_tag_ids":[],"label_names":[],"body":null}],"sort_by":"position","sort_order":"asc"}'
  recorded_at: Mon, 04 Aug 2025 07:47:57 GMT
recorded_with: VCR 6.3.1
