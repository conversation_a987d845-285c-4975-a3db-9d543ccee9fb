# frozen_string_literal: true

ActiveAdmin.register Partner do
  extend RemoteResourcesAdminActions

  show_remote_ops_menu do
    title = 'Publishes partner attributes to <PERSON><PERSON><PERSON> for all deals associated with this partner.'
    item 'Publish Attributes to all Partner Deals', url_for([:admin, :partner, action: :publish_partner_attributes_to_all_deals, id: request.params['id']]), title:
  end

  after_save do |_resource|
    auto_publish :hubspot_properties
    Address::GeocodeJob.perform_async(resource.address.id) if resource.address&.persisted?
  end

  includes :theme, :finance_contracts, partner_programs: [
    :program,
    :certificate_theme,
    :site_partner_dataset,
    :site_program_dataset,
    :site_partner_program_dataset,
    :certificate_theme,
  ]

  menu parent: 'Base', priority: 1

  permit_params :name, :short_name, :formal_name, :slug, :abbreviation, :certifier, :time_zone, :proxy_host, :status, :currency_code, :promos_eva_discount_code_id,
    :section_style, ecom_payment_method_ids: [], ecom_payment_providers: [],
    address_attributes: [:id, :street_address_1, :street_address_2, :city, :state, :country, :zip, :_destroy],
    theme_attributes: [
      :id,
      :cobranding_text,
      :course_nomenclature, :certificate_nomenclature,
      :logo, :logo_on_primary_background,
      :font_color_on_primary_background, :font_color_on_secondary_background,
      :from_email, :reply_to_email,
      :primary_color, :secondary_color,
      :favicon,
    ],
    certificate_theme_attributes: [
      :id, :administrator_name, :administrator_title, :certificate_type, :partner_name, :title, :logo, :_destroy,
    ],
    finance_relationships_attributes: [
      :id, :name, :default,
      contracts_attributes: [
        :id, :starts_on, :fee_rate_param, :per_unit_revenue_cents_param, :processing_fee_carve_out_rate_param,
      ],
    ]

  filter :uid_cont, label: 'UID'
  filter :name_cont, label: 'Name'
  filter :short_name_cont, label: 'Short Name'
  filter :slug_cont, label: 'Slug'
  filter :abbreviation_cont, label: 'Abbreviation'
  filter :proxy_host_cont, label: 'Proxy Host'
  filter :certifier
  filter :dns_status, as: :select, collection: -> { Partner.dns_status_alt_name_to_ids }
  filter :section_style, as: :select, collection: -> { Partner.section_style_alt_name_to_ids }

  filter :created_at, as: :date_time_picker_filter

  index do
    column(:id)
    column(:uid) { |p| copyable(p.humanized_uid) }
    column(:name)
    column(:slug) { |p| copyable(p.slug) }
    column(:status) { |p| status_tag(p.status_name, class: p.status) }
    column(:time_zone)
    column(:dns_status) { |p| status_tag(p.dns_status, class: p.dns_status) }
  end

  show do
    attributes_table do
      row(:id) { |p| copyable(p.id) }
      row(:uid) { |p| copyable(p.humanized_uid) }
      row(:name) { |p| copyable(p.name) }
      row(:short_name) { |p| copyable(p.short_name) }
      row(:formal_name) { |p| copyable(p.formal_name) }
      row(:slug) { |p| copyable(p.slug) }
      row(:abbreviation) { |p| copyable(p.abbreviation) }
      row(:status) { |p| status_tag(p.status_name, class: p.status) }
      row(:currency_code)
      row(:certifier, &:certifier_name)
      row(:time_zone)
      if resource.promos_eva_discount_code.present?
        row(:eva_discount_code) do |p|
          copyable(p.promos_eva_discount_code.active_admin_display_name, value: p.promos_eva_discount_code.humanized_code)
        end
      end
      row(:section_style) { |p| status_tag(p.section_style_name, class: p.section_style) }
      row(:payment_methods, &:ecom_payment_methods)
      row(:payment_providers) { |p| p.ecom_payment_providers.map { |pp| Ecom::PaymentProvider::KEY_NAME_MAP[pp] } }
      row(:created_at)
      row(:updated_at)
    end

    columns do
      column do
        panel "Theme" do
          attributes_table_for(resource.theme) do
            row(:cobranding_text)
            row(:course_nomenclature)
            row(:certificate_nomenclature)
            row(:from_email) { |t| copyable(t.from_email) }
            row(:reply_to_email) { |t| copyable(t.reply_to_email) }
            row(:logo) { |t| render_image(model: t, image_method: :logo, width: '300rem') if t.logo.present? }
            row(:logo_on_primary_background) do |t|
              div(style: "display: inline-block; padding: 1rem; background-color: #{t.primary_color || '#000000'}") do
                render_image(model: t, image_method: :logo_on_primary_background, width: '300rem') if t.logo_on_primary_background.present?
              end
            end
            row(:primary_color) { |t| render_color(color: t.primary_color) }
            row(:secondary_color) { |t| render_color(color: t.secondary_color) }
            row(:font_color_on_primary_background) do |t|
              render_text_on_background(
                text_color: t.font_color_on_primary_background,
                background_color: t.primary_color,
              )
            end
            row(:font_color_on_secondary_background) do |t|
              render_text_on_background(
                text_color: t.font_color_on_secondary_background,
                background_color: t.secondary_color,
              )
            end
            row(:primary_color_pixel) do |t|
              render_image(model: t, image_method: :primary_color_pixel, width: '25rem')
            end
            row(:favicon) { |t| render_image(model: t, image_method: :favicon, width: '25rem') }
          end
        end
      end

      column do
        if resource.address.present?
          panel "Address" do
            attributes_table_for(resource.address) do
              row(:street_address_1)
              row(:street_address_2)
              row(:city)
              row(:state)
              row(:country)
              row(:zip)
            end
          end
        end
        attributes_table(title: 'Proxy') do
          row(:proxy_host) { |p| p.proxy_host.present? ? link_to(p.proxy_host, "https://#{p.proxy_host}", target: '_BLANK') : nil }
          row(:dns_status) { |p| status_tag(p.dns_status, class: p.dns_status) }
          if resource.proxy_host.present?
            row(:onboarding_page) do |p|
              url = partners_dns_status_url(partner_uid: p.humanized_uid, subdomain: 'partners')
              copyable(
                link_to('Proxy Onboarding Page', url, target: '_BLANK'),
                url,
              )
            end
          end
        end
        panel "Certificate Theme" do
          if resource.certifier_third_party?
            attributes_table_for(resource.certificate_theme) do
              row(:administrator_name)
              row(:administrator_title)
              row(:certificate_type)
              row(:partner_name)
              row(:logo) { |t| render_image(model: t, image_method: :logo, width: '300rem') if t.logo.present? }
            end
          else
            div(style: 'display: flex; flex-direction: column; height: 10em; font-size: 2em; color: #AAAAAA; align-items: center; justify-content: center;') do
              div { "Certifier is: <em>#{resource.certifier_name}</em>".html_safe }
              div { "No certificate theme is needed." }
            end
          end
        end
      end
    end

    if authorized?(:read, 'Partner Contracts')
      panel "Relationships" do
        resource.finance_relationships.each do |relationship|
          attributes_table_for(relationship) do
            row(:name)
            row(:id)
            row(:uid) { |p| copyable(p.humanized_uid) }
            row(:default)
            row(:contracts) do
              table_for(relationship.contracts) do
                column(:starts_on)
                column(:fee_percent, &:fee_rate_param_humanized)
                column(:per_unit_revenue_amount, &:per_unit_revenue_cents_param_humanized)
                column(:processing_fee_carve_out_percent, &:processing_fee_carve_out_rate_param_humanized)
              end
            end
          end
        end
      end
    end

    if resource.partner_programs.present?
      hr
      panel "Launch Dashboard" do
        table_for(resource.partner_programs) do
          render(partial: 'admin/launch_dashboard_items/index', locals: { context: self })
        end
      end
    end

    remote_resources_for(resource)
  end

  form(data: { turbo: false }) do |_f|
    semantic_errors
    inputs "Details" do
      input(:name)
      input(:short_name)
      input(:formal_name)
      input(:slug) if resource.persisted?
      input(:abbreviation, hint: 'Used for partner-specific content filtering (e.g., partner:UGA, partnerprogram:UGA-DMS)')
      input(:proxy_host, hint: 'Ex: fig.ced.unlv.edu')
      input(:status)
      input(:currency_code, as: :select, collection: Ecom::Currency::VALID_CODES) unless resource.persisted?
      input(:certifier)
      input(:time_zone)
      input(:section_style)
      input(
        :promos_eva_discount_code_id,
        as: :search_select,
        url: proc { admin_promos_discount_codes_path },
        fields: %w[humanized_code description],
        display_name: :dropdown_name,
        method_model: Promos::DiscountCode,
      )
      on_site_payment_methods = Ecom::PaymentMethod.offer_on_site.order(:kind)
      input(
        :ecom_payment_methods,
        label: 'Payment Methods',
        as: :check_boxes,
        collection: on_site_payment_methods.map { |pm| [pm.kind_name, pm.id] },
        multiple: true,
      )
      # ensure any other payment methods are not removed by including in the form as hidden
      available_payment_methods = partner.new_record? ? Ecom::PaymentMethod.all : partner.ecom_payment_methods
      (available_payment_methods - on_site_payment_methods).each do |pm|
        b.input name: 'partner[ecom_payment_method_ids][]', type: :hidden, value: pm.id.to_s
      end
      input(
        :ecom_payment_providers,
        label: 'Payment Providers',
        as: :check_boxes,
        collection: Ecom::PaymentProvider::KEY_NAME_MAP.invert,
        multiple: true,
      )
    end

    inputs "Address" do
      has_many :address, allow_destroy: true, new_record: resource.address.blank? do |a|
        a.input :street_address_1
        a.input :street_address_2
        a.input :city
        a.input :state
        a.input :country, as: :select, collection: Address::COUNTRIES
        a.input :zip
      end
    end

    inputs "Theme" do
      has_many :theme, heading: false, new_record: false do |tf|
        tf.input(:cobranding_text)
        tf.input(:course_nomenclature)
        tf.input(:certificate_nomenclature)
        tf.input(:from_email)
        tf.input(:reply_to_email)
        tf.input(:logo, as: :file, hint: image_hint(model: object.theme, image_method: :logo), label: 'Logo*')
        tf.input(
          :logo_on_primary_background,
          as: :file,
          hint: image_hint(model: object.theme, image_method: :logo_on_primary_background, style: "padding: 1rem 1.5rem; background-color: #{object.theme.primary_color || '#000000'}"),
          label: 'Logo on Primary Background*',
        )
        tf.input(:primary_color)
        tf.input(:secondary_color)
        tf.input(:font_color_on_primary_background)
        tf.input(:font_color_on_secondary_background)
        tf.input(:favicon, as: :file, hint: image_hint(model: object.theme, image_method: :favicon))
      end
    end

    inputs "Certificate Theme" do
      has_many :certificate_theme, heading: false, allow_destroy: true do |ct|
        ct.input(:administrator_name)
        ct.input(:administrator_title)
        ct.input(:certificate_type)
        ct.input(:partner_name)
        ct.input(:logo, as: :file, hint: image_hint(model: object.certificate_theme, image_method: :logo))
      end
    end

    if authorized?(:update, 'Partner Contracts')
      inputs "Relationships" do
        has_many :finance_relationships, heading: false, new_record: resource.persisted? do |fr|
          fr.input(:name)
          fr.input(:default)

          fr.has_many :contracts, heading: 'Contracts', new_record: resource.persisted? do |c|
            c.input(:starts_on, as: :date_picker)
            c.input(:fee_rate_param, label: 'Fee Percent')
            c.input(:per_unit_revenue_cents_param, label: 'Per Unit Revenue Amount')
            c.input(:processing_fee_carve_out_rate_param, label: 'Processing Fee Carve Out Percent')
          end
        end
      end
    end

    actions
  end

  member_action :publish_partner_attributes_to_all_deals, method: %i[get post] do
    if request.post?
      begin
        Partner::PublishPartnerAttributesToDealsJob.perform_async(resource.id)
        redirect_to admin_partner_path(resource), notice: 'Partner theme attributes publishing job has been queued. This may take some time for partners with many deals.'
      rescue => e
        flash.now[:error] = e.message
        render 'publish_partner_attributes_to_all_deals'
      end
    end
  end

  controller do
    def action_to_permission(action_name)
      case action_name
      when 'publish_partner_attributes_to_all_deals'
        :update
      else
        super
      end
    end

    def apply_decorations(resource)
      super.tap do
        next unless resource

        next unless action_name == 'new'

        if resource.theme.blank?
          resource.build_theme(
            from_email: Partner::Theme::DEFAULT_FROM_EMAIL,
            reply_to_email: Partner::Theme::DEFAULT_REPLY_TO_EMAIL,
            course_nomenclature: Partner::Theme::DEFAULT_COURSE_NOMENCLATURE,
            certificate_nomenclature: Partner::Theme::DEFAULT_CERTIFICATE_NOMENCLATURE,
          )
        end
        resource.build_certificate_theme if resource.certificate_theme.blank?
        if resource.finance_relationships.empty?
          resource.finance_relationships.build(name: Finance::Relationship::DEFAULT_NAME, default: true)
          resource.finance_relationships.first.contracts.build(starts_on: Time.zone.today, fee_rate_param: '30')
        end
      end
    end

    def create
      attributes = permitted_params[:partner].to_h
      @partner = Partner::ReplaceCommand.call!(attributes:)
      redirect_to admin_partner_path(@partner), notice: 'Partner was successfully created.'
    rescue ActiveRecord::RecordInvalid => e
      flash[:error] = e
      @resource = e.record
      render :new
    end

    def update
      partner = Partner.find_by!(slug: params[:id])
      attributes = permitted_params[:partner].to_h
      @partner = Partner::ReplaceCommand.call!(attributes:, partner:)

      redirect_to admin_partner_path(@partner), notice: 'Partner was successfully updated.'
    rescue ActiveRecord::RecordInvalid => e
      flash[:error] = e
      @resource = e.record
      render :edit
    end
  end
end
