# frozen_string_literal: true

ActiveAdmin.register LearningDelivery::RiskAssessment do
  menu parent: 'Learning Delivery', label: 'Risk Assessments'
  actions :index, :show

  includes enrollment: :learner

  permit_params :enrollment_id, :risk_type, :risk_level, :risk_details

  scope :all, default: true
  scope :on_track
  scope :low_risk
  scope :high_risk

  # Add filters for the index page
  filter :learner_first_name_or_learner_last_name_or_learner_primary_email_address_cont, label: 'Learner',
    placeholder: 'First name, Last name, or Email'
  filter :enrollment_uid_cont, label: 'Enrollment UID'
  filter :risk_type, as: :select, collection: -> { LearningDelivery::RiskAssessment.risk_type_alt_name_to_ids }
  filter :risk_level, as: :select, collection: -> { LearningDelivery::RiskAssessment.risk_level_alt_name_to_ids }
  filter :assessed_at

  index do
    selectable_column
    id_column
    stack :enrollment, :learner
    column :risk_type, &:risk_type_html
    column :risk_level, &:risk_level_html
    column :assessed_at
    actions
  end

  form do |f|
    f.inputs do
      f.input :enrollment_id
      f.input :risk_type
      f.input :risk_level
      f.input :risk_details
    end
    f.actions
  end

  show do
    attributes_table do
      row :id
      row :enrollment
      row :learner
      row :risk_type, &:risk_type_html
      row :risk_level, &:risk_level_html
      row(:risk_details) { render_json resource.risk_details }
      row(:assessed_at) { |r| r.assessed_at&.to_fs(:datetime_with_zone) }
      row(:last_lms_activity_at) { |r| r.enrollment.last_lms_activity_at&.to_fs(:datetime_with_zone) }
      row(:created_at) { |r| r.created_at&.to_fs(:datetime_with_zone) }
      row(:updated_at) { |r| r.updated_at&.to_fs(:datetime_with_zone) }
    end
  end
end
