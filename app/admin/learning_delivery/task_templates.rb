# frozen_string_literal: true

ActiveAdmin.register LearningDelivery::TaskTemplate do
  menu parent: 'Learning Delivery', label: 'Task Templates'

  permit_params :title, :description, :recommendation, :reason, :task_type, :sub_type

  filter :uid_cont, label: 'UID'
  filter :title_cont, label: 'Title'
  filter :task_type_cont, label: 'Task Type'
  filter :sub_type_cont, label: 'Sub Type'
  filter :created_at, as: :date_time_picker_filter
  filter :updated_at, as: :date_time_picker_filter

  index do
    column :id
    column :uid
    column :title
    column :task_type
    column :sub_type
    column :created_at
    column :updated_at
  end

  show do
    attributes_table do
      row :id
      row :uid
      row :title
      row :task_type
      row :sub_type
      row :description do |template|
        simple_format template.description
      end
      row :recommendation do |template|
        simple_format template.recommendation
      end
      row :reason do |template|
        simple_format template.reason
      end
      row :created_at
      row :updated_at
    end
  end

  form(data: { turbo: false }) do |f|
    semantic_errors
    f.inputs "Task Template Details" do
      f.input :task_type, as: :select
      f.input :sub_type, hint: "Optional sub-type for further categorization"
      variables_list = [
        ["Announcement", LearningDelivery::Task::AnnouncementPresenter::VARIABLES],
        ["Resolve Duplicate Enrollment", LearningDelivery::Task::ResolveDuplicateEnrollmentPresenter::VARIABLES],
        ["Fix Merged Remote Contact", LearningDelivery::Task::FixMergedRemoteContactPresenter::VARIABLES],
        ["Reach Out", LearningDelivery::Task::ReachOutPresenter::VARIABLES],
        ["Remove Learner", LearningDelivery::Task::RemoveLearnerPresenter::VARIABLES],
      ].map do |name, vars|
        "<strong>#{name}</strong>:<br>#{vars.join(', ')}"
      end.join("<br><br>").html_safe

      note "Variables available for task types:" do
        para variables_list
      end

      [:title, :description, :reason, :recommendation].each do |field|
        f.input field, hint: "See variables list above", input_html: { rows: 10 }
      end
    end
    f.actions
  end
end
