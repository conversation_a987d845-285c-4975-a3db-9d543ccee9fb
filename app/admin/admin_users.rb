# frozen_string_literal: true

ActiveAdmin.register AdminUser do
  permit_params :first_name, :last_name, :email, :slack_member_id, :time_zone, :status

  menu parent: 'Admin'

  filter :email_cont, label: 'Email'
  filter :first_name_cont, label: 'First Name'
  filter :last_name_cont, label: 'Last Name'
  filter :status, as: :select, collection: AdminUser.status_alt_name_to_ids
  filter :time_zone, as: :select, collection: -> { ActiveSupport::TimeZone.all.map(&:name) }

  index do
    id_column
    column(:first_name)
    column(:last_name)
    column(:email) { |au| link_to(au.email, "mailto:#{au.email}") }
    column(:status, &:status_html)
    column(:time_zone)
  end

  show do
    attributes_table do
      row(:first_name)
      row(:last_name)
      row(:email) { |au| link_to(au.email, "mailto:#{au.email}") }
      row(:status, &:status_html)
      row('Slack Member ID', &:slack_member_id)
      row(:time_zone)
    end

    panel 'Roles' do
      table_for(admin_user.roles.order(:name)) do
        column(:name) { |r| link_to(r.name, admin_role_path(r)) }
      end
    end

    panel 'Permissions' do
      table_for(admin_user.permissions.order(:name).includes(:resource).includes(:roles)) do
        column(:name)
        column(:resource)
        column :via_roles do |permission|
          permission.roles & admin_user.roles
        end
      end
    end
  end

  form(data: { turbo: false }) do |_f|
    semantic_errors
    inputs "Details" do
      input :first_name
      input :last_name
      input :email, input_html: { disabled: object.persisted? }
      input :status
      input :slack_member_id, label: 'Slack Member ID'
      input :time_zone
    end
    actions
  end

  action_item :manage_authorizations, only: %i[show edit] do
    link_to 'Manage Authorizations', manage_authorizations_admin_admin_user_path(resource) if authorized?(:update, 'Admin')
  end

  action_item :view_as_user, only: %i[show edit] do
    link_to 'View As User', view_as_user_admin_admin_user_path(resource), method: :post if authorized?(:update, 'Admin') && !Rails.env.production?
  end

  action_item :impersonate_admin_user, only: :show do
    next if AdminUserImpersonation.authenticated_admin_user(session).present?

    allow_subdomain = lambda { |subdomain|
      AdminUserImpersonation.allow_subdomain?(authenticated_admin_user: current_admin_user, impersonated_admin_user: resource, subdomain:)
    }

    subdomain_settings = {
      'admin' => {
        title: 'Admin',
        allow?: allow_subdomain.call('admin'),
        redirect_to: admin_dashboard_url,
        redirect_name: 'the Admin dashboard',
      },
      'learning-delivery' => {
        title: 'Learning Delivery',
        allow?: allow_subdomain.call('learning-delivery'),
        redirect_to: resource.learning_delivery_employee.then { |e| e && UrlBuilder::LearningDelivery.learning_delivery_employee_url(e) },
        redirect_name: 'their Learning Delivery profile',
      },
    }

    if subdomain_settings.any? { |_, setting| setting[:allow?] }
      dropdown_menu "Impersonate User", button: { class: "button" } do
        subdomain_settings.each do |subdomain, setting|
          next unless setting[:allow?]

          item "Impersonate #{setting[:title]} user",
            AdminUserImpersonation.build_start_url(
              authenticated_admin_user: current_admin_user,
              impersonated_admin_user: resource,
              redirect_to: setting[:redirect_to],
              subdomains: subdomain,
            ),
            data: {
              confirm: "You are about to impersonate #{resource} and redirect to #{setting[:redirect_name]}. Continue?",
            }
        end
      end
    end
  end

  member_action :manage_authorizations, method: :get do
    @admin_user = AdminUser.find(params[:id])

    render 'manage_authorizations'
  end

  member_action :update_authorizations, method: :patch do
    @admin_user = AdminUser.find(params[:id])

    begin
      update_roles_and_permissions
      flash[:notice] = 'Admin User was successfully updated'

      redirect_to admin_admin_user_path @admin_user
    rescue StandardError => e
      flash[:alert] = "Admin User could not be updated. #{e.message}"

      render 'manage_authorizations'
    end
  end

  member_action :view_as_user, method: :post do
    authorize! :none if Rails.env.production?

    AdminUser::MatchAuthorizationsCommand.call!(current_admin_user:, admin_user: resource)

    flash[:notice] = "Your authorization has been updated to match the admin user: #{resource.full_name}"
    redirect_to admin_dashboard_path
  end

  controller do
    before_action :notice_on_end_impersonation, only: :show, if: -> { params[:from] == 'end_impersonation' }

    def create
      attributes = permitted_params[:admin_user].to_h

      @resource = AdminUser::ReplaceCommand.call!(attributes:)

      redirect_to admin_admin_user_path(@resource)
    rescue ActiveRecord::RecordInvalid => e
      flash[:error] = e
      @resource = e.record
      render :new
    end

    def update
      @resource = AdminUser.find(params[:id])
      attributes = permitted_params[:admin_user].to_h

      AdminUser::ReplaceCommand.call!(attributes:, admin_user: @resource)

      redirect_to admin_admin_user_path(@resource)
    rescue ActiveRecord::RecordInvalid => e
      flash[:error] = e
      @resource = e.record
      render :edit
    end

    def action_to_permission(action)
      case action
      when 'manage_authorizations', 'view_as_user'
        :update
      else
        super
      end
    end

    def action_methods
      if nested_under_belongs_to_resource?
        %w[index show]
      else
        super
      end
    end

    def authorized_roles
      Role::FetchAuthorizedRolesCommand.new(user: current_admin_user).call!
    end

    def authorized_resources
      Permission::FetchAuthorizedResourcesCommand.new(user: current_admin_user).call!
    end

    def all_resources
      Permission.resource_groups
    end

    helper_method :authorized_resources
    helper_method :authorized_roles
    helper_method :all_resources

    private

    def notice_on_end_impersonation
      redirect_to admin_admin_user_path(resource, request.query_parameters.except('from')), notice: "Impersonation of #{resource} has ended"
    end

    def admin_user_params
      params.require(:admin_user).permit(:email, :password, :password_confirmation, roles_attributes: [:id, :name, :_destroy], permissions_attributes: [:id, :resource_group, :level, :resource_id, :_destroy])
    end

    def update_roles_and_permissions
      if admin_user_params[:permissions_attributes]
        permissions = admin_user_params[:permissions_attributes].values.reject { |hash| hash['_destroy'] == '1' }
        permissions.each do |permission|
          validate_permission(permission)
        end
        Permission::UpdatePermissionsCommand.new(record: @admin_user, permissions:).call!
      end

      return unless admin_user_params[:roles_attributes]

      role_names = admin_user_params[:roles_attributes].values.reject { |hash| hash['_destroy'] == '1' }.pluck('name')
      role_names.each do |role_name|
        validate_role(role_name)
      end
      Role::UpdateRolesCommand.new(admin_user: @admin_user, role_names:).call!
    end

    def nested_under_belongs_to_resource?
      params[:role_id].present? || params[:permission_id].present?
    end

    def validate_role(role_name)
      return if authorized_roles.pluck(:name).include?(role_name) || @admin_user.roles.pluck(:name).include?(role_name)

      raise 'You do not have authorization for this resource'
    end

    def validate_permission(permission)
      if authorized_resources.include?(permission[:resource_group]) || @admin_user.permissions.pluck(:resource_group).include?(permission[:resource_group])
        return
      end

      raise 'You do not have authorization for this resource'
    end
  end
end
