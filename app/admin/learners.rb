# frozen_string_literal: true

ActiveAdmin.register Learner do
  extend RemoteResourcesAdminActions

  show_remote_ops_menu

  menu parent: 'Customers'

  after_save do |_resource|
    resource.emails.each do |email|
      Email::ValidateJob.perform_async(email.id) if email.saved_change_to_address?
    end
  end

  includes :emails, :ecom_orders, :syllabus_requests, :registrations, :live_session_reviews, enrollments: [:section, :cohort, :program]

  permit_params :first_name, :last_name, :phone, :time_zone, emails_attributes: %i[id address primary _destroy]

  actions :index, :show, :edit, :update

  filter :uid_cont, label: 'UID'
  filter :sis_key_cont, label: 'SIS Key'
  filter :emails_id, as: :search_select_filter, label: 'Email', fields: [:address], display_name: :address, method_model: Email
  filter :ecom_orders_id, as: :search_select_filter, label: 'Order', fields: [:uid], display_name: :humanized_uid, method_model: Ecom::Order
  filter :first_name_cont, label: 'First Name'
  filter :last_name_cont, label: 'Last Name'
  filter :phone_standardized_cont, label: 'Phone'
  filter :time_zone, as: :select, collection: -> { ActiveSupport::TimeZone.all.map(&:name) }
  filter :created_at, as: :date_time_picker_filter

  index do
    column(:id)
    column(:uid) { |l| copyable(l.humanized_uid) }
    column(:first_name)
    column(:last_name)
    column(:phone)
    column(:time_zone)
    list_column(:enrollments) { |l| l.enrollments.primary.map { |e| display_section(e.section) } }
  end

  show do
    attributes_table do
      row(:id) { |l| copyable(l.id) }
      row(:uid) { |l| copyable(l.humanized_uid) }
      row(:sis_key) { |l| copyable(l.sis_key) }
      row(:full_name) { |l| copyable(l.full_name) }
      row(:phone) { |l| copyable(l.phone&.local_number) }
      row(:time_zone)
      row(:created_at) { |e| e.created_at.to_fs(:datetime_with_zone) }
    end

    panel 'Emails' do
      table_for learner.emails do
        column(:address) { |e| copyable(link_to_email(e.address), e.address) }
        column(:primary)
      end
    end

    panel 'Enrollments' do
      table_for learner.enrollments.order(created_at: :desc) do
        column(:uid) { |e| copyable(link_to(e.humanized_uid, admin_enrollment_path(e)), e.humanized_uid) }
        column(:status, &:status_html)
        column(:partner_program)
        column(:cohort)
        column(:section)
        column(:registration)
        column(:extended_until)
        column(:primary)
        column(:created_at) { |e| e.created_at.to_fs(:datetime_with_zone) }
      end
    end

    if learner.syllabus_requests.present?
      panel 'Syllabus Requests' do
        table_for learner.syllabus_requests.order(created_at: :desc) do
          column(:id) { |e| link_to e.id, admin_syllabus_request_path(e) }
          column(:partner)
          column(:program)
          column(:created_at) { |e| e.created_at.to_fs(:datetime_with_zone) }
        end
      end
    end

    if learner.registrations.present?
      panel 'Registrations' do
        table_for learner.registrations.order(created_at: :desc) do
          column(:id) { |e| copyable(link_to(e.id, admin_registration_path(e)), e.id) }
          column(:email) { |e| copyable(link_to_email(e.email_address), e.email_address) }
          column(:partner)
          column(:program)
          column(:cohort)
          column(:section)
          column(:created_at) { |e| e.created_at.to_fs(:datetime_with_zone) }
        end
      end
    end

    if learner.live_session_reviews.present?
      panel('Live Session Reviews') do
        render partial: 'admin/live_sessions/reviews/table', locals: { reviews: learner.live_session_reviews, context: self }
      end
    end


    if learner.ecom_orders.present?
      panel 'Orders' do
        table_for learner.ecom_orders.order(created_at: :desc) do
          column(:uid) { |e| copyable(link_to(e.humanized_uid, admin_ecom_order_path(e)), e.humanized_uid) }
          column(:status) { |e| status_tag(e.status, class: e.status) }
          column(:created_at) { |e| e.created_at.to_fs(:datetime_with_zone) }
        end
      end
    end

    remote_resources_for(resource)
  end

  form(data: { turbo: false }) do |_f|
    semantic_errors
    panel 'Readonly' do
      attributes_table_for(resource) do
        row(:id)
      end
    end
    inputs "Details" do
      input :first_name
      input :last_name
      input :phone
      input :time_zone
    end

    inputs "Emails" do
      has_many :emails, allow_destroy: true, new_record: 'Add Email' do |email|
        email.input :address
        email.input :primary
      end
    end
    actions
  end

  controller do
    rescue_from PG::ForeignKeyViolation, with: :show_foreign_key_violation

    def show_foreign_key_violation(_error)
      flash[:error] = 'Cannot delete when associated records exist'

      redirect_to resource ? resource_path(resource) : admin_learners_path
    end

    def index
      index! do |format|
        format.json do
          json_collection = Learner.ransack(params[:q]).result
          render json: json_collection.as_json(methods: [:full_name])
        end
      end
    end
  end
end
