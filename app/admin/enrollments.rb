# frozen_string_literal: true

ActiveAdmin.register Enrollment do
  menu parent: 'Customers'

  includes :partner, :partner_program, :live_session_reviews, :highest_risk_assessment, lms_submissions: [assignment: :assignment_group],
    learner: :primary_email, section: [:cohort, :program]

  actions :index, :show

  scope :all, default: true
  scope :re_enrolled_at_no_cost, group: :re_enrolls
  scope :re_enrolled_at_discount, group: :re_enrolls

  filter :uid_cont, label: 'UID'
  filter :learner_first_name_cont, label: 'First Name'
  filter :learner_last_name_cont, label: 'Last Name'
  filter :learner_primary_email_address_cont, label: 'Email'
  filter :learner_phone_standardized_cont, label: 'Phone'
  filter :partner_program_id,
    as: :search_select_filter,
    label: 'Partner Program',
    url: proc { admin_partner_programs_path },
    fields: %w[name],
    display_name: 'name',
    order_by: 'id desc'
  filter :partner_program_partner_id, label: 'Partner', as: :select, collection: -> { Partner.order(:name).pluck(:name, :id) }
  filter :section_cohort_program_id, label: 'Program', as: :select, collection: -> { Program.order(:name).pluck(:name, :id) }
  filter :section_cohort_id,
    as: :search_select_filter,
    label: 'Cohort Name',
    url: proc { admin_cohorts_path },
    fields: %w[name],
    display_name: 'name',
    method_model: Section,
    order_by: :name
  filter :cohort, label: 'Cohort Key', as: :select, collection: -> { Cohort.order(:key).pluck(:key, :id) }
  filter :section_uid_cont, label: 'Section UID'
  filter :status, as: :select, collection: -> { Enrollment.status_alt_name_to_ids }
  filter :post_learner
  filter :exited
  filter :created_at, as: :date_time_picker_filter
  filter :clas_cohort_admission_key_eq, label: 'CLAS Cohort Admission ID'

  index do
    column(:uid) { |e| link_to(e.humanized_uid, admin_enrollment_path(e)) }
    stack 'Status' => :status_html,
      "Created At (#{Time.zone.tzinfo.current_period.abbreviation})" => proc { |e| e.created_at.to_fs(:datetime) }
    stack :learner, 'Email' => proc { |e| link_to_email(e.learner.primary_email_address) }
    stack :partner, 'Program > Cohort > Section' => proc { |e| display_section(e.section) }
    stack 'Risk Level' => :risk_level_html,
      'Risk Assessment' => proc { |e| e.risk_reason }
    column('Last LMS Activity') { |e| e.last_lms_activity_at ? "#{time_ago_in_words(e.last_lms_activity_at)} ago" : 'Never' }
  end

  show do
    render(partial: 'admin/enrollments/show', locals: { enrollment: resource })
  end

  action_item :create_manual, only: :index do
    link_to('Create Manual Registration', create_manual_admin_registrations_path) if authorized?(:create, 'Customers')
  end

  action_item :post_enrollment_ops, only: :show do
    transferable = Enrollment::TransferCommand.callable?(enrollment: resource)
    exitable = Enrollment::ExitCommand.callable?(enrollment: resource)
    activatable = Enrollment::ActivateCommand.activatable?(enrollment: resource)
    reactivatable = Enrollment::ReactivateCommand.reactivatable?(enrollment: resource)
    lms_access_revocable = Enrollment::RevokeCanvasAccessCommand.callable?(enrollment: resource)
    pausable = resource.active_status?
    course_risk_updatable = Enrollment::UpdateCourseRiskCommand.callable?(enrollment: resource) && Rails.env.in?(%w[development staging])
    pass_status_updatable = Enrollment::UpdatePassStatusCommand.callable?(enrollment: resource) && Rails.env.in?(%w[development staging])
    no_pass_status_updatable = Enrollment::UpdateNoPassStatusCommand.callable?(enrollment: resource) && Rails.env.in?(%w[development staging])
    certificate_issuable = Enrollment::IssueCertificatesCommand.callable?(scope: resource) && Rails.env.in?(%w[development staging])
    re_evaluatable = Enrollment::ReEvalPassStatusCommand.callable?(enrollment: resource)
    partner_updatable = Enrollment::UpdatePartnerCommand.callable?(enrollment: resource)
    status_changeable = Enrollment::StatusChangeForm.new(enrollment_id: resource.id).status_options.present?

    if authorized?(:update, 'Customers') &&
        [transferable, exitable, activatable, reactivatable, pausable, course_risk_updatable, pass_status_updatable, no_pass_status_updatable, certificate_issuable,
         re_evaluatable, partner_updatable, status_changeable, lms_access_revocable,].any?
      dropdown_menu "Post Enrollment Ops" do
        item 'Transfer', new_transfer_admin_enrollment_path(resource), title: 'Transfer to another section' if transferable
        item 'Exit', new_exit_admin_enrollment_path(resource), title: 'Unenroll / Drop / Withdraw a learner' if exitable
        item 'Extend', edit_extension_admin_enrollment_path(resource), title: 'Extend the enrollment' if resource.extendable?
        item 'Change Status', new_status_change_admin_enrollment_path(resource), title: 'Override enrollment status' if status_changeable

        if activatable
          item 'Activate', new_activate_admin_enrollment_path(resource),
            title: 'Add the learner to the Canvas course'
        end
        if reactivatable
          item 'Re-Activate', new_reactivate_admin_enrollment_path(resource),
            title: 'Re-Add the learner to the Canvas course'
        end
        if pausable
          item 'Deactivate', new_pause_admin_enrollment_path(resource),
            title: 'Remove the learner from the Canvas course and pause the enrollment'
        end
        if course_risk_updatable
          item '[QA] Update Course Risk', update_course_risk_admin_enrollment_path(resource), method: :post,
            title: 'Update the course risk for this enrollment'
        end
        if pass_status_updatable
          item '[QA] Evaluate Pass Status', update_pass_status_admin_enrollment_path(resource), method: :post,
            title: 'Evaluate assignments and update the pass status for this enrollment'
        end
        if no_pass_status_updatable
          item '[QA] Evaluate No Pass Status', update_no_pass_status_admin_enrollment_path(resource), method: :post,
            title: 'Evaluate assignments and update the no pass status for this enrollment'
        end
        if re_evaluatable
          item 'Re-Evaluate Pass Status', re_eval_pass_status_admin_enrollment_path(resource), method: :post,
            title: 'Change NoPass status to Pass if all required assignments are completed now'
        end
        if certificate_issuable
          item '[QA] Issue Certificate', issue_certificates_admin_enrollment_path(resource), method: :post,
            title: 'Issue Certificate for this enrollment'
        end
        if partner_updatable && authorized?(:update, 'Order Management')
          item 'Change Partner', edit_partner_admin_enrollment_path(resource),
            title: 'Change the partner for this enrollment'
        end
        if lms_access_revocable
          item 'Revoke Canvas Access', revoke_canvas_access_admin_enrollment_path(resource),
            method: :post,
            title: 'Revoke access to Canvas Course for this Enrollment'
        end
      end
    end
  end

  member_action :new_transfer, method: :get do
    @transfer_form = Enrollment::TransferForm.new(enrollment_id: resource.id)
  end

  member_action :transfer, method: :post do
    form_params = params.require(:enrollment_transfer_form).permit([:enrollment_id, :section_id, :reason])
    @transfer_form = Enrollment::TransferForm.new(form_params.merge(admin_user: current_admin_user))

    new_enrollment = @transfer_form.save!
    redirect_to admin_enrollment_path(new_enrollment), notice: 'Successfully Transferred'
  rescue ActiveRecord::RecordInvalid
    render 'new_transfer'
  end

  member_action :new_exit, method: :get do
    @exit_form = Enrollment::ExitForm.new(enrollment_id: resource.id)
  end

  member_action :exit, method: :post do
    form_params = params.require(:enrollment_exit_form).permit([:status, :exit_requested_on, :reason, :enrollment_id, :notes, :revoke_canvas_enrollment, :notify_learner])
    @exit_form = Enrollment::ExitForm.new(form_params.merge(admin_user: current_admin_user))

    @exit_form.save!
    redirect_to admin_enrollment_path(@exit_form.enrollment),
      notice: "Successfully #{resource.status.humanize}! Make sure to refund the learner if necessary."
  rescue ActiveRecord::RecordInvalid
    render 'new_exit'
  end

  member_action :update_course_risk, method: :post do
    Enrollment::UpdateCourseRiskCommand.call!(section: resource.section, enrollments: Enrollment.where(id: resource))
    redirect_to admin_enrollment_path(resource), notice: "Course Risk Updated to #{resource.reload.course_risk_name}"
  end

  member_action :update_pass_status, method: :post do
    Enrollment::UpdatePassStatusCommand.call!(enrollments: Enrollment.where(id: resource), section: resource.section)
    notice = resource.reload.status == 'pass' ? 'Pass Status Updated' : 'Pass Status Not Updated'
    redirect_to admin_enrollment_path(resource), notice:
  end

  member_action :revoke_canvas_access, method: :post do
    Enrollment::RevokeCanvasAccessCommand.call!(enrollment: resource)
    if resource.reload.remote_canvas_enrollment
      redirect_to admin_enrollment_path(resource), alert: 'Canvas access has not been revoked'
    else
      redirect_to admin_enrollment_path(resource), notice: 'Canvas access has been revoked'
    end
  end

  member_action :update_no_pass_status, method: :post do
    Enrollment::UpdateNoPassStatusCommand.call!(enrollments: Enrollment.where(id: resource))
    notice = resource.reload.status == 'no_pass' ? 'No Pass Status Updated' : 'No Pass Status Not Updated'
    redirect_to admin_enrollment_path(resource), notice:
  end

  member_action :issue_certificates, method: :post do
    Enrollment::IssueCertificatesCommand.call!(enrollments: Enrollment.where(id: resource))
    notice = resource.reload.status == 'certificate_issued' ? 'Certificate Issued' : 'Certificate Not Issued'
    redirect_to admin_enrollment_path(resource), notice:
  end

  member_action :new_activate, method: :get

  member_action :activate, method: :post do
    notify_learner = ActiveModel::Type::Boolean.new.cast(params[:reactivate][:notify_learner].to_i)
    notify_in = notify_learner ? 5.seconds : nil

    Enrollment::ActivateCommand.call!(enrollment: resource, admin_user: current_admin_user, notify_in:)
    redirect_to admin_enrollment_path(resource), notice: 'Activated Successfully'
  end

  member_action :new_reactivate, method: :get

  member_action :reactivate, method: :post do
    notify_learner = ActiveModel::Type::Boolean.new.cast(params[:reactivate][:notify_learner].to_i)

    Enrollment::ReactivateCommand.call!(enrollment: resource, admin_user: current_admin_user, notify_learner:)
    redirect_to admin_enrollment_path(resource), notice: 'Re-Activated Successfully'
  end

  member_action :new_pause, method: :get

  member_action :pause, method: :post do
    return unless resource.active_status?

    pause_params = params.require(:pause).permit([:reason, :notes])
    Enrollment::PauseCommand.call!(enrollment: resource, admin_user: current_admin_user, reason: pause_params[:reason], notes: pause_params[:notes])
    redirect_to admin_enrollment_path(resource), notice: 'Paused Successfully'
  end

  member_action :edit_extension, method: :get do # rubocop:disable Lint/EmptyBlock
  end

  member_action :update_extension, method: :post do
    form_params = params.require(:enrollment).permit([:extended_until, :extension_reason])
    form_params[:extended_by] = current_admin_user

    resource.update!(form_params)
    Deal::PublishToHubspotJob.perform_async(resource.deal_id)

    redirect_to admin_enrollment_path(resource), notice: 'Extension Updated. Hubspot Deal will be updated shortly.'
  end

  member_action :re_eval_pass_status, method: :post do
    Enrollment::ReEvalPassStatusCommand.call!(enrollment: resource, admin_user: current_admin_user)

    if resource.reload.status == 'pass'
      redirect_to admin_enrollment_path(resource), notice: 'Re-evaluation Successful. Learner Passed'
    else
      redirect_to admin_enrollment_path(resource), alert: 'Learner did NOT Pass'
    end
  end

  member_action :edit_partner, method: :get

  member_action :update_partner, method: :post do
    form_params = params.require(:update_partner).permit(:partner_id)
    form_params[:admin_user] = current_admin_user
    partner = Partner.find(form_params[:partner_id])

    Enrollment::UpdatePartnerCommand.call!(enrollment: resource, partner:)
    redirect_to admin_enrollment_path(resource), notice: 'Partner Changed Successfully'
  end

  member_action :new_status_change, method: :get do
    @status_change_form = Enrollment::StatusChangeForm.new(enrollment_id: resource.id)
  end

  member_action :status_change, method: :post do
    form_params = params.require(:enrollment_status_change_form).permit([:enrollment_id, :status, :notes, :reason])
    @status_change_form = Enrollment::StatusChangeForm.new(form_params.merge(admin_user: current_admin_user))

    @status_change_form.save!
    redirect_to admin_enrollment_path(@status_change_form.enrollment), notice: 'Status updated successfully'
  rescue ActiveRecord::RecordInvalid
    render 'new_status_change'
  end

  member_action :recalc_risk, method: :post do
    LearningDelivery::RiskAssessment::AssessCommand.call!(enrollment: resource, publish: true)
    redirect_to admin_enrollment_path(resource), notice: 'Risk Recalculated Successfully'
  end

  controller do
    def action_to_permission(action)
      case action
      when 'create_manual'
        :create
      when 'new_transfer', 'transfer', 'new_exit', 'exit', 'activate', 'reactivate', 'pause', 'update_course_risk', 'edit_extension', 'update_extension',
        'issue_certificates', 'update_pass_status', 'update_no_pass_status', 're_eval_pass_status', 'new_status_change', 'status_change',
        'revoke_canvas_access', 'recalc_risk'
        :update
      else
        super
      end
    end

    def authorized_resource_group
      case action_name
      when 'edit_partner', 'update_partner'
        'Order Management'
      end
    end

    def index
      index! do |format|
        format.json do
          uid_cont = params.dig(:q, :groupings, '0', :uid_cont)

          json_collection = if uid_cont.present?
            Enrollment.uid_cont(uid_cont)
          else
            Enrollment.ransack(params[:q]).result
          end
          render json: json_collection.as_json(methods: [:humanized_uid])
        end
      end
    end
  end
end
