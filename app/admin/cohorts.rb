# frozen_string_literal: true

ActiveAdmin.register Cohort do
  extend RemoteResourcesAdminActions

  show_remote_ops_menu

  includes :program, sections: { enrollments: { learner: :primary_email }, live_sessions: :reviews }

  menu parent: 'Base', priority: 3

  permit_params(
    :add_period_starts_on,
    :add_period_ends_on,
    :drop_period_ends_on,
    :ends_on,
    :extension_period_ends_on,
    :key,
    :lms_closes_on,
    :lms_opens_on,
    :starts_on,
    :transfer_period_ends_on,
    :program_id,
    sections_attributes: %i[live_day_of_the_week live_start_time],
  )

  filter :program, as: :select, collection: -> { Program.order(:name).pluck(:name, :id) }
  filter :key_cont, label: 'Key'
  filter :name_cont, label: 'Name'
  filter :status, as: :select, collection: -> { Cohort.status_alt_name_to_ids }
  filter :add_period_starts_on, as: :date_time_picker_filter
  filter :lms_opens_on, as: :date_time_picker_filter
  filter :starts_on, as: :date_time_picker_filter
  filter :add_period_ends_on, as: :date_time_picker_filter
  filter :drop_period_ends_on, as: :date_time_picker_filter
  filter :transfer_period_ends_on, as: :date_time_picker_filter
  filter :ends_on, as: :date_time_picker_filter
  filter :extension_period_ends_on, as: :date_time_picker_filter
  filter :lms_closes_on, as: :date_time_picker_filter

  index do
    column(:id)
    column(:status) { |c| status_tag(c.status_name) }
    column(:program)
    column(:add_period_active?)
    column(:starts_on)
    column(:week_number)
    column(:key) { |c| copyable(c.key) }
    column(:sections) { |c| c.sections.size }
    column(:enrollments) { |c| c.enrollments.primary.retained.size }
    column('NPS') { |c| render_nps(NpsScore::AggregateScoreCommand.call!(c.live_session_reviews.pluck(:score))) }
  end

  show do
    attributes_table do
      row(:id)
      row(:program)
      row(:program_duration) { |c| "#{c.program.duration_in_weeks} weeks" }
      row(:name) { |c| copyable(c.name) }
      row(:key) { |c| copyable(c.key) }
      row(:sis_key) do |c|
        rc = c.remote_canvas_course || c.build_remote_canvas_course
        copyable(rc.alternate_key)
      end
      row(:status) { |c| status_tag(c.status_name) }
      row(:add_period_active?)
      row(:add_period_starts_on) { |c| date_icon(c.add_period_starts_on) + date_with_diff(c.add_period_starts_on, diff_from: c.starts_on, interval: 'month') }
      row(:lms_opens_on) { |c| date_icon(c.lms_opens_on) + date_with_diff(c.lms_opens_on, diff_from: c.starts_on) }
      row(:starts_on) { |c| date_icon(c.starts_on) + c.starts_on.to_fs(:long) }
      row(:week_number)
      row(:add_period_ends_on) { |c| date_icon(c.add_period_ends_on) + date_with_diff(c.add_period_ends_on, diff_from: c.starts_on) }
      row(:drop_period_ends_on) { |c| date_icon(c.drop_period_ends_on) + date_with_diff(c.drop_period_ends_on, diff_from: c.starts_on) }
      row(:transfer_period_ends_on) { |c| date_icon(c.transfer_period_ends_on) + date_with_diff(c.transfer_period_ends_on, diff_from: c.starts_on) }
      row(:ends_on) { |c| date_icon(c.ends_on) + date_with_diff(c.ends_on, diff_from: c.starts_on) }
      row(:extension_period_ends_on) { |c| date_icon(c.extension_period_ends_on) + date_with_diff(c.extension_period_ends_on, diff_from: c.starts_on) }
      row(:lms_closes_on) { |c| date_icon(c.lms_closes_on) + date_with_diff(c.lms_closes_on, diff_from: c.starts_on, interval: 'year') }
    end

    panel 'Sections' do
      table_for(cohort.sections.includes(:partner, :remote_canvas_section).sort_by(&:name)) do
        span i "Note: Retained Enrollments = Enrollments in #{Enrollment::RETAINED_STATUSES.to_sentence} statuses"
        column(:id)
        column(:name) { |s| link_to(s.name, admin_section_path(s)) }
        column(:live_day_of_the_week, &:live_day_of_the_week_name)
        column(:live_start_time) { |s| s.live_start_time&.to_fs(:time_with_zone) }
        column(:retained_enrollments) { |s| s.enrollments.primary.retained.size }
        column(:instructor) { |s| render_link_to_employee(s.instructor) }
        column(:advocate) { |s| render_link_to_employee(s.advocate) }
        column(:grader) { |s| render_link_to_employee(s.grader) }
        column(:canvas_key) do |s|
          if s.remote_canvas_section
            remote_key(s.remote_canvas_section)
          else
            status_tag('Not connected', class: 'error')
          end
        end
        column(:partner) { |s| s.partner&.name } if cohort.sections.any? { |s| s.partner.present? }
        column(:nps) { |s| render_nps(NpsScore::AggregateScoreCommand.call!(s.live_session_reviews.pluck(:score))) }
      end
    end

    panel 'NPS Summary' do
      render partial: 'admin/live_sessions/nps_score_summary', locals: {
        nps_scores: resource.live_session_reviews.map(&:nps_score),
      }
    end

    if resource.lms_modules.present? && authorized?(:read, 'LMS')
      panel 'Modules' do
        table_for(resource.lms_modules) do
          column(:title) { |m| link_to(m.title, admin_lms_module_path(m)) }
          column(:week_number) { |m| m.week.number }
        end
      end

      render(partial: 'admin/lms/assignments/summary_table', locals: { assignments: resource.assignments })
    end


    cohort.sections.each do |section|
      panel(link_to("#{section.name} (#{section.live_name}): #{pluralize(section.enrollments.primary.retained.size, 'Enrollment')}", admin_enrollments_path(q: { section_uid_cont: section.uid }))) do
        param_name = "section_#{id}_enrollments_page"
        paginated_collection(section.enrollments.primary.page(params[param_name]).per(20), param_name:, download_links: false) do
          table_for(collection) do
            column(:uid) { |e| copyable(link_to(e.humanized_uid, admin_enrollment_path(e)), e.humanized_uid) }
            column(:status, &:status_html)
            column(:full_name) { |e| copyable(e.learner.full_name) }
            column(:email) { |e| copyable(link_to_email(e.learner.primary_email_address), e.learner.primary_email_address) }
            column(:phone) { |e| copyable(e.learner.phone&.local_number) }
          end
        end
      end
    end

    remote_resources_for(cohort)
  end

  form(data: { turbo: false }) do |_f|
    semantic_errors
    panel 'Readonly' do
      attributes_table_for(resource) do
        row(:id)
        row(:name)
        row(:status, &:status_name)
      end
    end
    inputs "Details" do
      input :program, as: :select, collection: Program.order(:name).pluck(:name, :id)

      if resource.persisted?
        input :key
        input :add_period_starts_on
        input :lms_opens_on
        input :starts_on
        input :add_period_ends_on
        input :drop_period_ends_on
        input :transfer_period_ends_on
        input :ends_on
        input :extension_period_ends_on
        input :lms_closes_on
      else
        input :starts_on

        has_many :sections, heading: false, new_record: false do |s|
          s.input :live_day_of_the_week
          s.input :live_start_time, as: :time_picker, hint: "Time in your time zone: #{Time.zone.name}"
        end
      end
    end

    if resource.persisted?
      panel "Grader Select" do
        para "Assign a grader to all sections in this cohort."
        div class: 'input' do
          label_tag "Grader", nil, class: 'label'
          select_tag "grader_id",
            options_for_select(
              LearningDelivery::Employee.joins(:roles)
                .where(learning_delivery_roles: { abbreviation: 'G' })
                .map { |e| [e.full_name, e.id] },
              resource.grader&.id,
            ),
            include_blank: 'Make no grader changes',
            class: 'select'
        end
      end
    end
    actions
  end

  action_item :new_section, only: :show do
    link_to 'Create New Section', new_admin_section_path(cohort_id: resource.id)
  end

  action_item :manual_open, only: :show, if: proc { authorized?(:update, 'Base') && Cohort::OpenCommand.openable?(cohort: resource) } do
    link_to 'Manual Open', new_manual_open_admin_cohort_path(resource), method: :get
  end

  member_action :new_manual_open, method: :get

  member_action :manual_open, method: :post do
    manual_open_params = params.require(:manual_open).permit(:'notify_in(4i)', :'notify_in(5i)')

    hours = manual_open_params['notify_in(4i)'].presence.to_i
    minutes = manual_open_params['notify_in(5i)'].presence.to_i
    notify_in = (hours.hours + minutes.minutes) || 0

    Cohort::OpenCommand.call!(cohort: resource, notify_in:)
    redirect_to admin_cohort_path(resource), notice: 'Cohort Opened Successfully'
  end

  controller do
    def action_to_permission(action)
      case action
      when 'manual_open'
        :update
      else
        super
      end
    end

    def index
      index! do |format|
        format.json do
          json_collection = Cohort.ransack(params[:q]).result
          render json: json_collection.as_json(methods: [:starting_month])
        end
      end
    end

    def apply_decorations(resource)
      super.tap do
        next unless resource

        resource.sections.build if resource.sections.blank?
      end
    end

    def create
      program = Program.find_by(id: permitted_params.dig(:cohort, :program_id))
      Cohort.create!(program:) if program.nil? # surface missing program to form

      starts_on = permitted_params.dig(:cohort, :starts_on)
      section_args = permitted_params.dig(:cohort, :sections_attributes, '0').slice(:live_day_of_the_week, :live_start_time).to_h.symbolize_keys
      @cohort = Cohort::CreateCommand.call!(program:, starts_on:, **section_args)

      redirect_to admin_cohort_path(@cohort), notice: 'Cohort created'
    rescue ActiveRecord::RecordInvalid => e
      @cohort = e.record
      render :new, error: 'Cohort creation failed'
    end

    def update
      super do |_success, _failure|
        Cohort::UpdateStatusesCommand.call!(scope: Cohort.where(id: resource.id)) if resource.valid?
        Cohort::UpdateGraderCommand.call!(cohort: resource, grader_id: params[:grader_id]) if params[:grader_id].present?
      rescue Enrollment::ActivateCommand::SectionNotSetupError => e
        flash[:error] = "Could not activate all enrollments: #{e.message}"
      end
    end
  end
end
