# frozen_string_literal: true

module Site
  ActiveAdmin.register PartnerDataset do
    json_editor

    menu parent: 'Site', label: 'Partner Datasets'

    permit_params :partner_id, :home_url, :back_to_name, :faqs, :brand_logos_allowed, :show_our_course_attracts_block, :logo_css, :cobranding_css, :squeeze_page_logo_css, :squeeze_page_cobranding_css, :custom_disclaimer, :custom_eva_banner_text,
      :custom_footer_text, :overrides, :certificate_name, :ab_testing_html

    index do
      column(:id)
      column(:partner)
      column(:home_url) { |d| copyable(link_to(d.home_url, d.home_url, target: '_BLANK'), d.home_url) if d.home_url.present? }
      column(:brand_logos_allowed)
      column(:show_our_course_attracts_block)
      column(:faqs) { |d| d.faqs.present? }
    end

    show do
      attributes_table do
        row(:id)
        row(:partner)
        row(:home_url) { |d| copyable(link_to(d.home_url, d.home_url, target: '_BLANK'), d.home_url) if d.home_url.present? }
        row(:back_to_name) { |t| copyable(t.back_to_name) }
        row(:brand_logos_allowed)
        row(:show_our_course_attracts_block)
        row(:logo_css) { |t| copyable(t.logo_css) }
        row(:cobranding_css) { |t| copyable(t.cobranding_css) }
        row(:squeeze_page_logo_css) { |t| copyable(t.squeeze_page_logo_css) }
        row(:squeeze_page_cobranding_css) { |t| copyable(t.squeeze_page_cobranding_css) }
        row(:certificate_name) { |p| copyable(p.certificate_name) }
        row(:custom_eva_banner_text)
        row(:custom_disclaimer) { |p| p.custom_disclaimer&.html_safe }
        row(:custom_footer_text) { |p| p.custom_footer_text&.html_safe }
        row(:faqs) { |e| copyable(render_json(e.faqs), JSON.pretty_generate(e.faqs), :block) }
        row(:ab_testing_html) { |e| copyable(e.ab_testing_html) }
        row(:overrides) { |e| copyable(render_json(e.overrides), JSON.pretty_generate(e.overrides), :block) }
      end
    end

    form(data: { turbo: false }) do |_f|
      semantic_errors
      panel 'Readonly' do
        attributes_table_for(resource) do
          row(:id)
          if resource.persisted?
            row(:partner)
            row(:created_at)
            row(:updated_at)
          end
        end
      end
      inputs "Details" do
        if !resource.persisted?
          input(
            :partner,
            as: :select,
            collection: Partner.includes(:site_partner_dataset).where(site_partner_dataset: { id: nil }).order(:name).pluck(:name, :id),
          )
        end
        input(
          :brand_logos_allowed,
          as: :boolean,
          label: label_with_guide(label: 'Brand logos allowed?', guide_path: 'site/partner_datasets/brand_logos_allowed.png'),
          hint: 'Uncheck if the partner does not allow showing other brand logos on landing/syllabus/squeeze pages.',
        )
        input(
          :show_our_course_attracts_block,
          as: :boolean,
          label: label_with_guide(label: 'Uncheck to hide `Our Course attracts` block on landing/syllabus/squeeze pages when brand_logos_allowed is true',
            guide_path: 'site/partner_datasets/show_our_course_attracts_block.png',
          ),
          hint: 'Controls whether the `Our Course attracts` block is displayed on these pages.',
        )
        input(
          :home_url,
          hint: 'If left blank, no back button will be displayed on the header banner',
          label: label_with_guide(label: 'Home URL', guide_path: 'site/partner_datasets/home_url.png'),
        )
        input(
          :back_to_name,
          hint: 'Override name in header banner that says "Back to ___". Default is {PARTNER_NAME}',
          label: label_with_guide(label: 'Back to name', guide_path: 'site/partner_datasets/back_to_name.png'),
        )
        input(:logo_css,
          hint: 'Custom css used for the partner logo(e.g., margin-left: 10px; margin-top: 20px;).',
          label: label_with_guide(label: 'Logo CSS', guide_path: 'site/partner_datasets/partner_logo.png'),
        )
        input(
          :cobranding_css,
          hint: 'Custom css used for the cobranding_text (e.g., margin-left: 10px; color: red;).',
          label: label_with_guide(label: 'Cobranding CSS', guide_path: 'site/partner_datasets/cobranding_text.png'),
        )
        input(:squeeze_page_logo_css,
          hint: 'Custom css used for the partner logo(e.g., margin-left: 10px; margin-top: 20px;).',
          label: label_with_guide(label: 'Squeeze Page Logo CSS', guide_path: 'site/partner_datasets/squeeze_page_logo.png'),
        )
        input(
          :squeeze_page_cobranding_css,
          hint: 'Custom css used for the cobranding_text(e.g., margin-left: 10px; color: red;).',
          label: label_with_guide(label: 'Squeeze Page Cobranding CSS', guide_path: 'site/partner_datasets/squeeze_page_cobranding_text.png'),
        )
        input(
          :certificate_name,
          hint: 'Override name mentioned by earned certificate. Default is {PARTNER_FORMAL_NAME}',
          label: label_with_guide(label: 'Certificate name', guide_path: 'site/partner_datasets/certificate_name.png'),
        )
        input(
          :custom_eva_banner_text,
          hint: "Override default EVA banner text: " \
                "<strong>#{Components::EvaBannerPresenter::DEFAULT_BANNER_TEXT}</strong> 10% off. Enroll and save »".html_safe,
          label: label_with_guide(label: 'Custom EVA banner text', guide_path: 'site/partner_datasets/custom_eva_banner_text.png'),
        )
        input(:custom_disclaimer,
          hint: "You can include HTML for any tags like: #{Components::CustomDisclaimerPresenter.allowed_html}",
          label: label_with_guide(label: 'Custom disclaimer', guide_path: 'site/partner_datasets/custom_disclaimer.png'),
        )
        input(
          :custom_footer_text,
          hint: "HTML displayed above standard footer. #{Components::FooterPresenter.allowed_html}",
          label: label_with_guide(label: 'Custom footer text', guide_path: 'site/partner_datasets/custom_footer_text.png'),
        )
        input(
          :faqs,
          as: :jsonb,
          hint: %({"Variables available: #{PartnerProgram::InterpolateCommand::VARIABLES.join(', ')}).html_safe,
          label: label_with_guide(label: "FAQS #{json_hint}".html_safe, guide_path: 'site/partner_datasets/faqs.png'),
        )
        input(
          :ab_testing_html,
          hint: "For partners with a custom domain, they need their own version of html code to install the A/B testing tool (VWO)",
          label: 'AB testing HTML',
        )
        input(
          :overrides,
          as: :jsonb,
          hint: %({"Variables available: #{PartnerProgram::InterpolateCommand::VARIABLES.join(', ')}).html_safe,
          label: "Overrides #{json_hint(custom_hint: PartnerDataset::OVERRIDES_EXAMPLE)}".html_safe,
        )
      end
      actions
    end
  end
end
