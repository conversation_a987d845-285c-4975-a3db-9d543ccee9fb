# frozen_string_literal: true

ActiveAdmin.register PartnerProgram do
  extend RemoteResourcesAdminActions

  show_remote_ops_menu

  includes :program, :partner

  menu parent: 'Base', priority: 2

  permit_params :partner_id, :program_id, :custom_program_name, :custom_program_short_name, :default, :certificate_badge, :status,
    certificate_theme_attributes: [
      :id, :administrator_name, :administrator_title, :certificate_type, :partner_name, :logo, :_destroy,
    ]

  filter :uid_cont, label: 'UID'
  filter :partner, collection: -> { Partner.order(:name).pluck(:name, :id) }
  filter :program, collection: -> { Program.order(:name) }
  filter :name_cont, label: 'Name'
  filter :custom_program_name_cont, label: 'Custom Program Name'
  filter :custom_program_short_name, label: 'Custom Program Short Name'
  filter :created_at, as: :date_time_picker_filter
  filter :status, as: :select, collection: -> { PartnerProgram.status_alt_name_to_ids }

  index do
    column(:id)
    column(:uid) { |pp| copyable(pp.humanized_uid) }
    column(:partner)
    column(:program)
    column(:default)
    column(:status) { |pp| status_tag(pp.status_name, class: pp.status) }
    column(:custom_program_name)
  end

  show do
    attributes_table do
      row(:id) { |pp| copyable(pp.id) }
      row(:uid) { |pp| copyable(pp.humanized_uid) }
      row(:partner)
      row(:program)
      row(:default)
      row(:status) { |pp| status_tag(pp.status_name, class: pp.status) }
      row(:custom_program_name) do |pp|
        render_with_fallback(
          pp.custom_program_name,
          fallback: nil,
          hint: "None Set. Using Program Name: <strong>#{pp.program.name}</strong>",
        )
      end
      row(:custom_program_short_name) do |pp|
        render_with_fallback(
          pp.custom_program_short_name,
          fallback: nil,
          hint: "None Set. Using Program Short Name: <strong>#{pp.program.short_name}</strong>",
        )
      end
      row(:certificate_badge) { |pp| render_image(model: pp, image_method: :certificate_badge) }
    end


    if resource.certificate_theme.present?
      panel "Certificate Theme" do
        attributes_table_for(resource) do
          row(:administrator_name) do |pp|
            render_with_fallback(
              pp.certificate_theme&.administrator_name,
              fallback: pp.partner.certificate_theme&.administrator_name,
              hint: 'from partner',
            )
          end
          row(:administrator_title) do |pp|
            render_with_fallback(
              pp.certificate_theme&.administrator_title,
              fallback: pp.partner.certificate_theme&.administrator_title,
              hint: 'from partner',
            )
          end
          row(:certificate_type) do |pp|
            render_with_fallback(
              pp.certificate_theme&.certificate_type,
              fallback: pp.partner.certificate_theme&.certificate_type,
              hint: 'from partner',
            )
          end
          row(:partner_name) do |pp|
            render_with_fallback(
              pp.certificate_theme&.partner_name,
              fallback: pp.partner.certificate_theme&.partner_name,
              hint: 'from partner',
            )
          end
          row(:logo) do |pp|
            if pp.certificate_theme&.logo&.attached? || pp.partner.certificate_theme&.logo&.attached?
              render_with_fallback(
                render_image(model: pp.certificate_theme, image_method: :logo, width: '300rem'),
                fallback: render_image(model: pp.partner.certificate_theme, image_method: :logo, width: '300rem'),
                hint: 'from partner',
              )
            end
          end
        end
      end
    end

    panel 'Site Pages' do
      div class: 'site-pages-actions' do
        a 'Copy Partner Program Links', href: '#', class: 'button copy-all-links partner-program-highlight', title: 'Copies URLs from highlighted rows'
      end

      table_for(resource.ordered_site_pages, row_class: ->(sp) { PartnerProgram::ORDERED_PARTNER_PROGRAM_PAGES.include?(sp.template) ? 'partner-program-highlight' : '' }) do
        column(:id) { |sp| link_to sp.id, admin_site_page_path(sp) }
        column(:template)
        column(:url) do |sp|
          copyable(link_to(sp.url, sp.url, target: '_blank', rel: 'noopener'), sp.url)
        end
        column(:short_url) do |sp|
          copyable(link_to(sp.short_url, sp.short_url, target: '_blank', rel: 'noopener'), sp.short_url)
        end
      end
    end

    remote_resources_for(resource)
  end

  form(data: { turbo: false }) do |_f|
    semantic_errors
    if object.persisted?
      panel 'Readonly' do
        attributes_table_for(resource) do
          row(:id)
          row(:uid, &:humanized_uid)
          row(:partner)
          row(:program)
        end
      end
    end

    inputs "Details" do
      if !object.persisted?
        input(:partner, collection: Partner.active.order(:name))
        input(:program, collection: Program.active.order(:name))
      end
      input(:default, as: :radio, inline_label: false)
      input(:custom_program_name, hint: object.program ? "overrides program name: <strong>#{object.program&.name}</strong>".html_safe : nil)
      input(
        :custom_program_short_name,
        hint: object.program ? "overrides program short name: <strong>#{object.program&.short_name}</strong>".html_safe : nil,
      )
      input(:certificate_badge, as: :file, hint: image_hint(model: object, image_method: :certificate_badge))
      input(:status)
    end

    inputs "Certificate Theme (fields override partner certificate theme)" do
      has_many :certificate_theme, heading: false do |ct|
        ct.input(:administrator_name)
        ct.input(:administrator_title)
        ct.input(:certificate_type)
        ct.input(:partner_name)
        ct.input(:logo, as: :file, hint: image_hint(model: object.certificate_theme, image_method: :logo))
      end
    end
    actions
  end

  controller do
    after_create do
      Site::Page::RebuildCommand.call!(partner_program: resource) if resource.valid?
      auto_publish :remote_hubspot_product
    end

    after_update do
      auto_publish :remote_hubspot_product
    end

    def index
      index! do |format|
        format.json do
          name_cont = params.dig(:q, :groupings, '0', :name_cont)
          json_collection = name_cont.present? ? PartnerProgram.name_cont(name_cont) : PartnerProgram.ransack(params[:q]).result
          render json: json_collection.as_json(methods: [:name, :program_name])
        end
      end
    end
  end
end
