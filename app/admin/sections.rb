# frozen_string_literal: true

ActiveAdmin.register Section do
  menu parent: 'Base', priority: 3

  includes :cohort, :program, :enrollments, :ecom_variant, :live_sessions, :partner, :live_session_reviews

  permit_params :chat_join_url, :chat_workspace_key, :conferencing_url, :live_day_of_the_week, :live_start_time, :live_end_time, :suffix, :cohort_id,
    :partner_id, :instructor_id, :advocate_id, :grader_id,
    weeks_attributes: [
      :id,
      live_sessions_attributes: %i[
        id
        section_week_id
        starts_at
        ends_at
        leader_id
        moderator_id
        _destroy
      ],
    ],
    ecom_variant_attributes: %i[
      id
      currency_code
      list_cents_param
      standard_discount_cents_param
      upfront_discount_cents_param
    ]

  filter :uid_cont, label: 'UID'
  filter :cohort_program_id, label: 'Program', as: :select, collection: -> { Program.order(:name).pluck(:name, :id) }
  filter :cohort_id,
    as: :search_select_filter,
    label: 'Cohort',
    url: proc { admin_cohorts_path },
    fields: %w[name key],
    display_name: 'name',
    order_by: :name
  filter :suffix_cont, label: 'Suffix', hint: '"Section 2" the suffix is "2"'
  filter :live_day_of_the_week, as: :select, collection: -> { Section.live_day_of_the_week_alt_name_to_ids }
  filter :live_start_time
  filter :partner
  filter :instructor_id,
    as: :search_select_filter,
    url: proc { admin_learning_delivery_employees_path(q: { employee_roles_role_id_eq: LearningDelivery::Role.instructor.id }) },
    fields: %w[first_name last_name],
    display_name: :full_name,
    minimum_input_length: 0
  filter :advocate_id,
    as: :search_select_filter,
    url: proc { admin_learning_delivery_employees_path(q: { employee_roles_role_id_eq: LearningDelivery::Role.advocate.id }) },
    fields: %w[first_name last_name],
    display_name: :full_name,
    minimum_input_length: 0
  filter :grader_id,
    as: :search_select_filter,
    url: proc { admin_learning_delivery_employees_path(q: { employee_roles_role_id_eq: LearningDelivery::Role.grader.id }) },
    fields: %w[first_name last_name],
    display_name: :full_name,
    minimum_input_length: 0


  index do
    column(:id)
    column(:uid) { |s| copyable(s.humanized_uid) }
    column(:program)
    column(:cohort)
    column(:name)
    column(:enrollments) { |s| s.enrollments.primary.retained.size }
    column(:live_day_of_the_week, &:live_day_of_the_week_name)
    column(:live_start_time) { |section| section.live_start_time&.to_fs(:time_with_zone) }
    column('NPS') { |s| render_nps(NpsScore::AggregateScoreCommand.call!(s.live_session_reviews.map(&:score))) }
    column(:instructor) { |s| render_link_to_employee(s.instructor) }
    column(:advocate) { |s| render_link_to_employee(s.advocate) }
    column(:grader) { |s| render_link_to_employee(s.grader) }
  end

  show do
    columns do
      column do
        attributes_table do
          row(:id) { |s| copyable(s.id) }
          row(:uid) { |s| copyable(s.humanized_uid) }
          row(:program)
          row(:cohort)
          row(:name)
          if resource.partner.present?
            row(:partner) do |s|
              link_to(admin_partner_path(s.partner)) do
                render_image(model: s.partner.theme, image_method: :logo, alt: s.partner.name)
              end
            end
          end
        end

        panel 'Variant' do
          attributes_table_for(resource.ecom_variant) do
            row(:record, &:itself)
            row(:list_price, &:list_currency)
            row(:standard_discount_amount) { |variant| render_standard_discount(retail_price: variant.retail_price) }
            row(:upfront_discount_amount) { |variant| render_upfront_discount(retail_price: variant.retail_price) }
            partner_pricing = Ecom::Variant.where(section: resource).where.not(partner: nil).count
            if partner_pricing > 0
              row(:alternate_pricing) do
                link_to(
                  "#{pluralize(partner_pricing, 'alternate price')} - view all pricing",
                  admin_ecom_variants_path(q: { section_id_eq: resource.id }),
                )
              end
            end
          end
        end

        if resource.lms_modules.present? && authorized?(:read, 'LMS')
          panel 'Modules' do
            table_for(resource.lms_modules) do
              column(:title) { |m| link_to(m.title, admin_lms_module_path(m)) }
              column(:week_number) { |m| m.week.number }
            end
          end

          render(partial: 'admin/lms/assignments/summary_table', locals: { assignments: resource.assignments })
        end

        panel(
          "#{link_to(pluralize(resource.enrollments.primary.retained.size, 'Enrollment'), admin_enrollments_path(q: { section_uid_cont: resource.uid }))} (#{link_to('↗ Learning Delivery', UrlBuilder::LearningDelivery.learning_delivery_learners_url(section_uid: resource.humanized_uid), target: '_blank', rel: 'noopener')})".html_safe,
        ) do
          table_for(resource.enrollments.primary) do
            column(:enrollment) { |e| copyable(link_to(e.learner.full_name, admin_enrollment_path(e)), e.learner.full_name) }
            column(:status, &:status_html)
            column(:risk_level, &:risk_level_html)
          end
        end
      end

      column do
        attributes_table(title: 'Community Details') do
          row(:live_day_of_the_week, &:live_day_of_the_week_name)
          row(:live_start_time) { |section| section.live_start_time&.to_fs(:time_with_zone) }
          row(:live_end_time) { |section| section.live_end_time&.to_fs(:time_with_zone) }
          row(:chat_join_url) do |section|
            if section.chat_join_url.present?
              copyable(
                link_to(section.chat_join_url, section.chat_join_url, target: '_blank', rel: 'noopener'),
                section.chat_join_url,
              )
            end
          end
          row(:chat_workspace_key) { |s| copyable(s.chat_workspace_key) }
          row(:conferencing_url) do |section|
            if section.conferencing_url.present?
              copyable(
                link_to(section.conferencing_url, section.conferencing_url, target: '_blank', rel: 'noopener'),
                section.conferencing_url,
              )
            end
          end
          row(:instructor) { |s| render_link_to_employee(s.instructor) }
          row(:advocate) { |s| render_link_to_employee(s.advocate) }
          row(:grader) { |s| render_link_to_employee(s.grader) }
        end

        if resource.live_sessions.present?
          panel 'Live Sessions' do
            table_for(resource.live_sessions.includes(:reviews).order(:starts_at)) do
              column('') { |ls| date_icon(ls.starts_at.to_date) }
              column(:uid) { |ls| link_to(ls.humanized_uid, admin_live_session_path(ls)) }
              column(:week_number) { |ls| ls.section_week.number }
              column(:date) { |ls| ls.starts_at.strftime('%a, %b %-d') }
              column(:time) do |ls|
                start_time = ls.starts_at.strftime("%-I:%M%P")
                end_time = ls.ends_at.strftime('%-I:%M%P %Z')
                time = "#{start_time} - #{end_time}"
                copyable(time, "#{ls.starts_at.strftime('%a, %b %-d')} #{time}")
              end
              column(:leader) { |ls| render_link_to_employee(ls.leader) }
              column(:moderator) { |ls| render_link_to_employee(ls.moderator) }
              column("NPS Score") do |live_session|
                render_nps(live_session.aggregate_nps_score)
              end
            end
          end

          panel 'NPS Summary' do
            render partial: 'admin/live_sessions/nps_score_summary', locals: {
              nps_scores: resource.live_session_reviews.map(&:nps_score),
            }
          end
        end

        if resource.live_session_reviews.any?
          panel(link_to(pluralize(resource.live_session_reviews.size, 'Reviews').to_s, admin_live_session_reviews_path(q: { section_uid_cont: resource.uid }))) do
            render partial: 'admin/live_sessions/reviews/table', locals: { reviews: resource.live_session_reviews.order(:live_session_id, :score), context: self }
          end
        end
      end
    end

    remote_resources_for(resource)
  end

  form(data: { turbo: false }) do |_f|
    semantic_errors

    panel 'Readonly' do
      attributes_table_for(resource) do
        row(:id)
        row(:uid, &:humanized_uid)
      end
    end

    inputs "Details" do
      input :cohort_id, as: :nested_select,
        level_1: {
          attribute: :program_id,
          method_model: Program,
          collection: Program.active,
          display_name: :short_name,
          input_html: { disabled: resource.persisted? },
        },
        level_2: {
          attribute: :cohort_id,
          method_model: Cohort,
          display_name: :starting_month,
          minimum_input_length: 0,
        }
      input(
        :partner,
        as: :select,
        collection: Partner.active.dedicated_sections.sort_by(&:name),
        include_blank: true,
      )
      input :chat_join_url
      input :chat_workspace_key
      input :conferencing_url
      input :live_day_of_the_week
      input :live_start_time, as: :time_picker, hint: "Time in your time zone: #{Time.zone.name}"
      input :live_end_time, as: :time_picker, hint: "(Optional) Leave blank will use #{Section::LIVE_SESSION_DEFAULT_DURATION / 1.hour} hours duration"
      input :suffix if resource.persisted?
      input :instructor_id,
        as: :search_select,
        url: proc { admin_learning_delivery_employees_path(q: { type_eq: 'LearningDelivery::Employee::Instructor' }) },
        fields: %w[first_name last_name],
        display_name: :full_name,
        minimum_input_length: 0
      input :advocate_id,
        as: :search_select,
        url: proc { admin_learning_delivery_employees_path(q: { type_eq: 'LearningDelivery::Employee::Advocate' }) },
        fields: %w[first_name last_name],
        display_name: :full_name,
        minimum_input_length: 0

      input :grader_id,
        as: :search_select,
        url: proc { admin_learning_delivery_employees_path(q: { type_eq: 'LearningDelivery::Employee::Grader' }) },
        fields: %w[first_name last_name],
        display_name: :full_name,
        minimum_input_length: 0
    end

    if resource.new_record?
      panel 'Variant' do
        attributes_table_for(resource) do
          row('List Price') { '<i>defaults from Product list price from selected Cohort\'s Program</i>'.html_safe }
          row('Actual Price') { '<i>defaults from Product actual price from selected Cohort\'s Program</i>'.html_safe }
        end
      end
    else
      render(
        partial: 'admin/ecom_pricing/pricing_inputs_with_preview',
        locals: { resource:, pricing_association: :ecom_variant, heading: 'Variant', context: self },
      )
    end

    if resource.live_sessions.present?
      has_many :weeks, heading: 'Live Sessions', new_record: false do |w|
        w.has_many :live_sessions, heading: '', new_record: false do |ls|
          ls.input :section_week_id, as: :select, collection: resource.weeks.includes(:cohort_week).map { |e| [e.number, e.id] }
          ls.input :starts_at, as: :datetime_picker
          ls.input :ends_at, as: :datetime_picker
          ls.input :leader_id,
            as: :search_select,
            url: proc { admin_learning_delivery_employees_path(q: { type_eq: 'LearningDelivery::Employee::Instructor' }) },
            fields: %w[first_name last_name],
            display_name: :full_name,
            minimum_input_length: 0
          ls.input :moderator_id,
            as: :search_select,
            url: proc { admin_learning_delivery_employees_path(q: { type_eq: 'LearningDelivery::Employee::Advocate' }) },
            fields: %w[first_name last_name],
            display_name: :full_name,
            minimum_input_length: 0
        end
      end
    end

    actions
  end

  action_item :actions, only: :show do
    dropdown_menu "Actions" do
      if resource.cohort.created? || (resource.cohort.status.in?(%w[opened started]) && !resource.remote_canvas_section)
        action_title = resource.remote_canvas_section.present? ? 'Edit Canvas Section Key' : 'Connect Canvas Section'
        title = "Canvas Section Key can be set/reset as long as Cohort is in Created Status."
        item(action_title, connect_canvas_section_admin_section_path(resource), title:)
      end

      if resource.cohort.status.in?(%w[created opened started ended extension_period_ended])
        title = 'Publishes all section and cohort related attributes to Hubspot for all enrollments in this section.'
        item('Publish to all associated Hubspot Deals', publish_section_attributes_to_hubspot_admin_section_path(resource), title:)
      end
    end
  end

  member_action :connect_canvas_section, method: %i[get post] do
    @remote_canvas_section = resource.remote_canvas_section || resource.build_remote_canvas_section
    if @remote_canvas_section.remote_canvas_course
      @key_options = @remote_canvas_section.remote_canvas_course.remote_list_sections
        .pluck('name', 'id')
        .map { |name, id| [ "#{name} (Key: #{id})", id ] }
        .sort_by(&:first)
    else
      @key_options = []
      flash.now[:warn] = 'Cohort should have been connected to a Canvas Course first'
    end

    if request.post?
      begin
        if @remote_canvas_section.persisted? && !resource.cohort.status.in?(%w[created])
          raise 'Canvas Section Key can be changed only when Cohort is in Created Status.'
        end

        @remote_canvas_section.key = params[:remote_canvas_section][:key]
        @remote_canvas_section.save!

        Section::AssignToAssignmentsJob.perform_async(resource.id)
        Section::PublishCalendarEventsJob.perform_async(resource.id) if Section::PublishCalendarEventsCommand.callable?(section: resource)
        Section::EmployeeEnrollment::SynchronizeJob.perform_async(resource.id)

        redirect_to admin_section_path(resource), notice: 'Canvas Section connected successfully'
      rescue => e
        flash.now[:error] = e.message
        render 'connect_canvas_section'
      end
    else
      render 'connect_canvas_section'
    end
  end

  member_action :publish_section_attributes_to_hubspot, method: %i[get post] do
    if request.post?
      begin
        deal_ids = resource.enrollments.primary.pluck(:deal_id)
        remote_deals = Remote::Hubspot::Deal.where(core_record_id: deal_ids, core_record_type: 'Deal')
        remote_deals.in_groups_of(Remote::Hubspot::BatchPublishCommand::BATCH_LIMIT, false) do |batch|
          Remote::Hubspot::BatchPublishCommand.call!(collection: batch, payload: :section_attributes_payload)
        end

        redirect_to admin_section_path(resource), notice: 'All enrollments published to Hubspot successfully'
      rescue => e
        flash.now[:error] = e.message
        render 'publish_section_attributes_to_hubspot'
      end
    end
  end

  controller do
    def action_to_permission(action_name)
      case action_name
      when 'connect_canvas_section', 'publish_section_attributes_to_hubspot'
        :update
      else
        super
      end
    end

    def apply_decorations(resource)
      super.tap do
        next unless resource

        if resource.ecom_variant.blank?
          resource.build_ecom_variant

          populate_variant(resource)
        end
      end
    end

    def index
      index! do |format|
        format.json do
          uid_cont = params.dig(:q, :groupings, '0', :uid_cont)
          program_cohort_suffix_cont = params.dig(:q, :groupings, '0', :program_cohort_suffix_cont)
          registration_form = params.dig(:q, :registration_form)

          json_collection = if uid_cont.present?
            Section.uid_cont(uid_cont)
          elsif program_cohort_suffix_cont.present?
            Section.program_cohort_suffix_cont(program_cohort_suffix_cont).ransack(params[:q]).result
          elsif registration_form.present?
            cohort_id = params.dig(:q, :cohort_id_eq)
            partner_id = params.dig(:q, :partner_id)
            cohort = Cohort.find_by(id: cohort_id)
            partner = Partner.find_by(id: partner_id)


            sections = if partner.dedicated_sections?
              cohort.sections.dedicated_to(partner)
            else
              cohort.sections.commingled
            end
            sections.order(:suffix)
          else
            Section.ransack(params[:q]).result
          end

          render json: json_collection.as_json(
            methods: [
              :live_name,
              :full_name,
              :program_cohort_name,
              :program_cohort_name_with_advocate,
              :program_cohort_name_with_instructor,
            ],
          )
        end
      end
    end

    def new
      @section = Section.new(
        cohort_id: params[:cohort_id],
      )
      new!
    end

    def create
      attributes = permitted_params[:section].to_h
      @section = Section::ReplaceCommand.call!(attributes:)
      redirect_to admin_section_path(@section), notice: 'Section created successfully'
    rescue ActiveRecord::RecordInvalid => e
      flash[:error] = e
      @resource = e.record
      render :new
    end

    def update
      section = Section.find_from_uid_or_id(params[:id])
      attributes = permitted_params[:section].to_h
      @section = Section::ReplaceCommand.call!(attributes:, section:)
      Section::PublishCalendarEventsJob.perform_async(@section.id) if Section::PublishCalendarEventsCommand.callable?(section: @section)

      redirect_to admin_section_path(@section), notice: 'Section updated successfully'
    rescue ActiveRecord::RecordInvalid => e
      flash[:error] = e
      @resource = e.record
      render :edit
    end

    private

    def populate_variant(resource)
      cohort = resource.cohort || Cohort.find_by(id: params.dig(:section, :cohort_id))

      cohort&.program&.ecom_product.tap do |product|
        if product.blank?
          Rails.logger.warn "No Product found for the selected Cohort's Program: #{cohort&.id}"
          flash.now[:warn] = 'No Product found for the selected Cohort\'s Program' if action_name.in?(%w[create update])

          next
        end

        resource.ecom_variant.product = product
        resource.ecom_variant.currency_code = product.currency_code
        resource.ecom_variant.list_cents_param = product.list_cents_param
        resource.ecom_variant.standard_discount_cents_param = product.standard_discount_cents_param
        resource.ecom_variant.upfront_discount_cents_param = product.upfront_discount_cents_param
      end
    end
  end
end
