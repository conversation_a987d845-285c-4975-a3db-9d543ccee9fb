# frozen_string_literal: true

ActiveAdmin.register Deal do
  extend RemoteResourcesAdminActions

  show_remote_ops_menu do
    title = 'Refetch contact based on email and associate deal to identified contact'
    item 'Reassociate to Contact', url_for([:admin, :deal, action: :reassociate_to_contact, id: request.params['id']]), title:, method: :post
  end

  menu parent: 'Customers'
  actions :index, :show

  includes %i[email partner_program partner program remote_hubspot_contact remote_hubspot_deal remote_hubspot_line_item]

  scope :all, default: true

  scope 'Lead', :stage_lead, group: :open
  scope 'Prospect', :stage_prospect, group: :open
  scope 'Registrant', :stage_registrant, group: :open

  scope 'Enrollee', :stage_enrollee, group: :closed_won
  scope 'Learner', :stage_learner, group: :closed_won
  scope 'Post Learner', :stage_post_learner, group: :closed_won
  scope 'Other', :stage_other, group: :closed_won

  scope 'Suspect', :stage_suspect, group: :open

  filter :email_address_cont, label: 'Email'
  filter :partner_program_id,
    as: :search_select_filter,
    label: 'Partner Program',
    url: proc { admin_partner_programs_path },
    fields: %w[name],
    display_name: 'name',
    order_by: 'id desc'
  filter :status, as: :select, collection: -> { Deal.statuses }
  filter :stage, as: :select, collection: -> { Deal.stage_alt_name_to_ids }
  filter :source, as: :select, collection: -> { Deal.source_alt_name_to_ids }
  filter :remote_hubspot_contact_key_cont, label: 'Contact Key'
  filter :remote_hubspot_deal_key_cont, label: 'Deal Key'
  filter :remote_hubspot_line_item_key_cont, label: 'Line Item Key'
  filter :probability_of_enrollment_after_7_days, as: :numeric_range_filter

  index do
    column :id
    stack :email, :name
    stack(:partner_program, 'Partner | Program' => proc { |deal| [auto_link(deal.partner), auto_link(deal.program)].join(' | ').html_safe })
    stack('Status' => proc { |e| e.status.humanize }, 'Stage' => proc { |e| e.stage.humanize })
    column(:contact_key) { |deal| remote_key(deal.remote_hubspot_contact) if deal.remote_hubspot_contact }
    column(:deal_key) { |deal| remote_key(deal.remote_hubspot_deal) if deal.remote_hubspot_deal }
    column(:line_item_key) { |deal| remote_key(deal.remote_hubspot_line_item) if deal.remote_hubspot_line_item }
    column('Probability of Enrollment<br/>(After 7 Days)'.html_safe, &:probability_of_enrollment_after_7_days)
    column :updated_at
  end

  show do
    table_for deal do
      stack :became_a_lead_on, :last_engaged_at
      stack(
        'Became a Prospect On' => proc { deal.first_became_a_prospect_on || 'N/A' },
        '' => proc { link_to pluralize(deal.syllabus_requests.count, "Syllabus Request"), admin_syllabus_requests_path(q: { deal_id_eq: deal.id }) },
      )
      stack(
        'Became a Registrant On' => proc { deal.first_became_a_registrant_on || 'N/A' },
        '' => proc { link_to pluralize(deal.registrations.count, "Registration"), admin_registrations_path(q: { deal_id_eq: deal.id }) },
      )
      stack(
        'Became a Paid Enrollee On' => proc { deal.became_a_paid_enrollee_on || 'N/A' },
        '' => proc { link_to pluralize(deal.enrollments.count, "Enrollment"), admin_enrollments_path(q: { deal_id_eq: deal.id }) },
      )
    end
    attributes_table do
      row :id
      row :name
      row :email
      row :partner
      row :program
      row :partner_program
      row(:amount, &:deal_value_currency)
      row(:stage) { |deal| deal.stage.humanize }
      row(:status) { |deal| deal.status.humanize }
      row(:source) { |deal| deal.source.humanize }
      row(:probability_of_enrollment_after_7_days)
      row :close_date
      row(:clas_deal_key) { |deal| copyable(deal.clas_key) }
      row(:revived_at) { |deal| deal.revived_at&.to_fs(:datetime_with_zone) }
      row(:last_became_prospect_at) { |deal| deal.last_became_prospect_at&.to_fs(:datetime_with_zone) }
      row(:last_became_registrant_at) { |deal| deal.last_became_registrant_at&.to_fs(:datetime_with_zone) }
      row(:created_at) { |deal| deal.created_at&.to_fs(:datetime_with_zone) }
      row(:updated_at) { |deal| deal.updated_at&.to_fs(:datetime_with_zone) }
    end

    remote_resources_for(resource)
  end

  member_action :reassociate_to_contact, method: :post do
    deal = Deal.find(params[:id])
    remote_hubspot_deal = deal.remote_hubspot_deal || deal.build_remote_hubspot_deal.tap(&:remote_fetch)
    remote_hubspot_contact = deal.remote_hubspot_contact || deal.build_remote_hubspot_contact

    if remote_hubspot_deal.blank?
      redirect_to(resource_path(resource), alert: 'Deal must have a Hubspot deal to reassociate')
      return
    end

    begin
      Remote::Hubspot::Deal::ReassociateToContactCommand.new(
        remote_hubspot_deal:,
        remote_hubspot_contact:,
      ).call!
      redirect_to(resource_path(resource), notice: 'Deal successfully reassociated to contact')
    rescue => e
      message = "Failed to reassociate deal to contact: #{e.message}"
      ErrorReporter.report(error: e, source: self.class.name, trigger: '#reassociate_to_contact', message:)
      redirect_to(resource_path(resource), alert: message)
    end
  end

  controller do
    def action_to_permission(action_name)
      case action_name
      when 'reassociate_to_contact'
        :update
      else
        super
      end
    end
  end
end
