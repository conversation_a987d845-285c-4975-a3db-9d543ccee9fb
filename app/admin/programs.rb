# frozen_string_literal: true

ActiveAdmin.register Program do
  extend RemoteResourcesAdminActions

  show_remote_ops_menu

  after_save do |_resource|
    auto_publish :hubspot_properties
  end

  includes :theme, :ecom_product

  menu parent: 'Base', priority: 1

  permit_params :name, :short_name, :slug, :career_title, :career_industry, :admissions_schedule_url, :status, :certificate_key, :award_key, :cobranded_certificate_key,
    :duration_in_weeks, :key, :abbreviation, :lms_grading_assistant_id,
    theme_attributes: [
      :id,
      :primary_color,
      :image,
      :background_image,
    ],
    ecom_product_attributes: [
      :id,
      :list_cents_param,
      :standard_discount_cents_param,
      :upfront_discount_cents_param,
    ],
    partner_programs: [
      :program,
      :certificate_theme,
      :site_partner_dataset,
      :site_program_dataset,
      :site_partner_program_dataset,
      :certificate_theme,
    ]

  filter :name_cont, label: 'Name'
  filter :short_name_cont, label: 'Short Name'
  filter :slug_cont, label: 'Slug'
  filter :abbreviation_cont, label: 'Abbreviation'
  filter :key_cont, label: 'Key'
  filter :status, as: :select, collection: -> { Program.status_alt_name_to_ids }
  filter :admissions_schedule_url_cont, label: 'Admissions Schedule URL'
  filter :certificate_key_cont, label: 'Certificate Key'
  filter :cobranded_certificate_key_cont, label: 'Cobranded Certificate Key'
  filter :award_key_cont, label: 'Award Key'
  filter :created_at, as: :date_time_picker_filter

  index do
    column(:id)
    column(:name)
    column(:short_name)
    column(:slug) { |p| copyable(p.slug) }
    column(:key) { |p| copyable(p.key) }
    column(:status) { |p| status_tag(p.status_name, class: p.status) }
  end

  show do
    columns do
      column do
        attributes_table do
          row(:id) { |p| copyable(p.id) }
          row(:name) { |p| copyable(p.name) }
          row(:short_name) { |p| copyable(p.short_name) }
          row(:slug) { |p| copyable(p.slug) }
          row(:abbreviation) { |p| copyable(p.abbreviation) }
          row(:key) { |p| copyable(p.key) }
          row(:status) { |p| status_tag(p.status_name, class: p.status) }
          row(:duration_in_weeks) { |p| "#{p.duration_in_weeks} weeks" }
          row(:career_title) if resource.career_title.present?
          row(:career_industry) if resource.career_industry.present?
          row(:admissions_schedule_url) { |p| copyable(p.admissions_schedule_url) }
          row(:lms_grading_assistant)
        end
      end

      column do
        panel 'Product' do
          attributes_table_for(resource.ecom_product) do
            row(:record, &:itself)
            row(:list_price, &:list_currency)
            row(:standard_discount_amount) { |product| render_standard_discount(retail_price: product.retail_price) }
            row(:upfront_discount_amount) { |product| render_upfront_discount(retail_price: product.retail_price) }

            partner_pricing = Ecom::Product.where(program: resource).where.not(partner: nil).count
            if partner_pricing > 0
              row(:alternate_pricing) do
                link_to(
                  "#{pluralize(partner_pricing, 'alternate price')} - view all pricing",
                  admin_ecom_products_path(q: { program_id_eq: resource.id }),
                )
              end
            end
          end
        end

        attributes_table(title: 'Accredible Settings') do
          row(:certificate_key) { |p| copyable(p.certificate_key) }
          row(:cobranded_certificate_key) { |p| copyable(p.cobranded_certificate_key) }
          row(:award_key) { |p| copyable(p.award_key) }
        end
      end
    end

    panel 'Cohorts (Active / Future)' do
      cohorts =
        resource.cohorts.active
          .or(resource.cohorts.add_period_active)
          .or(resource.cohorts.where(starts_on: Time.zone.today..))
          .order(starts_on: :desc)
      table_for(cohorts) do
        column(:id) { |c| link_to(c.id, admin_cohort_path(c)) }
        column(:status) { |c| status_tag(c.status_name) }
        column(:add_period_active?)
        column(:starts_on)
        column(:key) { |c| copyable(c.key) }
        column(:sections) { |c| c.sections.size }
      end
    end

    panel '3rd Party Materials' do
      table_for(resource.external_content_materials) do
        column(:material_set)
        column(:name) { |m| link_to(m.name, admin_external_content_material_path(m)) }
        column(:available_codes) { |m| render_presenter(ExternalContent::Material::CodeProgressBarPresenter.new(material: m)) }
      end
    end


    if resource.partner_programs.present?
      hr
      panel "Launch Dashboard" do
        table_for(resource.partner_programs) do
          render(partial: 'admin/launch_dashboard_items/index', locals: { context: self })
        end
      end
    end

    remote_resources_for(resource)
  end


  form(data: { turbo: false }) do |_f|
    semantic_errors

    inputs "Details" do
      input(:name)
      input(:short_name)
      input(:slug) if resource.persisted?
      input(:abbreviation, hint: 'This is used in URLs like /dms-course. <strong>Changing it will break existing URLs</strong>'.html_safe)
      input(:key)
      input(:career_title, hint: "I'm a <strong>#{resource.career_title || '{CAREER_TITLE}'}</strong> by trade.  ".html_safe)
      input(:career_industry,
        hint: "I'm currently in a <strong>#{resource.career_industry.presence || '{CAREER_INDUSTRY}'}</strong> role. \
          (Note: only one of career_title or career_industry should be entered.)"
          .html_safe,
      )
      input(:duration_in_weeks, as: :number, input_html: { min: 1 })
      input(:admissions_schedule_url)
      input(:status)
      input(:lms_grading_assistant_id, as: :select, collection: Lms::GradingAssistant.all.map { |ga| [ga.name, ga.id] }, include_blank: true)
    end

    inputs "Accredible Settings" do
      input(:certificate_key)
      input(:cobranded_certificate_key)
      input(:award_key)
    end

    inputs "Product" do
      has_many :ecom_product, heading: false, new_record: false do |pf|
        pf.input(:list_cents_param, label: 'List Price')
        pf.input(:standard_discount_cents_param, label: 'Standard Discount')
        pf.input(:upfront_discount_cents_param, label: 'Upfront Discount')
      end
    end

    actions
  end

  controller do
    before_save :sync_product_name

    def apply_decorations(resource)
      super.tap do
        next unless resource

        resource.build_ecom_product if resource.ecom_product.blank?
      end
    end

    def sync_product_name(resource)
      return unless resource

      resource.ecom_product.name = params[:program][:name]
    end
  end
end
