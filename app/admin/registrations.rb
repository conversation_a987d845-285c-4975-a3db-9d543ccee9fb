# frozen_string_literal: true

ActiveAdmin.register Registration do
  menu parent: 'Customers'

  includes :email, partner_program: [:partner, :program], section: [:cohort, :program]

  actions :index, :show

  filter :uid_cont, label: 'UID'
  filter :learner_first_name_cont, label: 'First Name'
  filter :learner_last_name_cont, label: 'Last Name'
  filter :email_address_cont, label: 'Email'
  filter :learner_phone_standardized_cont, label: 'Phone'
  filter :admin_user, collection: -> { AdminUser.order(:first_name, :last_name) }
  filter :partner_program_id,
    as: :search_select_filter,
    label: 'Partner Program',
    url: proc { admin_partner_programs_path },
    fields: %w[name],
    display_name: 'name',
    order_by: 'id desc'
  filter :section_cohort_program_id, label: 'Program', as: :select, collection: -> { Program.order(:name).pluck(:name, :id) }
  filter :section_cohort_id,
    as: :search_select_filter,
    label: 'Cohort Name',
    url: proc { admin_cohorts_path },
    fields: %w[name],
    display_name: 'name',
    method_model: Section,
    order_by: :name
  filter :cohort, label: 'Cohort Key', as: :select, collection: -> { Cohort.order(:key).pluck(:key, :id) }
  filter :status, as: :select, collection: -> { Registration.status_alt_name_to_ids }
  filter :aspiration, as: :select, collection: -> { Registration.aspiration_alt_name_to_ids }
  filter :experience_level, as: :select, collection: -> { Registration.experience_level_alt_name_to_ids }
  filter :eva_type, as: :select, collection: -> { Registration.eva_types.keys.zip(Registration.eva_types.values) }
  filter :third_party_entity_cont, label: 'Third Party Entity'
  filter :created_at, as: :date_time_picker_filter
  filter :clas_key_eq, label: 'CLAS Submission ID'
  filter :clas_external_key_cont, label: 'CLAS External ID', placeholder: 'gf_enrollment_id contains...'

  index do
    column(:id)
    column(:status, &:status_html)
    column(:uid) { |r| copyable(r.humanized_uid) }
    column(:learner)
    column(:email) { |e| copyable(link_to_email(e.email_address), e.email_address) }
    stack :partner, 'Program > Cohort > Section' => proc { |e| display_section(e.section) }
    column(:created_at) { |e| e.created_at.to_fs(:datetime_with_zone) }
  end

  show do
    columns do
      column do
        panel('Registration') do
          attributes_table_for(resource) do
            row(:id) { |r| copyable(r.id) }
            row(:status, &:status_html)
            row(:uid) { |r| copyable(r.humanized_uid) }
            row(:learner)
            row(:email) { |e| copyable(link_to_email(e.email_address), e.email_address) }
            row(:order) { |r| r.ecom_order_item&.order }
            row(:payment_url) do |r|
              if r.ecom_order.present?
                o = r.ecom_order
                payment_url = UrlBuilder::Site.new(partner_program: o.order_items.first.partner_program).call!(template: :payment, order_id: o.humanized_uid)
                copyable(link_to(payment_url, payment_url, target: '_BLANK'), payment_url)
              end
            end
            row(:short_payment_url) do |r|
              if r.ecom_order.present?
                if r.ecom_order.short_payment_url.present?
                  copyable(link_to(r.ecom_order.short_payment_url, r.ecom_order.short_payment_url, target: '_BLANK'), r.ecom_order.short_payment_url)
                else
                  button_to(
                    'Shorten Payment URL',
                    update_short_payment_url_admin_ecom_order_path(r.ecom_order, redirect_url: admin_registration_path(r)),
                  )
                end
              end
            end
            if resource.confirmable? && resource.ecom_order.paid?
              row(:confirmation_url) do |r|
                confirmation_url = UrlBuilder::Site.new(partner_program: r.partner_program).call!(template: :order_confirm, order_id: r.ecom_order.humanized_uid, order_item_uid: r.ecom_order_item.humanized_uid)
                copyable(link_to(confirmation_url, confirmation_url, target: '_BLANK'), confirmation_url)
              end
            end
            row(:tracking_key) { |r| copyable(r.tracking_key) }
            row(:created_at) { |e| e.created_at.to_fs(:datetime_with_zone) }
          end
        end
      end

      column do
        panel('Course') do
          attributes_table_for(resource) do
            row(:program)
            row(:cohort)
            row(:section)
            row(:experience_level, &:experience_level_name)
            row(:aspiration, &:aspiration_name)
          end
        end
      end

      column do
        panel('Source') do
          attributes_table_for(resource) do
            row(:partner)
            row(:finance_relationship)
            row(:deal)
            row(:admin_user) if resource.admin_user.present?
            row(:reenrolled_from) if resource.reenrolled_from.present?
            row(:third_party_entity) if resource.third_party_entity.present?
            row(:eva_type) if resource.eva_type.present?
            row(:notes) if resource.notes

            row(:clas_key) { |r| copyable(r.clas_key) } if resource.clas_key.present?
            if resource.clas_external_key.present?
              row(:clas_external_key) { |r| copyable(r.clas_external_key) }
              row(:clas_payment_url) do |r|
                if r.clas_external_key.present?
                  enrollment_id = r.clas_external_key
                  # source: :core will force the Proxy to pass the request to the Webflow site, so we use source: :clas here,
                  # as we want to use this in Staging for testing the behavior of CLAS generated payment links
                  url = Clas::PaymentUrlBuilder.new(partner_program: r.partner_program, source: :clas).call!(enrollment_id:)
                  copyable(url.truncate(40), url)
                else
                  'Not Applicable (Applicable only for registrations imported from CLAS)'
                end
              end
            end

            if resource.promos_referrer
              panel 'Referral Program' do
                attributes_table_for resource.promos_referrer do
                  row(:uid) { |e| copyable(link_to(e.humanized_uid, admin_promos_referrer_path(e)), e.humanized_uid) }
                  row(:learner)
                  row(:enrollment)
                  row(:code) { |e| copyable(link_to(e.code, UrlBuilder::ReferralRock.search_code_url(e.code)), e.code) }
                end
              end
            end
          end
        end
      end
    end

    render(partial: 'admin/site/ad_tracking/panel', locals: { ad_tracking: resource.site_ad_tracking })

    panel 'Enrollments' do
      table_for(resource.enrollments.order(id: :desc)) do
        column(:uid) { |e| copyable(link_to(e.humanized_uid, admin_enrollment_path(e)), e.humanized_uid) }
        column(:status, &:status_html)
        column(:section) { |e| display_section(e.section) }
        column(:created_at) { |e| e.created_at.to_fs(:datetime_with_zone) }
      end
    end

    remote_resources_for(resource)
  end

  collection_action :build_link, method: :get do
    attrs = params.dig(:registration_build_link_form)&.slice(:partner_id, :partner_program_id, :finance_relationship_id)&.permit!
    @build_link_form = Registration::BuildLinkForm.new(attrs)
    @builder = Admin::MultiStepFormBuilder.new(
      step_names: %w[Partner PartnerProgram],
      current_step_number: params.dig(:registration_build_link_form, :current_step_number).to_i + 1,
    )
  end

  action_item :build_link do
    link_to 'Build Link', build_link_admin_registrations_path if authorized?(:update, 'Customers')
  end

  action_item :create_manual, only: :index do
    link_to('Create Manual Registration', create_manual_admin_registrations_path) if authorized?(:create, 'Customers')
  end

  collection_action :create_manual, title: 'Manual Registration', method: %i[get post] do
    attrs =
      params
        .dig(:registration_create_manual_form)
        &.slice(*(Registration::CreateManualForm.attribute_names + ['price_amount']))
        &.permit!
    attrs ||= {}
    @create_manual_form = Registration::CreateManualForm.new(attrs.merge(admin_user: current_admin_user))

    @builder = Admin::MultiStepFormBuilder.new(
      step_names: %w[Source Learner Partner PartnerProgram Registration Checkout Confirmation],
      current_step_number: params.dig(:registration_create_manual_form, :current_step_number).to_i + 1,
    )

    if @builder.complete?
      registration = @create_manual_form.save!

      payment_path, create_payment_message = begin
        if @create_manual_form.payment_method.manual_payment?
          [
            new_admin_payments_manual_payment_path('ecom_payment_id' => @create_manual_form.order.payment.id),
            'Create a Manual Payment',
          ]
        elsif @create_manual_form.payment_method.invoice_payment?
          [
            new_admin_payments_invoice_payment_path('ecom_payment_id' => @create_manual_form.order.payment.id),
            'Create an Invoice Payment',
          ]
        else
          raise "Unknown payment method: #{registration.ecom_order.payment.payment_method.kind}"
        end
      end

      redirect_to(
        payment_path,
        notice: "Learner is registered but not yet enrolled until a payment is received. " \
                "#{create_payment_message} to pay this order and to email the learner to complete enrollment.",
      )
    else
      @create_manual_form.valid?
    end
  end

  controller do
    def action_to_permission(action)
      case action
      when 'build_link', 'create_manual'
        :update
      else
        super
      end
    end
  end
end
