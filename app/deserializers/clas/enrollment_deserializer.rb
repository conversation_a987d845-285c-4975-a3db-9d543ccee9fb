# frozen_string_literal: true

module Clas
  class EnrollmentDeserializer < ApplicationDeserializer

    attr_reader :hash

    def initialize(hash)
      super()
      @hash = hash
      @retries = 0
    end

    def call!
      raise if type != 'enrollment'

      ActiveRecord::Base.transaction do
        registration.update!(learner:)

        Enrollment::ReplaceCommand.call!(
          attributes:,
          enrollment:,
          reason:,
          notes:,
        )
        save_remote_objects! if Rails.env.production?
      end

      enrollment
    rescue PG::UniqueViolation => e
      @retries += 1
      retry if @retries < 3
      raise e
    end


    private

    def reason
      @reason ||= hash['reason'] || default_reason
    end

    def default_reason
      return unless Enrollment::STATUS_REASON_REQUIRED.include?(status)

      'Migrated from CLAS with no reason provided'
    end

    def enrollment
      @enrollment ||= Enrollment.find_or_initialize_by(clas_cohort_admission_key:)
    end

    def attributes
      {
        certificate_issued_at:,
        certificate_url:,
        certifier:,
        clas_cohort_admission_key:,
        extended_until:,
        primary:,
        status:,
        registration:,
        ecom_order_item:,
        learner:,
        partner_program:,
        section:,
        deal:,
        created_at:, # significant because it influences became_a_lead_date property on Deals
      }
    end

    def registration
      @registration ||= Registration.find_by!(clas_key: clas_submission_key)
    end

    def ecom_order_item
      @ecom_order_item ||= registration.ecom_order_item
    end

    def learner
      @learner ||= Learner::ReplaceCommand.call!(
        first_name: first_name.presence || registration.first_name,
        last_name: last_name.presence || registration.last_name,
        phone: phone.to_s.gsub(/\D/, '').presence || registration.learner.phone,
        email_address: email_address.presence || registration.email_address,
      ).tap do |l|
        conflicting_learner = Learner.where(sis_key: gf_student_id).where.not(id: l.id).first
        if conflicting_learner.present?
          conflicting_learner.sis_key = nil
          conflicting_learner.send(:set_sis_key)
          conflicting_learner.save!
        end
        l.update!(sis_key: gf_student_id)
      end
    end

    def partner_program
      @partner_program ||= registration.partner_program
    end

    def section
      @section ||= cohort_section || registration.section
    end

    def cohort
      if instance_variable_defined?(:@cohort)
        @cohort
      else
        @cohort = Cohort.find_by(key: cohort_key)
      end
    end

    def cohort_section
      @cohort_section ||= cohort&.sections&.find_by(suffix: cohort_section_suffix)
    end

    def save_remote_objects!
      remote_canvas_user.update!(key: remote_canvas_user_key) if remote_canvas_user_key.present?
      remote_canvas_enrollment.update!(key: remote_canvas_enrollment_key) if remote_canvas_enrollment_key.present?
    end

    def remote_canvas_user
      @remote_canvas_user ||= learner.remote_canvas_user || learner.build_remote_canvas_user
    end

    def remote_canvas_enrollment
      @remote_canvas_enrollment ||= enrollment.remote_canvas_enrollment || enrollment.build_remote_canvas_enrollment
    end

    def deal
      @deal ||= Deal.find_by!(clas_key: deal_key)
    end
  end
end
