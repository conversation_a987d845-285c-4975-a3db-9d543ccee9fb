# frozen_string_literal: true

module Clas
  class ProspectDeserializer < ApplicationDeserializer
    attr_reader :hash

    DEFAULT_FIRST_NAME = 'Learner'

    def initialize(hash)
      super()
      @hash = hash
    end

    def call!
      raise if type != 'prospect'

      DistributedLock.lock!(key: ['Email', email_address]) do
        if existing_prospect
          # if we are optimizing by reducing the number of syllabus requests we create,
          # ensure we save the earliest created_at for a given Deal, if a syllabus request with matching signature exists already
          existing_prospect.update!(created_at:) if existing_prospect.created_at > Time.zone.parse(created_at)
        else
          SyllabusRequest::CreateCommand.call!(attributes:, email_address:, site_ad_tracking_attributes:)
        end
      end
    end


    private

    def attributes
      {
        first_name: first_name.presence || DEFAULT_FIRST_NAME,
        phone:,
        partner_program:,
        deal:,
        created_at:, # significant because it influences became_a_lead_date property on Deals
      }
    end

    def site_ad_tracking_attributes
      hash.slice(*Site::AdTracking::TRACKING_ATTRIBUTES.map(&:to_s))
    end

    def partner_program
      @partner_program ||= PartnerProgram.find_by!(partner:, program:)
    end

    def partner
      @partner ||= Partner.find_by!(slug: partner_slug)
    end

    def program
      @program ||= Program.find_by!(key: program_key)
    end

    def existing_email
      @existing_email ||= Email.address(email_address).first
    end

    def existing_prospect
      return if existing_email.blank?

      if instance_variable_defined?(:@existing_prospect)
        @existing_prospect
      else
        @existing_prospect = SyllabusRequest.find_by(email: existing_email, partner_program:, deal:)
      end
    end

    def deal
      @deal ||= Deal.find_by!(clas_key: deal_key)
    end
  end
end
