# frozen_string_literal: true

module Site
  class SyllabusRequestsController < ApplicationController
    scout_apm_sample_rate 0.25

    rescue_from ActionController::InvalidAuthenticityToken, with: :handle_invalid_authenticity_token

    def create
      partner_program = PartnerProgram.find_by!(uid: params[:partner_program_uid])
      set_time_zone(partner_program.time_zone)

      email_address = params[:email_address]
      attributes = syllabus_request_params.merge(partner_program:, promos_referrer: captured_referrer)

      if !Site::DetectBotCommand.call!(attributes: bot_attributes)
        syllabus_request = SyllabusRequest::CreateCommand.call!(
          attributes:, email_address:, site_ad_tracking_attributes:, context:, publish_to_hubspot: true,
        )

        save_learner_info_to_cookie(syllabus_request)
      end

      flash[:notice] = 'The syllabus you requested is on its way! Be sure to check your inbox.'

      redirect_to(UrlBuilder::Site.new(partner_program:).call!(template: :syllabus), allow_other_host: true)
    end


    private

    def handle_invalid_authenticity_token
      redirect_back(
        fallback_location: root_path,
        allow_other_host: true,
        flash: { error: "There was an issue with your submission, please try again." },
      )
    end

    def bot_attributes
      syllabus_request_params.merge(site_ad_tracking_attributes).merge(params.permit(syllabus_request: [:hp_key])[:syllabus_request] || {}).to_h
    end

    def syllabus_request_params
      params
        .expect(
          syllabus_request: [:first_name,
                             :email_address,
                             :phone,
                             :contact_opt_in,
                             :tracking_key,],
        )
    end

    def site_ad_tracking_attributes
      params.require(:syllabus_request) # rubocop:disable Rails/StrongParametersExpect
        .permit(
        site_ad_tracking_attributes: Site::AdTracking::TRACKING_ATTRIBUTES,
      )[:site_ad_tracking_attributes]&.merge(
        referrer: request.referrer,
        hutk_cookie_id: cookies['hubspotutk'],
      )&.compact_blank
    end

    def context
      {
        "pageUri" => page_uri,
        "pageName" => params.dig(:page_title) || 'Syllabus Request',
        "ipAddress" => request.remote_ip,
        "hutk" => cookies['hubspotutk'],
      }
    end

    def page_uri
      Site::AdTracking::BuildReferrerCommand.call!(
        referrer: request.referrer,
        site_ad_tracking_attributes:,
        request:,
      )
    end
  end
end
