# frozen_string_literal: true

module Site
  class OrdersController < ApplicationController
    before_action :redirect_to_site_root, if: :missing_order_parameters?
    before_action :validate_order_and_order_item
    before_action :validate_registration_and_order_status

    def confirm
      set_time_zone(partner_program.time_zone)
      @presenter = Site::Orders::ConfirmPresenter.new(order_item:)
      gtm_events.add_page_view(template: 'registration_confirmation', partner_program:)
    end

    def confirm_update
      begin
        if should_update_registration?
          Registration::ReplaceCommand.call!(
            registration:,
            site_ad_tracking_attributes:,
            attributes: build_registration_attributes,
            learner_attributes: {}, # Empty since we're not updating learner info in confirmation
            context:,
            publish_to_hubspot: false,
          )
        end

        Registration::ConfirmCommand.call!(registration:)

        redirect_to site_payment_confirmation_path(order_id: order.humanized_uid, payment_method_kind:)
      rescue ActiveRecord::RecordInvalid => e
        flash[:alert] = e.message
        ErrorReporter.report(error: e, source: 'Site::OrdersController#confirm_update', severity: :warn)
        redirect_to site_order_confirm_path(order_uid: order.humanized_uid, order_item_uid: order_item.humanized_uid)
      rescue StandardError => e
        flash[:alert] = 'Unable to confirm registration. Please try again.'
        ErrorReporter.report(error: e, source: 'Site::OrdersController#confirm_update', severity: :warn)
        redirect_to site_order_confirm_path(order_uid: order.humanized_uid, order_item_uid: order_item.humanized_uid)
      end
    end

    private

    def order
      @order ||= Ecom::Order.uid(params[:order_id]).first
    end

    def payment_method_kind
      @payment_method_kind ||= order.payment.payment_method.kind
    end

    def order_item
      @order_item ||= order&.order_items&.uid(params[:order_item_uid])&.first
    end

    def registration
      @registration ||= order_item.registration
    end

    def partner_program
      @partner_program ||= registration.partner_program
    end

    def build_registration_attributes
      attributes = {}
      if params[:section_uid].present?
        section = Section.find_by!(uid: params[:section_uid])
        attributes[:section] = section
      end

      registration_params = confirm_update_params
      %i[experience_level aspiration eva_type].each do |attr|
        attributes[attr] = registration_params[attr] if registration_params[attr].present?
      end

      attributes
    end

    def should_update_registration?
      params[:cohort_key].present? && params[:section_uid].present? &&
        confirm_update_params.values_at(:experience_level, :aspiration).all?(&:present?)
    end

    def confirm_update_params
      params.expect(registration: [:experience_level, :aspiration])
    end

    def site_ad_tracking_attributes
      params.require(:registration) # rubocop:disable Rails/StrongParametersExpect
        .permit(
          site_ad_tracking_attributes: Site::AdTracking::TRACKING_ATTRIBUTES,
        )[:site_ad_tracking_attributes]&.merge(
          referrer: request.referrer,
          hutk_cookie_id: cookies['hubspotutk'],
        )&.compact_blank
    end

    def context
      {
        pageUri: page_uri,
        pageName: params.dig(:page_title) || 'Order Confirmation',
        ipAddress: request.remote_ip,
        hutk: cookies['hubspotutk'],
      }
    end

    def page_uri
      Site::AdTracking::BuildReferrerCommand.call!(
        referrer: request.referrer,
        site_ad_tracking_attributes:,
        request:,
      )
    end

    def missing_order_parameters?
      params[:order_id].blank? || params[:order_item_uid].blank?
    end

    def redirect_to_site_root
      redirect_to(ApplicationController::HOME_URL, allow_other_host: true)
    end

    def validate_registration_and_order_status
      if registration.confirmed_status?
        redirect_to site_payment_confirmation_path(order_id: order.humanized_uid, payment_method_kind:)
        return
      end

      return if registration.confirmable? && order.paid?

      flash[:alert] = 'Registration or order is not in the correct status for confirmation.'
      redirect_to site_root_path
    end

    def validate_order_and_order_item
      if order.blank?
        error_message = "Order not found with ID: #{params[:order_id]}"
        ErrorReporter.report(
          error: StandardError.new(error_message),
          source: 'Site::OrdersController#validate_order_and_order_item',
          severity: :warn,
          context: { order_id: params[:order_id], order_item_uid: params[:order_item_uid] },
        )
        redirect_to site_root_path
        return
      end

      return if order_item.present?

      error_message = "Order item not found with UID: #{params[:order_item_uid]} for order: #{params[:order_id]}"
      ErrorReporter.report(
        error: StandardError.new(error_message),
        source: 'Site::OrdersController#validate_order_and_order_item',
        severity: :warn,
        context: { order_id: params[:order_id], order_item_uid: params[:order_item_uid] },
      )
      redirect_to site_root_path
      nil
    end
  end
end
