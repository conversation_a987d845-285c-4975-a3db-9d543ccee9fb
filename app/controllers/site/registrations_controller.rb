# frozen_string_literal: true

module Site
  class RegistrationsController < ApplicationController
    scout_apm_sample_rate 0.25
    before_action :capture_discount_code
    before_action :redirect_to_alternate_partner, only: :new, if: -> { partners_from_routing_params.blank? }
    after_action :clear_discount_code, only: :create

    rescue_from ActionController::InvalidAuthenticityToken, with: :handle_invalid_authenticity_token

    def new
      partner_program = partner_program_from_routing_params(
        scope:
          PartnerProgram
            .includes(partner: :ecom_payment_methods, program: { cohorts: :sections })
            .merge(Cohort.add_period_active)
            .order('cohorts.starts_on'),
      )

      set_time_zone(partner_program.time_zone)

      @presenter = Registrations::NewPresenter.new(
        partner_program:,
        params_cohort_key: params[:cohort_key],
        params_section_uid: params[:section_uid],
        params_registration_uid: params[:registration_uid],
        learner_cookie_info:,
        discount_code_code: session[:discount_code_code],
      )

      @registration = Registration.new(params[:registration] || { finance_relationship_id: }).tap do |r|
        r.build_site_ad_tracking if r.site_ad_tracking.blank?
      end

      gtm_events.add_page_view(template: 'registration', partner_program:)
    end

    def create
      partner_program = PartnerProgram.uid(params[:partner_program_uid]).first!
      set_time_zone(partner_program.time_zone)

      section = Section.uid(params[:section_uid]).first!
      admin_user = AdminUser.find_by(id: cookies.signed[:admin_user_id])

      registration = Registration::ReplaceCommand.call!(
        email_address: create_registration_params[:email_address],
        learner_attributes: create_registration_params.slice(:first_name, :last_name, :phone),
        site_ad_tracking_attributes:,
        attributes:
          create_registration_params
            .slice(:experience_level, :aspiration, :finance_relationship_id, :eva_type, :tracking_key)
            .merge(section:, admin_user:, partner_program:, promos_referrer: captured_referrer, status: :confirmed),
        context:,
        publish_to_hubspot: true,
      )

      Ecom::Order::CreateFromRegistrationCommand.call!(registration:, discount_code:)

      save_learner_info_to_cookie(registration)

      redirect_to(
        UrlBuilder::Site.new(partner_program:).call!(template: :payment, order_id: registration.ecom_order_item.order.humanized_uid),
        allow_other_host: true,
      )
    rescue ActiveRecord::RecordInvalid => e
      flash.now[:error] = e.message

      partner_program = PartnerProgram.uid(params[:partner_program_uid])
        .includes(:partner, program: { cohorts: :sections })
        .merge(Cohort.add_period_active)
        .order('cohorts.starts_on')
        .first!

      @presenter = Registrations::NewPresenter.new(partner_program:, learner_cookie_info:)
      @registration = registration || Registration.new

      ErrorReporter.report(error: e, source: 'Site::RegistrationsController#create', severity: :warn)
      render :new, status: :unprocessable_content
    end

    def redirect
      slug = params[:program_slug_with_enrollment].split('-enrollment').first
      program = Program.find_by!(slug:)
      partner_program = partner_program_from_routing_params(program:)

      forwarded_params = params.permit!.except(:program_slug_with_enrollment, :partner_identifier, :controller, :action)

      redirect_to_site_page(partner_program:, template: :registration, forwarded_params:)
    end


    private

    def handle_invalid_authenticity_token
      fallback_location = begin
        partner_program = partner_program_from_routing_params
        UrlBuilder::Site.new(partner_program:).call!(template: :registration)
      rescue
        root_path
      end

      redirect_back(
        fallback_location:,
        allow_other_host: true,
        flash: { error: "There was an issue with your submission, please try again." },
      )
    end

    def cannot_show_partner_program?(partner_program:)
      super || !partner_program.configured_for_site?
    end

    def discount_code
      @discount_code ||= begin
        discount_code = Promos::DiscountCode.enabled.code(session[:discount_code_code]).first if session[:discount_code_code]
        discount_code ||= Promos::DiscountCode.enabled.code(Setting.value_for(:referral_discount_code, :code)).first if captured_referrer.present?
        discount_code
      end
    end

    def create_registration_params
      params.expect(
        registration: [:first_name, :last_name, :email_address, :phone, :section_id, :experience_level, :aspiration, :finance_relationship_id,
                       :eva_type, :tracking_key,],
      )
    end

    def site_ad_tracking_attributes
      params.require(:registration) # rubocop:disable Rails/StrongParametersExpect
        .permit(
        site_ad_tracking_attributes: Site::AdTracking::TRACKING_ATTRIBUTES,
      )[:site_ad_tracking_attributes]&.merge(
        referrer: request.referrer,
        hutk_cookie_id: cookies['hubspotutk'],
      )&.compact_blank
    end

    def finance_relationship_id
      @finance_relationship_id ||= Finance::Relationship.uid(params[:finance_relationship_uid]).first&.id
    end

    def context
      {
        pageUri: page_uri,
        pageName: params.dig(:page_title) || 'Registration',
        ipAddress: request.remote_ip,
        hutk: cookies['hubspotutk'],
      }
    end

    def page_uri
      Site::AdTracking::BuildReferrerCommand.call!(
        referrer: request.referrer,
        site_ad_tracking_attributes:,
        request:,
      )
    end

    def redirect_to_alternate_partner
      partner_program = partner_program_from_routing_params
      forwarded_params = params.permit!.except(:program_slug, :partner_identifier, :controller, :action)

      redirect_to_site_page(partner_program:, template: :registration, forwarded_params:)
    end
  end
end
