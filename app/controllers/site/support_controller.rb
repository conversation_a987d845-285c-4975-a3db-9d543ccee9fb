# frozen_string_literal: true

module Site
  class SupportController < Site::ApplicationController
    scout_apm_sample_rate 0.1

    before_action :redirect_to_site_root, unless: -> { Setting.value_for(:development_flags, :zendesk_support_page).present? }

    rescue_from StandardError, with: :handle_support_error
    rescue_from Site::PartnerProgramRouting::ViewablePartnerProgramNotFound, with: :partner_program_not_found

    def show
      set_time_zone(partner_program.time_zone)

      @presenter = Support::ShowPresenter.new(
        partner_program:,
        section_id: params[:section_id],
        content_tags: params[:content_tags],
      )

      gtm_events.add_page_view(template: 'support', partner_program:)
      expires_in 30.minutes, public: true
    end

    private

    def partner_program
      @partner_program ||= partner_program_from_routing_params
    end

    def cannot_show_partner_program?(partner_program:)
      super || !partner_program.configured_for_site?
    end

    def partner_program_not_found
      redirect_to root_path, alert: 'Partner program not found'
    end

    def handle_support_error(exception)
      Rails.error.report(exception, context: { action: 'support_fetch' })
      redirect_to root_path, alert: 'Support articles temporarily unavailable'
    end
  end
end
