# frozen_string_literal: true

module Api
  module V1
    module ReferralRock
      # To develop:
      #   * Sign up for ngrok and run `ngrok http https://localhost:3000`
      #   * On Referral Rock create a new webook pointing at your ngrok url /v1/referral_rock/members
      #     * https://app.referralrock.com/clientadmin/integrations/webhooks/
      #   * Edit your local credentials to set REFERRAL_ROCK_MEMBER_ADD_KEY to the webhook signature key
      #   * Restart your web server
      #   * When you add a new member, the create action should be posted to
      #   * Webhook docs here: https://api.referralrock.com/help/webhook
      class MembersController < ApplicationController
        include Authentication

        skip_forgery_protection

        WEBHOOK_EVENT = 'MemberAdd'

        rescue_from ForbiddenError do
          head :forbidden
        end

        def create
          authenticate_request!(webhook_event:, webhook_event_key:)

          Promos::Referrer::ReplaceFromEmailCommand.call!(
            address: permitted_params[:Email],
            code: permitted_params[:ReferralCode],
          )

          head :ok
        end


        private

        def webhook_event
          WEBHOOK_EVENT
        end

        def webhook_event_key
          Rails.application.credentials.REFERRAL_ROCK_MEMBER_ADD_KEY
        end

        def permitted_params
          params.permit(:Email, :ReferralCode)
        end

      end
    end
  end
end
