# frozen_string_literal: true

module LearningDelivery
  class EmployeesController < ApplicationController

    def show
      employee, permission_level = authorize_for_target_employee!(params[:id])
      redirect_to(edit_learning_delivery_employee_availability_path(employee&.uid)) if !feature_enabled?(:lsa)
      add_breadcrumb('Employee Details')
      @presenter = Employees::ShowPresenter.new(employee:, permission_level:)
    end

  end
end
