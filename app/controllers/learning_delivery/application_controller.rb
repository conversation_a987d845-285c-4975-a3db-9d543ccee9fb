# frozen_string_literal: true

module LearningDelivery
  class ApplicationController < ::ApplicationController
    include Authorization
    include Admin::SetPaperTrailUser
    include SetTimeZone

    layout 'learning_delivery'

    helper_method :current_employee, :top_navbar_presenter, :sidebar_presenter, :breadcrumbs, :feature_enabled?

    rescue_from Authorization::ForbiddenError, with: :forbidden

    before_action :set_breadcrumbs



    private

    def feature_enabled?(feature_name)
      FeatureFlag.enabled?(feature_name)
    end

    def breadcrumbs
      @breadcrumbs ||= []
    end

    def add_breadcrumb(name, path = nil)
      breadcrumbs << Breadcrumb.new(name, path)
    end

    # Override this method in subclasses to set specific breadcrumbs
    def set_breadcrumbs
      if current_admin_user.admin?
        add_breadcrumb('Admin', UrlBuilder::Admin.root_url)
      else
        add_breadcrumb('Home', learning_delivery_employee_profile_path)
      end
    end

    def top_navbar_presenter
      @top_navbar_presenter ||= LearningDelivery::TopNavbarPresenter.new(
        current_admin_user,
      )
    end

    def sidebar_presenter
      @sidebar_presenter ||= LearningDelivery::SidebarPresenter.new(
        current_admin_user,
      )
    end

    def forbidden(error)
      @error = error
      render('admin/error', status: :forbidden, layout: false)
    end
  end
end
