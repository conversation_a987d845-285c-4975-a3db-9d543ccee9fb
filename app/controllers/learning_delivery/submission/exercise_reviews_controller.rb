# frozen_string_literal: true

module LearningDelivery
  module Submission
    class ExerciseReviewsController < ApplicationController
      def update
        authorize! :update, 'LMS'

        exercise_review = Lms::Submission::ExerciseReview.find(params[:id])
        command = Lms::Submission::ExerciseReview::UpdateCommand.new(
          exercise_review:,
          attributes: exercise_review_params,
          current_user: current_admin_user,
        )

        command.call!

        respond_to do |format|
          format.turbo_stream do
            presenter = exercise_presenter(exercise_review)
            render turbo_stream: turbo_stream.replace(
              presenter.dom_id,
              partial: presenter.to_partial_path,
              locals: { presenter: },
            )
          end
        end
      rescue => e
        render json: { status: 'error', errors: [e.message] }, status: :unprocessable_content
      end

      private

      def exercise_review_params
        params.expect(exercise_review: [:grade, :comment, :mark_as_reviewed]).tap do |attrs|
          attrs[:grade] = attrs[:grade].to_i if attrs[:grade].present?
        end
      end

      def exercise_presenter(exercise_review)
        LearningDelivery::Submissions::ShowPresenter::ExerciseReviewPresenter.new(
          exercise_review:,
        )
      end
    end
  end
end
