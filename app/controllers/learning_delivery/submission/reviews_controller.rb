# frozen_string_literal: true

module LearningDelivery
  module Submission
    class ReviewsController < ApplicationController
      def update
        authorize! :update, 'LMS'

        review_attributes = review_params
        review = Lms::Submission::Review.find(params[:id])

        command = Lms::Submission::Review::ReplaceCommand.new(
          review:,
          attributes: review_attributes,
          current_user: current_admin_user,
        )

        command.call!

        respond_to do |format|
          format.turbo_stream do
            presenter = LearningDelivery::Submissions::ShowPresenter::ReviewPresenter.new(
              submission: review.submission,
            )
            render turbo_stream: turbo_stream.replace(
              presenter.dom_id,
              partial: presenter.to_partial_path,
              locals: { presenter: },
            )
          end
        end
      rescue => e
        render json: { status: 'error', errors: [e.message] }, status: :unprocessable_content
      end

      private

      def find_review
        @review = Lms::Submission::Review.find(@review)
      end

      def review_params
        params.expect(review: [:grade, :comment]).tap do |attrs|
          attrs[:grade] = attrs[:grade].to_i if attrs[:grade].present?
        end
      end
    end
  end
end
