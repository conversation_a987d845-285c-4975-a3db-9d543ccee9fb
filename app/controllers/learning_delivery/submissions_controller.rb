# frozen_string_literal: true

module LearningDelivery
  class SubmissionsController < ApplicationController
    def index
      authorize! :read, 'LMS'
      @presenter = Submissions::IndexPresenter.new(
        params: index_permitted_params,
        current_employee:,
      )
    end

    def show
      authorize! :read, 'L<PERSON>'
      add_breadcrumb('Submission Details')
      @presenter = Submissions::ShowPresenter.new(
        submission: Lms::Submission.find(params[:id]),
        params: show_permitted_params,
        current_employee:,
      )
    end

    def publish
      authorize! :update, 'LMS'

      submission = Lms::Submission.find(params[:id])

      Lms::Submission::MarkAsManuallyGradedCommand.new(
        submission:,
        current_user: current_admin_user,
      ).call!

      Lms::Submission::PublishJob.perform_async(params[:id])

      render json: { status: 'success', message: 'Successfully published to Canvas' }
    rescue => e
      render json: { status: 'error', errors: [e.message] }, status: :unprocessable_content
    end

    private

    def set_breadcrumbs
      super
      add_breadcrumb('Submissions', learning_delivery_submissions_path(params: index_permitted_params))
    end

    def index_permitted_params
      params.permit(:review_status, :section_id, :assignment_template_id, :learner_id, :filter, :page, :per_page)
    end

    def show_permitted_params
      params.permit(index_permitted_params.keys)
    end

  end
end
