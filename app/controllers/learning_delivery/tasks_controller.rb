# frozen_string_literal: true

module LearningDelivery
  class TasksController < LearningDelivery::ApplicationController
    def index
      authorize! :read, 'Learning Delivery'
      @presenter = Tasks::IndexPresenter.new(
        params: index_permitted_params,
        current_admin_user:,
      )
    end

    def show
      authorize! :read, 'Learning Delivery'

      add_breadcrumb(params[:id])

      update_task_to_read
      @show_presenter = Tasks::TaskModalPresenter.new(
        task_id: params[:id],
        params: show_permitted_params,
        current_admin_user:,
      )

      respond_to do |format|
        format.turbo_stream do
          render turbo_stream: [
            turbo_stream.replace('task_modal',
              partial: 'learning_delivery/tasks/task_modal',
              locals: { show_presenter: @show_presenter },
            ),
            turbo_stream.update('breadcrumbs', partial: 'learning_delivery/breadcrumbs'),
          ]
        end
        format.html do
          @presenter = Tasks::IndexPresenter.new(
            params: show_permitted_params,
            current_admin_user:,
          )
          render 'index'
        end
      end
    end

    def update
      authorize! :write, 'Learning Delivery'

      if update_permitted_params[:status].present?
        command = LearningDelivery::Task::UpdateStatusCommand.new(
          task:,
          new_status: update_permitted_params[:status],
          user: current_admin_user,
        )

        if command.call
          render json: { status: 'success', message: "Task status updated to #{update_permitted_params[:status]}" }
        else
          render json: { status: 'error', errors: ['Failed to update task status'] }, status: :unprocessable_content
        end
      else
        render json: { status: 'error', errors: ['Status parameter is required'] }, status: :unprocessable_content
      end
    end

    private

    def update_task_to_read
      return unless task.status.to_sym.in?(Task::UpdateStatusCommand::VALID_STATUSES_FOR_TRANSITION_TO[:viewed])

      LearningDelivery::Task::UpdateStatusCommand.new(
        task:,
        new_status: :viewed,
      ).call
    rescue StandardError => e
      Rails.logger.error("Failed to update task read status: #{e.message}")
    end

    def task
      @task ||= LearningDelivery::Task.find_from_uid!(params[:id])
    end

    def set_breadcrumbs
      super
      add_breadcrumb('Tasks', learning_delivery_tasks_path(params: index_permitted_params))
    end

    def index_permitted_params
      permitted = [:status, :section_id, :filter, :page, :per_page]
      permitted += [:owner_id, :assigned_by_id] if current_admin_user&.admin?
      params.permit(permitted)
    end

    def show_permitted_params
      params.permit(index_permitted_params.keys + [:id])
    end

    def update_permitted_params
      params.permit(:status)
    end
  end
end
