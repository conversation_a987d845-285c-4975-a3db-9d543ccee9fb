# frozen_string_literal: true

module LearningDelivery
  class LearnersController < LearningDelivery::ApplicationController

    def index
      section = if params[:section_uid].present?
        if current_admin_user.admin?
          learner_and_section[:section]
        else
          current_employee.advocate_sections.active.find_from_uid(params[:section_uid])
        end
      end
      learner = learner_and_section[:learner]
      @presenter = Learners::IndexPresenter.new(section:, learner:, current_admin_user:, params: index_params)
    end

    def show
      learner = learner_and_section[:learner]
      section = learner_and_section[:section]
      enrollment = Enrollment.find_by(uid: params[:enrollment_uid])
      page = params[:page]
      per_page = params[:per_page]
      tab = params[:tab]
      @presenter = Learners::ShowPresenter.new(learner:, section:, enrollment:, per_page:, page:, tab:)
    end

    def risk_info
      risk = LearningDelivery::RiskAssessment.find(params[:risk_id])
      @presenter = Learners::LearnerRiskTypeElementDescriptionPresenter.new(risk:)
      respond_to do |format|
        format.turbo_stream
      end
    end

    private

    def learner_and_section
      section = Section.find_from_uid(params[:section_uid])
      learner = Learner.find_from_uid(params[:learner_uid] || params[:id])

      { section:, learner: }
    end

    def set_breadcrumbs
      super
      add_breadcrumb('Learner List', learning_delivery_learners_path)
    end

    def index_params
      params.permit(:page, :per_page, :section_uid, :learner_uid, :risk_value)
    end

    def show_params
      params.permit(:page, :per_page, :section_uid, :learner_uid, :enrollment_uid, :tab)
    end

  end
end
