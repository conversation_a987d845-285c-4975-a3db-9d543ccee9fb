# frozen_string_literal: true

module LearningDelivery
  class Employee
    class AvailabilitiesController < LearningDelivery::ApplicationController
      def edit
        employee, permission_level = authorize_for_target_employee!(params[:employee_id])
        add_breadcrumb('Edit Availability')
        @presenter = LearningDelivery::Employee::Availabilities::EditPresenter.new(employee:, permission_level:)
      end

      def update
        employee, permission_level = authorize_for_target_employee!(params[:employee_id])

        LearningDelivery::Employee::ReplaceCommand.call!(
          employee:,
          attributes: update_attributes,
        )

        LearningDelivery::Employee::ReplaceAvailabilitiesCommand.call!(employee:, slot_statuses:)

        path = if employee == current_employee
          learning_delivery_profile_availability_path
        else
          edit_learning_delivery_employee_availability_path(employee_id: employee.humanized_uid)
        end

        redirect_to path, notice: "Availability updated successfully"
      rescue ActiveRecord::RecordInvalid, ArgumentError => e
        flash[:error] = e.message
        @presenter = LearningDelivery::Employee::Availabilities::EditPresenter.new(employee:, permission_level:)
        render :edit
      end


      private

      def update_attributes
        @update_attributes ||= params.expect(
          employee: [:availability_notes,
                     :max_concurrent_sections,],
        )
      end

      def availability_params
        @availability_params ||= params.require(:availabilities).permit!
      end

      def slot_statuses
        @slot_statuses ||= availability_params.to_h.transform_keys do |key|
          AvailabilitySlot.find_by(id: key)
        end
      end
    end
  end
end
