# frozen_string_literal: true

module LearningDelivery
  class SectionsController < LearningDelivery::ApplicationController

    def index
      @presenter = LearningDelivery::Sections::IndexPresenter.new(
        employee: current_employee,
        params: index_params,
      )
    end

    private

    def set_breadcrumbs
      super
      add_breadcrumb('All Sections', learning_delivery_sections_path)
    end

    def index_params
      params.permit(:page, :per_page, :section_filter)
    end

  end
end
