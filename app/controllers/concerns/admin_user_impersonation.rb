# frozen_string_literal: true

# This Concern provides functionality for Admin User impersonation for a controller by implementing a #start and #end action.
# The original authenticated admin user is stored in the session during "start" and restored during "end".
# Both #start and #end are GET requests since often redirected to after OAuth login.
# A digest is used to verify the authenticity of the impersonation request as having come from the authenticated admin user.
# There are methods to build URLs for starting and ending impersonation across different subdomains.
module AdminUserImpersonation
  extend ActiveSupport::Concern

  SUBDOMAIN_PATH_PREFIX = {
    'admin' => nil,
    'learning-delivery' => 'learning_delivery',
  }.freeze

  class << self
    def allow_subdomain?(authenticated_admin_user:, impersonated_admin_user:, subdomain:)
      case subdomain
      when 'admin'
        [
          authenticated_admin_user.permissions.find_by(resource_group: 'Admin - Impersonation')&.write?,
          impersonated_admin_user.admin?, # only authenticated "admin" (i.e. non-Learning Delivery) users can view admin pages
        ].all?
      when 'learning-delivery'
        [
          authenticated_admin_user.permissions.find_by(resource_group: 'Learning Delivery - Impersonation')&.write?,
          impersonated_admin_user.learning_delivery_employee.present?,
        ].all?
      else
        false
      end
    end

    # @return [AdminUser, nil] Returns the authenticated admin user from the session. `nil` returned if not impersonating another admin user.
    def authenticated_admin_user(session)
      session[:authenticated_admin_user_id].presence && AdminUser.find(session[:authenticated_admin_user_id])
    end

    # This method is used to build the URL to start admin user impersonation.
    # It will impersonate an admin user on both the Admin and Learning Delivery subdomains.
    # The URL includes a digest for security that must match with the original authenticated admin user that is stored in the session.
    #
    # The redirect_to parameter is also included to specify where to redirect after impersonation.
    # This must be a full URL, including the hostname, since impersonation occurs across multiple subdomains.
    #
    # @param authenticated_admin_user [AdminUser] The admin user that is authenticated via OAuth.
    # @param impersonated_admin_user [AdminUser] The admin user being impersonated.
    # @param redirect_to [String] The URL to redirect to after impersonation. This should include the hostname, not just the path.
    # @param subdomains [String, Array<String>] The subdomain(s) for impersonation.
    # @return [String] The full URL for the impersonation action.
    def build_start_url(authenticated_admin_user:, impersonated_admin_user:, redirect_to:, subdomains: ['admin'])
      Array.wrap(subdomains).inject(redirect_to) do |redirect_to_url, subdomain|
        url_builder = UrlBuilder.const_get(subdomain.to_s.underscore.camelize)

        url_method = [SUBDOMAIN_PATH_PREFIX.fetch(subdomain), 'admin_users_start_impersonation_url'].compact.join('_')

        url_builder.send(
          url_method,
          admin_user_id: impersonated_admin_user.id,
          redirect_to: redirect_to_url,
          digest: build_digest(
            authenticated_admin_user:,
            impersonated_admin_user:,
          ),
        )
      end
    end

    # This method is used to build the URL to end admin user impersonation.
    # It will end impersonation an admin user on both the Admin and Learning Delivery subdomains.
    # The URL includes a digest for security that must match with the original authenticated admin user that is stored in the session.
    #
    # The redirect_to parameter is also included to specify where to redirect after impersonation.
    # This must be a full URL, including the hostname, since impersonation occurs across multiple subdomains.
    #
    # @param authenticated_admin_user [AdminUser] The admin user that is authenticated via OAuth.
    # @param impersonated_admin_user [AdminUser] The admin user being impersonated.
    # @param redirect_to [String] The URL to redirect to after impersonation. This should include the hostname, not just the path.
    # @param subdomains [String, Array<String>] The subdomain(s) for impersonation.
    # @return [String] The full URL for the impersonation action.
    def build_end_url(authenticated_admin_user:, impersonated_admin_user:, redirect_to:, subdomains: ['admin'])
      Array.wrap(subdomains).inject(redirect_to) do |redirect_to_url, subdomain|
        url_builder = UrlBuilder.const_get(subdomain.to_s.underscore.camelize)

        url_method = [SUBDOMAIN_PATH_PREFIX.fetch(subdomain), 'admin_users_end_impersonation_url'].compact.join('_')

        url_builder.send(
          url_method,
          redirect_to: redirect_to_url,
          digest: build_digest(
            authenticated_admin_user:,
            impersonated_admin_user:,
          ),
        )
      end
    end

    private

    def build_digest(authenticated_admin_user:, impersonated_admin_user:)
      value = [
        Rails.application.secret_key_base, # unique for each environment
        'admin_user_impersonation',
        authenticated_admin_user.id,
        impersonated_admin_user.id,
      ].join(':')

      Digest::SHA256.base64digest(value)
    end
  end

  def start
    # Already impersonating another user, so return to having the authenticated admin user as the current_admin_user.
    authenticated_admin_user = if session[:authenticated_admin_user_id].present?
      AdminUser.find(session[:authenticated_admin_user_id]).tap do |admin_user|
        bypass_sign_in(admin_user)
        session.delete(:authenticated_admin_user_id)
      end
    else
      current_admin_user
    end

    impersonated_admin_user = AdminUser.find(params[:admin_user_id])

    verify_authorized_admin_user!(authenticated_admin_user:, impersonated_admin_user:)

    verify_digest!(authenticated_admin_user:, impersonated_admin_user:)

    bypass_sign_in(impersonated_admin_user)

    session[:authenticated_admin_user_id] = authenticated_admin_user.id

    redirect_to redirect_to_param, allow_other_host: true
  end

  def end
    raise "Not impersonating any user" if session[:authenticated_admin_user_id].nil?

    authenticated_admin_user = AdminUser.find(session[:authenticated_admin_user_id])
    impersonated_admin_user = current_admin_user

    verify_authorized_admin_user!(authenticated_admin_user:, impersonated_admin_user:)

    verify_digest!(authenticated_admin_user:, impersonated_admin_user:)

    bypass_sign_in(authenticated_admin_user)

    session.delete(:authenticated_admin_user_id)

    redirect_to redirect_to_param, allow_other_host: true
  end

  private

  def redirect_to_param
    @redirect_to_param ||= params[:redirect_to] || (raise "redirect_to parameter is required")
  end

  def digest_param
    @digest_param ||= params[:digest] || (raise "digest parameter is required")
  end

  def verify_authorized_admin_user!(authenticated_admin_user:, impersonated_admin_user:)
    return if AdminUserImpersonation.allow_subdomain?(authenticated_admin_user:, impersonated_admin_user:, subdomain: request.subdomain)

    raise "You must be signed in as an admin user to impersonate"
  end

  def verify_digest!(authenticated_admin_user:, impersonated_admin_user:)
    digest = AdminUserImpersonation.send(:build_digest, authenticated_admin_user:, impersonated_admin_user:)

    return if digest_param == digest

    raise "Invalid digest"
  end
end
