# frozen_string_literal: true

module LearningDelivery
  module Authorization
    extend ActiveSupport::Concern

    ForbiddenError = Class.new(StandardError)

    included do
      before_action :authenticate_admin_user!
    end

    def current_employee
      current_admin_user&.learning_delivery_employee
    end


    private

    def authorize!(action, resource)
      return if Permission::AuthorizeCommand.call!(admin_user: current_admin_user, action:, resource:)

      raise ForbiddenError
    end

    def authorize_for_target_employee!(employee_uid)
      target_employee = Employee.find_from_uid(employee_uid)
      employee, permission_level = if employee_uid.present? && current_employee != target_employee
        authorize! :read, 'Learning Delivery - Management'
        [target_employee, :manager]
      elsif employee_uid.blank? || current_employee == target_employee
        authorize! :read, 'Learning Delivery'
        [current_employee, :employee]
      end
      raise ActiveRecord::RecordNotFound if employee.nil?

      [employee, permission_level]
    end
  end
end
