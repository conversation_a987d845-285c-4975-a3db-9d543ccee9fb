doctype html
html[lang="en"]
  head
    title= yield(:meta_title) || "Ziplines Education"
    meta[name="description" content=(yield(:meta_description) || "Ziplines Education is a platform that helps students learn and grow through personalized learning experiences.")]
    meta content="width=device-width,initial-scale=1" name="viewport"
    = csrf_meta_tags
    = csp_meta_tag

    = stylesheet_link_tag "tailwind", "data-turbo-track": "reload"
    = stylesheet_link_tag "site", "data-turbo-track": "reload"

    / TODO: Maybe reeplace this with Importmap
    = stylesheet_link_tag "https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css"
    = javascript_include_tag "https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"

    = javascript_importmap_tags

    - # Calendly
    = javascript_include_tag("https://assets.calendly.com/assets/external/widget.js", async: true)
    = stylesheet_link_tag("https://assets.calendly.com/assets/external/widget.css")

    - # Google Tag Manager
    javascript:
      (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+#{['i', 'dl', "'#{Site::GtmEvents.id_suffix}'"].compact.join('+').html_safe};f.parentNode.insertBefore(j,f);
      })(window,document,'script','dataLayer','GTM-KWLJKZN');

    - # VWO
    - if @presenter.try(:ab_testing_html).present?
      = raw @presenter.ab_testing_html
    - else
      link[rel="preconnect" href="https://dev.visualwebsiteoptimizer.com"]
      script#vwoCode[type='text/javascript']
        | window._vwo_code || (function() {
        | var account_id=605027,
        | version=2.1,
        | settings_tolerance=2000,
        | hide_element='body',
        | hide_element_style = 'opacity:0 !important;filter:alpha(opacity=0) !important;background:none !important;transition:none !important;',
        | f=false,w=window,d=document,v=d.querySelector('#vwoCode'),cK='_vwo_'+account_id+'_settings',cc={};try{var c=JSON.parse(localStorage.getItem('_vwo_'+account_id+'_config'));cc=c&&typeof c==='object'?c:{}}catch(e){}var stT=cc.stT==='session'?w.sessionStorage:w.localStorage;code={nonce:v&&v.nonce,use_existing_jquery:function(){return typeof use_existing_jquery!=='undefined'?use_existing_jquery:undefined},library_tolerance:function(){return typeof library_tolerance!=='undefined'?library_tolerance:undefined},settings_tolerance:function(){return cc.sT||settings_tolerance},hide_element_style:function(){return'{'+(cc.hES||hide_element_style)+'}'},hide_element:function(){if(performance.getEntriesByName('first-contentful-paint')[0]){return''}return typeof cc.hE==='string'?cc.hE:hide_element},getVersion:function(){return version},finish:function(e){if(!f){f=true;var t=d.getElementById('_vis_opt_path_hides');if(t)t.parentNode.removeChild(t);if(e)(new Image).src='https://dev.visualwebsiteoptimizer.com/ee.gif?a='+account_id+e}},finished:function(){return f},addScript:function(e){var t=d.createElement('script');t.type='text/javascript';if(e.src){t.src=e.src}else{t.text=e.text}v&&t.setAttribute('nonce',v.nonce);d.getElementsByTagName('head')[0].appendChild(t)},load:function(e,t){var n=this.getSettings(),i=d.createElement('script'),r=this;t=t||{};if(n){i.textContent=n;d.getElementsByTagName('head')[0].appendChild(i);if(!w.VWO||VWO.caE){stT.removeItem(cK);r.load(e)}}else{var o=new XMLHttpRequest;o.open('GET',e,true);o.withCredentials=!t.dSC;o.responseType=t.responseType||'text';o.onload=function(){if(t.onloadCb){return t.onloadCb(o,e)}if(o.status===200||o.status===304){_vwo_code.addScript({text:o.responseText})}else{_vwo_code.finish('&e=loading_failure:'+e)}};o.onerror=function(){if(t.onerrorCb){return t.onerrorCb(e)}_vwo_code.finish('&e=loading_failure:'+e)};o.send()}},getSettings:function(){try{var e=stT.getItem(cK);if(!e){return}e=JSON.parse(e);if(Date.now()>e.e){stT.removeItem(cK);return}return e.s}catch(e){return}},init:function(){if(d.URL.indexOf('__vwo_disable__')>-1)return;var e=this.settings_tolerance();w._vwo_settings_timer=setTimeout(function(){_vwo_code.finish();stT.removeItem(cK)},e);var t;if(this.hide_element()!=='body'){t=d.createElement('style');var n=this.hide_element(),i=n?n+this.hide_element_style():'',r=d.getElementsByTagName('head')[0];t.setAttribute('id','_vis_opt_path_hides');v&&t.setAttribute('nonce',v.nonce);t.setAttribute('type','text/css');if(t.styleSheet)t.styleSheet.cssText=i;else t.appendChild(d.createTextNode(i));r.appendChild(t)}else{t=d.getElementsByTagName('head')[0];var i=d.createElement('div');i.style.cssText='z-index: ********** !important;position: fixed !important;left: 0 !important;top: 0 !important;width: 100% !important;height: 100% !important;background: white !important;display: block !important;';i.setAttribute('id','_vis_opt_path_hides');i.classList.add('_vis_hide_layer');t.parentNode.insertBefore(i,t.nextSibling)}var o=window._vis_opt_url||d.URL,s='https://dev.visualwebsiteoptimizer.com/j.php?a='+account_id+'&u='+encodeURIComponent(o)+'&vn='+version;if(w.location.search.indexOf('_vwo_xhr')!==-1){this.addScript({src:s})}else{this.load(s+'&x=true')}}};w._vwo_code=code;code.init();})();


    = content_for(:head)

  body[class="#{content_for(:body_css_class)} #{Rails.env}-env" data-controller="scrolling" data-env="#{Rails.env}"]
    - # Google Tag Manager (noscript)
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KWLJKZN#{Site::GtmEvents.id_suffix.html_safe}" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>

    = link_to('Skip to main content', "#main-content", class: "z-50 border-white text-white mix-blend-normal bg-red-700 rounded p-5 text-3xl left-2 transition-[top] duration-1000 absolute top-[-100px] focus:top-2.5")

    = yield

    - # Hubspot Tracking
    - if Rails.env.production?
      = javascript_include_tag '//js.hs-scripts.com/3461273.js', id: 'hs-script-loader', async: true
    - elsif Rails.env.staging?
      = javascript_include_tag '//js.hs-scripts.com/46992314.js', id: 'hs-script-loader', async: true

    = content_for(:scripts)

    javascript:
      // Reload Hubspot Widget on Turbo Navigation
      document.addEventListener('turbo:load', function() {
        var script = document.getElementById('hs-script-loader');
        if (script) {
          window.HubSpotConversations.resetAndReloadWidget();
        }
      });

      // Enhanced HubSpot widget support for programmatic control
      document.addEventListener('DOMContentLoaded', function() {
        // Check if chat should auto-open from URL hash
        if (window.location.hash === '#hs-chat-open') {
          var attempts = 0;
          var maxAttempts = 20;
          var checkAndOpen = function() {
            if (window.HubSpotConversations && window.HubSpotConversations.widget && window.HubSpotConversations.widget.open) {
              try {
                window.HubSpotConversations.widget.open();
                // Remove hash after opening
                history.replaceState(null, null, window.location.pathname + window.location.search);
              } catch (error) {
                console.warn('Failed to open HubSpot chat from URL hash:', error);
              }
            } else if (attempts < maxAttempts) {
              attempts++;
              setTimeout(checkAndOpen, 250);
            }
          };
          checkAndOpen();
        }
      });

      // Reload TrustPilot Widget on Turbo Navigation
      document.addEventListener('turbo:render', function(){
        const trustbox = document.getElementById('trustpilot-widget');
        window.Trustpilot.loadFromElement(trustbox);
      });

    - gtm_events.each do |event|
      javascript:
        window.dataLayer = window.dataLayer || [];
        window.dataLayer.push(#{event.to_json.html_safe});

