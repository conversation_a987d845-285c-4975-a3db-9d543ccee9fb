doctype html
html[lang="en"]
  head
    title= yield(:meta_title) || breadcrumbs.map(&:name).reverse.join(" | ") || "Ziplines Education"
    meta content="width=device-width,initial-scale=1" name="viewport"
    = csrf_meta_tags
    = csp_meta_tag

    = stylesheet_link_tag "tailwind", "data-turbo-track": "reload"
    = stylesheet_link_tag "learning_delivery", "data-turbo-track": "reload"
    = stylesheet_link_tag "https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"
    = stylesheet_link_tag "https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:ital,wght@0,100..700;1,100..700&display=swap"
    = stylesheet_link_tag "https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/css/tom-select.default.min.css"
    = javascript_importmap_tags

    = content_for(:head)

  body class="#{content_for(:body_css_class)} #{Rails.env}-env #{controller.full_controller_name} #{controller.full_controller_name}_#{controller.action_name}" data-env="#{Rails.env}"
    .bg-bg-secondary-grey-zip.h-full.px-0.min-h-screen
      .w-full
        - flash.each do |type, message|
          = render_presenter(FlashMessagePresenter.new(type:, message:))

        / Learning Delivery Navigation Layout
        .main-container.flex.fixed.w-full
          = render_presenter(sidebar_presenter)
          = render_presenter(top_navbar_presenter)

          / Main Content Area
          .main-content.w-full.relative
            = yield

    = content_for(:scripts)
