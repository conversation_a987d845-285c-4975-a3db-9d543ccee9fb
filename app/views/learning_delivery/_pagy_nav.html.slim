- pagy = presenter.pagy_info
- if pagy && pagy.pages > 1
  nav.flex.items-center.gap-x-2
    / First page button
    - if pagy.page > 1
      = link_to pagy_url_for(pagy, 1), class: 'p-2 text-gray-400 hover:text-gray-600 transition-colors', 'aria-label': 'First page' do
        i.fa-solid.fa-angles-left

    / Previous button
    - if pagy.prev
      = link_to pagy_url_for(pagy, pagy.prev), class: 'p-2 text-gray-400 hover:text-gray-600 transition-colors', 'aria-label': 'Previous page' do
        i.fa-solid.fa-angle-left
    - else
      span.p-2.text-gray-300.cursor-not-allowed
        i.fa-solid.fa-angle-left

    / Page numbers
    - pagy.series.each do |item|
      - if item.is_a?(String)
        span.px-3.py-1.bg-blue-zip.text-white.rounded.text-sm.font-medium.text-center
          = item

      - elsif item.is_a?(Integer)
        = link_to item, pagy_url_for(pagy, item), class: 'min-w-[32px] px-3 py-1 text-gray-600 hover:bg-gray-100 rounded text-sm text-center transition-colors'
      - elsif item == :gap
        span.px-2.text-gray-400.text-sm ...

    / Next button
    - if pagy.next
      = link_to pagy_url_for(pagy, pagy.next), class: 'p-2 text-gray-400 hover:text-gray-600 transition-colors', 'aria-label': 'Next page' do
        i.fa-solid.fa-angle-right
    - else
      span.p-2.text-gray-300.cursor-not-allowed
        i.fa-solid.fa-angle-right

    / Last page button
    - if pagy.page < pagy.pages
      = link_to pagy_url_for(pagy, pagy.pages), class: 'p-2 text-gray-400 hover:text-gray-600 transition-colors', 'aria-label': 'Last page' do
        i.fa-solid.fa-angles-right
