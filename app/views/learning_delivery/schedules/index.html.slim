= turbo_frame_tag('schedule') do
  .flex.items-start.justify-between.gap-x-8

    .flex.flex-col.gap-y-6.rounded-lg.border.border-gray-200.bg-white.py-3.px-6[class="w-[55%]"]
      .sticky.top-0.bg-white.z-10.py-3.drop-shadow-md
        .text-2xl.font-semibold.text-center Sections
        .flex.justify-center= render_presenter(@presenter.section_filters_presenter)
      .flex.flex-col= render_presenters(@presenter.section_presenters)

    .flex.flex-col.rounded-lg.border.border-gray-200.bg-white.py-3.px-6.sticky.top-0[class="w-[45%]"]
      .sticky.top-0.bg-white.z-10.pt-3.pb-9.drop-shadow-md
        .text-2xl.font-semibold.text-center People
      .flex.flex-col.gap-y-6.overflow-y-auto[class="max-h-[80vh]"]= render_presenters(@presenter.employee_presenters)
