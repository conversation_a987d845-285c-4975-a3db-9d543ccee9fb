// Single sidebar that expands/contracts
.side-navbar.fixed.h-screen.top-0.left-0.z-40.bg-bg-secondary-grey-zip.transition-all.duration-300[
  data-controller="expandable toggle"
  data-action="mouseenter->expandable#open mouseleave->expandable#close mouseleave->toggle#hide"
  data-expandable-target="expandable"
  class="w-14"
]
  // Logo section (blue)
  .brand-icon.bg-blue-zip.p-2.pt-0.flex.items-center.min-h-60.justify-between
    // Logo - changes based on sidebar state
    .logo-container.flex.items-center
      = image_tag 'site/ziplines/logo_short_form.svg', alt: "Ziplines logo", class: "slim-logo"
      = image_tag 'learning_delivery/icons/learning-delivery-zipline-logo.svg', alt: "Ziplines logo", class: "expanded-logo hidden"

    // Close button - only visible when expanded
    button.text-white.hidden.expanded-only[data-action="click->expandable#close" title="Collapse sidebar"]
      i.fa.fa-angles-left

  // Navigation section
  nav.flex.flex-col.p-2.mt-1.h-full

    // Dashboard link
    - if presenter.employee && feature_enabled?(:lsa)
      a.nav-item.flex.items-center.rounded-md.bg-blue-zip.text-white.p-2.mb-2[href=learning_delivery_employee_profile_path]
        i.fas.fa-home.icon-fixed-width
        span.ml-2.nav-text.hidden Dashboard
    - elsif presenter.active_admin_path
      a.nav-item.flex.items-center.rounded-md.bg-blue-zip.text-white.p-2.mb-2[href=presenter.active_admin_path]
        i.fas.fa-home.icon-fixed-width
        span.ml-2.nav-text.hidden Active Admin


    - if feature_enabled?(:lsa)

      // Section divider (only visible in slim mode)
      .section-divider.w-10.border-t.border-solid.border-gray-300.my-4.mx-auto.slim-only

      // Sections & Modules header - only visible when expanded
      .section-header.hidden.expanded-only.mt-2
        span.text-gray-400.text-sm.p-4.ml-5.bg-bg-secondary-grey-zip Sections & Module
        .border-b.border-solid.border-gray-300.-mt-3

      // Sections icon/link with dropdown
      .nav-item.text-gray-700.rounded-md.hover:bg-gray-100.mt-2
        .w-full.items-center.relative[data-action="click->toggle#toggle"]
          .gap-2.inline
            i.fas.fa-book.icon-fixed-width
            span.ml-2.nav-text.hidden Active Sections
          i.fa.fa-angle-down.right-4.absolute.hidden.expanded-only

        // Toggleable content
        .hidden.w-full.pl-2[data-toggle-target="toggleable"]
          - presenter.active_sections.each do |section|
            // Each section with its own toggle controller
            .section-item.py-1.w-full[data-controller="toggle"]
              // Section title that toggles its own content
              .flex.w-full.items-center.rounded-md.hover:bg-gray-100.p-2[data-action="click->toggle#toggle"]
                span.w-2.h-2.rounded-full.bg-gray-400.mr-2
                span.text-sm #{presenter.section_title(section)}
                i.fa.fa-angle-down.ml-auto

              .hidden.pl-4[data-toggle-target="toggleable"]
                .py-1
                  .flex.items-center.gap-2.w-full.rounded-md.hover:bg-gray-100.p-2
                    span.w-2.h-2.rounded-full.bg-gray-400
                    span.text-sm Learners
                .py-1
                  a.flex.items-center.gap-2.w-full.rounded-md.hover:bg-gray-100.p-2
                    span.w-2.h-2.rounded-full.bg-gray-400
                    span.text-sm Tasks
                .py-1
                  a.flex.items-center.gap-2.w-full.rounded-md.hover:bg-gray-100.p-2
                    span.w-2.h-2.rounded-full.bg-gray-400
                    span.text-sm Assignments

      // All Sections icon/link
      a.nav-item.flex.items-center.text-gray-700.p-2.rounded-md.hover:bg-gray-100.mt-4[href=learning_delivery_sections_path]
        i.fas.fa-layer-group.icon-fixed-width
        span.ml-2.nav-text.hidden All Sections
      // NPS icon/link
      .nav-item.flex.items-center.text-gray-700.p-2.rounded-md.hover:bg-gray-100.mt-2
        i.fas.fa-chart-column.icon-fixed-width
        span.ml-2.nav-text.hidden All NPS

    - if feature_enabled?(:grading)
      // Section divider (only visible in slim mode)
      .section-divider.w-10.border-t.border-solid.border-gray-300.my-4.mx-auto.slim-only

      // Activity header - only visible when expanded
      .section-header.hidden.expanded-only.mt-2
        span.text-gray-400.text-sm.p-4.ml-5.bg-bg-secondary-grey-zip Activity
        .border-b.border-solid.border-gray-300.-mt-3

      - if feature_enabled?(:grading)
        // Grading icon/link
        a.nav-item.flex.items-center.text-gray-700.p-2.rounded-md.hover:bg-gray-100.mt-2[href=learning_delivery_submissions_path]
          i.fas.fa-file-circle-check.icon-fixed-width
          span.ml-2.nav-text.hidden Grading

      - if feature_enabled?(:lsa)
        // Tasks icon/link
        a.nav-item.flex.items-center.text-gray-700.p-2.rounded-md.hover:bg-gray-100.mt-4
          i.fas.fa-tasks.icon-fixed-width
          span.ml-2.nav-text.hidden Tasks


    - if feature_enabled?(:lsa)
      // Section divider (only visible in slim mode)
      .section-divider.w-10.border-t.border-solid.border-gray-300.my-4.mx-auto.slim-only

      // Analytics & Reports header - only visible when expanded
      .section-header.hidden.expanded-only.mt-2
        span.text-gray-400.text-sm.p-4.ml-5.bg-bg-secondary-grey-zip Analytics & Reports
        .border-b.border-solid.border-gray-300.-mt-3

      // Analytics icon/link
      .nav-item.flex.items-center.text-gray-700.p-2.rounded-md.hover:bg-gray-100.mt-2
        i.fas.fa-chart-line.icon-fixed-width
        span.ml-2.nav-text.hidden Analytics

      // Reports icon/link
      .nav-item.flex.items-center.text-gray-700.p-2.rounded-md.hover:bg-gray-100.mt-2
        i.fas.fa-file-alt.icon-fixed-width
        span.ml-2.nav-text.hidden Reports

    // Section divider (only visible in slim mode)
    .section-divider.w-10.border-t.border-solid.border-gray-300.my-4.mx-auto.slim-only

    // My Account header - only visible when expanded
    .section-header.hidden.expanded-only.mt-2
      span.text-gray-400.text-sm.p-4.ml-5.bg-bg-secondary-grey-zip My Account
      .border-b.border-solid.border-gray-300.-mt-3

        // Availability icon/link
    - if presenter.employee
      a.nav-item.flex.items-center.text-gray-700.p-2.rounded-md.hover:bg-gray-100.mt-2[href= edit_learning_delivery_employee_availability_path(presenter.employee.uid)]
        i.fa-regular.fa-calendar-days.icon-fixed-width
        span.ml-2.nav-text.hidden My Availability

    - if feature_enabled?(:lsa)
      // Help icon/link
      .nav-item.flex.items-center.text-gray-700.p-2.rounded-md.hover:bg-gray-100.mt-2
        i.fa-solid.fa-question.icon-fixed-width
        span.ml-2.nav-text.hidden Help

    // Logout icon/link
    = link_to destroy_admin_user_session_path, method: :delete, class: "nav-item flex items-center text-gray-700 p-2 rounded-md hover:bg-gray-100 mt-2"
      i.fa-solid.fa-arrow-right-from-bracket.icon-fixed-width
      span.ml-2.nav-text.hidden Logout
