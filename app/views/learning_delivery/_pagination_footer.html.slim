.px-6.py-3.border-t.border-gray-200.bg-gray-50
  .flex.items-center.justify-end.whitespace-nowrap
    span.text-sm.text-gray-600.mr-2 Rows per page:
    = select_tag :per_page,
      options_for_select(presenter.option_items, presenter.limit),
      class: 'text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 mr-4',
      data: { controller: 'pagination', pagination_base_url_value: url_for(request.query_parameters.except(:per_page, :page)), action: 'change->pagination#handleChange' }
    span.text-sm.text-gray-600.mr-4
      = "#{presenter.from}-#{presenter.to} of #{presenter.count}"
    - if presenter.pages > 1
      = render 'learning_delivery/pagy_nav', presenter:
