.w-full Missing Assignments List #{presenter.risk.risk_details["missing_assignments_count"]}
.rounded-lg.bg-gray-50
  .lg:flex.w-full.block
  .bg-white.rounded-lg.w-full
    .overflow-x-auto.overflow-y-hidden.max-h-57
    table.w-full.text-left.text-sm.border.border-gray-300.rounded-lg
      thead.bg-bg-secondary-grey-zip
      tr
        th.p-3.5.text-extra-dark-grey-zip.text-sm.font-semibold.border-b.border-tertiary-light-grey-zip.min-w-56 Assignment Name
        th.p-3.5.text-extra-dark-grey-zip.text-sm.font-semibold.border-b.border-tertiary-light-grey-zip.w-56 Cohort
        th.p-3.5.text-extra-dark-grey-zip.text-sm.font-semibold.border-b.border-tertiary-light-grey-zip.w-96 Assignment Group
      tbody#table-body[data-toggle-visibility-target= 'table']
        - presenter.assignment_list(presenter.risk.risk_details["missing_assignment_ids"]).each do |assignment|
          tr
            td = assignment.name
            td = presenter.cohort_url(assignment.cohort)
            td = presenter.assighnment_group_detail(assignment.assignment_group.name, assignment.assignment_group.created_at)