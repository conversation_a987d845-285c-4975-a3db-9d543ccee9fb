div.pl-4[data-controller="toggle-visibility" data-toggle-visibility-target-class-value='toggle-visibility' data-toggle-visibility-active-row-class-value='bg-blue-50' data-sidebar-menu-target= 'content']
  .gap-6.flex[data-controller="task-layout"]
    .w-full.mt-6.shadow-md.bg-white.rounded-md.mb-6[data-task-layout-target="content"]
      .p-5.border.border-gray-200.shadow-sm.bg-white.border-b-0.rounded-t-md
        .w-full.text-gray-800.text-lg.mb-3 Filters
        = turbo_frame_tag "filters" do
          = form_with url: learning_delivery_learners_path, method: :get, local: true, data: { turbo_frame: "tasks_table", action: "click->sidebar-menu#collapse" } do |form|
            .flex.gap-4
              .w-1/3
                = form.select :section_uid,
                  options_for_select(@presenter.section_options, params[:section_uid]), {}, { class: "pr-14 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5", onchange: "this.form.submit();" }

              .w-1/3
                = form.select :learner_uid, options_for_select([['Select User', '']] + @presenter.student_options, params[:learner_uid]), {}, { class: "pr-14 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5", onchange: "this.form.submit();" }
              .w-1/3
                = form.select :risk_value, options_for_select([['Select Risk', '']] + @presenter.risk_status, params[:risk_value]), {}, { class: "pr-14 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5", onchange: "this.form.submit();" }
            = form.hidden_field :per_page, value: params[:per_page]
      .border-gray-200.border-t.px-5.py-4.text-gray-800.text-lg.border-l.border-r
        | Learner List
      = turbo_frame_tag "tasks_table" do
        .rounded-lg.bg-gray-50
          .lg:flex.w-full.block.mx-4
            .bg-white.rounded-lg.w-full
              - if @presenter.section.present?
                .overflow-x-auto.overflow-y-hidden.max-h-57
                  table.w-full.text-left.text-sm.border.border-gray-300.rounded-lg
                    thead.bg-bg-secondary-grey-zip
                      tr
                        th.p-3.5.text-extra-dark-grey-zip.text-sm.font-semibold.border-b.border-tertiary-light-grey-zip.min-w-56 Student
                        th.p-3.5.text-extra-dark-grey-zip.text-sm.font-semibold.border-b.border-tertiary-light-grey-zip.w-56 Email
                        th.p-3.5.text-extra-dark-grey-zip.text-sm.font-semibold.border-b.border-tertiary-light-grey-zip.w-56 Enrollment Status
                        th.p-3.5.text-extra-dark-grey-zip.text-sm.font-semibold.border-b.border-tertiary-light-grey-zip.w-56.text-left At Risk Status
                        th.p-3.5.text-extra-dark-grey-zip.text-sm.font-semibold.border-b.border-tertiary-light-grey-zip.w-56 At Risk Reason
                        th.p-3.5.text-extra-dark-grey-zip.text-sm.font-semibold.border-b.border-tertiary-light-grey-zip.w-56 Last Activity
                    tbody#table-body[data-toggle-visibility-target= 'table']
                      = render_presenters(@presenter.learner_item_presenters)
                = render_presenter(@presenter.pagination_presenter)
              - else
                .p-8.text-center.text-gray-500
                  .text-lg.mb-2 No Section Selected
                  .text-sm Select a section from the dropdown above to view learners in that section.
