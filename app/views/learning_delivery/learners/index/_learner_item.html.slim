tr.task-row onclick="window.location='#{learning_delivery_learner_path(presenter.enrollment.learner.uid, section_uid: presenter.section.uid, enrollment_uid: presenter.enrollment.uid)}'" style="cursor: pointer;"

  td.p-3.text-extra-dark-grey-zip.text-sm.font-normal.border-b.border-tertiary-light-grey-zip #{presenter.full_name}
  td.p-3.text-extra-dark-grey-zip.text-sm.font-normal.border-b.border-tertiary-light-grey-zip #{presenter.primary_email_address}
  td.p-3.border-b.border-tertiary-light-grey-zip.text-sm.font-normal.border-b.border-tertiary-light-grey-zip
    span[class=merge("bg-tertiary-light-grey-zip py-1 px-4 rounded-full inline-block text-sm font-normal text-dark-green-zip #{presenter.enrollment_status_text_and_colors[:bg_class]} text-black")]
      span[class=merge("w-2 h-2 p-1 bg-dark-green-zip rounded-full inline-block mr-2 #{presenter.enrollment_status_text_and_colors[:dot_class]}")]
      | #{presenter.enrollment_status_text_and_colors[:label]}
  - if presenter.enrollment.risk_level.present?
    td.p-3.border-b.border-tertiary-light-grey-zip.text-sm.font-normal.border-b.border-tertiary-light-grey-zip
      span[class=merge("bg-tertiary-light-grey-zip py-1 px-4 rounded-full inline-block text-sm font-normal text-dark-green-zip #{presenter.risk_status_text_and_colors[:bg_class]} text-black")]
        span[class=merge("w-2 h-2 p-1 bg-dark-green-zip rounded-full inline-block mr-2 #{presenter.risk_status_text_and_colors[:dot_class]}")]
        | #{presenter.risk_status_text_and_colors[:label]}
  - else
    td.pl-4.text-extra-dark-grey-zip.text-sm.font-normal.border-b.border-tertiary-light-grey-zip
  td.p-3.text-extra-dark-grey-zip.text-sm.font-normal.border-b.border-tertiary-light-grey-zip.items-center= presenter.risk_reason
  td.p-3.text-extra-dark-grey-zip.text-sm.font-normal.border-b.border-tertiary-light-grey-zip.items-center= presenter.date_ago(presenter.enrollment.last_lms_activity_at)