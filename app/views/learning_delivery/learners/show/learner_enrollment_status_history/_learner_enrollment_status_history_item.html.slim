tr
  td.py-4.px-5
    span
      = presenter.created_at(presenter.status.created_at.to_s)[:date]
      br
      = presenter.created_at(presenter.status.created_at.to_s)[:time]
  td.py-4.px-5
    span[class=merge("bg-tertiary-light-grey-zip py-1 px-4 rounded-full inline-block text-sm font-normal text-dark-green-zip #{presenter.enrollment_status_text_and_colors(presenter.status.status_was)[:bg_class]} #{presenter.enrollment_status_text_and_colors(presenter.status.status_was)[:text_class]}")]
      | #{presenter.enrollment_status_text_and_colors(presenter.status.status_was)[:label]}
  td.py-4.px-5
    span[class=merge("bg-tertiary-light-grey-zip py-1 px-4 rounded-full inline-block text-sm font-normal text-dark-green-zip #{presenter.enrollment_status_text_and_colors(presenter.status.status_became)[:bg_class]} #{presenter.enrollment_status_text_and_colors(presenter.status.status_became)[:text_class]}")]
      | #{presenter.enrollment_status_text_and_colors(presenter.status.status_became)[:label]}
  td.py-4.px-5 = presenter.status.reason
  td.py-4.px-5
    -if presenter.status.admin_user_id.present?
      div.relative.inline-block.group
        div.w-10.h-10.font-bold.flex.items-center.justify-center.rounded-full.cursor-pointer
          = render_presenter(presenter.admin_name_presenter(presenter.status.admin_user_id))
        div.absolute.bottom-12.left-1/2.-translate-x-1/2.bg-gray-800.text-white.text-sm.py-1.px-2.rounded.shadow.opacity-0.group-hover:opacity-100.transition-opacity.duration-200
          = presenter.admin_name(presenter.status.admin_user_id)
          div.absolute.top-full.left-1/2.-translate-x-1/2.border-4.border-transparent.border-t-gray-800