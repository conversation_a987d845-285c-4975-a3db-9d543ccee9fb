
.grid.grid-cols-12
 .col-span-8
  .overflow-hidden.rounded-2xl.border.border-gray-200.bg-white.pt-5
    - if presenter.task_list_item_presenters.any?
      .flex.item-center.justify-start.mb-4.px-5
        .text-xl.font-semibold.text-gray-800
          | Tasks List
      .rounded-lg
        .lg:flex.w-full.block
        .bg-white.rounded-lg.w-full
          .overflow-x-auto.overflow-y-hidden.max-h-57
          table.w-full.text-left.text-sm.border-b.border-gray-300.rounded-lg
            thead.bg-gray-100
              tr
                th.py-5.px-5.text-extra-dark-grey-zip.text-sm.font-semibold.min-w-56 Task Name
                th.py-5.px-5.text-extra-dark-grey-zip.text-sm.font-semibold.w-56 Due Date
                th.py-5.px-5.text-extra-dark-grey-zip.text-sm.font-semibold.w-96 Status
            tbody.divide-y.divide-gray-100.dark#table-body[data-toggle-visibility-target= 'table']
              = render_presenters(presenter.task_list_item_presenters)
          = render_presenter(presenter.pagination_presenter)
    - else
      .p-8.text-center.text-gray-500
        .text-lg.mb-2 No Tasks Available