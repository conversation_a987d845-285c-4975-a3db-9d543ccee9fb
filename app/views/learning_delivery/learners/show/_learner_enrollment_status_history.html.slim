.grid.grid-cols-12
 .col-span-8
  .overflow-hidden.rounded-2xl.border.border-gray-200.bg-white.pt-5
    - if presenter.enrollment_status_list_item_presenters.any?
      .flex.item-center.justify-start.mb-4.px-5
        .text-xl.font-semibold.text-gray-800
          | Enrollment Status History
      .rounded-lg
        .lg:flex.w-full.block
        .bg-white.rounded-lg.w-full
          .overflow-x-auto.overflow-y-hidden.max-h-57
          table.w-full.text-left.text-sm.border-b.border-gray-300.rounded-lg
            thead.bg-gray-100
              tr
                th.py-5.px-5.text-extra-dark-grey-zip.text-sm.font-semibold.min-w-56 Created at
                th.py-5.px-5.text-extra-dark-grey-zip.text-sm.font-semibold.w-56 Status Was
                th.py-5.px-5.text-extra-dark-grey-zip.text-sm.font-semibold.w-96 Status Became
                th.py-5.px-5.text-extra-dark-grey-zip.text-sm.font-semibold.w-56 Reason
                th.py-5.px-5.text-extra-dark-grey-zip.text-sm.font-semibold.w-56 Admin
            tbody.divide-y.divide-gray-100.dark#table-body[data-toggle-visibility-target= 'table']
              = render_presenters(presenter.enrollment_status_list_item_presenters)
          = render_presenter(presenter.pagination_presenter)
    - else
      .p-8.text-center.text-gray-500
        .text-lg.mb-2 No Enrollment Status History Available