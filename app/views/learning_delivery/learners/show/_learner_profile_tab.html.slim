div
  .lg:flex.gap-6.mb-3.block
    h3.text-tertiary-black-zip.md:text-2xl.font-semibold.text-lg= presenter.learner_full_name(presenter.enrollment)
    .flex.gap-2.sm:mt-0.mt-2.flex-wrap.md:flex-nowrap
      button.bg-bg-secondary-grey-zip.rounded-full.sm:px-3.px-1.text-xs.sm:text-sm.flex.items-center.h-7.text-blacktext.border.border-blue-zip
        | learner
      button.rounded-full.h-7.text-sm.font-medium.text-blacktext
        span[class=merge("bg-tertiary-light-grey-zip py-1 px-4 rounded-full inline-block text-sm font-normal text-dark-green-zip #{presenter.enrollment_status_text_and_colors(presenter.enrollment)[:bg_class]} text-black")]
          span[class=merge("w-2 h-2 p-1 bg-dark-green-zip rounded-full inline-block mr-2 #{presenter.enrollment_status_text_and_colors(presenter.enrollment)[:dot_class]}")]
          | #{presenter.enrollment_status_text_and_colors(presenter.enrollment)[:label]}
      button.rounded-full.h-7.text-sm.font-medium.text-blacktext
        span[class=merge("bg-tertiary-light-grey-zip py-1 px-4 rounded-full inline-block text-sm font-normal text-dark-green-zip #{presenter.risk_status_text_and_colors(presenter.enrollment)[:bg_class]} text-black")]
          span[class=merge("w-2 h-2 p-1 bg-dark-green-zip rounded-full inline-block mr-2 #{presenter.risk_status_text_and_colors(presenter.enrollment)[:dot_class]}")]
          | #{presenter.risk_status_text_and_colors(presenter.enrollment)[:label]}
  .sm:flex.md:gap-2.mt-3.gap-3.block.sm:mb-0.mb-3
    .sm:block.flex.gap-8.sm:border-r.border-solid.border-gray-300.pr-4.border-0
      p.text-secondary-zip.text-sm.font-normal.sm:w-full
        | Active Section
      h6.font-semibold.text-sm
        | #{presenter.format_section_title(presenter.section)}
    .sm:block.flex.gap-8.sm:border-r.border-solid.border-gray-300.md:px-4.px-0.border-0
      p.text-secondary-zip.text-sm.font-normal.sm:w-full
        | Email
      h6.font-semibold.text-sm
        | #{presenter.learner_email(presenter.enrollment)}
    .sm:block.flex.gap-8.md:pl-4.pl-0
      p.text-secondary-zip.text-sm.font-normal.sm:w-full
        | Enrollment No
      h6.font-semibold.text-sm
        | #{presenter.enrollment.uid}