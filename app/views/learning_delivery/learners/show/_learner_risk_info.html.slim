
.w-full.text-gray-800.text-xl.p-5.border.border-gray-200.border-b-0 Overall Summary
span.flex.gap-2
  p
    Overall Risk Level
  p
    | #{presenter.enrollment.risk_level_name}

span
  p
    Last LMS Activity At
  p = presenter.lms_last_activity(presenter.enrollment.last_lms_activity_at)

- if presenter.risk_type_breakdowns.present?
  .w-full.text-gray-800.text-xl.p-5.border.border-gray-200.border-b-0 Risk Type Breakdown
  rounded-lg.bg-gray-50
    .lg:flex.w-full.block
      .bg-white.rounded-lg.w-full
        .overflow-x-auto.overflow-y-hidden.max-h-57
          table.w-full.text-left.text-sm.border.border-gray-300.rounded-lg
            thead.bg-bg-secondary-grey-zip
              tr
                th.p-3.5.text-extra-dark-grey-zip.text-sm.font-semibold.border-b.border-r.border-tertiary-light-grey-zip.min-w-56 ID
                th.p-3.5.text-extra-dark-grey-zip.text-sm.font-semibold.border-b.border-r.border-tertiary-light-grey-zip.w-56 Risk Type
                th.p-3.5.text-extra-dark-grey-zip.text-sm.font-semibold.border-b.border-r.border-tertiary-light-grey-zip.w-96 Risk Level
                th.p-3.5.text-extra-dark-grey-zip.text-sm.font-semibold.border-b.border-r.border-tertiary-light-grey-zip.w-96 Assessed At
            tbody#table-body[data-toggle-visibility-target= 'table']
              - presenter.risk_type_breakdowns.each do |risk|

                tr onclick="Turbo.visit('#{risk_info_learning_delivery_learner_path(@presenter.learner.uid, risk_id: risk.id, format: :turbo_stream)}')"

                  td = risk.id
                  td = risk.risk_type
                  td = risk.risk_level
                  td = presenter.accessed_at(risk.assessed_at)

  #risk-description
