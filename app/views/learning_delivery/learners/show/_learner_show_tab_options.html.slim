div.mt-10.mb-6[data-controller= "switch-tabs" data-switch-tabs-active-class-value="text-white bg-purple-900", data-switch-tabs-inactive-class-value= "hover:text-purple-700 text-purple-900" ]
  nav.flex.space-x-8 aria-label="Tabs"
    = link_to "Assignments List",
        learning_delivery_learner_path(presenter.learner.uid, section_uid: presenter.section.uid, enrollment_uid: presenter.enrollment.uid),
        class: "px-4 py-2 text-sm font-medium text-white bg-purple-900 rounded-md", data: { switch_tabs_target: "tab"}

    = link_to "Tasks List",
        learning_delivery_learner_path(presenter.learner.uid, section_uid: presenter.section.uid, enrollment_uid: presenter.enrollment.uid, tab: "learner_tasks_list"),
        class: "px-4 py-2 text-sm font-medium text-purple-900 hover:text-purple-700", data: { switch_tabs_target: "tab" }

    = link_to "Enrollment Status History",
        learning_delivery_learner_path(presenter.learner.uid, tab: "learner_enrollment_status_history", section_uid: presenter.section.uid, enrollment_uid: presenter.enrollment.uid),
        class: "px-4 py-2 text-sm font-medium text-purple-900 hover:text-purple-700", data: { switch_tabs_target: "tab"}

    = link_to "Risk Info",
        learning_delivery_learner_path(presenter.learner.uid, tab: "learner_risk_info", section_uid: presenter.section.uid, enrollment_uid: presenter.enrollment.uid),
        class: "px-4 py-2 text-sm font-medium text-purple-900 hover:text-purple-700", data: { switch_tabs_target: "tab"}