.grid.grid-cols-12
 .col-span-8
  .overflow-hidden.rounded-2xl.border.border-gray-200.bg-white.pt-5
    .flex.item-center.justify-start.mb-4.px-5
      .text-xl.font-semibold.text-gray-800
        | Assignments List
    .rounded-lg
      .lg:flex.w-full.block
      .bg-white.rounded-lg.w-full
        .overflow-x-auto.overflow-y-hidden.max-h-57
        table.w-full.text-left.text-sm.border-b.border-gray-300.rounded-lg
          thead.bg-gray-100
            tr
              th.py-5.px-5.text-extra-dark-grey-zip.text-sm.font-semibold.min-w-56 Assignment Name
              th.py-5.px-5.text-extra-dark-grey-zip.text-sm.font-semibold.w-56 Status
              th.py-5.px-5.text-extra-dark-grey-zip.text-sm.font-semibold.w-96 Submission Date
              th.py-5.px-5.text-extra-dark-grey-zip.text-sm.font-semibold.w-56 Module Status
          tbody.divide-y.divide-gray-100.dark#table-body[data-toggle-visibility-target= 'table']
            = render_presenters(presenter.assignment_list_item_presenters)
        = render_presenter(presenter.pagination_presenter)