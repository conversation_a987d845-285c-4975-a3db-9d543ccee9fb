.main-navbar.fixed.top-0.right-0.left-14.z-20.w-auto.transition-all.duration-300.ease-in-out
  = render(partial: 'shared/admin_users/impersonation_notice')
  nav.navbar.py-3.px-3.bg-blue-zip.flex.justify-between.items-center.min-h-60
    = render partial: 'learning_delivery/breadcrumbs'

    .navbar-right.flex.md:gap-4.justify-between.md:w-auto.w-full.pr-3

      div.flex[data-controller="toggle-outside-click"]
        .flex
          .md:ml-5.ml-2.user-profile.w-8.h-8.rounded-full.relative
            .[data-action='click->toggle-outside-click#toggle']
              .md:w-32.w-15.cursor-pointer[class="size-10"]
                = render_presenter(presenter.avatar_presenter(classes: 'size-8 rounded-full'))
            ul.hidden.absolute.w-64.right-0.top-8.bg-white.rounded-md.px-0.py-4.shadow-md.z-40.mt-4[
              data-toggle-outside-click-target="toggleable"]
              li.border-b.border-solid.border-gray-200.pb-3.px-6
                .flex.gap-2
                  .min-w-10.text-center
                    = render_presenter(presenter.avatar_presenter(classes: 'size-8 rounded-full w-10 h-10'))
                  .flex.flex-col.font-semibold.text-gray-800
                    span = presenter.full_name
                    span.text-gray-500.font-normal.flex
                      p = presenter.role_names

              - if presenter.availability_path
                li.border-b.border-solid.border-gray-200.px-6.py-3.flex.items-center.gap-3.hover:bg-gray-100.cursor-pointer
                  a[href=presenter.availability_path]
                    i.fa-regular.fa-calendar-days
                    span.ml-2 My Availability

              - if presenter.active_admin_path
                li.border-b.border-solid.border-gray-200.px-6.py-3.flex.items-center.gap-3.hover:bg-gray-100.cursor-pointer
                  a[href=presenter.active_admin_path]
                    i.fa-solid.fa-user-shield
                    span.ml-2 Active Admin

              - if feature_enabled?(:lsa)
                li.border-b.border-solid.border-gray-200.p-3.px-6.flex.items-center.gap-3.hover:bg-gray-100.cursor-pointer
                  i.fa-regular.fa-circle-question
                  | FAQ

              li.pb-3.px-6.flex.items-center.gap-3.pt-2
                = link_to destroy_admin_user_session_path, method: :delete, class: "bg-red-500 rounded-md text-center flex items-center justify-center px-4 py-3 w-full gap-2 text-white hover:bg-red-600 transition-colors duration-200" do
                  | Logout
                  i.fa-solid.fa-arrow-right-from-bracket
