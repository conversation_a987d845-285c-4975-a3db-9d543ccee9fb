.pl-6.pr-6
  .w-full.mt-6.shadow-md.bg-white.rounded-md.mb-6
    .p-5.border.border-gray-200.shadow-sm.bg-white.border-b-0.rounded-t-md
      .w-full.md:w-3/12
        .w-full.text-gray-800.text-lg.mb-3 Filter
        = turbo_frame_tag "filters" do
          = form_with url: request.path, method: :get, local: true, data: { turbo_frame: "sections_table"} do |form|
            .flex.gap-4
              .w-full
                = form.select :section_filter, options_for_select(@presenter.section_filter_options, params[:section_filter]), { prompt: 'All Section' }, { class: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500", onchange: "this.form.submit();" }
            / Hidden fields to preserve pagination settings
            = form.hidden_field :per_page, value: params[:per_page]

    = turbo_frame_tag "sections_table" do
      .overflow-x-auto
        table.w-full.bg-white.border.border-gray-300.rounded-b-md.mx-4.mb-4
          thead.bg-gray-50
            tr
              th.p-3.5.text-extra-dark-grey-zip.text-sm.font-semibold.border-b.border-r.border-tertiary-light-grey-zip.w-32 UID
              th.p-3.5.text-extra-dark-grey-zip.text-sm.font-semibold.border-b.border-r.border-tertiary-light-grey-zip.w-40 Program
              th.p-3.5.text-extra-dark-grey-zip.text-sm.font-semibold.border-b.border-r.border-tertiary-light-grey-zip.w-32 Cohort
              th.p-3.5.text-extra-dark-grey-zip.text-sm.font-semibold.border-b.border-r.border-tertiary-light-grey-zip.w-32 Name
              th.p-3.5.text-extra-dark-grey-zip.text-sm.font-semibold.border-b.border-r.border-tertiary-light-grey-zip.w-32 Cohort Status
              th.p-3.5.text-extra-dark-grey-zip.text-sm.font-semibold.border-b.border-r.border-tertiary-light-grey-zip.w-24 Learners
              th.p-3.5.text-extra-dark-grey-zip.text-sm.font-semibold.border-b.border-r.border-tertiary-light-grey-zip.w-32 Live Day Of The Week
              th.p-3.5.text-extra-dark-grey-zip.text-sm.font-semibold.border-b.border-r.border-tertiary-light-grey-zip.w-32 Live Start Time
              th.p-3.5.text-extra-dark-grey-zip.text-sm.font-semibold.border-b.border-r.border-tertiary-light-grey-zip.w-20 NPS
          tbody#table-body
            = render_presenters(@presenter.section_item_presenters)
      = render_presenter(@presenter.pagination_presenter)
