tr[class="#{presenter.row_classes}"]
  td.p-3.text-extra-dark-grey-zip.text-sm.font-normal.border-b.border-r.border-tertiary-light-grey-zip.text-center
    - if presenter.active?
      = link_to(presenter.humanized_uid, learning_delivery_learners_path(section_uid: presenter.section.humanized_uid), class: "text-gray-600 hover:text-gray-800 underline", data: { turbo: false })
    - else
      = presenter.humanized_uid
  td.p-3.text-extra-dark-grey-zip.text-sm.font-normal.border-b.border-r.border-tertiary-light-grey-zip.text-center = presenter.program_name
    / = link_to presenter.program_name, "#", class: "text-gray-600 hover:text-gray-800 underline"
  td.p-3.text-extra-dark-grey-zip.text-sm.font-normal.border-b.border-r.border-tertiary-light-grey-zip.text-center = presenter.cohort_name
    / = link_to presenter.cohort_name, "#", class: "text-gray-600 hover:text-gray-800 underline"
  td.p-3.text-extra-dark-grey-zip.text-sm.font-normal.border-b.border-r.border-tertiary-light-grey-zip.text-center
    = presenter.section_name
  td.p-3.text-extra-dark-grey-zip.text-sm.font-normal.border-b.border-r.border-tertiary-light-grey-zip.text-center
    span[class="#{presenter.status_badge_classes}"]
      = presenter.cohort_status
  td.p-3.text-extra-dark-grey-zip.text-sm.font-normal.border-b.border-r.border-tertiary-light-grey-zip.text-right.pr-8
    = presenter.enrollments_count
  td.p-3.text-extra-dark-grey-zip.text-sm.font-normal.border-b.border-r.border-tertiary-light-grey-zip
    = presenter.live_day_of_the_week
  td.p-3.text-extra-dark-grey-zip.text-sm.font-normal.border-b.border-r.border-tertiary-light-grey-zip
    = presenter.live_start_time
  td.p-3.text-extra-dark-grey-zip.text-sm.font-normal.border-b.border-r.border-tertiary-light-grey-zip.text-right.pr-8
    = presenter.nps_score
