/ Header
.px-6.py-4.border-b.border-gray-200.shadow-md
  .flex.items-start.justify-between
    .flex-1
      .flex.items-start
        h2.mr-2.text-xl.font-semibold.text-gray-900 = presenter.display_title
        ml-4 = render_presenter(presenter.status_presenter)

    button.ml-4.p-2.rounded-lg.text-gray-400.hover:text-gray-600.hover:bg-white.hover:bg-opacity-50.transition-all data-action="click->overlay-panel#close"
      span.sr-only Close panel
      i.fas.fa-times.h-5.w-5

.m-4.p-4.rounded-xl.overflow-hidden class=presenter.info_background_class
  .flex.items-center.items-start
    .rounded-xl.text-white.p-4 class=presenter.task_icon_color_class
      i.fas.text-2xl class=presenter.task_icon_class
    .text-sm.ml-2
      .prose.gray-900.font-semibold.text-base = presenter.title
      .text-gray-600.mt-1 = "Due #{presenter.formatted_due_at}" if presenter.formatted_due_at.present?

