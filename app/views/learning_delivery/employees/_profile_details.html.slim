.border.border-border-gray-200.rounded-lg.pt-5
  .p-4.border-b.border-border-gray-200
    h5.text-base.font-semibold.text-extra-dark-grey-zip.pb-4.flex.gap-2
      = image_tag('learning_delivery/icons/user-square.svg')
      | Basic Information

    .bg-bg-secondary-grey-zip.rounded-lg.p-5
      .md:flex.gap-3.mb-2.justify-between.block
        h4.text-tertiary-black-zip.text-sm.font-normal.w-24
          | Designation
        p.text-secondary-zip.text-sm.font-light
          - presenter.employee.roles.each do |role|
            - if role == presenter.employee.roles.last
              span #{role.abbreviation}
            - else
              span #{role.abbreviation} ,

      .md:flex.block.gap-3.mb-2.justify-between
        h4.text-tertiary-black-zip.text-sm.font-normal.w-24
          | Joining Date
        p.text-secondary-zip.text-sm.font-light #{presenter.employee.joined_on.strftime("%B %-d, %Y")}
      .md:flex.block.gap-3.mb-2.justify-between
        h4.text-tertiary-black-zip.text-sm.font-normal.w-24
          | Personal Email
        p.text-secondary-zip.text-sm.font-light
          =presenter.employee.personal_email
      .md:flex.block.gap-3.mb-2.justify-between
        h4.text-tertiary-black-zip.text-sm.font-normal.w-24
          | Work Email
        p.text-secondary-zip.text-sm.font-light
          =presenter.employee.company_email
      .md:flex.block.gap-3.mb-2.justify-between
        h4.text-tertiary-black-zip.text-sm.font-normal.w-24
          | Timezone
        p.text-secondary-zip.text-sm.font-light
          =presenter.employee.time_zone
      .flex.gap-3.mb-2
  .p-4.border-b.border-border-gray-200
    .md:flex.block.justify-between.items-center.pb-4
      h5.text-base.font-semibold.text-extra-dark-grey-zip.flex.gap-2
        = image_tag('learning_delivery/icons/calendar-plus.svg')
        | Availability
      a.text-brand-blue-zip.font-normal.text-sm.underline Set Availability
    .bg-bg-secondary-grey-zip.rounded-lg.p-5
     .sm:flex.justify-between.block
                .sm:flex-nowrap.flex.flex-wrap.gap-1.items-center
                  p.text-blacktext.text-base.font-normal.mr-4
                    | This week
                  - Date::DAYNAMES.rotate.each do |day|
                    - if presenter.available?(presenter.employee_current_availabilities, day)
                      p.bg-green-zip.size-8.rounded-full.py-2.px-2.md:mr-2.mr-1.text-white.flex.justify-center.items-center #{day[0]}
                    - else
                      p.bg-tertiary-light-grey-zip.size-8.rounded-full.py-2.px-2.md:mr-2.mr-1.text-white.flex.justify-center.items-center #{day[0]}
  .mt-3.p-4.border-b.border-border-gray-200
    h4.text-extra-dark-grey-zip.text-base.font-semibold.mb-4.flex.gap-2
      = image_tag('learning_delivery/icons/link.svg')
      | Links
    .sm:flex.justify-between.gap-1.block.btn-div
      a.bg-bg-secondary-grey-zip.text-tertiary-black-zip.text-xs.font-medium.lg:py-2.lg:px-3.py-4.rounded-xl.flex.items-center.gap-1.w-full.whitespace-nowrap.justify-center.sm:mb-0.mb-3[href="#{presenter.employee.company_profile_url}"]
        = image_tag('learning_delivery/icons/zipline-short-form-logo-dark.svg')
        | Ziplines Profile
      a.bg-bg-secondary-grey-zip.text-tertiary-black-zip.text-xs.font-medium.px-2.rounded-xl.flex.items-center.gap-1.w-full.whitespace-nowrap.justify-center.sm:mb-0.mb-3.sm:py-0.py-4[href=""]
        = image_tag('learning_delivery/icons/resume.svg')
        | Resume
      a.bg-bg-secondary-grey-zip.text-tertiary-black-zip.text-xs.font-medium.px-2.rounded-xl.flex.items-center.gap-1.w-full.whitespace-nowrap.justify-center.sm:mb-0.mb-3.sm:py-0.py-4[href="#{presenter.employee.linked_in_url}"]
        = image_tag('learning_delivery/icons/linkedin.svg')
        | LinkedIn

  - if false && @presenter.manager_view? # Temprary removed from view, need discussion
    .mt-4
      .p-4
        h5.text-base.font-semibold.text-extra-dark-grey-zip.pb-4.flex.gap-2
          = image_tag('learning_delivery/icons/notes.svg')
          | Write a quick note
        .border.border-border-gray-200.rounded-lg.p-5.text-text-disabled-grey-zip.text-sm.font-normal
          p.mb-3 Lorem ipsum dolor sit amet consectetur. Egestas.
          p.mb-5.max-w-5/6 Lorem ipsum dolor sit amet consectetur. In ipsum sit lorem cursus. Sit risus mauris eget eu elit a.
          button.bg-blue-zip.px-4.py-3.rounded-lg.mt-4.text-white
            | Save

      .p-4.border-b.border-border-gray-200
        .bg-bg-secondary-grey-zip.rounded-lg.p-5.text-extra-dark-grey-zip
          p.max-w-9/10 Lorem ipsum dolor sit amet consectetur. Egestas.Lorem ipsum dolor sit amet consectetur. In ipsum sit lorem cursus. Sit risus mauris eget eu elit a.
          .flex.justify-between.items-center.mt-4.justify-between
            .flex.gap-1
              span.bg-tertiary-light-grey-zip.px-3.py-2.rounded-sm.mr-3.text-xs.font-normal
                | #timezone
              span.bg-tertiary-light-grey-zip.px-3.py-2.rounded-sm.text-xs.font-normal
                | #meeting
            .flex.gap-2
              a
                i.fa-solid.fa-trash.text-brand-primary-gray-zip
              a
                i.fa-solid.fa-pen.text-brand-primary-gray-zip
