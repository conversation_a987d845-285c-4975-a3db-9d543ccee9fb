
#accordion-collapse.rounded-t-lg.bg-bg-secondary-grey-zip
  div[data-controller='toggle'data-toggle-open-value="true"]
    .bg-white.p-4.pb-2.rounded-lg.border-none.border-tertiary-light-grey-zip.shadow-custom
      div[data-action='click->toggle#toggle touch->toggle#toggle']
        h2#accordion-collapse-heading-1.bg-white
          button.flex.items-center.justify-between.w-full.p-2.pt-0.pl-0.font-medium.rtl:text-right.text-gray-500.rounded-t-xl.bg-white.gap-3.bg-white[type="button"  aria-expanded="true" aria-controls="accordion-collapse-body-1" ]
            span.text-blacktext.text-base.font-semibold.pl-1
              | Active Sections
            svg.w-6.h-6.rounded-full.rotate-180.shrink-0.border.border-blue-zip.text-blue-zip[class="p-1.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6"]
              path[stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 1 5 5 9 1"]
      .[data-toggle-target='toggleable']
        #accordion-collapse-body-1.leave-to.enter-to[data-toggle-target='toggleable' aria-labelledby="accordion-collapse-heading-1"]
        - if presenter.sections.any?(&:active?)
            - presenter.sections.select(&:active?).each do |section|
              .border.border-tertiary-light-grey-zip.rounded-lg.bg-white.mb-4
                .sm:flex.justify-between.block.sm:mt-0.mt-2.bg-bg-secondary-grey-zip.p-3
                  h6.text-blacktext.text-base.font-semibold.mb-0
                    | #{presenter.format_section_title(section)}
                    button.text-green-zip.font-normal.px-3.py-1.rounded-full.text-sm.ml-4[class="bg-[#6EB77C1A]"]
                      | Started
                  p.flex.gap-2.sm:mt-0.mt-2
                    span[data-controller="copy-to-clipboard" data-action="click->copy-to-clipboard#copy" class="cursor-pointer w-6 relative group"]
                      = image_tag 'learning_delivery/icons/zoom.svg', class: 'hover:scale-110 transition-transform duration-200'
                      span.hidden[data-copy-to-clipboard-target="content"]= presenter.conferencing_url(section)
                      span.absolute.bottom-full.left-1/2.transform.-translate-x-1/2.mb-2.px-2.py-1.text-xs.text-white.bg-gray-800.rounded.opacity-0.group-hover:opacity-100.transition-opacity.duration-200.pointer-events-none.whitespace-nowrap Copy zoom link
                    = svg(:signal, class: 'w-6 h-6 text-orange-600')
                    | Live at #{presenter.live_day_and_time(section)}
                .sm:flex.justify-between.block.p-3.mt-1.pb-1
                  .sm:flex-nowrap.flex.flex-wrap.gap-1.items-center
                    span.text-extra-dark-grey-zip.text-base.font-semibold
                      | Week: #{presenter.week_number(section)}
                .grid.sm:grid-cols-3.grid-cols-1.lg:gap-4.gap-2.p-3
                  = link_to learning_delivery_learners_path(section_uid: section.humanized_uid), class: "block"
                    .bg-extra-light-yellow-zip.rounded-xl
                      .flex.lg:gap-6.iteams-center.relative.gap-1.xl:py-4.xl:px-6.px-2.py-4
                        .chart-progress.text-dark-yellow-zip.font-semibold[role="progressbar" aria-valuenow="#{presenter.enrollments_percentage(section)}" aria-valuemin="0" aria-valuemax="100" style="--value:#{presenter.enrollments_percentage(section)}"]
                          | #{presenter.enrollments_percentage(section)}
                          span.text-base.pt-2
                            |%
                        div.pl-6
                          p.text-extra-dark-grey-zip.text.font-normal.text-xs
                            | Total
                          h2.text-extra-dark-grey-zip.font-semibold.md:text-2xl.pb-1.text-lg.border-b.border-b-light-yellow-zip.pb-2 #{presenter.enrollments_count(section)}
                          p.text-secondary-zip.text-xs.font-normal.pt-2
                            | At Risk
                          h2.text-extra-dark-grey-zip.font-semibold.md:text-2xl.pb-1.text-lg.pt-0 #{presenter.enrollments_at_high_risk_count(section)}
                      .flex.justify-between.items-center.xl:py-4.xl:px-6.px-2.py-4.rounded-bl-xl.rounded-br-xl[class="bg-[#CA8A041A]"]
                        h4.text-dark-yellow-zip.md:text-xl.text-sm.font-semibold.flex.gap-1
                          = image_tag('learning_delivery/icons/learning.svg')
                          | Learners
                        i.fa-solid.fa-arrow-right
                  .rounded-xl[class="bg-[#6EB77C1A]"]
                    .flex.lg:gap-6.iteams-center.relative.gap-1.xl:py-4.xl:px-6.opacity-75.px-2.py-4
                      .chart-progress2.text-dark-green-zip.font-semibold[role="progressbar" aria-valuenow="#{presenter.tasks_completed_precentage(section)}" aria-valuemin="0" aria-valuemax="100" style="--value:#{presenter.tasks_completed_precentage(section)}"]
                        | #{presenter.tasks_completed_precentage(section)}
                        span.text-base.pt-2
                          |%
                      div.pl-6
                        p.text-extra-dark-grey-zip.text.font-normal.text-xs
                          | Total
                        h2.text-extra-dark-grey-zip.font-semibold.md:text-2xl.pb-1.text-lg.border-b.border-b-light-yellow-zip.pb-2
                          |  #{presenter.total_tasks_count(section)}
                        p.text-secondary-zip.text-xs.font-normal.pt-2
                          | Completed
                        h2.text-extra-dark-grey-zip.font-semibold.md:text-2xl.pb-1.text-lg.pt-0
                          | #{presenter.section_completed_tasks_count(section)}
                    .flex.justify-between.items-center.xl:py-4.xl:px-6.opacity-75.px-3.py-4.rounded-bl-xl.rounded-br-xl[class="bg-[#6EB77C1A]"]
                      h4.text-dark-green-zip.md:text-xl.text-sm.font-semibold.flex.gap-1
                        = image_tag('learning_delivery/icons/task-list.svg')
                        | Tasks
                      a
                      i.fa-solid.fa-arrow-right
                  .rounded-xl.opacity-75[class="bg-[#BBBBBB1A]"]
                    .flex.lg:gap-4.iteams-center.relative.xl:py-4.xl:px-6.gap-1.py-3.px-2
                      .ct-donut-wrapper
                        svg[aria-hidden="true" focusable="false" viewBox="0 0 44 44" width="120px" height="120px"]
                          circle[cx="50%" cy="50%" fill="transparent" r="15.909091" stroke="#eee" stroke-width="8"]
                          circle.ct-donut-slice[show-tip="ctDonutTip" cx="50%" cy="50%" fill="transparent" r="15.909091" stroke="#ED4634" stroke-dasharray="10 90" stroke-width="8" stroke-dashoffset="25" slice-title="Invalid" percent-value="10%" usage="100" total-val="1000"]
                            animate[attributeName="stroke-dashoffset" from="100" to="25" dur="0.5s"]
                          circle.ct-donut-slice[show-tip="ctDonutTip" cx="50%" cy="50%" fill="transparent" r="15.909091" stroke="#F8C715" stroke-dasharray="35 65" stroke-width="8" stroke-dashoffset="15" slice-title="Unverified" percent-value="35%" usage="350" total-val="1000"]
                            animate[attributeName="stroke-dashoffset" from="100" to="15" dur="0.5s"]
                          circle.ct-donut-slice[show-tip="ctDonutTip" cx="50%" cy="50%" fill="transparent" r="15.909091" stroke="#166534" stroke-dasharray="55 45" stroke-width="8" stroke-dashoffset="-20" slice-title="Valid" percent-value="55%" usage="550" total-val="1000"]
                            animate[attributeName="stroke-dashoffset" from="100" to="-20" dur="0.5s"]
                          circle[cx="50%" cy="50%" fill="#fafafa" r="16"]

                          text.ct-donut-center[x="35%" y="60%"]
                            | 70
                        .ct-tip
                      div.pl-6
                        div.space-y-0
                          .flex.items-center.space-x-3
                            .w-4.h-4.rounded.bg-red-500
                            .flex.flex-col
                              span.text-gray-800.font-medium[class="text-[13px]"] Detractors
                              span.text-gray-600.text-sm[class="text-[10px]"] 26

                          .flex.items-center.space-x-3
                            .w-4.h-4.rounded.bg-yellow-400
                            .flex.flex-col
                              span.text-gray-800.font-medium[class="text-[13px]"] Passive
                              span.text-gray-600.text-sm[class="text-[10px]"] 40

                          .flex.items-center.space-x-3
                            .w-4.h-4.rounded.bg-green-800
                            .flex.flex-col
                              span.text-gray-800.font-medium[class="text-[13px]"] Promoters
                              span.text-gray-600.text-sm[class="text-[10px]"] 117

                    .flex.justify-between.opacity-1.items-center.xl:py-4.xl:px-6.py-3.px-4.rounded-bl-xl.rounded-br-xl[class="bg-[#BBBBBB1A]"]
                      h4.md:text-xl.text-sm.font-semibold.flex.gap-1[class="text-[#232323]"]
                        = image_tag('learning_delivery/icons/Nps.svg')
                        | NPS Score
                      a
                      i.fa-solid.fa-arrow-right


/ donut chart start
