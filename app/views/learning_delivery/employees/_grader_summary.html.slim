#accordion-collapse.rounded-t-lg.bg-bg-secondary-grey-zip.mb-4
  div
    .bg-white.p-4.rounded-lg.border-none.border-tertiary-light-grey-zip.shadow-custom
      .border.border-gray-200.rounded-md
        div.px-3.py-1.bg-bg-secondary-grey-zip.rounded-md[data-action='click->toggle#toggle touch->toggle#toggle']
          h2#accordion-collapse-heading-1
            button.flex.items-center.justify-between.w-full.p-2.pl-0.font-medium.rtl:text-right.text-gray-500.rounded-t-xl.gap-3[type="button"  aria-expanded="true" aria-controls="accordion-collapse-body-1" ]
              span.text-blacktext.text-base.font-semibold.pl-1
                | Grading Overview
        .p-4
          #accordion-collapse-body-1.leave-to.enter-to[data-toggle-target='toggleable' aria-labelledby="accordion-collapse-heading-1"]
            .rounded-lg.bg-white
              .grid.sm:grid-cols-3.grid-cols-1.lg:gap-4.gap-2
                .bg-tertiary-light-grey-zip.rounded-xl.opacity-75
                  .flex.lg:gap-6.iteams-center.justify-center.relative.gap-1.xl:py-4.xl:px-6.px-2.py-4
                    .chart-progress4.text-purple-zip.font-semibold[role="progressbar" aria-valuenow="#{presenter.submitted_assignments_percentage}" aria-valuemin="0" aria-valuemax="100" style="--value:#{presenter.submitted_assignments_percentage}"]
                      = presenter.submitted_assignments_percentage
                      span.text-base.pt-2
                        |%
                    .pl-6.iteams-center.justify-center.flex.flex-col.text-center
                      p.text-extra-dark-grey-zip.text.font-normal.text-xs Submitted
                      h2.text-extra-dark-grey-zip.font-semibold.md:text-2xl.text-lg = presenter.submitted_assignments_count

                  .flex.justify-center.opacity-1.px-2
                    h4.md:text-xl.text-sm.font-semibold.text-purple-zip.flex.gap-1.mb-4.text-center
                      | Assignments Submitted
                .bg-green-50.rounded-xl.opacity-75
                  .flex.lg:gap-6.iteams-center.justify-center.relative.gap-1.xl:py-4.xl:px-6.px-2.py-4
                    .chart-progress2.text-green-zip.font-semibold[role="progressbar" aria-valuenow="#{presenter.graded_assignments_percentage}" aria-valuemin="0" aria-valuemax="100" style="--value:#{presenter.graded_assignments_percentage}"]
                      = presenter.graded_assignments_percentage
                      span.text-base.pt-2
                        |%
                    .pl-6.iteams-center.justify-center.flex.flex-col.text-center
                      p.text-extra-dark-grey-zip.text.font-normal.text-xs Graded
                      h2.text-extra-dark-grey-zip.font-semibold.md:text-2xl.text-lg = presenter.graded_assignments_count

                  .flex.justify-center.opacity-1.px-2
                    h4.md:text-xl.text-sm.font-semibold.flex.gap-1.mb-4.text-green-zip.text-center
                      | Assignments Graded

                .bg-red-50.rounded-xl.opacity-75
                  .flex.lg:gap-6.iteams-center.justify-center.relative.gap-1.xl:py-4.xl:px-6.px-2.py-4
                    .chart-progress5.text-red-zip.font-semibold[role="progressbar" aria-valuenow="#{presenter.pending_assignments_percentage}" aria-valuemin="0" aria-valuemax="100" style="--value:#{presenter.pending_assignments_percentage}"]
                      = presenter.pending_assignments_percentage
                      span.text-base.pt-2
                        |%
                    .pl-6.iteams-center.justify-center.flex.flex-col.text-center
                      p.text-extra-dark-grey-zip.text.font-normal.text-xs Pending
                      h2.text-extra-dark-grey-zip.font-semibold.md:text-2xl.text-lg = presenter.pending_assignments_count

                  .flex.justify-center.opacity-1.px-2
                    h4.md:text-xl.text-sm.font-semibold.text-red-zip.flex.gap-1.mb-4.text-center
                      | Pending Grading

            = link_to learning_delivery_submissions_path, class: "mt-4 block bg-[#241451] text-white text-center px-6 py-3 rounded-lg text-sm font-medium"
              | View All Assignments



