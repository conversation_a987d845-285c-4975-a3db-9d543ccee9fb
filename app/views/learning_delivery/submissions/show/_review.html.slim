div[id=presenter.dom_id]
  = render_presenter(presenter.header_presenter)
      

  // Exercise Reviews section
  - if presenter.current_review.nil?
    .border.border-gray-200.rounded-lg.shadow-sm.bg-white
      .p-6.text-center.text-gray-500
        h2.text-xl.font-bold No current review. 
        .text-m Please run autograding against the latest attempt.
  - else
    .border.border-gray-200.rounded-lg.shadow-sm.bg-white.relative.mb-28
      - unless presenter.publishable?
        .absolute.w-full.h-full.inset-0.z-50.cursor-not-allowed
      h2.p-6.text-xl.font-bold Exercise Details
      
      // Table header
      .pl-6.grid.grid-cols-6.gap-4.bg-gray-100.mg-0.p-3.font-medium.text-gray-700
        .col-span-1 Exercise
        .col-span-1 Grade
        .col-span-2 Comment
        .col-span-1 Status
        .col-span-1 Action
      
      / Table rows with alternating colors
      .border.border-gray-200.border-t-0
        - if presenter.exercise_review_presenters.empty?
          .p-6.text-gray-500.text-center.col-span-6 No exercises found.
        - else
          - presenter.exercise_review_presenters.each_with_index do |exercise_presenter, index|
            div class="#{index.even? ? 'bg-white' : 'bg-gray-50'}"
              = render_presenter(exercise_presenter)
      
      
      // Table row for overall review
      .border.border-t-0.rounded-b-lg.bg-blue-950.text-white.flex.justify-between.items-center.p-6
        //left side with overall grade and comment
        .flex.justify-start.max-w-3xl
          // grade dropdown
          .flex.flex-col.flex-shrink-0.mr-6.items-center data-controller="dropdown turbo-dropdown" data-action="click@window->dropdown#hide touchstart@window->dropdown#hide keydown.up->dropdown#previousItem keydown.down->dropdown#nextItem keydown.esc->dropdown#hide" data-turbo-dropdown-save-url-value=presenter.review_update_path data-turbo-dropdown-attribute-name-value="grade" data-turbo-dropdown-save-status-id-value=presenter.save_status_id
            .font-medium.mb-1.mr-2 Overall Grade
            .relative
              button.w-full.flex.items-center.justify-between.my-1.px-3.py-2.text-sm.font-medium.rounded-full.transition-colors type="button" data-action="click->dropdown#toggle" class=presenter.review_grade_class
                .flex
                  = presenter.review_grade.presence || "Select Grade..."
                i.fa.fa-chevron-down.ml-2
              
              .hidden.absolute.z-10.mt-1.w-48.rounded-md.bg-white.shadow-xl.ring-1.ring-black.ring-opacity-5.focus:outline-none.left-0 data-dropdown-target="menu"
                .p-2.max-h-60.overflow-auto
                  - presenter.grade_options_with_classnames.map do |label, value, classNames|
                    button.w-full.flex.items-center.justify-between.my-1.px-3.py-2.text-sm.font-medium.rounded-full.transition-colors data-action="click->dropdown#toggle click->turbo-dropdown#save" data-dropdown-target="menuItem" data-value=value data-label=label  class=classNames
                      = label
            
              .mt-1.absolute
                span.relative.top-0.left-4.text-xs id=presenter.save_status_id
          
          //comment textarea
          - if presenter.display_overall_comment?
            .flex.flex-col.flex-grow.relative.justify-start.items-start.min-w-0
              .font-medium.mb-1.mr-2 Overall Comment
              .relative.w-full data-controller="turbo-textarea" data-turbo-textarea-save-url-value=presenter.review_update_path data-turbo-textarea-attribute-name-value="comment" data-turbo-textarea-save-status-id-value=presenter.editable_textarea_save_status_id
                p.w-full.pr-5.text-sm  data-turbo-textarea-target="display" data-action="click->turbo-textarea#edit"
                  = presenter.review_comment
                button.absolute.top-0.right-0.hover:text-gray-700 data-action="click->turbo-textarea#edit" data-turbo-textarea-target="displayButton" type="button"
                  i.fas.fa-edit

                textarea.hidden.absolute.top-0.w-full.h-full.text-sm.border.border-gray-300.p-2.text-gray-900.rounded.focus:outline-none.focus:ring-blue-500.focus:border-blue-500.hover:border-blue-300.transition-colors rows="1" data-turbo-textarea-target="textarea" data-action="input->turbo-textarea#debouncedSave blur->turbo-textarea#saveAndClose keydown.enter->turbo-textarea#saveAndClose" id="review-comment-textarea" name="submission[review_comment]"
                  = presenter.review_comment

                .text-right.mt-3
                  span.text-xs.relative.top-0 id=presenter.editable_textarea_save_status_id


        - if presenter.publishable?
          //right side with preview and publish buttons
          .flex.justify-end.items-center
            
            // Preview button column
            .text-center data-controller="modal"
              = render_presenter(presenter.preview_modal_presenter)
              button.mr-2.py-2.px-4.border.rounded.hover:bg-blue-800.focus:outline-none.focus:shadow-outline.transition-colors data-action="modal#open" type="button" 
                | Preview

            // Publish button column
            .text-center.relative data-controller="button-save" data-button-save-save-url-value=presenter.publish_path data-button-save-method-value="post" data-button-save-save-status-id-value=presenter.button_save_status_id data-button-save-redirect-url-value=presenter.redirect_next_path
              button.bg-white.text-blue-950.font-bold.py-2.px-4.border.rounded.hover:bg-blue-200.focus:outline-none.focus:shadow-outline.transition-colors data-publish-to-canvas-target="button" data-action="click->button-save#save" type="button"
                | Publish
              .absolute
                span.relative.top-2.text-xs.text-gray-500 id=presenter.button_save_status_id


