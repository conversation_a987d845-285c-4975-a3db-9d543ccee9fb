#submission_status.p-6.mb-6.border-b.rounded-lg.relative.min-h-40 class=presenter.status_presenter.status_class
  // Status display positioned at top right
  .absolute.top-4.right-6
    span.px-3.py-1.text-sm.rounded-full.shadow-sm.inline-block.text-black.bg-white
      = presenter.status_presenter.text

  .absolute.bottom-4.right-6
    - if presenter.show_admin_link?
      button.border.border-white.text-white.bg-transparent.font-bold.py-2.px-4.rounded.focus:outline-none.focus:ring-2.focus:ring-white.transition-colors.mr-2
        = link_to presenter.admin_submission_url, target: "_blank", class: "hover:text-blue-600 inline-flex items-center" do
          | Admin 
          i.fas.fa-external-link-alt.ml-2
    button.border.border-white.text-white.bg-transparent.font-bold.py-2.px-4.rounded.focus:outline-none.focus:ring-2.focus:ring-white.transition-colors
      = link_to presenter.submission_url, target: "_blank", class: "hover:text-blue-600 inline-flex items-center" do
        | View Submission 
        i.fas.fa-external-link-alt.ml-2

  .space-y-4
    .flex.items-center
      h1.text-2xl.font-bold = presenter.assignment_name
    
    .flex.items-center
      span.font-medium.mr-2 = presenter.section_name
      span.font-medium.mr-2 Week
      span.font-medium = presenter.week
    
    .flex.items-center
      span.font-medium = presenter.student_name