dialog.backdrop:bg-black/80.rounded-lg  data-modal-target="dialog"
  .bg-white.rounded-lg.shadow-xl.max-w-2xl.w-full
    .flex.justify-between.items-center.border-b.border-gray-200.p-4
      h3.text-lg.font-medium.text-gray-900 Confirm Publish to Canvas
      button.text-gray-400.hover:text-gray-500.focus:outline-none data-action="modal#close" type="button"
        i.fas.fa-times
    
    .flex.justify-between.items-center.px-6.py-2.m-2.border-b.rounded-lg class=presenter.review_grade_background_color
      .label.font-medium Overall Grade
      .my-1.px-3.py-2.text-sm.font-medium.rounded-full class=presenter.review_grade_class
        = presenter.review_grade
      
    .font-medium.p-4.text-left Comment

    .bg-gray-50.px-6.py-4.rounded-lg.m-6.text-left= simple_format(presenter.comment_preview)
    
    .bg-blue-950.px-4.py-3.pb-6.flex.justify-end.items-center.text-white
      button.mr-2.py-2.px-4.border.rounded.hover:bg-blue-800.focus:outline-none.focus:shadow-outline.transition-colors data-action="modal#close" type="button"
        |Back to edit
      div data-controller="button-save" data-button-save-save-url-value=presenter.publish_path data-button-save-method-value="post" data-button-save-save-status-id-value=presenter.save_status_id data-button-save-redirect-url-value=presenter.redirect_next_path
        button.bg-white.text-blue-950.font-bold.py-2.px-4.border.rounded.hover:bg-blue-200.focus:outline-none.focus:shadow-outline.transition-colors data-publish-to-canvas-target="button" data-action="click->button-save#save" type="button"
          |Publish to Canvas
        .absolute
          span.relative.top-0.text-xs id=presenter.save_status_id
              