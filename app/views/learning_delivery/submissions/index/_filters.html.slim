
.sticky.top-0.bg-white.z-10.py-3.px-6.pb-6.border-b.border-gray-200
  div
    .inline-block.text-xl.font-semibold.text-left Filter
    .inline-block.ml-4 
      = link_to "Clear", presenter.submissions_path_with_per_page, data: { turbo_action: "advance" },  class: "px-4 rounded-full border border-slate-300 text-sm font-medium cursor-pointer inline-block"
      
  .flex.justify-start
    .flex.gap-x-4.mt-2
      = form_with url: presenter.submissions_path_with_per_page, method: :get, local: true, class: "flex items-center gap-x-4", data: { controller: "tom-select", tom_select_auto_submit_value: true, turbo_action: "advance" } do |f|
        = f.select :section_id, options_for_select(presenter.section_options, presenter.selected_section_id), { include_blank: "Select Section" }, class: "tom-select rounded-md border border-slate-300 w-80", data: { tom_select_target: "select", tom_select_placeholder_value: "Search section...", tom_select_allow_clear_value: true }
        = f.select :assignment_template_id, options_for_select(presenter.assignment_template_options, presenter.selected_assignment_template_id), { include_blank: "Select Assignment" }, class: "tom-select rounded-md border border-slate-300 w-[38rem]", data: { tom_select_target: "select", tom_select_placeholder_value: "Search assignment...", tom_select_allow_clear_value: true }
        = f.select :learner_id, options_for_select(presenter.learner_options, presenter.selected_learner_id), { include_blank: "Select Learner" }, class: "tom-select rounded-md border border-slate-300 w-80", data: { tom_select_target: "select", tom_select_placeholder_value: "Search learner...", tom_select_allow_clear_value: true }
        
        = f.hidden_field :per_page, value: params[:per_page] if params[:per_page]

.flex.justify-start.items-center
  .h2.p-6.pt-3.text-2xl.font-bold Submission List
  .flex.gap-x-2.p-6.pt-3
    = form_with url: presenter.submissions_path_with_per_page, method: :get, local: true, data: { turbo_action: "advance" } , class: "inline" do |f|
      = f.hidden_field :section_id, value: params[:section_id] if params[:section_id]
      = f.hidden_field :assignment_template_id, value: params[:assignment_template_id] if params[:assignment_template_id]
      = f.hidden_field :learner_id, value: params[:learner_id] if params[:learner_id]
      = f.hidden_field :filter, value: 'needs_review'
      = f.hidden_field :per_page, value: params[:per_page] if params[:per_page]
      button(type='submit' class="px-4 py-2 bg-gray-100 rounded-lg text-sm font-medium cursor-pointer #{'bg-indigo-100 text-indigo-700' if presenter.filter_needs_review?}")
        span Needs Review
        - if presenter.needs_review_count > 0
          span.ml-1.inline-flex.items-center.justify-center.px-2.py-1.text-xs.font-bold.leading-none.text-white.bg-indigo-700.rounded-lg = presenter.needs_review_count
    
    = form_with url: presenter.submissions_path_with_per_page, method: :get, local: true, data: { turbo_action: "advance" } , class: "inline" do |f|
      = f.hidden_field :section_id, value: params[:section_id] if params[:section_id]
      = f.hidden_field :assignment_template_id, value: params[:assignment_template_id] if params[:assignment_template_id]
      = f.hidden_field :learner_id, value: params[:learner_id] if params[:learner_id]
      = f.hidden_field :filter, value: 'new_comments'
      = f.hidden_field :per_page, value: params[:per_page] if params[:per_page]
      button type="submit" class="px-4 py-2 bg-gray-100 rounded-lg text-sm font-medium cursor-pointer #{'bg-indigo-100 text-indigo-700' if presenter.filter_new_comments?}"
        span New Comments
        - if presenter.new_comments_count > 0
          span.ml-1.inline-flex.items-center.justify-center.px-2.py-1.text-xs.font-bold.leading-none.text-white.bg-indigo-700.rounded-full = presenter.new_comments_count

    - if presenter.show_processing_filter?
      = form_with url: presenter.submissions_path_with_per_page, method: :get, local: true, data: { turbo_action: "advance" } , class: "inline" do |f|
        = f.hidden_field :section_id, value: params[:section_id] if params[:section_id]
        = f.hidden_field :assignment_template_id, value: params[:assignment_template_id] if params[:assignment_template_id]
        = f.hidden_field :learner_id, value: params[:learner_id] if params[:learner_id]
        = f.hidden_field :filter, value: 'processing'
        = f.hidden_field :per_page, value: params[:per_page] if params[:per_page]
        button type="submit" class="px-4 py-2 bg-gray-100 rounded-lg text-sm font-medium cursor-pointer #{'bg-indigo-100 text-indigo-700' if presenter.filter_processing?}"
          span Processing
          - if presenter.processing_count > 0
            span.ml-1.inline-flex.items-center.justify-center.px-2.py-1.text-xs.font-bold.leading-none.text-white.bg-indigo-700.rounded-full = presenter.processing_count

    = form_with url: presenter.submissions_path_with_per_page, method: :get, local: true, data: { turbo_action: "advance" } , class: "inline" do |f|
      = f.hidden_field :section_id, value: params[:section_id] if params[:section_id]
      = f.hidden_field :assignment_template_id, value: params[:assignment_template_id] if params[:assignment_template_id]
      = f.hidden_field :learner_id, value: params[:learner_id] if params[:learner_id]
      = f.hidden_field :filter, value: 'all'
      = f.hidden_field :per_page, value: params[:per_page] if params[:per_page]
      button type="submit" class="px-4 py-2 bg-gray-100 rounded-lg text-sm font-medium cursor-pointer #{'bg-indigo-100 text-indigo-700' if presenter.filter_all?}"
        span All