= render_presenter(@presenter.shared_setup_presenter)
= render_presenter(@presenter.meta_tags_presenter)
= render(partial: 'site/components/trustpilot_widget')

- content_for(:head) do
  link[rel="canonical" href="#{@presenter.canonical_url}"]

= site_cache(@presenter.cache_key('landing-header')) do
  - # Header
  .sticky.z-40.top-0[data-controller="button-dropdown"]
    = render_presenter(@presenter.header_banner_presenter)
    .bg-p.min-w-full.py-3
      .flex.justify-between.max-w-7xl.mx-auto.px-4.relative
        div
          = image_tag(@presenter.logo_on_primary_background.cdn_url, class: 'max-w-72', alt: "#{@presenter.partner.name} logo", style: @presenter.logo_css)
          - if @presenter.cobranding_text.present?
            .text-xs.text-pbg.mt-3[style= "#{@presenter.cobranding_css}"]= @presenter.cobranding_text
          .flex.items-center.lg:hidden.absolute.right-5[class="top-[10%]"]
            button.relative.inline-flex.items-center.justify-center.rounded-md.p-2.font-medium.text-pbg.hover:bg-gray-100.hover:text-gray-500.focus:outline-none.focus:ring-2.focus:ring-inset.focus:ring-indigo-500[type="button" data-button-dropdown-target="button" data-action="button-dropdown#toggle click@window->button-dropdown#hide" aria-controls="mobile-menu" aria-expanded="false"]
              span.absolute.-inset-0.5
              span.sr-only
                | Open menu
              = svg(:bars_3, class: 'block h-6 w-6', data: { 'button-dropdown-target' => 'openButton'})
              = svg(:x, class: 'hidden h-6 w-6', data: { 'button-dropdown-target' => 'closeButton'})
          .hidden.lg:relative.md:ml-4.md:flex.md:items-center
            .relative.ml-4.flex-shrink-0.group
              div
                button#user-menu-button.relative.flex.rounded-full.font-medium.bg-white.focus:outline-none.focus:ring-2.focus:ring-indigo-500.focus:ring-offset-2[type="button" aria-expanded="false" aria-haspopup="true"]
                  span.absolute.-inset-1.5
                  span.sr-only
                    | Open user menu
              .hidden.absolute.top-6.right-0.mt-2.w-48.origin-top-right.group-hover:block[aria-orientation="vertical" aria-labelledby="user-menu-button" tabindex="-1" data-transition-enter="transition ease-out duration-100" data-transition-enter-from="opacity-0 scale-95" data-transition-enter-to="opacity-100 scale-100" data-transition-leave="transition ease-in duration-75" data-transition-leave-from="opacity-100 scale-100" data-transition-leave-to="opacity-0 scale-95"]
                .rounded-md.bg-white.mt-1.py-1.shadow-lg.ring-1.ring-black.ring-opacity-5.focus:outline-none[aria-orientation="vertical" aria-labelledby="user-menu-button" tabindex="-1"]
        .hidden.lg:flex.gap-x-8.items-center.text-pbg.text-sm
          a.hover:underline(href="#Curriculum" data-action="scrolling#scroll") CURRICULUM
          a.hover:underline(href="#tuition-and-dates" data-action="scrolling#scroll") TUITION & DATES
          a.hover:underline(href="#testimonials" data-action="scrolling#scroll") TESTIMONIALS
          a.hover:underline(href="#faqs" data-action="scrolling#scroll") FAQ
          a.hover:underline(href="#explore" data-action="scrolling#scroll") MEET WITH AN ADVISOR
          a.font-medium.rounded.px-4.py-2.text-sbg.bg-s.hover:backdrop-brightness-75.hover:underline[href=@presenter.enroll_url] ENROLL

    .absolute.w-full.lg:hidden.px-8.py-8.bg-s.text-center.text-lg.backdrop-brightness-200[data-button-dropdown-target="menu" aria-label="Global" data-transition-enter="transition ease-out duration-100" data-transition-enter-from="opacity-0 scale-95" data-transition-enter-to="opacity-100 scale-100" data-transition-leave="transition ease-in duration-75" data-transition-leave-from="opacity-100 scale-100" data-transition-leave-to="opacity-0 scale-95"]
      .flex.flex-col.gap-y-4.min-w-full
        a.block.text-sbg.hover:underline(href="#Curriculum" data-action="button-dropdown#close scrolling#scroll") CURRICULUM
        a.block.text-sbg.hover:underline(href="#tuition-and-dates" data-action="button-dropdown#close scrolling#scroll") TUITION & DATES
        a.block.text-sbg.hover:underline(href="#testimonials" data-action="button-dropdown#close scrolling#scroll") TESTIMONIALS
        a.block.text-sbg.hover:underline(href="#faqs" data-action="button-dropdown#close scrolling#scroll") FAQ
        a.block.text-sbg.hover:underline(href="#explore" data-action="button-dropdown#close scrolling#scroll") MEET WITH AN ADVISOR
        a.font-medium.block.text-sbg.rounded.px-4.py-2.backdrop-brightness-75.hover:backdrop-brightness-50.hover:underline[href=@presenter.enroll_url] ENROLL

  - # Mobile sticky footer
  .flex.flex-col.py-5.bg-p.text-pbg.fixed.bottom-0.w-full.md:hidden[class="z-[99]"]
    a.rounded.font-medium.text-sbg.text-xs.py-2.mx-auto.bg-s.px-32.w-auto.hover:backdrop-brightness-75.hover:underline[href=@presenter.enroll_url]
      | ENROLL NOW

- # Hero
#main-content.md:flex.relative[class="md:h-[460px]"]
  .flex.flex-col.md:flex-row.text-center.md:text-left.max-w-7xl.w-full.mx-auto.relative
    .flex.flex-col.my-8.md:my-0.gap-y-4.items-center.md:items-start.justify-center.max-w-3xl.relative.px-6.md:pr-4[class="md:w-[90%] min-[1024px]:w-[67%] min-[1160px]:w-[90%] 2xl:w-[90%]"]
      - if @presenter.discount_code_name
        .w-full
          .w-fit.whitespace-nowrap.text-sm.bg-discount-green-zip.border-2.border-dotted.border-secondary-discount-green-zip.p-1
            span.uppercase= @presenter.discount_code_name
            span= ':'
            span.ml-8.span.font-bold= "-#{@presenter.promos_discount_currency_floored}"
      h1.mt-4.md:mt-2.text-4xl.md:text-4xl.xl:text-5xl.w-full.font-semibold[class="min-[1160px]:text-5xl"]= @presenter.hero_text
      .text-lg.xl:text-lg.md:text-md= @presenter.hero_subtext
      a.font-medium.rounded.mb-8.px-12.py-2.text-pbg.bg-p.w-full.sm:w-auto.hover:backdrop-brightness-75.hover:underline.md:mr-auto[href=@presenter.enroll_url] Enroll Now
    .flex.flex-col.justify-end.w-full.md:flex-row.relative.z-10.items-end
      = image_tag(@presenter.hero_image.cdn_url, class: 'hidden lg:inline-block h-4/5 relative right-[-43px]', alt: 'Professional in work setting')
      .px-6.md:pl-10.pt-5.pb-5.md:pb-0.bg-s.w-full.mx-auto.md:mx-0[class= "md:h-[460px]"]
        .p-5.bg-white.rounded-lg.sm:w-3/4.mx-auto[class="md:w-[396px]" aria-label="Form to View Syllabus"]
          = render_presenter(@presenter.syllabus_form_presenter(location: 'hero'))
  .bg-s.text-sbg.py-10.pr-8.pl-7.absolute.md:h-full.bottom-0.right-0.w-full[class="md:w-1/2 lg:w-[25%] 2xl:w-[39%]"]

= site_cache(@presenter.cache_key('landing-body')) do
  - # 3 Points
  .flex.flex-wrap.justify-center.bg-gray-100
    .md:flex.flex-col.max-w-7xl.w-full.py-3.5
      .text-center.mb-6.mx-2.5.flex.justify-between.items-center.pt-6.flex-col.sm:flex-row
        - @presenter.key_points.each do |hash|
          - title = hash.keys.first
          - subtitle = hash.values.first
          .flex.items-center.space-x-2.flex-col.mb-4
            = svg(:check, class: 'w-6 h-6 inline-block')
            .font-bold= title
            | #{subtitle}

  - # Step 1
  #Curriculum.max-w-7xl.mx-auto.px-4.mt-16[data-scrolling-offset="180"]
    .mx-auto.max-w-3xl
      h2.font-semibold.text-4xl.m-auto.text-center= @presenter.secondary_headline

    .flex.flex-col-reverse.gap-y-8.lg:flex-row.justify-between.items-start.my-16.gap-x-24
      .text-lg
        .font-medium.rounded.inline-block.mb-2.py-1.px-3.text-pbg.bg-p Step 1
        h3.my-3.font-semibold.text-4xl =@presenter.step_1_heading
        p.mb-2[class="lg:max-w-[710px]"]= @presenter.step_1_blurb
        = render_presenters(@presenter.expandable_skill_presenters)
        a.flex.items-center.gap-x-2.mt-6.font-semibold.text-p.hover:underline(href="#syllabus-request" data-action="scrolling#scroll")
          | Download Syllabus
          = svg(:arrow_right, class: 'w-6 h-6')

      div[class='w-full relative lg:w-[450px]' data-controller="modal modal-overflow"]
        dialog.backdrop:bg-black/80[data-modal-target="dialog" style="width: 80%" data-action="keydown.esc->modal-overflow#close"]
          button.font-medium.rounded-full.hover:cursor-pointer.absolute.z-10.bg-white.text-black.h-9.w-9.leading-8.flex.justify-center.text-2xl.right-4.top-2.close-button type="button" aria-label="Close" data-action="click->modal#hide click->modal-overflow#close"
            | x
          .aspect-w-16.aspect-h-9
            iframe[width="640" height="360" frameborder="0" allow="autoplay; fullscreen; picture-in-picture; clipboard-write" loading="lazy" tabindex="0" data-modal-overflow-target="video" data-video-url=@presenter.preview_video_url]
        a.hover:cursor-pointer[data-action="click->modal-overflow#open click->modal#open" href="javascript:void(0);" data-turbo="false" aria-label="View Introduction to #{@presenter.program_short_name} Video"]
          img.video-img[src=url_for(@presenter.preview_video_image.cdn_url) loading="lazy" alt="Introduction to #{@presenter.program_short_name} Video Still"]

  - # TrustBox widget - Micro Combo
  .max-w-7xl.w-full.mx-auto.rounded.bg-neutral-100.py-4
    .trustpilot-widget.hidden.sm:block.sm:min-h-5[data-locale="en-US" data-template-id="5419b6ffb0d04a076446a9af" data-businessunit-id="65bbfcf2e0b59cd0eea2041b" data-style-height="20px" data-style-width="100%" data-theme="light"]
      a[href="https://www.trustpilot.com/review/ziplines.com" target="_blank" rel="noopener"] Trustpilot
    .trustpilot-widget.sm:hidden[class="min-h-[150px]" data-locale="en-US" data-template-id="53aa8807dec7e10d38f59f32" data-businessunit-id="65bbfcf2e0b59cd0eea2041b" data-style-height="150px" data-style-width="100%" data-theme="light" style="position: relative;"]

  - # Step 2
  .max-w-7xl.mx-auto.px-4.mt-16
    .flex.flex-col.gap-y-8.lg:flex-row.justify-between.items-start.my-16.gap-x-16
      .w-full
        .flex.justify-center.rounded.border.border-gray-300.object-fill.overflow-hidden[class="max-w-[500px] mx-auto lg:max-w-none lg:mx-8"]
          = image_tag(@presenter.sample_certificate.cdn_url, style: 'max-width: none; width: 110%; margin: -13px -13px -29px', alt: 'Sample Certificate')
        - if @presenter.brand_logos_allowed?
          .flex.justify-center.items-center.pt-8.gap-x-6
            - @presenter.applied_technologies.credential.each do |applied_technology|
              = image_tag(applied_technology.logo.cdn_url, loading: :lazy, alt: applied_technology.name, class: 'object-fill h-11, w-10', style: applied_technology.logo_css)

      .w-full
        .text-lg
          .font-medium.rounded.inline-block.mb-2.py-1.px-3.text-pbg.bg-p Step 2
          .font-semibold.text-4xl.m-auto.mb-2.5= @presenter.step_2_title
          = @presenter.step_2_body
          a.flex.items-center.gap-x-2.mt-8.font-semibold.text-p.hover:underline[href=@presenter.enroll_url]
            | Enroll Now
            = svg(:arrow_right, class: 'w-6 h-6')
  - # Step 3
  .max-w-7xl.mx-auto.px-4.mt-16
    .flex.flex-col-reverse.gap-y-8.lg:flex-row.justify-between.items-start.my-16.gap-x-16
      .w-full
        .text-lg
          .rounded.inline-block.mb-2.py-1.px-3.text-pbg.bg-p Step 3
          h3.my-3.font-semibold.text-4xl = @presenter.step_3_title
          p.mb-2.font-light= @presenter.expandable_career_blurb
          = render_presenters(@presenter.expandable_career_presenters)
          - if @presenter.expandable_so_you_can_presenters.present?
            .font-semibold.my-4 So you can...
            = render_presenters(@presenter.expandable_so_you_can_presenters)

      .mx-auto.lg:mx-8.px-8.text-sbg.rounded-md.lg:w-full.lg:max-w-none[class="max-w-[420px] md:max-w-[500px] #{@presenter.career_quote_image_classes[:container]}"]
        = image_tag(@presenter.career_quote_image.cdn_url, class: "max-h-[70vh] lg:max-h-none drop-shadow-md -mt-12 -ml-12 #{@presenter.career_quote_image_classes[:image_tag]}", alt: @presenter.career_quote_alt_text)

  - # Explore Options
  #explore.justify-center.w-px.max-w-screen-xl.h-px.mx-auto.flex.absolute.-mt-32[data-scrolling-offset="40"]
  .max-w-7xl.mx-auto.px-4.mt-16
    .flex.flex-col.gap-y-6.items-center.bg-gray-100.p-12.rounded-xl.text-center
      .text-4xl.font-semibold Explore your options
      .text-lg.font-light=@presenter.explore_options
      a.flex.items-center.gap-x-2.mt-6.font-semibold.hover:underline[href="#" onclick="Calendly.initPopupWidget({url: '#{@presenter.admissions_schedule_url}'});return false;" data-controller="calendly"]
        | Schedule a time
        = svg(:arrow_right, class: 'w-6 h-6')
  - # Instructors
  .max-w-7xl.mx-auto.px-4.mt-16
    .text-center.max-w-3xl.mx-auto
      h3.text-4xl.font-semibold=@presenter.instructors_headline
      .mt-4.font-light =@presenter.instructors_blurb

    .mt-16.flex.justify-center
      = render_presenter(@presenter.instructors_presenter)

  = render_presenter(@presenter.eva_banner_presenter)

  - # Enroll
  #tuition-and-dates.max-w-7xl.mx-auto.px-4.mt-32.text-center[data-scrolling-offset="180"]
    h3.text-4xl.font-semibold.mb-8 Enroll now in #{@presenter.program_short_name}
    .flex.flex-wrap.max-w-4xl.border.rounded.border-gray-300.mx-auto
      .flex.flex-col.basis-0.grow.gap-y-5.p-12.text-lg
        .text-3xl #{@presenter.program_short_name.titlecase} #{@presenter.course_nomenclature.titlecase}
        p= @presenter.program_duration_text
        p
          | Flexible online learning with
          span.font-semibold<> optional
          | mentor-led live sessions
        p
          .mb-4.text-base.text-nowrap
            .mb-1.text-center
              div[class="#{@presenter.discount_code_name ? 'float-start' : 'inline-block'}"] Price:
              div[class="#{@presenter.discount_code_name ? 'text-right' : 'ml-2 inline-block'}"]
                strike<>= @presenter.list_currency_ceiled
                span.text-p.font-bold.pl-0.5<> #{@presenter.sale_currency_ceiled} (SAVE #{number_to_percentage(@presenter.sale_savings_percent, precision: 0)})

            - if @presenter.discount_code_name
              .mb-1.text-sm.bg-discount-green-zip.border-2.border-dotted.border-secondary-discount-green-zip.p-1
                .float-start
                  span.uppercase= @presenter.discount_code_name
                  span= ':'
                .text-right.font-bold= "-#{@presenter.promos_discount_currency_floored}"
              .mb-1
                .float-start Total:
                .text-right.font-bold.text-p= @presenter.actual_currency_ceiled

        a.font-medium.rounded.inline-block.mb-2.py-1.px-3.text-pbg.bg-p.uppercase.hover:brightness-90.hover:underline[href=@presenter.enroll_url] Secure Your Spot Now
        - if @presenter.offer_buy_now_pay_later?
          .text-sm.text-center.affirm-as-low-as[data-page-type="cart" data-amount="#{@presenter.recurring_actual_cents}"]


      .flex.flex-col.basis-0.grow.gap-y-1.p-12.bg-gray-100.border-l
        i.text-p.fa-solid.fa-dollar-sign.mt-5
        p Flexible payment options are available
        a.italic.hover:underline[href=@presenter.site_reimbursement_url]
          | Get reimbursed by your employer!
          .underline Learn more
        i.text-p.fa-solid.fa-headphones.mt-5
        p On-demand with live sessions
        i.text-p.fa-solid.fa-chalkboard-user.mt-5
        p Expert instructors
        i.text-p.fa-solid.fa-award.mt-5
        p= @presenter.program_certificate_details


  - # Cohorts
  .max-w-7xl.mx-auto.px-4.mt-16
    a.block.max-w-4xl.mx-auto.mb-6.py-8.text-center.text-lg.leading-9.hover:bg-gray-100[href=@presenter.site_reimbursement_url]
      .font-light.uppercase Did You Know?
      .font-medium Many employers help with continuing education expenses.
      .font-light
        | Here are some
        span.underline<> helpful tips
        |  to get reimbursed by your employer
        = svg(:arrow_right, class: 'inline-block ml-2 w-6 h-6')

    .flex.flex-col.max-w-4xl.mx-auto.gap-y-3.max-w-4xl
      = render_presenters(@presenter.cohort_presenters)

  - # Learners Trust Pilot
  #testimonials.max-w-7xl.mx-auto.px-4.mt-32.text-center.scroll-my-10[data-scrolling-offset="180"]
    h3.text-4xl.font-semibold.mb-8 See what our learners have to say
    .mx-auto.w-10/12
      .trustpilot-widget.min-h-60[data-locale="en-US" data-template-id="54ad5defc6454f065c28af8b" data-businessunit-id="65bbfcf2e0b59cd0eea2041b" data-style-height="240px" data-style-width="100%" data-theme="light" data-tags="General" data-stars="1,2,3,4,5" data-review-languages="en"]
        a[href="https://www.trustpilot.com/review/ziplines.com" target="_blank" rel="noopener"] Trustpilot

    - # Testimonial
    .mx-auto.mt-20.w-9/12
      = render_presenter(@presenter.testimonials_presenter)

  - # Brand logos
  - if @presenter.brand_logos_allowed? && @presenter.show_our_course_attracts_block?
    .max-w-7xl.mx-auto.px-4.mt-20.mb-8.text-center
      h3.text-3xl.font-semibold.text-gray-800.mb-6 Our course attracts professionals from leading brands
      .flex.flex-wrap.pt-7.justify-center.items-center.space-x-0.lg:flex-nowrap.lg:space-x-14.lg:px-14.xl:px-0.px-0
        - @presenter.company_brands.each do |company_brand|
          .lg:w-auto.w-1/3.text-center
            = image_tag(company_brand.brand_logo.cdn_url, class: "lg:h-14 h-8 mx-auto mb-6 lg:mb-0", alt: "#{company_brand.company_name} logo", style: company_brand.logo_css)

- # Course Syllabus
#syllabus-request[data-scrolling-offset="180"]
.mt-36.pt-10.pb-14.bg-gray-100
  h3.text-4xl.font-semibold.mb-8.text-center View the course syllabus
  .bg-white.rounded.pt-8.pb-14.px-5.lg:max-w-xl.mx-auto
    = render_presenter(@presenter.syllabus_form_presenter(location: 'footer'))

= site_cache(@presenter.cache_key('landing-footer')) do
  - # FAQs
  #faqs.max-w-4xl.mx-auto.px-4.mt-32.faqs.font-semibold[data-scrolling-offset="180"]
    h3.text-4xl.font-semibold.mb-8 Frequently Asked Questions
    = render_presenters(@presenter.faqs_presenters)

  - if @presenter.show_blog_link?
    .max-w-4xl.mx-auto.px-4
      p.text-center
        a.mt-2.5.flex.items-center.justify-center.hover:cursor-pointer[href=@presenter.blog_url]
          span.ml-2.hover:text-gray-500.underline
            | Get advice on #{@presenter.program_short_name} by checking out our recent articles.
          i.fas.fa-newspaper.ml-2

  - # Custom Disclaimer
  .container.mx-auto.max-w-4xl
    = render_presenter(@presenter.disclaimer_presenter)
  .flex.flex-col.mt-16.py-5.bg-p.text-pbg
    h3.mb-4.text-xl.text-center= @presenter.banner_cta_header_text
    a.font-medium.rounded.text-xl.py-2.mx-auto.bg-s.text-sbg.px-10.md:px-32.w-auto.hover:backdrop-brightness-75.hover:underline[href=@presenter.enroll_url] ENROLL NOW

  = render_presenter(@presenter.footer_presenter)

- content_for(:scripts)
  - if @presenter.offer_buy_now_pay_later?
    = render(partial: 'shared/affirm_script')
  div[data-controller="store-marketing-tracking-fields"]
