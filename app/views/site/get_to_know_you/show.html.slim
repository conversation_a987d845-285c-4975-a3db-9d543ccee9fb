= render_presenter(@presenter.shared_setup_presenter)

- content_for(:meta_title) { "Welcome to your #{@presenter.partner_short_name} #{@presenter.course_nomenclature&.titlecase}" }
- content_for(:meta_description) { "Getting started on your #{@presenter.partner_short_name} #{@presenter.course_nomenclature&.titlecase}." }

.sticky.z-40.top-0
  .bg-p.min-w-full
    .flex.justify-between.max-w-7xl.mx-auto.p-4
      div
        = image_tag(@presenter.logo_on_primary_background.cdn_url, class: 'max-w-72', alt: "#{@presenter.partner.name} logo", style: @presenter.logo_css)
        - if @presenter.cobranding_text.present?
          .text-xs.text-pbg.mt-3[style= "#{@presenter.cobranding_css}"]= @presenter.cobranding_text

#main-content.w-full.bg-gray-100
  .flex.container.mx-auto.justify-between.max-w-6xl.items-center.pb-10.flex-wrap.md:flex-nowrap
    .m-0.w-full[class="!ml-0 lg:m-0 pl-3"]
      .text-6xl.font-bold.mt-20.mb-5.text-center.md:text-left Hello and Welcome!
      p.mb-5.text-lg.text-center.md:text-left Success looks different for everyone. To better support you, our Learner Success Team would like to learn a little bit more about you and your intentions for this course. Please take 3 minutes to complete this brief survey.

- # Hubspot Form
.container.mx-auto.mb-24.w-full.max-w-6xl.mt-0.xl:max-w-6xl
  #hubspot-form.-mt-10.justify-center.items-center.p-12.bg-white.shadow-xl.rounded-lg.flex-wrap.relative.overflow-hidden

- # Footer
= render_presenter(@presenter.footer_presenter)

= content_for(:scripts)
  javascript:
    document.addEventListener('turbo:load', function() {
      var script = document.createElement('script');
      script.src = '//js.hsforms.net/forms/v2.js';
      script.onload = function() {
        hbspt.forms.create({
          region: 'na1',
          portalId: '3461273',
          formId: 'f8aee531-6cb9-4c8a-8def-d56392c175cb',
          target: '#hubspot-form'
        });
      };
      document.head.appendChild(script);
    });
