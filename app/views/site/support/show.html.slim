= render_presenter(@presenter.shared_setup_presenter)
= render_presenter(@presenter.meta_tags_presenter)
= render(partial: 'site/components/trustpilot_widget')

- content_for(:head) do
  link[rel="canonical" href="#{@presenter.canonical_url}"]

/ Cached header section following existing pattern
= site_cache(@presenter.cache_key('support-header')) do
  .sticky.z-40.top-0.bg-white.border-b.border-gray-200[data-controller="button-dropdown"]
    = render_presenter(@presenter.header_banner_presenter)
    .bg-p.min-w-full.py-3
      .flex.justify-between.max-w-7xl.mx-auto.px-4.relative
        div
          = image_tag(@presenter.logo_on_primary_background.cdn_url, class: 'max-w-72', alt: "#{@presenter.partner.name} logo", style: @presenter.logo_css)
          - if @presenter.cobranding_text.present?
            .text-xs.text-pbg.mt-3[style= "#{@presenter.cobranding_css}"]= @presenter.cobranding_text
          / Mobile hamburger menu button
          .flex.items-center.lg:hidden.absolute.right-5.top-1/2.-translate-y-1/2
            button.relative.inline-flex.items-center.justify-center.rounded-md.p-2.font-medium.text-pbg.hover:bg-gray-100.hover:text-gray-500.focus:outline-none.focus:ring-2.focus:ring-inset.focus:ring-indigo-500[type="button" data-button-dropdown-target="button" data-action="button-dropdown#toggle click@window->button-dropdown#hide" aria-controls="mobile-menu" aria-expanded="false"]
              span.absolute.-inset-0.5
              span.sr-only
                | Open menu
              = svg(:bars_3, class: 'block h-6 w-6', data: { 'button-dropdown-target' => 'openButton'})
              = svg(:x, class: 'hidden h-6 w-6', data: { 'button-dropdown-target' => 'closeButton'})

        .hidden.lg:flex.lg:ml-4.lg:items-center
          .header-cta-container
            = link_to "VIEW SYLLABUS", @presenter.syllabus_url, class: "header-cta-button text-white px-4 py-2 w-auto h-auto", style: "background-color: transparent; border: 1px solid transparent;"
            = link_to "ENROLL", @presenter.enroll_url, class: "header-cta-button text-sbg bg-s w-auto h-auto font-medium rounded px-4 py-2 !border-none hover:underline"

    / Mobile menu dropdown
    .absolute.w-full.lg:hidden.px-8.py-8.bg-s.text-center.text-lg.backdrop-brightness-200[data-button-dropdown-target="menu" aria-label="Global" data-transition-enter="transition ease-out duration-100" data-transition-enter-from="opacity-0 scale-95" data-transition-enter-to="opacity-100 scale-100" data-transition-leave="transition ease-in duration-75" data-transition-leave-from="opacity-100 scale-100" data-transition-leave-to="opacity-0 scale-95"]
      .flex.flex-col.gap-y-4.min-w-full
        a.font-medium.block.text-sbg.bg-s.rounded.px-4.py-2.hover:underline[href=@presenter.enroll_url data-action="button-dropdown#close"] ENROLL NOW
        a.block.text-sbg.hover:underline[href=@presenter.syllabus_url data-action="button-dropdown#close"] VIEW SYLLABUS

/ Main content following site pattern
main#main-content.max-w-7xl.mx-auto.px-4.py-6.lg:py-12
    - # TrustBox widget
    .max-w-7xl.w-full.mx-auto.rounded.py-4
      .trustpilot-widget[data-locale="en-US" data-template-id="54197383fd9dceac42a68694" data-businessunit-id="65bbfcf2e0b59cd0eea2041b" data-style-width="100%" data-style-size="S" data-text-color="dark" data-headline="star" data-support-text="review-number" data-background="none" data-external-elements-color="dark"]
        a[href="https://www.trustpilot.com/review/ziplines.com" target="_blank" rel="noopener"] Trustpilot

    / Hero Section
    section.text-center.mb-8
      h1.hero-title-responsive.mb-8.mt-5.leading-none[class="text-[38px] md:text-[48px]"]
        | Get the answers you need, your way
      p.hero-description
        | We're here to support you — whenever and however you need us.

    section.mb-12
      .flex.flex-col.lg:flex-row.support-cards-container.mx-auto.justify-center
        - @presenter.support_options.each do |option|
          = render 'support_card', option: option

    / Mobile-only Enroll Now Button
    .mobile-enroll-button.lg:hidden.text-center.mb-8
      = link_to "ENROLL NOW", @presenter.enroll_url, class: "mobile-enroll-btn bg-s text-sbg"

    / FAQ Section
    - if @presenter.articles?
      section[data-controller="support-faq" data-support-faq-total-value=@presenter.total_articles_count]
        .text-center.mb-12
          h2.faq-title.mb-4[class="!text-[34px] md:!text-[33px]"] How can we help? Search our FAQs

        / Filter Dropdown
        .mb-4
          .sr-only Filter articles by category
        .mb-12[data-support-faq-target="filterContainer"]
          .mx-auto.w-full.max-w-xs.sm:max-w-sm[class="md:max-w-[24rem]"]
            label.sr-only[for="content-tag-filter"] Filter articles by category
            .relative
              select#content-tag-filter.w-full.px-3.py-2.text-base.font-medium.border.rounded-md.transition-colors.duration-200.bg-white.cursor-pointer.focus:outline-none.focus:ring-2.focus:ring-offset-2.font-barlow.brand-navy.border-brand-navy[
                data-support-faq-target="filterSelect"
                data-action="change->support-faq#filterByTag"
                aria-label="Filter articles by category"
              ]
                option[value="all"] All Categories
                - @presenter.available_content_tags.each do |display_name, tag_key|
                  option[value=tag_key]= display_name

        / FAQ Accordion
        .max-w-4xl.mx-auto[data-support-faq-target="articlesContainer"]
          - @presenter.articles.each_with_index do |article, index|
            .border-b.faq-item[
              data-support-faq-target="faqItem"
              data-tags=@presenter.article_tags(article).join(',')
              data-controller="accordion"
            ]
              button.w-full.cursor-pointer.py-3.px-2.text-left.hover:bg-gray-200.focus:outline-none[
                type="button"
                id="accordion-button-#{index}"
                data-action="click->accordion#toggle"
                data-accordion-target="trigger"
                aria-expanded="false"
                aria-controls="accordion-content-#{index}"
              ]
                .flex.justify-between.items-center
                  h3.text-lg.font-medium.text-gray-900.pr-4
                    = article[:title]
                  svg.w-5.h-5.text-gray-500.transform.transition-transform.duration-200[data-accordion-target="icon" viewBox="0 0 20 20" fill="currentColor"]
                    path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"

              .overflow-hidden.transition-all.duration-300.ease-in-out.max-h-0[
                id="accordion-content-#{index}"
                data-accordion-target="content"
                aria-labelledby="accordion-button-#{index}"
              ]
                .pl-8.pb-4.text-gray-700.prose.prose-base.max-w-none
                  = @presenter.sanitized_article_body(article)

        .text-center.py-12.hidden[data-support-faq-target="noResults"]
          .w-16.h-16.mx-auto.mb-4.bg-gray-100.rounded-full.flex.items-center.justify-center
            svg.w-8.h-8.text-gray-400[viewBox="0 0 24 24" fill="none" stroke="currentColor"]
              circle cx="11" cy="11" r="8"
              path d="m21 21-4.35-4.35"
          h3.text-lg.font-medium.text-gray-900.mb-2 No articles found
          p.text-gray-500 Try selecting a different category filter

    / Custom Disclaimer Section (below FAQs)
    - if @presenter.show_custom_disclaimer?
      section.max-w-4xl.mx-auto.py-8.border-t.border-gray-200.mt-8
        = render_presenter(@presenter.disclaimer_presenter)

    / Trustpilot Reviews Section
    #testimonials.max-w-7xl.mx-auto.px-4.mt-8.text-center.scroll-my-10[data-scrolling-offset="180"]
      .mx-auto.w-full.sm:w-10/12
        .trustpilot-widget[data-locale="en-US" data-template-id="53aa8912dec7e10d38f59f36" data-businessunit-id="65bbfcf2e0b59cd0eea2041b" data-style-height="140px" data-style-width="100%" data-stars="1,2,3,4,5" data-review-languages="en" data-font-family="Barlow"]
          a[href="https://www.trustpilot.com/review/ziplines.com" target="_blank" rel="noopener"] Trustpilot


/ Footer following existing site pattern
= render_presenter(@presenter.footer_presenter)
