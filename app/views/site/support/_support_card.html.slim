/ Support card partial - reusable component for support options
/ Parameters: option (hash with card data)
- tag = option[:url] ? :a : :div
- tag_attributes = {}
- tag_attributes[:href] = option[:url] if option[:url]
- if option[:controller]
  - tag_attributes[:'data-controller'] = option[:controller]
- if option[:action]
  - tag_attributes[:'data-action'] = option[:action]
- if option[:data_attributes]
  - option[:data_attributes].each do |key, value|
    - tag_attributes[:"data-#{key}"] = value
- tag_attributes[:class] = option[:css_classes]

= content_tag tag, tag_attributes do
  .support-card-content.flex.flex-col.h-full
    .flex.items-center.mb-2
      = option[:icon]
      .ml-3.flex-1
        .flex.items-center
          h3.support-card-title= option[:title]
          = @presenter.arrow_image
    p.support-card-description= option[:description]
