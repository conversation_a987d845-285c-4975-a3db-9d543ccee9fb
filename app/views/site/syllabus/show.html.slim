= render_presenter(@presenter.shared_setup_presenter)
= render_presenter(@presenter.meta_tags_presenter)
= render(partial: 'site/components/trustpilot_widget')

- content_for(:head) do
  link[rel="canonical" href="#{@presenter.canonical_url}"]


= site_cache(@presenter.cache_key('syllabus-header')) do
  -# Header
  .sticky.z-40.top-0
    .flex.justify-center.min-w-full.bg-s.text-sbg
      = render_presenter(@presenter.header_banner_presenter)
    .bg-p.py-2.w-full
      .flex.justify-between.mx-auto.px-4.max-w-7xl.xl:max-w-6xl
        div
          a[href=UrlBuilder::Site.new(partner_program:@presenter.partner_program).call!(template: :landing) aria-label="Go to program overview page"]
            = image_tag(@presenter.logo_on_primary_background.cdn_url, class: 'max-w-72', alt: "#{@presenter.partner.name} logo", style: @presenter.logo_css)
            - if @presenter.cobranding_text.present?
              .text-xs.text-pbg.mt-3[style= "#{@presenter.cobranding_css}"]= @presenter.cobranding_text
        .flex.items-center.hidden.md:flex
          a.flex.cursor-pointer.text-white.items-center.text-base.hover:underline[href="#" onclick="Calendly.initPopupWidget({url: '#{@presenter.admissions_schedule_url}'});return false;"]
            i.fa-regular.fa-calendar.my-1.mt-1.px-2.py-4x.text-white
            | BOOK TIME WITH AN ADVISOR
          .ml-4
            a.font-medium.rounded.px-4.py-2.text-sbg.text-xs.bg-s.hover:backdrop-brightness-75.hover:underline[href=@presenter.enroll_url] ENROLL

#main-content.w-full.bg-gray-100
  - # Flash Messages
  .mx-auto.w-full
    = render_flash_messages

  .flex.container.mx-auto.justify-between.max-w-6xl.items-center.md:pb-10.flex-wrap.md:flex-nowrap.pb-5
    .m-0.md:w-1/2.lg:w-3/4.w-full[class="!ml-0 lg:m-0 pl-3"]
      p.m-4.ml-0.uppercase.text-center.md:text-left Program Syllabus
      .text-6xl.md:max-lg:text-5xl.text-black.text-center.md:text-left.font-bold.mt-2.mb-5= @presenter.hero_text
      p.mb-5.text-gray-700.text-center.md:text-left= @presenter.headline_blurb
      .flex.p-3.pl-0.md:justify-start.justify-center.md:mb-0.mb-8
        a.mb-8.uppercase.font-bold.text-p.pl-2.hover[href=@presenter.syllabus_url target="_blank"]
          | Download Syllabus &nbsp;
          i.fa-solid.fa-file-arrow-down
    .md:hidden
      = render_presenter(@presenter.countdown_presenter)


    .w-full.md:w-1/2.md:max-w-80.mb-8.md:ml-8.ml-4.mr-4.md:mt-20.mt-10
      .border.bg-gray-200.relative.rounded-lg.w-full
        .bg-gray-100.absolute.left-0.right-0.mx-auto.w-16.px-3.-top-5[class="text-[#ba0c2f]"]
        .w-full
          a.text-sbg.bg-s.text-center.py-2.text-xl.block.uppercase.w-full.hover[href=@presenter.enroll_url]
            | Reserve My Seat
        .px-5.mt-8.px-5
          ul.mb-4
            li.py-2.border-b.border-dotted.border-black.flex.items-center
              span.rounded-full.p-1.bg-p.w-6.h-6.mr-3.text-white.flex.justify-center
                i.fa-solid.fa-piggy-bank
              .text-sm.text-black= @presenter.program_duration_summary
            - if @presenter.certificate_details.present?
              li.py-2.border-b.border-dotted.border-black.flex.items-center
                span.rounded-full.p-1.bg-p.w-6.h-6.mr-3.text-white.flex.justify-center
                  = svg(:file_certificate)
                .text-sm.text-black= @presenter.certificate_details
            li.py-2.border-b.border-dotted.border-black.flex.items-center
              span.rounded-full.p-1.bg-p.w-6.h-6.mr-3.text-white.flex.justify-center
                i.fa-solid.fa-check
              .text-sm.text-black Industry Expert Instructors
            li.py-2.border-b.border-dotted.border-black.flex.items-center
              span.rounded-full.p-1.bg-p.w-6.h-6.mr-3.text-white.flex.justify-center
                i.fa-solid.fa-arrow-pointer
              .text-sm.text-black Online (On-Demand + Live Sessions)
            li.py-2.flex
              span.rounded-full.p-1.bg-p.w-6.h-6.mr-3.min-w-6.text-white.flex.justify-center
                i.fa-solid.fa-dollar-sign
              .text-sm
                .mb-4.text-base.text-nowrap
                  .mb-1.text-center
                    div[class="#{@presenter.discount_code_name ? 'float-start' : 'inline-block'}"] Price:
                    div[class="#{@presenter.discount_code_name ? 'text-right' : 'ml-2 inline-block'}"]
                      strike<>= @presenter.list_currency_ceiled
                      span.text-p.font-bold.pl-0.5<> #{@presenter.sale_currency_ceiled} (SAVE #{number_to_percentage(@presenter.sale_savings_percent, precision: 0)})

                  - if @presenter.discount_code_name
                    .mb-1.text-sm.bg-discount-green-zip.border-2.border-dotted.border-secondary-discount-green-zip.p-1
                      .float-start
                        span.uppercase= @presenter.discount_code_name
                        span= ':'
                      .text-right.font-bold= "-#{@presenter.promos_discount_currency_floored}"
                    .mb-1
                      .float-start Total:
                      .text-right.font-bold.text-p= @presenter.actual_currency_ceiled
                .text-sm Flexible Payment Options Available!

        p.flex.justify-center
          a.rounded.text-white.bg-p.mx-2.mt-0.text-sm.py-2.text-center.uppercase.w-full.font-medium.p-1.hover:underline[href=@presenter.enroll_url]
            | Reserve My Seat
          - if @presenter.offer_buy_now_pay_later?
            .p-3.text-sm.text-center.affirm-as-low-as[data-page-type="cart" data-amount="#{@presenter.recurring_actual_cents}"]


.hidden.md:flex
  = render_presenter(@presenter.countdown_presenter)

= site_cache(@presenter.cache_key('syllabus-body')) do
  .container.mx-auto.max-w-7xl.xl:max-w-6xl.mb-2.md:mt-16.mt-10
    .card.border.relative.flex.rounded-lg.mt-44.px-8.py-8.bg-gray-50.mx-auto.mt-8.w-full
      .rounded-lg.flex.items-center.flex-wrap.md:flex-nowrap.md:space-x-12
        .mb-5
          p.text-sm.uppercase.text-black.font-semibold
            | Watch our course preview
          p.text-3xl.font-bold.my-2= @presenter.preview_video_headline
          p.text-gray-600= @presenter.preview_video_blurb
        div[class='w-4/5 m-auto max-w-[400px] md:max-w-[315px]' data-controller="modal modal-overflow"]
          dialog.backdrop:bg-black/80[data-modal-target="dialog" style="width:80%" data-action="keydown.esc->modal-overflow#close"]
            button.rounded-full.hover:cursor-pointer.absolute.z-10.bg-white.text-black.h-9.w-9.leading-8.flex.justify-center.text-2xl.right-4.top-2.close-button type="button" aria-label="Close" data-action="click->modal#hide click->modal-overflow#close"
              | x
            .aspect-w-16.aspect-h-9
              iframe[width="640" height="360" frameborder="0" allow="autoplay; fullscreen; picture-in-picture; clipboard-write" loading="lazy" tabindex="0" data-modal-overflow-target="video" data-video-url=@presenter.preview_video_url]
          a.hover:cursor-pointer[href="javascript:void(0);" data-turbo="false" data-action="click->modal-overflow#open click->modal#open" aria-label="View Introduction to #{@presenter.program_short_name} Video"]
            img.video-img[src=@presenter.preview_video_image.cdn_url loading="lazy" alt="Introduction to #{@presenter.program_short_name} Video Still"]

  .container-6.w-container.mx-auto.max-w-7xl.xl:max-w-6xl.mb-2.flex.gap-10.items-start.md:pl-5.xl:pl-0.mt-10
    .hidden.md:flex.flex-col.text-nowrap.sticky[class="top-[120px]"]
      a.my-2.text-base.text-gray-500.hover:underline(href="#curriculum" data-action="scrolling#scroll") Curriculum
      a.my-2.text-base.text-gray-500.hover:underline(href="#career" data-action="scrolling#scroll") Career Accelerator
      a.my-2.text-base.text-gray-500.hover:underline(href="#instructors" data-action="scrolling#scroll") Instructors
      a.my-2.text-base.text-gray-500.hover:underline(href="#our-learners" data-action="scrolling#scroll") Our Learners
      a.my-2.text-base.text-gray-500.hover:underline(href="#your-time" data-action="scrolling#scroll") Your Time
      a.my-2.text-base.text-gray-500.hover:underline(href="#earn" data-action="scrolling#scroll") What You'll Earn
      a.my-2.text-base.text-gray-500.hover:underline(href="#tuition" data-action="scrolling#scroll") Upcoming Courses


    #curriculum.mx-auto.md:mx-0.w-4/5[data-scrolling-offset="150"]
      h3.my-3.font-semibold.text-4xl What You'll Learn
      p.mb-2= @presenter.expandable_learnings_blurb
      = render_presenters(@presenter.expandable_curriculum_presenters)

      .bg-p.p-8.text-center.mt-16.mb-8
        h3.mb-2.text-white.font-semibold.text-xl READY TO GET SKILLED IN #{@presenter.program_short_name.upcase}?
        a.text-sbg.items-center.bg-s.rounded.w-full.block.my-3.font-medium.text-sm.p-2.hover:underline[href=@presenter.enroll_url] ENROLL AND SAVE
        br
        a.mb-3.text-white.curser-pointer.hover:underline[href="#" onclick="Calendly.initPopupWidget({url: '#{@presenter.admissions_schedule_url}'});return false;"]
          | Interested but need to know more? Schedule time with one of our Admissions Advisors »

      #career.justify-center.w-px.max-w-screen-xl.h-px.mx-auto.flex.mt-10[data-scrolling-offset="150"]
      .mb-10
        h3.my-3.font-semibold.text-4xl = @presenter.step_3_title
        p.mb-2.font-light = @presenter.expandable_career_blurb
        = render_presenters(@presenter.expandable_career_presenters)
        - if @presenter.expandable_so_you_can_presenters.present?
          .font-semibold.my-4 So you can...
          = render_presenters(@presenter.expandable_so_you_can_presenters)

      #instructors.justify-center.w-px.max-w-screen-xl.h-px.mx-auto.flex.md:mt-32.mt-22[data-scrolling-offset="150"]
      h2.text-black.text-4xl.font-semibold.mb-2= @presenter.instructors_headline.titlecase
      p.mb-10.text-gray-600= @presenter.instructors_blurb
      .mt-16
        = render_presenter(@presenter.instructors_presenter)
      .px-6.py-5.bg-gray-50.rounded-xl.mt-2.md:mt-32
        #our-learners.text-black.text-4xl.font-semibold.mt-8.mb-2[data-scrolling-offset="150"] Our Learners
        p.mb-6.text-base= @presenter.our_learners_blurb
        = render_presenters(@presenter.our_students_presenters)

      .px-6.py-5.bg-gray-50.rounded-xl.mt-16
        h2#your-time.text-black.text-4xl.font-semibold.mb-2[data-scrolling-offset="150"] Your Time
        p.mb-4=@presenter.your_time_blurb

      .px-6.py-5.bg-gray-50.rounded-xl.mt-16
        h2#earn.text-black.text-4xl.font-semibold.mb-3.mt-4[data-scrolling-offset="150"]  What You'll Earn
        = render_presenters(@presenter.what_you_earn_presenters)

        - if @presenter.brand_logos_allowed?
          .flex.flex-row.flex-wrap.mt-12.space-x-4
            - @presenter.applied_technologies.credential.each do |applied_technology|
              = image_tag(applied_technology.logo.cdn_url, class: 'h-14', alt: "#{applied_technology.name} logo", style: applied_technology.logo_css_large)


  .container-6.w-container.mx-auto.max-w-6xl.mb-2.items-start.text-center.md:mt-32.mt-30.flex.lg:gap-10
    .w-1/12
    .w-4/5
      .flex.border.rounded.border-gray-300.mx-auto.flex-wrap.md:mt-0.mt-3
        .flex.flex-col.basis-0.grow.gap-y-5.p-6.sm:p-12.text-lg.min-w-full.sm:min-w-0
          #tuition.scroll-my-44.justify-center.w-px.max-w-screen-xl.h-px.mx-auto.flex[data-scrolling-offset="200"]
          .text-3xl #{@presenter.program_short_name.titlecase} #{@presenter.course_nomenclature.titlecase}
          p = @presenter.program_duration_text
          p
            | Flexible online learning with
            span.font-semibold<> optional
            | mentor-led live sessions
          p[class="text-[18px] md:text-[20px]"]
            .mb-4.text-nowrap
              .mb-1.text-center
                div[class="#{@presenter.discount_code_name ? 'float-start' : 'inline-block'}"] Price:
                div[class="#{@presenter.discount_code_name ? 'text-right' : 'ml-2 inline-block'}"]
                  strike<>= @presenter.list_currency_ceiled
                  span.text-p.font-bold.pl-0.5<> #{@presenter.sale_currency_ceiled} (SAVE #{number_to_percentage(@presenter.sale_savings_percent, precision: 0)})

              - if @presenter.discount_code_name
                .mb-1.text-sm.bg-discount-green-zip.border-2.border-dotted.border-secondary-discount-green-zip.p-1
                  .float-start
                    span.uppercase= @presenter.discount_code_name
                    span= ':'
                  .text-right.font-bold= "-#{@presenter.promos_discount_currency_floored}"
                .mb-1
                  .float-start Total:
                  .text-right.font-bold.text-p= @presenter.actual_currency_ceiled

          a.font-medium.rounded.inline-block.mb-2.py-1.px-3.text-pbg.bg-p.uppercase.hover:brightness-90.hover:underline[href=@presenter.enroll_url] Secure Your Spot Now
          - if @presenter.offer_buy_now_pay_later?
            .text-sm.text-center.affirm-as-low-as[data-page-type="cart" data-amount="#{@presenter.recurring_actual_cents}"]


        .flex.flex-col.basis-0.grow.gap-y-1.p-6.sm:p-12.bg-gray-100.border-l.min-w-full.sm:min-w-0
          i.text-p.fa-solid.fa-dollar-sign.mt-5
          p Flexible payment options are available
          a.italic.text-gray-700.mt-4[href=@presenter.site_reimbursement_url]
            | Get reimbursed by your employer!
            .underline.text-gray-700.hover:cursor-pointer Learn more
          i.text-p.fa-solid.fa-headphones.mt-5
          p On-demand with live sessions
          i.text-p.fa-solid.fa-chalkboard-user.mt-5
          p Expert instructors
          i.text-p.fa-solid.fa-award.mt-5
          p.text-gray-700= @presenter.program_certificate_details

      - # Cohorts
      .max-w-7xl.mx-auto.mt-16
        a.block.max-w-4xl.mx-auto.mb-6.py-8.text-center.text-lg.leading-9.hover:bg-gray-100[href=@presenter.site_reimbursement_url]
          .font-light.uppercase Did You Know?
          .font-medium Many employers help with continuing education expenses.
          .font-light
            | Here are some
            span.underline<> helpful tips
            |  to get reimbursed by your employer
            = svg(:arrow_right, class: 'inline-block ml-2 w-6 h-6')

        .flex.flex-col.mx-auto.gap-y-3.w-full
          = render_presenters(@presenter.cohort_presenters)

  - # Learners Trust Pilot
  .max-w-7xl.mx-auto.md:mt-32.mt-20.px-4.text-center.md:mb-10.mb-5
    h3.text-4xl.font-semibold.mb-8 See what our learners have to say
    .mx-auto.w-10/12
      .trustpilot-widget[data-locale="en-US" data-template-id="54ad5defc6454f065c28af8b" data-businessunit-id="65bbfcf2e0b59cd0eea2041b" data-style-height="240px" data-style-width="100%" data-theme="light" data-tags="General" data-stars="1,2,3,4,5" data-review-languages="en"]
        a[href="https://www.trustpilot.com/review/ziplines.com" target="_blank" rel="noopener"] Trustpilot

    - # Testimonial
    .mx-auto.mt-20.w-9/12
      = render_presenter(@presenter.testimonials_presenter)

  - # Brand logos
  - if @presenter.brand_logos_allowed? && @presenter.show_our_course_attracts_block?
    .max-w-7xl.mx-auto.px-4.mt-20.mb-8.text-center
      h3.text-3xl.font-semibold.text-gray-800.mb-6 Our course attracts professionals from leading brands
      .flex.flex-wrap.pt-7.justify-center.items-center.space-x-0.lg:flex-nowrap.lg:space-x-14.lg:px-14.xl:px-0.px-0
        - @presenter.company_brands.each do |company_brand|
          .lg:w-auto.w-1/3.text-center
            = image_tag(company_brand.brand_logo.cdn_url, class: "lg:h-14 h-8 mx-auto mb-6 lg:mb-0", alt: "#{company_brand.company_name} logo", style: company_brand.logo_css)

  - # FAQs
  div#faqs
  .max-w-4xl.mx-auto.px-4.md:mt-32.mt-8.faqs.font-semibold
    h3.text-4xl.font-semibold.mb-8 Frequently Asked Questions
    = render_presenters(@presenter.faqs_presenters)

  - # Custom Disclaimer
  .container.max-w-4xl.mx-auto.mb-24
    = render_presenter(@presenter.disclaimer_presenter)

  .flex.flex-col.py-5.bg-p.text-pbg.fixed.bottom-0.w-full.opacity-0[data-controller="footer-visibility"]
    a.font-medium.rounded.text-sbg.py-2.mx-auto.bg-s.px-32.w-auto.hover:backdrop-brightness-75.hover:underline[href=@presenter.enroll_url] ENROLL NOW

  = render_presenter(@presenter.footer_presenter)

- content_for(:scripts)
  - if @presenter.offer_buy_now_pay_later?
    = render(partial: 'shared/affirm_script')
  div[data-controller="store-marketing-tracking-fields"]

