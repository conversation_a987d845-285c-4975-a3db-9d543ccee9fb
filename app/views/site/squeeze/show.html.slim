= render_presenter(@presenter.shared_setup_presenter)
= render_presenter(@presenter.meta_tags_presenter)
= render(partial: 'site/components/trustpilot_widget')

- content_for(:head) do
  link[rel="canonical" href="#{@presenter.canonical_url}"]

- if captured_referral_code.present?
  .w-full
    .bg-p.text-pbg.p-4.mb-5.text-center
      span.mr-2 📢
      | You've been referred by a friend—enjoy $100 off when you enroll in any course!

- hero_version = Setting.value_for(:squeeze_page_hero_version, @presenter.program.abbreviation).presence.to_i
- bg_style = hero_version == 1 ? "background-image: url(#{asset_path('site/squeeze/left-arrow.webp')}); background-repeat: no-repeat; background-position: right; background-size: contain; background-attachment: scroll; padding-bottom: 1px" : ""
.w-full[data-gtm-position="1" data-gtm-block="hero" style="#{bg_style}" class="#{hero_version == 1 ? 'bg-none-mobile' : ''}"]
  .max-w-7xl.mx-auto[data-gtm-position="1" data-gtm-block="hero"]
    - if hero_version == 1
      = render partial: 'site/squeeze/hero_v1'
    - else
      = render partial: 'site/squeeze/hero_v0'

= site_cache(@presenter.cache_key('squeeze_section_1')) do
  .relative
    .bg-p.text-white.relative.py-10[class="min-h-[117px]"]
      .container.mx-auto.flex.justify-center.sm:justify-between.items-center.space-x-8.flex-col.sm:flex-row
        .flex.items-center.space-x-2.flex-col.sm:flex-row
          - @presenter.points_info.each_with_index do |data, index|
            div(class="w-10 h-10 px-4 bg-p rounded-full flex items-center justify-center text-white border border-white")= index + 1
            p.sm:w-full.w-8/12.mx-auto.text-center.sm:text-start.my-3.sm:my-1 #{data}

    .w-full.flex.relative.justify-center.absolute.-top-1
      .w-0.h-0.border-solid.border-t-p[class="border-r-[60px] border-l-[60px] border-t-[40px] border-x-[#fff0]"]

  .flex.flex-row.flex-wrap.my-4.max-w-7xl.mx-auto.px-4.lg:pl-12[data-gtm-position="2" data-gtm-block="summary"]
    .mt-20.w-full.pl-6.md:w-2/4
      = render_presenters(@presenter.skills_presenters)
      - if @presenter.applied_technologies_after_skills? && @presenter.brand_logos_allowed?
          = render_presenter(@presenter.applied_technologies_presenter)
      .mt-8
      = render_presenters(@presenter.services_presenters)
      - if @presenter.applied_technologies_after_services? && @presenter.brand_logos_allowed?
          = render_presenter(@presenter.applied_technologies_presenter)

    div.mt-20.w-full.md:w-2/4
      div.mx-auto.relative.w-full.sm:w-9/12
        = image_tag(@presenter.sample_certificate.cdn_url, alt: 'Sample certificate')
        .bg-p.text-center.text-white.w-full.py-6.absolute.bottom-0
          p.text-2xl LIMITED-TIME OFFER
      .bg-gray-50.max-w-7xl.mx-auto.w-full.rounded-b-lg.sm:w-9/12
        .px-5.py-5.pt-0.flex.justify-center
          span.rounded-full.mt-6.p-1.bg-p.w-6.h-6.mr-3.min-w-6.text-white.flex.justify-center
            i.fa-solid.fa-dollar-sign
          .mt-4.[class="text-[21px] sm:text-[24px] md:text-[23px] lg:text-[24px]"]
            .flex
              strike.text-gray-500.font-semibold<>= @presenter.list_currency_ceiled
              .pl-2.text-p.font-bold.flex.flex-wrap
                = @presenter.actual_currency_ceiled
                .pl-1= "(Save #{number_to_percentage(@presenter.savings_percent, precision: 0)})"
            p.text-sm.text-center Flexible Payment Options Available!
        .bg-gray-300.text-center.mx-5.p-3
          strong No Risk Enrollment
          br
          | Not satisfied? Drop within 7 days after the course start and you’ll receive a full refund.
        - if @presenter.offer_buy_now_pay_later?
          .p-3.text-sm.mt-3.mb-10.text-center.affirm-as-low-as[data-page-type="cart" data-amount="#{@presenter.recurring_actual_cents}"]

- # Course Syllabus
.mt-20.pt-10.pb-14.bg-gray-100.flex.max-w-6xl.mx-auto.flex-wrap.rounded-lg.items-stretch[data-gtm-position="3" data-gtm-block="form_2"]
  .flex.flex-col.basis-1.grow.gap-y-1.px-6.pb-12.pt-8.bg-gray-100.mx-10.md:border-e-2.justify-center
    strong.text-2xl #{@presenter.sneak_peek_heading}
    p.text-base=@presenter.sneak_peek_blurb
  .bg-white.rounded.pt-8.pb-14.px-5.mx-10.pl-5.mx-auto.md:mr-10.md:max-w-md
    = render_presenter(@presenter.syllabus_form_presenter(location: 'callout'))

= site_cache(@presenter.cache_key('squeeze_section_2')) do
  - # Learners Trust Pilot
  .max-w-5xl.mx-auto.px-4.mt-32.text-center.mx-auto[data-gtm-position="4" data-gtm-block="testimonial"]
    h3.text-4xl.font-semibold.mb-8 See what our learners have to say
    .trustpilot-widget[data-locale="en-US" data-template-id="54ad5defc6454f065c28af8b" data-businessunit-id="65bbfcf2e0b59cd0eea2041b" data-style-height="240px" data-style-width="100%" data-theme="light" data-tags="General" data-stars="1,2,3,4,5" data-review-languages="en"]
      a[href="https://www.trustpilot.com/review/ziplines.com" target="_blank" rel="noopener"] Trustpilot

    - # Testimonial
    #testimonials.justify-center.w-px.max-w-screen-xl.h-px.mx-auto.flex.absolute.-mt-64[data-gtm-position="5" data-gtm-block="video"]
    .mx-auto.mt-20
      = render_presenter(@presenter.testimonials_presenter)

  - # Brand logos
  - if @presenter.brand_logos_allowed? && @presenter.show_our_course_attracts_block?
    .max-w-7xl.mx-auto.px-4.mt-20.mb-8.text-center[data-gtm-position="6" data-gtm-block="reference"]
      h3.text-3xl.font-semibold.text-gray-800.mb-6 Our course attracts professionals from leading brands
      .flex.flex-wrap.pt-7.justify-center.items-center.space-x-0.lg:flex-nowrap.lg:space-x-14.lg:px-14.xl:px-0.px-0
        - @presenter.company_brands.each do |company_brand|
          .lg:w-auto.w-1/3.text-center
            = image_tag(company_brand.brand_logo.cdn_url, class: "lg:h-14 h-8 mx-auto mb-6 lg:mb-0", alt: "#{company_brand.company_name} logo", style: company_brand.logo_css)


  - # FAQs
  .max-w-4xl.mx-auto.px-4.mt-32.faqs.font-semibold[data-gtm-position="7" data-gtm-block="FAQ"]
    h3.text-4xl.font-semibold.mb-8 Frequently Asked Questions
    = render_presenters(@presenter.faqs_presenters)

  - # Custom Disclaimer
  .container.mx-auto.mb-24.max-w-4xl
    = render_presenter(@presenter.disclaimer_presenter)

  = render_presenter(@presenter.footer_presenter)

- content_for(:scripts)
  - if @presenter.offer_buy_now_pay_later?
    = render(partial: 'shared/affirm_script')
  div[data-controller="store-marketing-tracking-fields"]

