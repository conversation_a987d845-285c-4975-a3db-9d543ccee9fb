.mb-4
  = render_flash_messages

- url = UrlBuilder::Site.new(partner_program: presenter.partner_program).call!(template: :create_syllabus_request)
= form_with( \
  model: @syllabus_request, \
  url:, method: :post, \
  data: { \
    controller: 'marketing-tracking-form gtm-form-event form-action-normalizer', \
    'marketing-tracking-form-target': 'form', \
    'gtm-form-event-form-type-value': 'syllabus_request', \
    'gtm-form-event-template-value': presenter.template, \
    'gtm-form-event-request-prefix-value': 'SR', \
    'gtm-form-event-form-location-value': presenter.location, \
    'gtm-form-event-partner': presenter.partner.slug, \
    'gtm-form-event-program': presenter.program.slug, \
    'form-action-normalizer-target': 'form', \
  }) do |f|
  div[class="#{presenter.options[:heading_classes]}"] #{presenter.options[:headline_text]}
  = hidden_field_tag :page_title, presenter.meta_tags_presenter.meta_title
  = hidden_field_tag :partner_program_uid, presenter.partner_program_uid
  = f.text_field :first_name, required: true, value: presenter.learner_cookie_info['first_name'], autocomplete: 'given-name', placeholder: "First Name*", 'aria-label': 'First Name', \
    data: { 'gtm-form-event-target': "firstNameInput" }, \
    class: "block w-full mb-4 border-none rounded-sm bg-gray-100 py-3 pl-4 pr-12 text-base font-medium text-gray-900 placeholder:text-gray-400 focus:outline-none"
  = text_field_tag :email_address, nil, type: :email, required: true, value: presenter.learner_cookie_info['email'], autocomplete: 'email', placeholder: presenter.options[:email_input_placeholder], 'aria-label': 'Email address', \
    data: { 'gtm-form-event-target': "emailAddressInput", controller: "email-validation", action: "input->email-validation#validate" }, \
    class: "block w-full mb-4 border-transparent rounded-sm bg-gray-100 py-3  pl-4 pr-12 text-base font-medium text-gray-900 placeholder:text-gray-400 shadow-none-input !shadow-none"
  = f.text_field :phone, \
    autocomplete: 'tel', \
    value: presenter.learner_cookie_info['phone'], \
    class: "block w-full mb-4 border-none rounded-sm bg-gray-100 py-3 pl-4 pr-12 text-base font-medium text-gray-900 placeholder:text-gray-400 focus:outline-none", \
    placeholder: presenter.options[:phone_input_placeholder], \
    data: { controller: 'mask', mask_pattern_value: '(*************', 'gtm-form-event-target': "phoneInput" }, \
    'aria-label': 'Phone number'
  = f.hidden_field :hp_key
  = f.hidden_field :tracking_key, \
    data: { 'gtm-form-event-target': "trackingKey" }
  = f.fields_for :site_ad_tracking do |at|
    - Site::AdTracking::JS_TRACKED_ATTRIBUTES.each do |attribute|
      = at.hidden_field(attribute)
  label.cursor-pointer
    = f.check_box :contact_opt_in, class: "h-4 w-4 mr-3 rounded border-gray-300", checked: true
    span.text-gray-600 I provide my consent to be contacted using calls or text messaging at the phone number provided above.
  .mt-4
    = f.submit "Submit", class: presenter.options[:submit_button_classes]
