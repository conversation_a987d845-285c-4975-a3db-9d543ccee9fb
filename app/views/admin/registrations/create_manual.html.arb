# frozen_string_literal: true

create_manual_form = @_assigns[:create_manual_form]
builder = @_assigns[:builder]

active_admin_form_for(create_manual_form, url: create_manual_admin_registrations_path, data: { turbo: false }) do |f|
  f.semantic_errors(*f.object.errors.attribute_names)
  builder.set_view(form: f, context: self)

  builder.render_steps do |step|
    case step.name
    when 'Source'
      f.inputs do
        f.input :email_address, input_html: { autofocus: step.current?, required: true }, label: 'Email*'
        f.input :reenrolled_from_id,
          as: :search_select,
          url: proc { admin_enrollments_path },
          label: 'Reenrolled From',
          fields: %w[uid],
          display_name: 'humanized_uid',
          hint: '(Optional - Enrollment UID of previous enrollment they are reenrolling from)'
        f.input :notes, as: :text, input_html: { rows: 3 }, hint: '(Optional - Partner facing notes)'
      end
    when 'Learner'
      f.inputs do
        f.input :first_name, input_html: { autofocus: step.current?, required: true }, label: 'First Name*'
        f.input :last_name, input_html: { required: true }, label: 'Last Name*'
        f.input :phone, input_html: { required: true }, label: 'Phone*'
      end
    when 'Partner'
      f.inputs do
        f.input :partner_id,
          as: :select,
          collection: Partner.order(:name),
          input_html: { autofocus: step.current?, required: true },
          label: 'Partner*'
      end
    when 'PartnerProgram'
      f.inputs do
        f.input :partner_program_id,
          as: :select,
          collection: create_manual_form.partner_program_options,
          input_html: { autofocus: step.current?, required: true },
          label: 'PartnerProgram*'
        f.input :finance_relationship_id,
          as: :select,
          collection: create_manual_form.finance_relationship_options,
          selected: create_manual_form.selected_finance_relationship_id,
          input_html: { required: true },
          label: 'Finance Relationship*'
        f.input :third_party_entity, hint: 'Optional'
      end
    when 'Registration'
      f.inputs do
        f.input :section_id, as: :nested_select,
          level_1: {
            attribute: :cohort_id,
            method_model: Cohort,
            collection: create_manual_form.cohort_options_for_nested_select,
            display_name: :starting_month,
            input_html: { autofocus: step.current? },
            label: 'Cohort (Optional)',
            hint: 'Leave blank if the Learner can self select their Cohort during Registration confirmation',
          },
          level_2: {
            attribute: :section_id,
            display_name: :full_name,
            method_model: Section,
            minimum_input_length: 0,
            order_by: :live_day_of_the_week,
            filters: {
              partner_id: create_manual_form.partner_id,
              registration_form: true,
            },
            label: 'Section (Optional)',
            hint: 'Leave blank if the Learner can self select their Session time during Registration confirmation',
          }

        # EVA Type field
        if create_manual_form.eva_enabled?
          f.input :eva_type,
            label: 'EVA Type',
            collection: create_manual_form.eva_type_options,
            hint: "(Optional) Will apply a #{create_manual_form.eva_discount_description} discount"
        end
      end
    when 'Checkout'
      f.inputs do
        f.input :price_amount, input_html: { autofocus: step.current?, required: true }, label: 'Price*'
        f.input(
          :payment_method_id,
          as: :select,
          collection: create_manual_form.payment_method_options,
          selected: create_manual_form.selected_payment_method_id,
          input_html: { required: true },
          label: 'Payment Method*',
          hint: 'You will also need to create a new Invoice Payment (external settlement) or Manual Payment (Stripe invoice) after form submission.',
        )
      end
    when 'Confirmation'
      attributes_table_for(create_manual_form) do
        row :email_address
        row :reenrolled_from if create_manual_form.reenrolled_from_id.present?
        row :notes if create_manual_form.notes.present?
        row :first_name
        row :last_name
        row :phone
        row :partner
        row :finance_relationship
        row :third_party_entity
        row :program
        row(:cohort) do |form|
          status = form.cohort_id.present? ? 'Admin Selected' : 'Auto Assigned'
          "#{link_to(form.cohort.starting_month, admin_cohort_path(form.cohort), target: '_BLANK')} (#{status})".html_safe
        end
        row(:section) do |form|
          status = form.section_id.present? ? 'Admin Selected' : 'Auto Assigned'
          "#{link_to(form.section.full_name, admin_section_path(form.section), target: '_BLANK')} (#{status})".html_safe
        end
        row :eva_type, &:eva_type_name if create_manual_form.eva_enabled?
        row :price, &:price_currency
        row(:payment_method) { |form| form.payment_method.kind_name }
        row(:registration_status) { |form| form.status.to_s.humanize }
      end
    end
  end
end
