# frozen_string_literal: true

context.instance_eval do
  column(:uid) { |pp| link_to(pp.humanized_uid, admin_partner_program_path(pp), target: '_BLANK') }
  column(:partner) { |e| link_to(e.partner.short_name, admin_partner_path(e.partner), target: '_BLANK') } if !resource.is_a?(Partner)
  column(:program) { |e| link_to(e.program.abbreviation.upcase, admin_program_path(e.program), target: '_BLAN<PERSON>') } if !resource.is_a?(Program)
  column(:status) { |pp| status_tag(pp.status_name, class: pp.status) }
  column(:default) do |pp|
    if pp.partner.default_program.present?
      link_to(pp.partner.default_program.abbreviation.upcase, admin_program_path(pp.partner.default_program), target: '_BLANK')
    else
      status_tag('None', class: 'error')
    end
  end
  column('Partner<br/>Dataset'.html_safe) do |pp|
    render_model_presence_link(
      path_name: 'site_partner_dataset',
      model: pp.site_partner_dataset,
      new_params: { site_partner_dataset: { partner_id: pp.partner_id } },
    )
  end
  column('Program<br/>Dataset'.html_safe) do |pp|
    render_model_presence_link(
      path_name: 'site_program_dataset',
      model: pp.site_program_dataset,
      new_params: { site_program_dataset: { program_id: pp.program_id } },
    )
  end
  column('Partner<br/>Program<br/>Dataset'.html_safe) do |pp|
    render_model_presence_link(
      path_name: 'site_partner_program_dataset',
      model: pp.site_partner_program_dataset,
      new_params: { site_partner_program_dataset: { partner_program_id: pp.id } },
    )
  end
  column(:landing) do |pp|
    render_model_presence_link(
      path_name: 'site_landing_page_template',
      model: pp.site_landing_page_template,
      new_params: { site_landing_page_template: { program_id: pp.program_id } },
      presence_url: UrlBuilder::Site.new(partner_program: pp).call!(template: :landing),
      alternate_url: pp.partner.dns_connected? ? UrlBuilder::Site.new(partner_program: pp, bypass_proxy: true).call!(template: :landing) : nil,
    )
  end
  column(:squeeze) do |pp|
    render_model_presence_link(
      path_name: 'site_squeeze_page_template',
      model: pp.site_squeeze_page_template,
      new_params: { site_squeeze_page_template: { program_id: pp.program_id } },
      presence_url: UrlBuilder::Site.new(partner_program: pp).call!(template: :squeeze),
      alternate_url: pp.partner.dns_connected? ? UrlBuilder::Site.new(partner_program: pp, bypass_proxy: true).call!(template: :squeeze) : nil,
    )
  end
  column(:syllabus) do |pp|
    render_model_presence_link(
      path_name: 'site_syllabus_page_template',
      model: pp.site_syllabus_page_template,
      new_params: { site_syllabus_page_template: { program_id: pp.program_id } },
      presence_url: UrlBuilder::Site.new(partner_program: pp).call!(template: :syllabus),
      alternate_url: pp.partner.dns_connected? ? UrlBuilder::Site.new(partner_program: pp, bypass_proxy: true).call!(template: :syllabus) : nil,
    )
  end
  column(:registration) do |pp|
    render_presence_link(
      presence: pp.default_pricing_available?,
      presence_url: UrlBuilder::Site.new(partner_program: pp).call!(template: :registration),
      alternate_url: pp.partner.dns_connected?.presence &&
        UrlBuilder::Site.new(partner_program: pp, bypass_proxy: true).call!(template: :registration),
      failure_title: 'No default pricing available',
    )
  end
  column('GTKY'.html_safe) do |pp|
    render_presence_link(
      presence: pp.site_partner_program_dataset.present?,
      presence_url: UrlBuilder::Site.new(partner_program: pp).call!(template: :get_to_know_you),
      alternate_url: pp.partner.dns_connected?.presence &&
        UrlBuilder::Site.new(partner_program: pp, bypass_proxy: true).call!(template: :get_to_know_you),
      failure_title: 'Missing Site Partner Program Dataset',
    )
  end
  column(:reimbursement) do |pp|
    render_model_presence_link(
      path_name: 'site_reimbursement_page_template',
      model: pp.site_reimbursement_page_template,
      new_params: { site_reimbursement_page_template: { program_id: pp.program_id } },
      presence_url: UrlBuilder::Site.new(partner_program: pp).call!(template: :reimbursement),
      alternate_url: pp.partner.dns_connected? ? UrlBuilder::Site.new(partner_program: pp, bypass_proxy: true).call!(template: :reimbursement) : nil,
    )
  end
  if Setting.value_for(:development_flags, :zendesk_support_page).present?
    column(:support) do |pp|
      render_presence_link(
        presence: pp.configured_for_site?,
        presence_url: UrlBuilder::Site.new(partner_program: pp).call!(template: :support),
        alternate_url: pp.partner.dns_connected?.presence &&
          UrlBuilder::Site.new(partner_program: pp, bypass_proxy: true).call!(template: :support),
        failure_title: 'Partner program not configured for site',
      )
    end
  end
end
