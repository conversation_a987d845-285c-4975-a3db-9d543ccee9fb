panel 'Submission Review' do
  unless Array.wrap(local_assigns[:except]).include?(:context)
    panel 'Context' do
      attributes_table_for(review) do
        row(:learner)
        row(:enrollment)
        row(:section) { |r| display_section(r.section) }
        row(:week) { |r| "Week: #{r.cohort_week&.number}" }
        row(:assignment)
        row :attempt
        row(:submission) { |r| copyable(link_to(r.submission.id, admin_lms_submission_path(r.submission)), r.submission.id) }
        row(:lms_url) { |r| render_external_link(url: r.lms_url, text: '🔗 View') }
        row(:grading_config)
        row(:grade) { |r| status_tag(r.submission.grade) }
        row(:grader_view) { |r| link_to '🔗 View', r.submission.grader_url }
      end
    end
  end

  columns do
    column do
      panel 'Details' do
        attributes_table_for(review) do
          row(:uid) { |r| copyable(link_to(r.humanized_uid, admin_lms_submission_review_path(r)), r.humanized_uid) }
          row(:type) { |r| r.training ? "🏋️ Training" : "⚡ Live" }
          row(:attempt) { |r| r.current? ? status_tag('current', class: 'lms_submission_review') : r.attempt }
          row :state, &:state_html
          row :grade, &:grade_html
          row :auto_grader_grade, &:auto_grader_grade_html
          row(:comment) { |r| render_simple_format(r.comment) }
          row(:manually_reviewed_by)
          row(:submission_url) { |r| render_external_link(url: r.submission.url, text: '🔗 View') }
          row :submission_export do
            if review.submission_export.attached?
              link_to('Download PDF', review.submission_export.cdn_url, target: '_blank', rel: 'noopener')
            else
              'No PDF available'
            end
          end
        end
      end
    end

    column do
      panel 'Timeline' do
        attributes_table_for(review) do
          row(:created_at) { |r| r.created_at.to_fs(:datetime_with_zone) }
          row(:pdf_generated_at) { |r| r.pdf_generated_at&.to_fs(:datetime_with_zone) }
          row(:published_at) { |r| r.published_at&.to_fs(:datetime_with_zone) }
          row(:auto_graded_at) { |r| r.auto_graded_at&.to_fs(:datetime_with_zone) }
          row(:manually_reviewed_at) { |r| r.manually_reviewed_at&.to_fs(:datetime_with_zone) }
          row(:graded_at) { |r| r.graded_at&.to_fs(:datetime_with_zone) }
        end
      end

      if review.training_evaluation.present?
        panel '🏋️ Training Evaluation' do
          active_admin_form_for(review.training_evaluation, url: update_training_evaluation_admin_lms_submission_review_path(review), method: :post) do |f|
            attributes_table_for(review.training_evaluation) do
              row(:status, &:status_html)
              row(:result, &:result_html)
              row(:evaluated_by)
              row(:evaluated_at) { |r| r.evaluated_at&.to_fs(:datetime_with_zone) }
            end

            f.inputs do
              f.input :result, as: :select, collection: Lms::Submission::Review::TrainingEvaluation.result_names.invert
              f.input :notes, as: :text, input_html: { rows: 3 }
            end
            f.actions do
              f.action :submit, label: "Update Evaluation"
            end
          end
        end
      end
    end
  end
end

panel 'Exercise Reviews' do
  review.exercise_reviews.sort_by { |er| er.exercise_config&.id.to_i }.each do |exercise_review|
    panel exercise_review.title do
      columns do
        column do
          attributes_table_for exercise_review do
            row(:state, &:state_html)
            row(:grade, &:grade_html)
            row(:autograder_confidence, &:autograder_confidence_html)
            row(:comment) { |r| render_simple_format(r.comment) }
            row(:manually_reviewed_by)
          end
        end

        column do
          attributes_table_for exercise_review do
            row(:created_at) { |r| r.created_at.to_fs(:datetime_with_zone) }
            row(:auto_graded_at) { |r| r.auto_graded_at&.to_fs(:datetime_with_zone) }
            row(:manually_reviewed_at) { |r| r.manually_reviewed_at&.to_fs(:datetime_with_zone) }
            row(:requires_manual_review?)
            row(:grading_instructions) { |r| render_simple_format(r.grading_instructions) }
            row(:extracted_work) { |r| render_simple_format(r.extracted_work) }
          end
        end
      end
    end
  end
end

panel 'Version History' do
  table_for review.versions.reorder(created_at: :desc) do
    column 'Date', :created_at do |version|
      version.created_at.to_fs(:datetime_with_zone)
    end

    column 'User' do |version|
      if version.whodunnit.present?
        admin_user = AdminUser.find_by(id: version.whodunnit)
        admin_user ? admin_user.full_name : "User ##{version.whodunnit}"
      else
        'System'
      end
    end

    column 'Action', :event do |version|
      css_class = case version.event
      when 'create' then 'bg-green-400'
      when 'update' then 'bg-yellow-400'
      when 'destroy' then 'bg-red-400'
      else 'bg-gray-400'
      end
      status_tag version.event, class: css_class
    end

    column 'Changes' do |version|
      if version.changeset.present?
        ul do
          version.changeset.each do |field, values|
            next if field == 'updated_at'

            from_value = values[0] || 'blank'
            to_value = values[1] || 'blank'

            li do
              strong field.humanize
              text_node ": #{from_value} → #{to_value}"
            end
          end
        end
      else
        'No changes recorded'
      end
    end
  end
end
