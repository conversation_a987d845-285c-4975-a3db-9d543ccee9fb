- authenticated_admin_user = AdminUserImpersonation.authenticated_admin_user(session)

- if current_admin_user && authenticated_admin_user
  - end_impersonation_url = AdminUserImpersonation.build_end_url( \
      authenticated_admin_user:,
      impersonated_admin_user: current_admin_user,
      redirect_to: UrlBuilder::Admin.admin_admin_user_url(current_admin_user, from: :end_impersonation),
      subdomains: request.subdomain)

  - # Having turbo links enabled here is causing the end_impersonation_url to be requested twice on learning-delivery.
  - # Due to this, disabling turbo for this link and manually implementing the confirmation dialog.
  - link_to_end_impersonation = link_to( \
    '🛑',
    end_impersonation_url,
    title: "End impersonation",
    onclick: "if (!confirm('Are you sure you want to stop impersonating #{escape_javascript(current_admin_user)}?')) { event.preventDefault(); }",
    data: { turbo: false })

  .impersonation-notice You are impersonating #{current_admin_user}. #{link_to_end_impersonation}
