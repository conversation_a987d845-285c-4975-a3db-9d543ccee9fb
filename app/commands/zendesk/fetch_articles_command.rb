# frozen_string_literal: true

module Zendesk
  class FetchArticlesCommand < ApplicationCommand
    DEFAULT_FAQ_SECTION_ID = 38840552319899 # Website FAQ section
    CACHE_TTL = 1.hour

    attr_reader :articles_section_id, :partner_program, :content_tags

    def initialize(partner_program:, articles_section_id: nil)
      @articles_section_id = articles_section_id || DEFAULT_FAQ_SECTION_ID
      @partner_program = partner_program
    end

    def call!
      Rails.cache.fetch(cache_key, namespace: 'zendesk', expires_in: CACHE_TTL, skip_nil: true) do
        fetch_and_process_articles
      end
    end

    private

    def cache_key
      @cache_key ||= begin
        components = [
          'zendesk_support',
          articles_section_id,
          partner_program.uid,
        ].compact

        Digest::SHA256.hexdigest(components.join('|'))[0, 16]
      end
    end

    def fetch_and_process_articles
      return [] if articles.blank?

      FilterAndFormatArticlesCommand.call!(
        articles:,
        partner_program:,
      )
    end

    def articles
      @articles ||= begin
        client.paginated_get('/help_center/articles/search', params: {
          section: articles_section_id,
        },
        )
      rescue ZendeskClient::Error => e
        Rails.error.report(e, context: {
          operation: 'fetch_articles',
          section_id: articles_section_id,
          partner_program:,
        },
        )
        []
      end
    end

    def client
      @client ||= ZendeskClient.new
    end
  end
end
