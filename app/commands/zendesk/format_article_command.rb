# frozen_string_literal: true

module Zendesk
  class FormatArticleCommand < ApplicationCommand
    attr_reader :article_data, :content_tag_names_mapping

    def initialize(article_data:, content_tag_names_mapping: {})
      @article_data = article_data
      @content_tag_names_mapping = content_tag_names_mapping
    end

    def call!
      {
        id: article_data['id'],
        title: formatted_title,
        body: article_data['body'] || '',
        position: article_data['position'].to_i,
        url: article_data['html_url'],
        updated_at: article_data['updated_at'],
        published: published?,
        label_names: article_data['label_names'] || [],
        content_tag_ids: article_data['content_tag_ids'] || [],
        content_tag_names: extract_content_tag_names,
      }
    end

    def published?
      !article_data&.dig('draft')
    end

    private

    def formatted_title
      return '' if title.blank?

      title.gsub(/\s*\([^)]*\)/, '').strip
    end

    def formatted_body
      return '' if body.blank?

      replace_url_variables(body)
    end

    def title
      article_data&.dig('title')
    end

    def body
      article_data&.dig('body')
    end

    def extract_content_tag_names
      return [] if content_tag_names_mapping.blank?

      article_tag_ids = article_data['content_tag_ids'] || []
      return [] if article_tag_ids.blank?

      id_to_name_mapping = content_tag_names_mapping.invert

      content_tag_names = article_tag_ids.filter_map do |tag_id|
        tag_name = id_to_name_mapping[tag_id]
        if tag_name.nil? && Rails.env.development?
          Rails.logger.debug { "Content tag ID #{tag_id} not found in mapping for article #{article_data['id']}" }
        end
        tag_name
      end

      content_tag_names
    rescue => e
      Rails.error.report(e, context: {
        operation: 'format_article_extract_content_tag_names',
        article_id: article_data&.dig('id'),
        content_tag_ids: article_data&.dig('content_tag_ids'),
      },
      )
      []
    end
  end
end
