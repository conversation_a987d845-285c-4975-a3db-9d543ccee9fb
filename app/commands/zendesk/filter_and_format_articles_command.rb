# frozen_string_literal: true

module Zendesk
  class FilterAndFormatArticlesCommand < ApplicationCommand
    attr_reader :articles, :partner_program

    delegate :partner, :program, to: :partner_program

    def initialize(articles:, partner_program:)
      @articles = articles
      @partner_program = partner_program
    end

    def call!
      return [] if articles.blank?

      filter_and_format_articles
    end

    def content_tag_mappings
      Rails.cache.fetch(
        "zendesk_content_tags_all",
        namespace: 'zendesk',
        expires_in: 1.hour,
        skip_nil: true,
      ) do
        fetch_content_tags_from_api
      end
    end

    private

    def filter_and_format_articles
      sorted_articles.map do |article|
        FormatArticleCommand.call!(article_data: article, content_tag_names_mapping: content_tag_mappings)
      end
    end

    def filtered_articles
      @filtered_articles ||= begin
        filtered_articles = filter_articles_by_labels(articles)

        filtered_articles.select { |article| published?(article) }
      end
    end

    def sorted_articles
      @sorted_articles ||= filtered_articles.sort_by { |article| article['position'].to_i }
    end

    def filter_articles_by_labels(raw_articles)
      expected_labels = ['Global']
      expected_labels << "program:#{program_abbreviation}"
      expected_labels << "partner:#{partner_abbreviation}" if partner_abbreviation.present?
      expected_labels << "partnerprogram:#{partner_abbreviation}-#{program_abbreviation}"

      raw_articles.select do |article|
        article_labels = article['label_names'] || []
        expected_labels.any? do |expected_label|
          article_labels.any? { |article_label| article_label.downcase == expected_label.downcase }
        end
      end
    end

    def published?(article)
      !article&.dig('draft')
    end

    def fetch_content_tags_from_api
      mappings = {}

      begin
        all_content_tags.to_h['records'].each do |tag|
          tag_name = tag['name']
          next if tag_name.blank?

          mappings[tag_name.snakecase] = tag['id']
        end

        mappings
      rescue ZendeskClient::Error => e
        report_zendesk_api_error(e)
        {}
      end
    end

    def program_abbreviation
      @program_abbreviation ||= program.abbreviation
    end

    def partner_abbreviation
      @partner_abbreviation ||= partner.abbreviation
    end

    def all_content_tags
      @all_content_tags ||= client.get('/guide/content_tags', params: { page: { size: 30 } })
    end

    def client
      @client ||= ZendeskClient.new
    end

    def report_zendesk_api_error(error)
      Rails.error.report(error, context: {
        operation: 'filter_articles_fetch_content_tags_all',
        partner_id: partner.id,
        program_id: program.id,
        program_abbreviation:,
      },
      )
    end
  end
end
