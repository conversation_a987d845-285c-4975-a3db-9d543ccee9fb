# frozen_string_literal: true

class CrmEvent
  class CreateDealFromLiveChatsCommand < ApplicationCommand
    attr_reader :event

    def initialize(event:)
      @event = event
    end

    def call!
      create_deal_from_live_chats
    end

    private

    def create_deal_from_live_chats
      if event_data['first_name'].blank?
        raise CrmEvent::ProcessCommand::IgnoreEventError.new('First name not present')
      elsif partner_program.blank?
        raise CrmEvent::ProcessCommand::IgnoreEventError.new('Partner program not identified from url')
      elsif deal_exists?
        raise CrmEvent::ProcessCommand::IgnoreEventError.new('Deal already exists')
      else
        create_syllabus_request
        log_process
      end
    end

    def create_syllabus_request
      attributes = attributes_for_syllabus_request.merge(partner_program:)
      SyllabusRequest::CreateCommand.call!(attributes:, email_address: contact_email, site_ad_tracking_attributes:, publish_to_hubspot: true,
        source: :conversation,
      )
    end

    def log_process
      JsonLogger.log(type: 'CrmEvent::CreateDealFromLiveChatsCommand', processed_event_data: event_data)
    end

    def deal_exists?
      email_record = Email.address(contact_email).first
      return false unless email_record

      Deal.exists?(email: email_record, partner_program:)
    end

    def partner_program
      @partner_program ||= site_page&.partner_program
    end

    def site_page
      if instance_variable_defined?(:@site_page)
        @site_page
      else
        @site_page = Site::Page.find_by(url: page_url)
      end
    end

    def contact_email
      @contact_email ||= event_data['email']
    end

    def event_page_url
      @event_page_url ||= event_data['page_url']
    end

    def page_url
      @page_url ||= begin
        uri = URI.parse(event_page_url)
        uri.query = nil
        uri.path = uri.path.chomp('/')
        uri.to_s
      end
    end

    def site_ad_tracking_attributes
      @site_ad_tracking_attributes ||= begin
        uri = URI.parse(event_page_url)
        query_params = Rack::Utils.parse_nested_query(uri.query)
        query_params.slice('fbclid', 'gclid', 'utm_campaign', 'utm_content', 'utm_medium', 'utm_source',
          'utm_term',
        ).merge('referrer' => event_page_url).compact_blank
      end
    end

    def attributes_for_syllabus_request
      {
        first_name: event_data['first_name'],
        phone: event_data['phone'],
      }
    end

    def event_data
      @event_data ||= event.event_data
    end
  end
end
