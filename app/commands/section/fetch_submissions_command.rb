# frozen_string_literal: true

class Section
  class FetchSubmissionsCommand < ApplicationCommand
    SUBMISSION_SYNCABLE_COHORT_STATUSES = %w[opened started ended extension_period_ended].freeze

    attr_reader :section, :states, :submitted_since, :graded_since

    delegate :enrollments, :remote_canvas_section, to: :section
    delegate :remote_list_submissions, to: :remote_canvas_section

    # @param section [Section] The section to fetch submissions for
    # @param states [Array<String>, String] Optional filter for submission states - 'unsubmitted', 'submitted', 'graded'
    # @param submitted_since [Time] Optional timestamp to only fetch submissions submitted since this time
    # @param graded_since [Time] Optional timestamp to only fetch submissions graded since this time
    def initialize(section:, states: nil, submitted_since: nil, graded_since: nil)
      @section = section
      @states = Array(states).compact_blank!
      @submitted_since = submitted_since
      @graded_since = graded_since
    end

    def call!
      sync_submissions
    end

    private

    def sync_submissions
      remote_submissions.each do |submission_data|
        remote_user_id = submission_data['user_id'].to_s
        enrollment = section_enrollments[remote_user_id]

        next unless enrollment

        submission_data['submissions'].each do |submission|
          remote_assignment_id = submission['assignment_id'].to_s
          remote_assignment = assignments_by_remote_id[remote_assignment_id]

          replace_submission(enrollment, remote_assignment.core_record, submission)
        end
      end
    end

    def replace_submission(enrollment, assignment, submission_attributes)
      Lms::Submission::ReplaceCommand.call!(
        enrollment:,
        assignment:,
        attributes: submission_attributes,
        lms_submission: find_existing_submission(enrollment, assignment),
      )
    end

    def section_enrollments
      @section_enrollments ||= enrollments.joins(:remote_canvas_user).includes(:remote_canvas_user).index_by do |enrollment|
        enrollment.remote_canvas_user.key
      end
    end

    def remote_assignment_ids
      @remote_assignment_ids ||= remote_submissions.flat_map do |submission_data|
        submission_data['submissions'].map { |sub| sub['assignment_id'] }
      end.uniq
    end

    def assignments_by_remote_id
      @assignments_by_remote_id ||= Remote::Canvas::Assignment.where(key: remote_assignment_ids)
        .includes(:core_record)
        .index_by(&:key)
    end

    def find_existing_submission(enrollment, assignment)
      Lms::Submission.find_by(
        enrollment:,
        assignment:,
      )
    end

    def remote_submissions
      @remote_submissions ||= remote_list_submissions(**filter_params)
    end

    def filter_params
      @filter_params ||= {
        workflow_state: states,
        submitted_since: submitted_since&.iso8601,
        graded_since: graded_since&.iso8601,
      }.compact_blank!
    end
  end
end
