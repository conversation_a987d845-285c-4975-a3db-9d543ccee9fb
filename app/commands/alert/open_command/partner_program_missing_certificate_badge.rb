# frozen_string_literal: true

class Alert
  class OpenCommand
    class PartnerProgramMissingCertificateBadge < ApplicationCommand

      DEFAULT_OWNER_EMAIL = '<EMAIL>'

      attr_reader :enrollments

      delegate :partner, :program, to: :partner_program

      def initialize(enrollments:)
        @enrollments = Array.wrap(enrollments)
      end

      def call!
        return if enrollments.blank?

        OpenCommand.call!(
          key:,
          title:,
          description:,
          resource: partner_program,
          owner:,
        )
      end

      private

      def partner_program
        @partner_program ||= enrollments.first.partner_program
      end

      def cohort
        @cohort ||= enrollments.first.cohort
      end

      def key
        @key ||= "partner_program_missing_certificate_badge_#{enrollments.map(&:id).join('_')}"
      end

      def title
        @title ||= "Missing certificate badge for #{partner.name} - #{program.name}"
      end

      def description
        @description ||= <<~EOT
          Partner program #{partner.name} - #{program.name} is missing a certificate badge and is configured to issue certificates.

          Cohort: #{cohort.name} is ending soon (#{cohort.ends_on.to_fs(:date)}) and students will be expecting certificates.

          Affected Enrollments: #{enrollments.map(&:humanized_uid).join(', ')}
        EOT
      end

      def owner
        if instance_variable_defined?(:@owner)
          @owner
        else
          @owner = AdminUser.find_by(email: DEFAULT_OWNER_EMAIL)
        end
      end
    end
  end
end
