# frozen_string_literal: true

class Alert
  class OpenCommand
    class PartnerMissingCertificateTheme < ApplicationCommand

      DEFAULT_OWNER_EMAIL = '<EMAIL>'

      attr_reader :enrollment

      delegate :partner, :cohort, to: :enrollment

      def initialize(enrollment:)
        @enrollment = enrollment
      end

      def call!
        OpenCommand.call!(
          key:,
          title:,
          description:,
          resource: cohort,
          owner:,
        )
      end


      private

      def key
        @key ||= "partner_missing_certificate_theme_#{partner.id}"
      end

      def title
        @title ||= "Partner missing certificate theme: #{partner.name}"
      end

      def description
        @description ||= <<~EOT
          Partner #{partner.name} is missing a completed certificate theme and is configured to issue certificates.

          Cohort: #{cohort.name} is ending soon (#{cohort.ends_on.to_fs(:date)}) and students will be expecting certificates.

          Enrollment: #{enrollment.humanized_uid} is affected.
        EOT
      end

      def resource
        partner
      end

      def owner
        if instance_variable_defined?(:@owner)
          @owner
        else
          @owner = AdminUser.find_by(email: DEFAULT_OWNER_EMAIL)
        end
      end

    end
  end
end
