# frozen_string_literal: true

class Alert
  class OpenCommand
    class AccredibleCertificateIncorrectBranding < ApplicationCommand

      DEFAULT_OWNER_EMAIL = '<EMAIL>'

      attr_reader :enrollment

      delegate :learner, :partner, :cohort, :remote_accredible_credential, to: :enrollment

      def initialize(enrollment:)
        @enrollment = enrollment
      end

      def call!
        OpenCommand.call!(
          key:,
          title:,
          description:,
          resource: enrollment,
          owner:,
        )
      end

      private

      def key
        @key ||= "accredible_certificate_incorrect_branding_#{enrollment.id}"
      end

      def title
        @title ||= "Certificate created on Accredible for #{learner.full_name} has incorrect branding and needs review."
      end

      def description
        @description ||= <<~TEXT
          Partner Logo and/or Badge image on Accredible does not match the current configurations for #{partner.name}.
          Cohort: #{cohort.name}
          Partner: #{partner.name}
          Enrollment: #{link_to_enrollment}
          Certificate: #{remote_accredible_credential.remote_link}
        TEXT
      end

      def resource
        enrollment
      end

      def owner
        if instance_variable_defined?(:@owner)
          @owner
        else
          @owner = AdminUser.find_by(email: DEFAULT_OWNER_EMAIL)
        end
      end

      def link_to_enrollment
        UrlBuilder::Admin.admin_enrollment_url(enrollment)
      end
    end
  end
end
