# frozen_string_literal: true

class Alert
  class OpenCommand
    class SectionMissingSetup < ApplicationCommand

      REQUIRED_FIELDS = %i[ chat_join_url chat_workspace_key conferencing_url remote_canvas_section ].freeze
      DEFAULT_OWNER_EMAIL = '<EMAIL>'

      attr_reader :section

      delegate :cohort, to: :section

      def initialize(section:)
        @section = section
      end

      def call!
        OpenCommand.call!(
          key:,
          title:,
          description:,
          resource: section,
          owner:,
        )
      end


      private

      def key
        @key ||= "section_missing_setup_#{section.id}"
      end

      def title
        @title ||= "Section missing setup: #{cohort.name}, #{section.name}"
      end

      def description
        @description ||= <<~EOT
          Cohort is opening soon (#{cohort.lms_opens_on.to_fs(:date)}), but the section is missing setup information.
          #{field_description}
        EOT
      end

      def field_description
        REQUIRED_FIELDS.map do |field|
          if field == :remote_canvas_section
            remote_canvas_section = section.send(field)
            " * #{field.to_s.humanize}: #{remote_canvas_section&.key}"
          else
            " * #{field.to_s.humanize}: #{section.send(field)}"
          end
        end.join("\n")
      end

      def resource
        section
      end

      def owner
        if instance_variable_defined?(:@owner)
          @owner
        else
          @owner = AdminUser.find_by(email: DEFAULT_OWNER_EMAIL)
        end
      end

    end
  end
end
