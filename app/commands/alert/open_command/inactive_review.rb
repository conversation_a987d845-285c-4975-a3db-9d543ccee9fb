# frozen_string_literal: true

class Alert
  class OpenCommand
    class InactiveReview < ApplicationCommand

      INACTIVE_DURATION_THRESHOLD = 4.hours
      DEFAULT_OWNER_EMAIL = '<EMAIL>'

      attr_reader :review

      def initialize(review:)
        @review = review
      end

      def call!
        OpenCommand.call!(
          key:,
          title:,
          description:,
          resource: review,
          owner:,
        )
      end

      private

      def key
        @key ||= "inactive_review_#{review.id}"
      end

      def title
        @title ||= "Inactive Submission Review Alert: #{review.uid}"
      end

      def description
        @description ||= <<~EOT
          Review #{admin_review_link} has been in #{review.state_name} state for over #{format_duration}.
          Last updated: #{review.updated_at.to_fs(:long)}
          Time inactive: #{time_inactive}
        EOT
      end

      def admin_review_link
        url = UrlBuilder::Admin.admin_lms_submission_review_url(review)
        url
      end

      def format_duration
        ActiveSupport::Duration.build(INACTIVE_DURATION_THRESHOLD.to_i).inspect
      end

      def time_inactive
        duration = Time.current - review.updated_at
        ActiveSupport::Duration.build(duration.to_i).inspect
      end

      def owner
        AdminUser.find_by(email: DEFAULT_OWNER_EMAIL)
      end

    end
  end
end
