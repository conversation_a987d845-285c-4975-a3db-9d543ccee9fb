# frozen_string_literal: true

module Payments
  class Transaction
    class Charge
      class CreateCommand < ApplicationCommand
        attr_reader(
          :event,
          :processor,
          :payer_processor_key,
          :transaction_processor_key,
          :installment_plan_processor_key,
          :manual_payment,
          :ecom_order,
          :ecom_payment_method,
          :amount_cents,
          :transacted_at,
        )

        # @param installment_plan_processor_key [String, nil] nil when not an installment payment method
        def initialize(
          event:,
          processor:,
          payer_processor_key:,
          transaction_processor_key:,
          installment_plan_processor_key:,
          manual_payment:,
          ecom_order:,
          ecom_payment_method:,
          amount_cents:,
          transacted_at:
        )
          @event = event
          @processor = processor
          @payer_processor_key = payer_processor_key
          @transaction_processor_key = transaction_processor_key
          @installment_plan_processor_key = installment_plan_processor_key
          @manual_payment = manual_payment
          @ecom_order = ecom_order
          @ecom_payment_method = ecom_payment_method
          @amount_cents = amount_cents
          @transacted_at = transacted_at

          if ecom_payment_method.kind == 'installments' && installment_plan_processor_key.blank?
            raise ArgumentError.new("Installment Plan key must be present for installment payment method")
          elsif ecom_payment_method.kind != 'installments' && installment_plan_processor_key.present?
            raise ArgumentError.new("Installment Plan key can only be specified for installment payment method")
          end
          raise ArgumentError.new("Processor is not supported: #{processor}") unless processor.to_s.in?(Ecom::PaymentMethod::PROCESSORS)
          raise ArgumentError.new("Event already has a transaction") unless event.payments_transaction.nil?
          raise ArgumentError.new("Event transactions is already processed") if event.processed?
        end

        def call!
          Ecom::Order::LockCommand.call!(order: ecom_order) do
            Ecom::Order::TransitionToPaidStatusCommand.call!(order: ecom_order, payment_method: ecom_payment_method)

            process_installment_plan if installment_plan_processor_key.present?

            transaction = create_charge_transaction

            process_manual_payment(transaction) if manual_payment

            event.update!(payments_transaction: transaction)

            Ecom::Payment::ApplyPaymentsTransactionCommand.call!(payment: ecom_order.payment, payments_transaction: transaction)

            transaction
          end
        end

        private

        def create_charge_transaction
          ecom_order.payment.payments_transactions.create!(
            type: 'Payments::Transaction::Charge',
            currency_code: ecom_order.currency_code,
            amount_cents:,
            transacted_at:,
            payer:,
            processor:,
            processor_key: transaction_processor_key,
          )
        end

        def process_installment_plan
          # when installment plan is already canceled and a manual payment charge is initiated, do not modify the existing installment plan
          return if manual_payment && ecom_order.payment.payments_installment_plan&.canceled?

          ::Payments::InstallmentPlan::CreateOrUpdateByProcessorKeyCommand.call!(
            processor:,
            processor_key: installment_plan_processor_key,
            status: 'active',
            ecom_payment: ecom_order.payment,
          )
        end

        def process_manual_payment(transaction)
          manual_payment.update!(transaction_charge: transaction)

          Payments::ManualPayment::UpdateStatusCommand.call!(
            manual_payment:,
            status: 'paid',
          )
        end

        def payer
          @payer ||= Payments::Payer.find_by!(processor_key: payer_processor_key)
        end
      end
    end
  end
end
