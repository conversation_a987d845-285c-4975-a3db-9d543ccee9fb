# frozen_string_literal: true

module Payments
  module Stripe
    module Event
      # Determines if the event is eligible for a core installment Stripe receipt email. Manual payments are not eligible
      # as subscription attribute of their stripe invoice would be nil.
      class EligibleForInstallmentReceiptEmailCommand < ApplicationCommand
        attr_reader :event

        def initialize(event:)
          @event = event
        end

        # @param event [Object] The event object containing metadata and processor event type.
        # @return [<PERSON><PERSON>an] True if eligible, false otherwise.
        def call!
          return false if invoice_id.blank?
          return false if invoice.subscription.blank?
          return false if installment_plan.blank?

          if event.processor_event_type == 'charge.failed'
            invoice.attempt_count.to_i <= 1
          else
            true
          end
        end

        private

        def invoice_id
          @invoice_id ||= event.meta.dig('data', 'object', 'invoice')
        end

        def invoice
          @invoice ||= StripeUtil.build_client.v1.invoices.retrieve(invoice_id)
        end

        def installment_plan
          if instance_variable_defined?(:@installment_plan)
            @installment_plan
          else
            @installment_plan = Payments::InstallmentPlan.find_by(processor_key: invoice.subscription)
          end
        end
      end
    end
  end
end
