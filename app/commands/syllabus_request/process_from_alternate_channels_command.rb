# frozen_string_literal: true

class SyllabusRequest
  class ProcessFromAlternateChannelsCommand < ApplicationCommand
    attr_reader :contact, :partner_channel, :active_program

    def initialize(contact:)
      @contact = contact
    end

    def call!
      process_contact
    end

    private

    def process_contact
      @active_program = contact.dig("properties", "active_program")
      @partner_channel = contact.dig("properties", "partner_channel")

      return if program.blank? || partner.blank?
      return if partner_program.blank? || deal_exists?

      create_syllabus_request
    end

    def partner_program
      if instance_variable_defined?(:@partner_program)
        @partner_program
      else
        @partner_program = PartnerProgram.find_by(partner:, program:)
      end
    end

    def partner
      return if partner_channel.blank?

      @partner ||= Remote::Hubspot::Deal::PartnerChannel.find_by!(core_record_type: 'Partner', key: partner_channel).core_record
    end

    def program
      return if active_program.blank?

      @program ||= Remote::Hubspot::Deal::ActiveProgram.find_by!(core_record_type: 'Program', key: active_program).core_record
    end

    def deal_exists?
      email_record = Email.address(contact_email)
      return false unless email_record

      Deal.exists?(email: email_record, partner_program:)
    end

    def create_syllabus_request
      attributes = attributes_for_syllabus_request.merge(partner_program:)

      SyllabusRequest::CreateCommand.call!(attributes:, email_address: contact_email, publish_to_hubspot: true)
    end

    def attributes_for_syllabus_request
      {
        first_name: contact.dig("properties", "firstname"),
        phone: contact.dig("properties", "phone"),
      }
    end

    def contact_email
      contact.dig("properties", "email")
    end
  end
end
