# frozen_string_literal: true

class Enrollment
  class ReplaceCommand < ApplicationCommand

    attr_reader :enrollment, :attributes, :reason, :admin_user, :status_became, :notes

    def initialize(attributes:, enrollment: nil, reason: nil, admin_user: nil, notes: nil)
      @enrollment = enrollment || Enrollment.new
      @attributes = attributes.with_indifferent_access
      @reason = reason
      @admin_user = admin_user
      @notes = notes
    end

    def call!
      ActiveRecord::Base.transaction do
        enrollment.attributes = attributes
        enrollment.deal ||= deal
        enrollment.save!
        enrollment.deal.tap do |d|
          d.status = :closed_won
          d.closed_at ||= Time.current
        end.save!

        status_became = enrollment.saved_change_to_status&.last || attributes[:status]

        StatusChange::CreateCommand.call!(enrollment:, reason:, admin_user:, status_became:, notes:) if !enrollment.saved_changes.empty?
      end

      alert_on_duplicate_enrollment_later if enrollment.previously_new_record?

      enrollment
    end

    private

    def deal
      @deal ||= Deal::AssignOpenDealCommand.call!(record: enrollment, email: enrollment.learner.primary_email)
    end

    def alert_on_duplicate_enrollment_later
      ActiveRecord.after_all_transactions_commit { AlertOnDuplicateJob.perform_async(enrollment.id) }
    end
  end
end
