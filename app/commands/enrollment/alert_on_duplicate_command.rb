# frozen_string_literal: true

class Enrollment
  class AlertOnDuplicateCommand < ApplicationCommand
    attr_reader :enrollment

    def self.callable?(enrollment:)
      enrollment.primary? && !enrollment.transfer?
    end

    def initialize(enrollment:)
      @enrollment = enrollment
    end

    def call!
      return unless callable?(enrollment:)
      return if duplicate_enrollment.blank?

      LearningDelivery::Task::CreateResolveDuplicateEnrollmentTaskCommand.call!(
        enrollment:,
        duplicate_enrollment:,
      )
    end

    private

    def duplicate_enrollment
      @duplicate_enrollment ||= Enrollment.primary
        .where.not(id: enrollment.id)
        .where(learner_id: enrollment.learner_id)
        .joins(:section)
        .where(sections: { cohort_id: enrollment.section.cohort_id })
        .first
    end
  end
end
