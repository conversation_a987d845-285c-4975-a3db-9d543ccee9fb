# frozen_string_literal: true

class Registration
  class SendManualRegistrationRemindersCommand < ApplicationCommand
    TIME_ZONE = 'Pacific Time (US & Canada)'
    REMINDER_THRESHOLDS = [1.day, 2.days].freeze

    def call!
      send_reminder_emails
    end

    private

    def send_reminder_emails
      Time.use_zone(TIME_ZONE) do
        REMINDER_THRESHOLDS.each do |threshold|
          interval = threshold.ago.all_day
          eligible_registrations_for_interval(interval).find_each do |registration|
            send_reminder_email(registration)
          rescue StandardError => e
            ErrorReporter.report(error: e, context: {
              registration_id: registration.id,
              reminder_interval: interval,
            },
            )
          end
        end
      end
    end

    def eligible_registrations_for_interval(interval)
      Time.use_zone(TIME_ZONE) do
        Registration
          .where(status: %i[created cohort_picked section_picked])
          .where.missing(:enrollments)
          .joins(:ecom_payment).merge(Ecom::Payment.joins(:payments_transactions))
          .distinct
          .group(:id)
          .having('MIN(payments_transactions.transacted_at) BETWEEN ? AND ?', interval.begin, interval.end)
      end
    end

    def send_reminder_email(registration)
      LearnerMailer.with(registration:).manual_registration_reminder.deliver_later
    end
  end
end
