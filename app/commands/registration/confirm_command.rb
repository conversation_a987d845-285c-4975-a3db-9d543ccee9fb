# frozen_string_literal: true

class Registration
  class ConfirmCommand < ApplicationCommand
    attr_reader :registration

    delegate :ecom_order_item, to: :registration, private: true

    def initialize(registration:)
      @registration = registration
    end

    def call!
      return registration if registration.confirmed_status?

      raise ArgumentError.new('Registration must be in confirmable status to be confirmed') unless registration.confirmable?

      ActiveRecord::Base.transaction do
        registration.update!(status: :confirmed)

        Ecom::OrderItem::ProcessRevenueCommand.call!(
          ecom_order_item:,
          payments_transaction: ecom_order_item.payment.payments_transactions.order(:created_at).last,
        )

        Ecom::Order::ConditionalFulfillCommand.call!(order: registration.ecom_order)
      end

      registration
    end
  end
end
