# frozen_string_literal: true

module Ecom
  class OrderItem
    class ProcessRevenueCommand < ApplicationCommand
      attr_reader :ecom_order_item, :payments_transaction

      delegate :order, to: :ecom_order_item, prefix: :ecom, private: true
      delegate :registration, to: :ecom_order_item, private: true

      def initialize(ecom_order_item:, payments_transaction:)
        @ecom_order_item = ecom_order_item
        @payments_transaction = payments_transaction
      end

      def call!
        return unless ecom_order.paid? && registration.confirmed_status?

        Finance::Booking::Revenue::BookCommand.call!(ecom_order_item:)
        Finance::Receipt::CreateCommand.call!(ecom_order_item:, payments_transaction:)
      end
    end
  end
end
