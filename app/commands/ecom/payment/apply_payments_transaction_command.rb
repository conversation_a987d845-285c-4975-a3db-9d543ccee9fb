# frozen_string_literal: true

module Ecom
  class Payment
    class ApplyPaymentsTransactionCommand < ApplicationCommand
      attr_reader :payment, :payments_transaction

      delegate :order, to: :payment, private: true

      def initialize(payment:, payments_transaction:)
        @payment = payment
        @payments_transaction = payments_transaction
      end

      def call!
        Ecom::Order::LockCommand.call!(order:) do
          raise "Order must be in paid or fulfilled status for order #{payment.order.id}}" unless payment.order.paid? || payment.order.fulfilled?

          Ecom::OrderItem::ProcessRevenueCommand.call!(ecom_order_item:, payments_transaction:)

          Ecom::Payment::ReplaceCommand.call!(payment:, attributes: { status: calculate_new_status })

          Ecom::Order::ConditionalFulfillCommand.call!(order:) if order.fulfillable?
        end

        send_internal_notification if payment.payment_method.partner_invoice?
      end

      private

      # TODO: Multi-item orders will need to be handled differently here
      def ecom_order_item
        @ecom_order_item ||= payment.order.order_items.first
      end

      def calculate_new_status
        return 'refunded' if payments_transaction.is_a?(Payments::Transaction::Refund) && !payment.paid_balance_cents.positive?

        payment.balance_paid? ? 'paid' : 'partial'
      end

      def send_internal_notification
        ActiveRecord.after_all_transactions_commit do
          PartnerSourcedRegistrationMailer.with(registration:).partner_invoice_payment_confirmation.deliver_later(wait: 5.seconds)
        end
      end

      def registration
        @registration ||= payment.order.order_items.first.reload.registration
      end
    end
  end
end
