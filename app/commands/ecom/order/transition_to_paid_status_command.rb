# frozen_string_literal: true

module Ecom
  class Order
    # NOTE: This command can be invoked either when the customer clicks "Pay" when completing payment OR
    # when a webhook is received from the payment processor indicating a successful payment.
    class TransitionToPaidStatusCommand < ApplicationCommand
      attr_reader :order, :payment_method

      def initialize(order:, payment_method:)
        @order = order
        @payment_method = payment_method
      end

      # @return [Ecom::Order] The order will always have a payment associated with it.
      def call!
        Ecom::Order::LockCommand.call!(order:) do
          return order if !order.cart?

          # Until now we calculate the order assuming upfront pricing. Now that the payment method is locked in, we recalculate
          # the order_item prices and the discount prices to reflect the payment method.
          recalculate_order_item_prices
          recalculate_discounts

          Ecom::Payment::CreateOrUpdatePendingCommand.call!(
            order:,
            payment_method:,
          )

          Ecom::Order::UpdateStatusCommand.call!(order:, status: 'paid')

          # Send email notifications for registrations in created status
          send_manual_registration_confirmations

          order
        end
      end

      def recalculate_order_item_prices
        # Prices are manually set for payment methods with manual pricing
        return if payment_method.manual_pricing?

        order.order_items.each do |order_item|
          pricing = Ecom::Pricing::BuildFromVariantCommand.call!(variant: order_item.variant, payment_method:)

          order_item.update!(
            price_cents: pricing.total_cents,
          )
        end
      end

      def recalculate_discounts
        order.promos_discount_applications.each do |discount_application|
          Promos::DiscountApplication::UpdateDiscountCentsCommand.call!(discount_application:)
        end
      end

      private

      def send_manual_registration_confirmations
        order.order_items.each do |order_item|
          registration = order_item.registration
          next unless registration&.confirmable?

          LearnerMailer.with(
            registration:,
          ).manual_registration_confirmation.deliver_later
        end
      end
    end
  end
end
