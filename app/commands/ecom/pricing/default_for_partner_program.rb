# frozen_string_literal: true

module Ecom
  class Pricing
    class DefaultForPartnerProgram < ApplicationCommand
      Error = Class.new(StandardError)
      NoConfiguredPricingError = Class.new(Error)

      attr_reader :partner_program, :payment_method, :promos_discount_codes

      delegate :program, to: :partner_program

      def initialize(partner_program:, payment_method: nil, promos_discount_codes: nil)
        @partner_program = partner_program
        @payment_method = payment_method || default_payment_method
        @promos_discount_codes = Array.wrap(promos_discount_codes)
      end

      # @return [Ecom::Pricing]
      # @raise [NoConfiguredPricingError] no pricing is configured (i.e. a Program with no Variants for Product
      #  or no Variant for non-USD currency Partner specific Product)
      def call!
        raise NoConfiguredPricingError.new unless variant

        Ecom::Pricing::BuildFromVariantCommand.new(
          variant:,
          payment_method:,
          promos_discount_codes:,
        ).call!
      end


      private

      # @return [Ecom::Variant, nil]
      def variant
        partner_variant || (can_use_program_default_variant? ? program_default_variant : nil)
      end

      def can_use_program_default_variant?
        partner_program.partner.currency_code == 'USD'
      end

      # @return [Ecom::Variant, nil]
      def partner_variant
        return @partner_variant if defined?(@partner_variant)

        @partner_variant = begin
          Ecom::Variant.where(section: default_active_section, partner: partner_program.partner).first if default_active_section
        end
      end

      # @return [Ecom::Variant, nil] nil is only returned when the program is not configured
      def program_default_variant
        @program_default_variant ||= begin
          add_period_active_cohorts.first&.sections&.first&.ecom_variant || program.ecom_product&.variants&.first
        end
      end

      # @return [Ecom::Section, nil]
      def default_active_section
        return @default_active_section if defined?(@default_active_section)

        @default_active_section = if partner_program.partner.dedicated_sections?
          add_period_active_cohorts.flat_map(&:sections).find { |s| s.partner_id == partner_program.partner_id }
        else
          add_period_active_cohorts.first&.sections&.first
        end
      end

      def add_period_active_cohorts
        program.cohorts.add_period_active.sort_by(&:starts_on)
      end

      def default_payment_method
        if instance_variable_defined?(:@default_payment_method)
          @default_payment_method
        else
          @default_payment_method = Ecom::PaymentMethod.find_by(kind: 'upfront')
        end
      end

    end
  end
end
