# frozen_string_literal: true

module LearningDelivery
  class Activity
    class ReplaceCommand < ApplicationCommand
      attr_reader :employee, :attributes, :activity

      class << self
        def callable?(employee:, attributes:)
          new(employee:, attributes:).callable?
        end
      end

      def initialize(employee:, attributes:, activity: nil)
        @employee = employee
        @attributes = attributes.with_indifferent_access
        @activity = activity || LearningDelivery::Activity.new
      end

      def call!
        return unless callable?

        ActiveRecord::Base.transaction do
          activity.attributes = attributes
          activity.employee = employee
          activity.save!
          activity
        end
      end

      def callable?
        employee.is_a?(LearningDelivery::Employee) && LearningDelivery::Activity.activity_types.key?(attributes['activity_type'])
      end
    end
  end
end
