# frozen_string_literal: true

module LearningDelivery
  class RiskAssessment
    class Rule
      class LateAssignmentsRule < Rule
        LOW_RISK_THRESHOLD = 2
        HIGH_RISK_THRESHOLD = 4

        delegate :learner, :cohort, :section, to: :enrollment

        def call!
          return unless assessable?

          RuleOutput.new(
            risk_level:,
            risk_details:,
            risk_type: :late_assignments,
          )
        end

        private

        def assessable?
          learner_activated? && assignments_due.exists?
        end

        def learner_activated?
          enrollment.learning_delivery_risk_assessments.not_activated.on_track.exists?
        end

        def assignments_due
          @assignments_due ||= section.assignments
            .impacts_outcome
            .where(due_at: ...Time.current)
        end

        def risk_level
          if late_assignments_count < LOW_RISK_THRESHOLD
            'on_track'
          elsif late_assignments_count < HIGH_RISK_THRESHOLD
            'low_risk'
          else
            'high_risk'
          end
        end

        def risk_details
          {
            late_assignments_count:,
            late_assignment_ids:,
          }
        end

        def late_assignments_count
          @late_assignments_count ||= late_assignments.count
        end

        def late_assignment_ids
          @late_assignment_ids ||= late_assignments.pluck(:id)
        end

        def late_assignments
          @late_assignments ||= submitted_assignments
            .where('lms_submissions.submitted_at > lms_assignments.due_at')
        end

        def submitted_assignments
          @submitted_assignments ||= assignments_due
            .joins(:submissions)
            .where(lms_submissions: { enrollment_id: enrollment.id })
            .where.not(lms_submissions: { state: :unsubmitted })
        end
      end
    end
  end
end
