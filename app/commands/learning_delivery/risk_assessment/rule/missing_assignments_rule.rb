# frozen_string_literal: true

module LearningDelivery
  class RiskAssessment
    class Rule
      class MissingAssignmentsRule < Rule
        LOW_RISK_THRESHOLD = 1
        HIGH_RISK_THRESHOLD = 3

        delegate :learner, :cohort, :section, to: :enrollment

        def call!
          return unless assessable?

          RuleOutput.new(
            risk_level:,
            risk_details:,
            risk_type: :missing_assignments,
          )
        end

        private

        def assessable?
          learner_activated? && assignments_due.exists?
        end

        def learner_activated?
          enrollment.learning_delivery_risk_assessments.not_activated.on_track.exists?
        end

        def assignments_due
          @assignments_due ||= section.assignments
            .impacts_outcome
            .where(due_at: ...Time.current)
        end

        def risk_level
          if missing_assignments_count < LOW_RISK_THRESHOLD
            'on_track'
          elsif missing_assignments_count < HIGH_RISK_THRESHOLD
            'low_risk'
          else
            'high_risk'
          end
        end

        def risk_details
          {
            missing_assignments_count:,
            missing_assignment_ids:,
          }
        end

        def missing_assignments_count
          @missing_assignments_count ||= missing_assignments.count
        end

        def missing_assignment_ids
          @missing_assignment_ids ||= missing_assignments.pluck(:id)
        end

        def missing_assignments
          @missing_assignments ||= assignments_due
            .where.not(id: submitted_assignment_ids)
        end

        def submitted_assignment_ids
          @submitted_assignment_ids ||= Lms::Submission
            .where(enrollment_id: enrollment.id)
            .where.not(state: :unsubmitted)
            .pluck(:lms_assignment_id)
        end
      end
    end
  end
end
