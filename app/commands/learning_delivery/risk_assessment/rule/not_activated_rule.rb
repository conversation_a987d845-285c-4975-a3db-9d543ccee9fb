# frozen_string_literal: true

module LearningDelivery
  class RiskAssessment
    class Rule
      class NotActivatedRule < Rule
        STANDARD_ACTIVATION_THRESHOLD = 2.days.freeze
        NEW_ENROLLMENT_GRACE_PERIOD = 24.hours.freeze

        delegate :learner, :cohort, :last_lms_activity_at, to: :enrollment
        delegate :remote_canvas_user, to: :learner
        delegate :remote_canvas_course, to: :cohort

        def call!
          return unless assessable?

          RuleOutput.new(
            risk_level:,
            risk_details:,
            risk_type: :not_activated,
          )
        end

        private

        def assessable?
          Time.current.after?(activation_threshold) &&
            !already_assessed_as_on_track?
        end

        def already_assessed_as_on_track?
          enrollment.learning_delivery_risk_assessments.not_activated.on_track.exists?
        end

        def activation_threshold
          @activation_threshold ||= [
            cohort.starts_on + STANDARD_ACTIVATION_THRESHOLD,
            enrollment.created_at + NEW_ENROLLMENT_GRACE_PERIOD,
          ].max
        end

        def risk_level
          return 'high_risk' if last_lms_activity_at.nil?

          last_lms_activity_at.after?(cohort.lms_opens_on) ? 'on_track' : 'high_risk'
        end

        def risk_details
          { last_login: last_lms_activity_at }
        end
      end
    end
  end
end
