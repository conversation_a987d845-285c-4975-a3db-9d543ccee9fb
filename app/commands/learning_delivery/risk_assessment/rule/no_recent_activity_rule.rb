# frozen_string_literal: true

module LearningDelivery
  class RiskAssessment
    class Rule
      class NoRecentActivityRule < Rule
        RECENT_ACTIVITY_THRESHOLD_DAYS = 10.days.freeze
        SECONDS_IN_A_DAY = 86400

        delegate :learner, :cohort, :last_lms_activity_at, to: :enrollment
        delegate :remote_canvas_user, to: :learner
        delegate :remote_canvas_course, to: :cohort

        def call!
          return unless assessable?

          RuleOutput.new(
            risk_level:,
            risk_details:,
            risk_type: :no_recent_activity,
          )
        end

        private

        def assessable?
          Time.current.after?(cohort.starts_on + RECENT_ACTIVITY_THRESHOLD_DAYS) &&
            !recently_assessed_as_on_track?
        end

        def recently_assessed_as_on_track?
          enrollment.learning_delivery_risk_assessments.no_recent_activity.on_track
            .exists?(assessed_at: RECENT_ACTIVITY_THRESHOLD_DAYS.ago..)
        end

        def risk_level
          return 'high_risk' if last_lms_activity_at.nil?

          last_lms_activity_at.after?(RECENT_ACTIVITY_THRESHOLD_DAYS.ago) ? 'on_track' : 'high_risk'
        end

        def risk_details
          { last_login: last_lms_activity_at, days_since_login: }
        end

        def days_since_login
          (Time.current - last_lms_activity_at).to_i / SECONDS_IN_A_DAY if last_lms_activity_at
        end
      end
    end
  end
end
