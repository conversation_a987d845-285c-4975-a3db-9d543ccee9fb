# frozen_string_literal: true

module LearningDelivery
  class Task
    class UpdateStatusCommand < ApplicationCommand
      attr_reader :task, :new_status, :user

      VALID_STATUSES_FOR_TRANSITION_TO = {
        viewed: %i[created assigned],
      }.freeze

      def initialize(task:, new_status:, user: nil)
        @task = task
        @new_status = new_status.to_sym
        @user = user
      end

      def call
        return false unless valid_transition?

        update_task_status
        task.reload
        true
      end

      private

      def valid_transition?
        case new_status
        when :viewed
          task.status.to_sym.in?(VALID_STATUSES_FOR_TRANSITION_TO[:viewed])
        else
          true
        end
      end

      def update_task_status
        case new_status
        when :viewed
          task.update!(
            status: new_status,
            viewed_at: Time.current,
          )
        when :completed
          task.update!(
            status: new_status,
            completed_at: Time.current,
          )
          create_task_complete_activity!
        else
          task.update!(status: new_status)
        end
      end

      def create_task_complete_activity!
        LearningDelivery::Activity::ReplaceCommand.new(
          employee: user&.learning_delivery_employee || task.owner,
          attributes: {
            activity_type: :task,
            title: "Completed #{task.title}",
            description: "User #{user&.full_name} completed task: #{task.uid}",
            target: task,
            metadata: { task_id: task.uid, completed_at: task.completed_at, due_at: task.due_at },
          },
        ).call!
      end
    end
  end
end
