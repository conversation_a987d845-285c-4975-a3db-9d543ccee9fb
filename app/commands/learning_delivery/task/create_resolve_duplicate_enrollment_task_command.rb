# frozen_string_literal: true

module LearningDelivery
  class Task
    class CreateResolveDuplicateEnrollmentTaskCommand < ApplicationCommand
      DEFAULT_OWNER_EMAIL = '<EMAIL>'

      attr_reader :enrollment, :duplicate_enrollment

      delegate :title, :description, :recommendation, :reason, :due_at, to: :presenter

      def self.callable?(enrollment:, duplicate_enrollment:)
        new(enrollment:, duplicate_enrollment:).callable?
      end

      def initialize(enrollment:, duplicate_enrollment:)
        @enrollment = enrollment
        @duplicate_enrollment = duplicate_enrollment
      end

      def call!
        return unless callable?

        create_task
      end

      def callable?
        enrollment.present? && duplicate_enrollment.present? &&
          enrollment.id != duplicate_enrollment.id &&
          enrollment.learner_id == duplicate_enrollment.learner_id &&
          enrollment.cohort.id == duplicate_enrollment.cohort.id &&
          enrollment.primary? && duplicate_enrollment.primary?
      end

      def presenter
        @presenter ||= LearningDelivery::Task::ResolveDuplicateEnrollmentPresenter.new(
          resource: enrollment,
          duplicate_enrollment:,
        )
      end

      private

      def create_task
        LearningDelivery::Task::ResolveDuplicateEnrollment.create!(
          resource: enrollment,
          status: :created,
          due_at:,
          title:,
          description:,
          recommendation:,
          reason:,
          owner:,
        )
      end

      def owner
        @owner ||= AdminUser.find_by!(email: DEFAULT_OWNER_EMAIL)
      end
    end
  end
end
