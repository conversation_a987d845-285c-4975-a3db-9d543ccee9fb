# frozen_string_literal: true

module Site
  class DetectBotCommand < ApplicationCommand

    RULES = %i[ fake_phone fake_utm_params honey_pot ].freeze

    attr_reader :attributes, :log

    # @param attributes [Hash]
    #   first_name
    #   phone
    #   utm_source
    #   utm_campaign
    #   hp_key - honeypot field
    def initialize(attributes:)
      @attributes = attributes.with_indifferent_access
      @log = { type: 'Site::DetectBotCommand', attributes: }
    end

    # @return [Boolean] True if we have detected a bot, false otherwise.
    def call!
      is_bot = RULES.inject(false) do |result, rule|
        rule_result = send(rule)
        log[rule] = rule_result
        result || send(rule)
      end

      write_log(result: is_bot)

      is_bot
    end


    private

    def write_log(attributes)
      JsonLogger.log(**log, **attributes)
    end

    def fake_phone
      !!phone&.gsub(/\s+/, '')&.match?(/\A[a-zA-Z]+\z/)
    end

    def fake_utm_params
      utm_source.to_s.strip == 'on' || utm_campaign.to_s.strip == 'on'
    end

    def honey_pot
      hp_key.present?
    end

    def method_missing(method_name, *_args, &)
      attributes[method_name]
    end

    def respond_to_missing?(method_name, include_private = false)
      attributes.key?(method_name) || super
    end

  end
end
