# frozen_string_literal: true

module Lms
  class Assignment
    class ReplaceFromCanvasAttributesCommand < ApplicationCommand
      attr_reader :cohort, :canvas_attributes, :lms_assignment

      def initialize(cohort:, canvas_attributes:)
        @cohort = cohort
        @canvas_attributes = canvas_attributes
        @lms_assignment = find_existing_assignment(key: canvas_attributes['id']) || Lms::Assignment.new(lms_module:, assignment_group:)
      end

      def call!
        ActiveRecord::Base.transaction do
          replace_lms_assignment
          update_remote_canvas_assignment
          assign_cohort_sections
          update_accepted_submission_types
        end

        lms_assignment
      end

      private

      def find_existing_assignment(key:)
        Remote::Canvas::Assignment.find_by(key:)&.core_record
      end

      def replace_lms_assignment
        attributes = {
          name:,
          grading_type: canvas_attributes['grading_type'],
          html_url: canvas_attributes['html_url'],
          published: canvas_attributes['published'],
          required:,
          status:,
          due_at: canvas_attributes['due_at'],
          lock_at: canvas_attributes['lock_at'],
          unlock_at: canvas_attributes['unlock_at'],
          assignment_template:,
        }
        ReplaceCommand.call!(
          lms_assignment:,
          attributes:,
        )
      end

      def update_remote_canvas_assignment
        remote_canvas_assignment.update!(key: canvas_attributes['id'])
      end

      def assign_cohort_sections
        return if canvas_attributes['has_overrides']

        Lms::Assignment::AssignCohortSectionsCommand.call!(cohort:, lms_assignment:)
      end

      def update_accepted_submission_types
        return if canvas_attributes['submission_types'].blank?

        Lms::Assignment::UpdateAcceptedSubmissionTypesCommand.call!(
          assignment: lms_assignment,
          submission_types: canvas_attributes['submission_types'],
        )
      end

      def assignment_template
        @assignment_template ||= Lms::AssignmentTemplate.find_or_create_by(
          name:,
          module_template: lms_module&.module_template,
        )
      end

      def lms_module
        @lms_module ||= canvas_attributes['lms_module']
      end

      def assignment_group
        @assignment_group ||= canvas_attributes['lms_assignment_group'] || Lms::AssignmentGroup.find_by!(cohort:, name: canvas_assignment_group_name)
      end

      def remote_canvas_assignment
        @remote_canvas_assignment ||= lms_assignment.remote_canvas_assignment || lms_assignment.build_remote_canvas_assignment
      end

      def name
        @name ||= canvas_attributes['name'].split(':', 2).last.strip
      end

      def required
        @required ||= !canvas_attributes['omit_from_final_grade'] && canvas_attributes['grading_type'] != 'not_graded'
      end

      def status
        return 'past_due' if canvas_attributes['due_at'].present? && Time.zone.parse(canvas_attributes['due_at']) < Time.current

        if (canvas_attributes['unlock_at'].present? && Time.zone.parse(canvas_attributes['unlock_at']) > Time.current) ||
            (canvas_attributes['lock_at'].present? && Time.zone.parse(canvas_attributes['lock_at']) < Time.current)
          'locked'
        else
          'unlocked'
        end
      end

      def canvas_assignment_group_name
        @canvas_assignment_group_name ||= canvas_client.get(
          "/courses/#{cohort.remote_canvas_course.key}/assignment_groups/#{canvas_attributes['assignment_group_id']}",
        ).dig('name')
      end

      def canvas_client
        @canvas_client ||= CanvasClient.new
      end
    end
  end
end
