# frozen_string_literal: true

module Lms
  class Assignment
    class ReplaceCommand < ApplicationCommand
      attr_reader :lms_assignment, :attributes

      def initialize(lms_assignment:, attributes:)
        @lms_assignment = lms_assignment
        @attributes = attributes
      end

      def call!
        ActiveRecord::Base.transaction do
          lms_assignment.assign_attributes(attributes)
          lms_assignment.save!
          lms_assignment
        end
      end
    end
  end
end
