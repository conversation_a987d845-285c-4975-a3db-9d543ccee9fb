# frozen_string_literal: true

module Lms
  class AssignmentGroup
    class ReplaceCommand < ApplicationCommand
      attr_reader :cohort, :attributes, :assignment_group

      def initialize(attributes:, cohort: nil, assignment_group: nil)
        @cohort = cohort || assignment_group&.cohort
        @attributes = attributes
        @assignment_group = assignment_group || Lms::AssignmentGroup.new(cohort:)
      end

      def call!
        ActiveRecord::Base.transaction do
          replace_assignment_group
          replace_independent_assignments
        end
      end

      private

      def update_attributes
        @update_attributes ||=
          attributes.with_indifferent_access
            .slice('name', 'position', 'short_name')
            .merge(short_name: attributes['short_name'] || mapped_short_name)
      end

      def mapped_short_name
        key = attributes['name'] || assignment_group.name
        Setting.value_for(:lms_assignment_group_short_name_mapping, key)
      end

      def replace_assignment_group
        assignment_group.update!(update_attributes)
      end

      def replace_independent_assignments
        return if independent_assignments.blank?

        independent_assignments.each do |assignment_data|
          assignment_data['lms_assignment_group'] = assignment_group

          Lms::Assignment::ReplaceFromCanvasAttributesCommand.call!(
            cohort:,
            canvas_attributes: assignment_data,
          )
        end
      end

      def independent_assignments
        @independent_assignments ||= attributes['assignments']
      end
    end
  end
end
