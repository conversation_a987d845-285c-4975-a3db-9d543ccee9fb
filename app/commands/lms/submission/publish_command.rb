# frozen_string_literal: true

module Lms
  class Submission
    class PublishCommand < ApplicationCommand
      attr_reader :submission, :review

      delegate :publishable?, to: :submission

      def initialize(submission:)
        @submission = submission
        @review = submission.current_review
      end

      def call!
        publish
      end

      private

      def publish
        return unless publishable?

        ActiveRecord::Base.transaction do
          mark_submission_as_graded!
          submission.remote_canvas_submission.remote_update
          publish_comment
          mark_as_published!
        end
      end

      def mark_submission_as_graded!
        submission.update!(
          state: :graded,
          grade: review.grade,
          graded_at: Time.current,
          graded_by: review.grader,
        )
      end

      def publish_grade_to_remote
        submission.remote_canvas_submission.remote_update
      end

      def publish_comment
        return if review.comments_to_publish.blank?

        Lms::Submission::Comment::CreateCommand.new(
          submission:,
          author: review.manually_reviewed_by&.learning_delivery_employee || review.grader,
          text: review.comments_to_publish,
        ).call!
      end

      def mark_as_published!
        review.published_at = Time.current
        review.state = Lms::Submission::Review.states[:published]
        review.save!
      end
    end
  end
end
