# frozen_string_literal: true

module Lms
  class Submission
    class ReplaceCommand < ApplicationCommand
      attr_reader :enrollment, :assignment, :attributes, :lms_submission

      def initialize(enrollment:, assignment:, attributes:, lms_submission: nil)
        @enrollment = enrollment
        @assignment = assignment
        @attributes = attributes
        @lms_submission = lms_submission || Lms::Submission.new(enrollment:, assignment:)
      end

      def call!
        ActiveRecord::Base.transaction do
          replace_submission
          update_remote_canvas_submission
        end

        lms_submission
      end

      private

      def replace_submission
        lms_submission.update!(
          grade: attributes['grade'],
          graded_at: attributes['graded_at'],
          url: submission_url,
          score: attributes['score'],
          state: attributes['workflow_state'],
          submission_type:,
          submitted_at: attributes['submitted_at'],
          graded_by: learning_delivery_employee,
          attempt: attributes['attempt'],
          seconds_late: attributes['seconds_late'],
        )
      end

      def update_remote_canvas_submission
        remote_canvas_submission.update!(key: attributes['id'])
      end

      def remote_canvas_submission
        @remote_canvas_submission ||= lms_submission.remote_canvas_submission || lms_submission.build_remote_canvas_submission
      end

      def submission_type
        @submission_type ||= attributes['submission_type'] || 'undefined'
      end

      def submission_url
        @submission_url ||= if attributes['submission_type'] == 'online_upload' && attachments.present?
          validate_attachment_count!
          latest_attachment['url']
        else
          attributes['url']
        end
      end

      def latest_attachment
        @latest_attachment ||= attachments.max_by { |a| a['created_at'] }
      end

      def attachments
        @attachments ||= Array(attributes['attachments'])
      end

      def validate_attachment_count!
        return if attachments.size == 1

        ErrorReporter.report(
          message: 'Multiple attachments detected for online_upload submission',
          severity: :info,
          submission_id: lms_submission.id,
          enrollment_id: enrollment.id,
          assignment_id: assignment.id,
          attachment_count: attachments.size,
          attachment_ids: attachments.pluck('id'),
        )
      end

      def learning_delivery_employee
        @learning_delivery_employee ||= Remote::Canvas::Employee.find_by(key: attributes['grader_id'])&.core_record
      end
    end
  end
end
