# frozen_string_literal: true

module Lms
  class Submission
    class Comment
      class FetchBySectionCommand < ApplicationCommand
        QUERY = <<~GRAPHQL
          query RecentSubmissionComments($sisCourseId: String!, $updatedSince: DateTime!, $sectionIds: [ID!]) {
            course(sisId: $sisCourseId) {
              submissionsConnection(filter: { updatedSince: $updatedSince, sectionIds: $sectionIds }) {
                nodes {
                  _id
                  commentsConnection {
                    nodes {
                      _id
                      comment
                      attempt
                      author { _id }
                    }
                  }
                }
              }
            }
          }
        GRAPHQL

        attr_reader :section, :updated_since

        delegate :cohort, :remote_canvas_section, to: :section
        delegate :remote_canvas_course, to: :cohort

        # @param section [Section]
        # @param updated_since [Time]
        def initialize(section:, updated_since:)
          @section = section
          @updated_since = updated_since
        end

        # @return [Array<Lms::Submission::Comment>]
        def call!
          submission_nodes.flat_map do |sub_node|
            canvas_submission_id = sub_node['_id']
            lms_submission = indexed_submissions[canvas_submission_id]
            next if lms_submission.blank?

            comments = sub_node.dig('commentsConnection', 'nodes') || []

            comments.each do |cnode|
              attributes = {
                'id' => cnode['_id'],
                'comment' => cnode['comment'],
                'attempt' => cnode['attempt'],
                'author_id' => cnode.dig('author', '_id'),
              }

              Lms::Submission::Comment::ReplaceCommand.call!(submission: lms_submission, attributes:)
            end
          end.compact_blank!
        end

        private

        def canvas_client
          @canvas_client ||= CanvasClient.new
        end

        def graphql_variables
          {
            sisCourseId: remote_canvas_course.alternate_key,
            updatedSince: updated_since.iso8601,
            sectionIds: [remote_canvas_section.key],
          }
        end

        def submission_nodes
          return @submission_nodes if defined?(@submission_nodes)

          result = canvas_client.graphql(query: QUERY, variables: graphql_variables)
          @submission_nodes = result.dig('data', 'course', 'submissionsConnection', 'nodes') || []
        rescue StandardError => e
          ErrorReporter.report(
            error: e,
            message: e.message,
            type: self.class.name,
            cohort_id: cohort.id,
            cohort_sis_id: remote_canvas_course.alternate_key,
            section_id: section.id,
            section_key: remote_canvas_section.key,
            updated_since:,
          )
          @submission_nodes = []
        end

        def indexed_submissions
          @indexed_submissions ||= section.cohort.lms_submissions
            .joins(:remote_canvas_submission)
            .where(remote_resources: { key: submission_nodes.pluck('_id') })
            .index_by { |s| s.remote_canvas_submission.key }
        end
      end
    end
  end
end
