# frozen_string_literal: true

module Lms
  class Submission
    class Comment
      class DeleteCommand < ApplicationCommand
        attr_reader :comment

        def initialize(comment:)
          @comment = comment
        end

        def call!
          ActiveRecord::Base.transaction do
            unpublish_comment!
            delete_comment!
          end
        end

        private

        def unpublish_comment!
          return if comment.remote_canvas_submission_comment.blank?

          Remote::UnpublishCommand.call!(remote_resource: comment.remote_canvas_submission_comment)
        end

        def delete_comment!
          comment.destroy!
        end
      end
    end
  end
end
