# frozen_string_literal: true

module Lms
  class Submission
    class Comment
      class CreateCommand < ApplicationCommand
        attr_reader :submission, :author, :text, :comment

        def initialize(submission:, author:, text: nil)
          @submission = submission
          <AUTHOR> author
          @text = text
        end

        def call!
          ActiveRecord::Base.transaction do
            @comment = create_comment!
            publish_comment!
            comment
          end
        end

        private

        def publish_comment!
          Remote::PublishCommand.call!(remote_resource: remote_canvas_submission_comment)
        end

        def create_comment!
          submission.comments.create!(
            text:,
            attempt: submission.attempt,
            author:,
            read_at:,
          )
        end

        def read_at
          Time.zone.now if author.is_a?(LearningDelivery::Employee)
        end

        def remote_canvas_submission_comment
          @remote_canvas_submission_comment ||= comment.build_remote_canvas_submission_comment
        end
      end
    end
  end
end
