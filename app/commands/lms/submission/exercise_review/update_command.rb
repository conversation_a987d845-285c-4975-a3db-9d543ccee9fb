# frozen_string_literal: true

module Lms
  class Submission
    class ExerciseReview
      class UpdateCommand < ApplicationCommand
        attr_reader :exercise_review

        def initialize(attributes:, current_user:, exercise_review: nil)
          @exercise_review = exercise_review || Lms::Submission::ExerciseReview.new
          @attributes = attributes
          @current_user = current_user
        end

        def call!
          ActiveRecord::Base.transaction do
            if @attributes[:mark_as_reviewed] == false
              undo_mark_as_reviewed!
            else
              @exercise_review.update!(filtered_attributes)
              mark_as_reviewed!
            end
          end
        end

        private

        def undo_mark_as_reviewed!
          @exercise_review.manually_reviewed_at = nil
          @exercise_review.manually_reviewed_by = nil
          @exercise_review.state = Lms::Submission::ExerciseReview.states[:manual_review_needed]

          @exercise_review.save!
        end

        def mark_as_reviewed!
          @exercise_review.manually_reviewed_at = Time.current
          @exercise_review.manually_reviewed_by = @current_user
          @exercise_review.state = Lms::Submission::ExerciseReview.states[:graded]

          @exercise_review.save!
        end

        def filtered_attributes
          @attributes.except(:mark_as_reviewed)
        end
      end
    end
  end
end
