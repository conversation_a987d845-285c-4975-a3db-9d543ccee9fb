# frozen_string_literal: true

module Lms
  class Submission
    class Review
      class ReplaceCommand < ApplicationCommand
        attr_reader :review, :attributes, :current_user

        def initialize(attributes:, current_user:, review: nil)
          @review = review || Lms::Submission::Review.new
          @attributes = attributes
          @current_user = current_user
        end

        def call!
          ActiveRecord::Base.transaction do
            update_review!
          end
        end

        private

        def update_review!
          review.update!(attributes)
          review
        end
      end
    end
  end
end
