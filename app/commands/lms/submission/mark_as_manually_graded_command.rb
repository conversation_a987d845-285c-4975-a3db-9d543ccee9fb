# frozen_string_literal: true

module Lms
  class Submission
    class MarkAsManuallyGradedCommand < ApplicationCommand
      attr_reader :submission, :current_user, :review

      def initialize(submission:, current_user:)
        @current_user = current_user
        @submission = submission
        @review = submission.current_review
      end

      def call!
        ActiveRecord::Base.transaction do
          mark_review_as_manually_graded!
          create_learning_delivery_activity!
        end
      end

      private

      def mark_review_as_manually_graded!
        review.manually_reviewed_at = Time.current
        review.manually_reviewed_by = current_user
        review.state = :graded
        review.graded_at = review.manually_reviewed_at
        review.save!
      end

      def create_learning_delivery_activity!
        LearningDelivery::Activity::ReplaceCommand.new(
          employee: current_user.learning_delivery_employee,
          attributes: {
            activity_type: :grade,
            title: "Graded week #{submission.cohort_week&.number} assignment for #{submission.learner.full_name}",
            description: "Assignment \"#{submission.assignment.name}\" received grade: #{review.grade}",
            target: review,
            metadata: {
              submission_id: submission.id,
              review_id: review.id,
              graded_at: review.graded_at,
              graded_by: current_user.id,
              grade: review.grade,
            },
          },
        ).call!
      end
    end
  end
end
