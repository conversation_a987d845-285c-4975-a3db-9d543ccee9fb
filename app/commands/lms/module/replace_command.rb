# frozen_string_literal: true

module Lms
  class Module
    class ReplaceCommand < ApplicationCommand
      attr_reader :cohort, :attributes, :lms_module

      delegate :client, :remote_parent, to: :remote_canvas_module

      def initialize(cohort:, attributes:, lms_module: nil)
        @cohort = cohort
        @attributes = attributes
        @lms_module = lms_module || Lms::Module.new(week:)
      end

      def call!
        ActiveRecord::Base.transaction do
          replace_lms_module
          update_remote_canvas_module
          replace_lms_assignments
        end

        lms_module
      end

      private

      def replace_lms_module
        lms_module.update!(
          title:,
          title_prefix:,
          week:,
          unlock_at: attributes['unlock_at'],
          position:,
          cohort:,
          module_template:,
        )
      end

      def replace_lms_assignments
        remote_module_assignments.each do |assignment_data|
          assignment_data['lms_module'] = lms_module

          Lms::Assignment::ReplaceFromCanvasAttributesCommand.call!(
            cohort:,
            canvas_attributes: assignment_data,
          )
        end
      end

      def update_remote_canvas_module
        remote_canvas_module.update!(key: attributes['id'])
      end

      def remote_canvas_module
        @remote_canvas_module ||= lms_module.remote_canvas_module || lms_module.build_remote_canvas_module
      end

      def remote_module_assignments
        @remote_module_assignments ||= Lms::Module::FetchRemoteAssignmentsData.call!(
          lms_module:,
          module_items: attributes['items'],
        )
      end

      def module_template
        @module_template ||= Lms::ModuleTemplate.find_or_create_by!(
          title:,
          title_prefix:,
          week_number:,
          program: cohort.program,
        )
      end

      def title
        @title ||= parsed_module_name.last.strip
      end

      def title_prefix
        @title_prefix ||= parsed_module_name.first.strip
      end

      def position
        @position ||= attributes['position']
      end

      def parsed_module_name
        @parsed_module_name ||= attributes['name'].split(':', 2)
      end

      def week
        if instance_variable_defined?(:@week)
          @week
        else
          @week = cohort.weeks.find_by(number: week_number)
        end
      end

      def week_number
        @week_number ||= if last_module?
          cohort.weeks.last.number
        else
          attributes['position']
        end
      end

      def last_module?
        @last_module ||= attributes['position'] == cohort.weeks.size
      end
    end
  end
end
