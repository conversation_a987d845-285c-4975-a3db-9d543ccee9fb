# frozen_string_literal: true

class Registration
  class BuildLinkForm < ApplicationForm
    attribute :partner_id, :integer
    attribute :partner_program_id, :integer
    attribute :finance_relationship_id, :integer

    validates :partner, presence: true
    validates :partner_program, presence: true
    validates :finance_relationship, presence: true

    delegate :program, to: :partner_program

    def partner
      @partner ||= Partner.find_by(id: partner_id) || partner_program&.partner
    end

    def partner_program
      if instance_variable_defined?(:@partner_program)
        @partner_program
      else
        @partner_program = PartnerProgram.find_by(id: partner_program_id)
      end
    end

    def finance_relationship
      if instance_variable_defined?(:@finance_relationship)
        @finance_relationship
      else
        @finance_relationship = Finance::Relationship.find_by(id: finance_relationship_id)
      end
    end

    def finance_relationship_options
      partner.finance_relationships.order(:name).map { |fr| [fr.name, fr.id] }
    end

    def selected_finance_relationship
      finance_relationship_id || partner.finance_relationships.default.first&.id
    end

    def partner_program_options
      partner.partner_programs.active.to_a.sort_by(&:program_name).map { |pp| [pp.program_name, pp.id] }
    end

  end
end
