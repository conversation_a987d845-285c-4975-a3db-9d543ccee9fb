# frozen_string_literal: true

class Registration
  class CreateManualForm < ApplicationForm
    include StoredInCents

    attribute :reenrolled_from_id, :integer
    attribute :notes, :string
    attribute :email_address, :string
    attribute :first_name, :string
    attribute :last_name, :string
    attribute :phone, :string
    attribute :partner_id, :integer
    attribute :partner_program_id, :integer
    attribute :finance_relationship_id, :integer
    attribute :eva_type, :string
    attribute :third_party_entity, :string
    attribute :price_cents, :integer
    attribute :payment_method_id, :integer
    attribute :admin_user
    attribute :cohort_id, :integer
    attribute :section_id, :integer

    stored_in_cents :price_cents, virtual_attribute: true

    delegate :program, to: :partner_program
    delegate :eva_enabled?, :dedicated_sections?, to: :partner
    delegate :eva_type_name, to: :reference_registration

    validates :price_cents, numericality: { integer_only: true, greater_than_or_equal_to: 0 }, if: -> { price_cents.present? }
    validate :partner_dependent_fields, :cohort_dependent_fields, :section_must_belong_to_cohort


    # Virtual Attributes
    # ------------------

    def reenrolled_from
      if instance_variable_defined?(:@reenrolled_from)
        @reenrolled_from
      else
        @reenrolled_from = Enrollment.find_by(id: reenrolled_from_id)
      end
    end

    def email
      @email ||= Email::ReplaceCommand.call!(address: email_address)
    end

    def first_name
      super || email.learner&.first_name
    end

    def last_name
      super || email.learner&.last_name
    end

    def phone
      super || email.learner&.phone&.local_number
    end

    def partner
      if instance_variable_defined?(:@partner)
        @partner
      else
        @partner = Partner.find_by(id: partner_id)
      end
    end

    def currency_code
      partner&.currency_code || 'USD'
    end

    def partner_id
      super || reenrolled_from&.partner&.id
    end

    def partner_program
      if instance_variable_defined?(:@partner_program)
        @partner_program
      else
        @partner_program = PartnerProgram.find_by(id: partner_program_id)
      end
    end

    def partner_program_id
      super || reenrolled_from&.partner_program&.id
    end

    def finance_relationship
      if instance_variable_defined?(:@finance_relationship)
        @finance_relationship
      else
        @finance_relationship = Finance::Relationship.find_by(id: finance_relationship_id)
      end
    end

    def cohort
      @cohort ||= if cohort_id.present?
        Cohort.find_by(id: cohort_id)
      else
        Program::AddableCohortsCommand.call!(program:, partner:).max_by(&:starts_on)
      end
    end

    def section
      @section ||= if section_id.present?
        Section.find_by(id: section_id)
      else
        begin
          sections = cohort.sections.where(partner: dedicated_sections? ? partner : nil)
          sections.min_by { |section| section.enrollments.primary.retained.count }
        end
      end
    end

    def price_cents
      super || (partner_program && Ecom::Pricing::DefaultForPartnerProgram.new(partner_program:).call!.actual_cents)
    end

    def payment_method
      if instance_variable_defined?(:@payment_method)
        @payment_method
      else
        @payment_method = Ecom::PaymentMethod.find_by(id: payment_method_id)
      end
    end

    def eva_discount_description
      partner.promos_eva_discount_code.discount.name
    end


    # Options
    # -------

    def finance_relationship_options
      partner.finance_relationships.order(:name).select { |fr| fr.active_contract.present? }.map { |fr| [fr.name, fr.id] }
    end

    def selected_finance_relationship_id
      finance_relationship_id || partner.finance_relationships.default.first&.id
    end

    def partner_program_options
      partner.partner_programs.active.to_a.sort_by(&:program_name).map { |pp| [pp.program_name, pp.id] }
    end

    def eva_type_options
      @eva_type_options ||= Registration.eva_type_names.invert
    end

    def payment_method_options
      Ecom::PaymentMethod.offer_on_manual_create.map { |pm| [pm.kind_name, pm.id] }
    end

    def selected_payment_method_id
      payment_method_id || Ecom::PaymentMethod.offer_on_manual_create.partner_invoice.first&.id
    end

    def cohort_options
      return [] if partner_program.blank?

      Program::AddableCohortsCommand.call!(program:, partner:)
        .map { |cohort| [cohort.name, cohort.id] }
        .sort_by { |name, _id| name }
    end

    def cohort_options_for_nested_select
      return Cohort.none if partner_program.blank?

      Program::AddableCohortsCommand.call!(program:, partner:)
        .sort_by(&:starts_on)
    end

    def selected_cohort_id
      cohort_id || cohort&.id
    end

    def selected_section_id
      section_id || section&.id
    end

    def status
      if cohort_id.present? && section_id.present?
        :section_picked
      elsif cohort_id.present?
        :cohort_picked
      else
        :created
      end
    end

    def registration
      return unless valid?

      @registration ||= Registration::ReplaceCommand.call!(
        email_address:,
        learner_attributes: { first_name:, last_name:, phone: },
        attributes: {
          finance_relationship_id:,
          eva_type:,
          section:,
          admin_user:,
          partner_program:,
          third_party_entity:,
          reenrolled_from:,
          notes:,
          status:,
        },
        publish_to_hubspot: true,
      )
    end

    def order
      return unless valid?

      @order ||= Ecom::Order::CreateFromRegistrationCommand.call!(registration:, price_cents:).tap do |order|
        create_pending_payment!(order:)
      end
    end

    private

    def persist!
      order.order_items.first.reload.registration
    end

    def payer_name
      "#{first_name} #{last_name}"
    end

    def reference_registration
      @reference_registration ||= Registration.new(eva_type:)
    end

    def partner_dependent_fields
      errors.add(:partner_program_id, 'is not for selected partner') if partner_program.present? && (partner_program.partner != partner)
      return unless finance_relationship.present? && partner.finance_relationships.exclude?(finance_relationship)

      errors.add(:finance_relationship_id,
        'is not for selected partner',
      )
    end

    def cohort_dependent_fields
      return if partner_program.blank?

      errors.add(:partner_program_id, 'No addable cohorts available for the selected partner program') if cohort.blank?

      return unless cohort.present? && section.blank?

      errors.add(:partner_program_id, 'No sections available for the selected partner program')
    end

    def create_pending_payment!(order:)
      Ecom::Payment::CreateOrUpdatePendingCommand.call!(
        order:,
        payment_method:,
      )
    end

    def section_must_belong_to_cohort
      errors.add(:cohort_id, 'must be selected when section is selected') if section_id.present? && cohort_id.blank?

      return unless section_id.present? && cohort_id.present?

      section = Section.find_by(id: section_id)
      cohort = Cohort.find_by(id: cohort_id)

      return unless section.present? && cohort.present? && section.cohort_id != cohort.id

      errors.add(:section_id, 'must belong to the selected cohort')
    end
  end
end
