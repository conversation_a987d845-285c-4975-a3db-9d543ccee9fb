# frozen_string_literal: true

class Enrollment
  class TransferForm < ApplicationForm

    ADD_PERIOD_BUFFER = 2.weeks

    attribute :enrollment_id, :integer
    attribute :section_id, :integer
    attribute :reason, :string
    attribute :admin_user

    validates :enrollment_id, presence: true
    validates :section_id, presence: true
    validates :reason, presence: { message: "must be provided for paused enrollments" }, if: :paused_enrollment?

    def id
      enrollment_id
    end

    def enrollment
      if instance_variable_defined?(:@enrollment)
        @enrollment
      else
        @enrollment = Enrollment.find_by(id: enrollment_id)
      end
    end

    def section
      if instance_variable_defined?(:@section)
        @section
      else
        @section = Section.find_by(id: section_id)
      end
    end

    def cohort
      @cohort ||= section&.cohort
    end

    def persist!
      Enrollment::TransferCommand.call!(
        enrollment:,
        new_section: section,
        reason:,
        admin_user:,
      )
    end

    private

    def paused_enrollment?
      enrollment.paused_status?
    end
  end
end
