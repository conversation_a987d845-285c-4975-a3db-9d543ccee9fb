# frozen_string_literal: true

module Payments
  class ManualPayment
    class CreateForm < ApplicationForm
      include StoredInCents

      attribute :ecom_payment_id, :integer
      attribute :payer_name, :string
      attribute :payer_email, :string
      attribute :amount_cents, :integer
      attribute :admin_user_id, :integer
      attribute :return_to, :string

      stored_in_cents :amount_cents, virtual_attribute: true

      validates :payer_name, presence: true
      validates :payer_email, presence: true, format: { with: Devise.email_regexp }
      validate :validate_manual_payment_amount_cents

      delegate :currency_code, to: :ecom_order

      def initialize(*)
        super

        set_defaults
      end

      def persist!
        Ecom::Order::LockCommand.call!(order: ecom_order) do
          manual_payment
        end
      end

      def manual_payment
        @manual_payment ||= if valid?
          Payments::ManualPayment::CreateCommand.call!(
            ecom_payment:,
            payer:,
            amount_cents:,
            admin_user:,
          )
        else
          Payments::ManualPayment.new
        end
      end

      def payer
        return unless valid?

        @payer ||= Payments::Payer::CreateCommand.call!(
          email: payer_email,
          name: payer_name,
          processor: 'stripe', # always force to use stripe for manual payments
        )
      end

      def ecom_payment
        ecom_order.payment
      end

      def ecom_order
        if instance_variable_defined?(:@ecom_order)
          @ecom_order
        else
          @ecom_order = Ecom::Order.joins(:payment).find_by(ecom_payments: { id: ecom_payment_id })
        end
      end

      def admin_user
        return @admin_user if defined?(@admin_user)

        @admin_user = AdminUser.find_by(id: admin_user_id)
      end

      def semantic_error_attributes
        errors.attribute_names.map(&:to_s) - attribute_names.map(&:to_s) - self.class.dependent_params.map(&:to_s)
      end

      private

      def validate_manual_payment_amount_cents
        Payments::ManualPayment.new(amount_cents_param:).tap do |manual_payment|
          manual_payment.valid?

          manual_payment.errors[:amount_cents].each { |error| errors.add(:amount_cents, error) }
          manual_payment.errors[:amount_cents_param].each { |error| errors.add(:amount_cents_param, error) }
        end
      end

      def set_defaults
        self.amount_cents ||= [0, ecom_payment.unpaid_balance_cents].max
        self.payer_email ||= ecom_order.learner.primary_email&.address
        self.payer_name ||= ecom_order.learner.full_name
      end
    end
  end
end
