# frozen_string_literal: true

module Payments
  class InvoicePayment
    class CreateForm < ApplicationForm
      include StoredInCents

      attribute :ecom_payment_id, :integer
      attribute :payer_name, :string
      attribute :payer_email, :string
      attribute :amount_cents, :integer
      attribute :invoice_number, :string
      attribute :admin_user_id, :integer
      attribute :return_to, :string

      stored_in_cents :amount_cents, virtual_attribute: true

      validates :payer_name, presence: true
      validates :payer_email, presence: true, format: { with: Devise.email_regexp }
      validate :validate_invoice_payment

      delegate :currency_code, to: :ecom_order

      def initialize(*)
        super

        set_defaults
      end

      def persist!
        Ecom::Order::LockCommand.call!(order: ecom_order) do
          invoice_payment.save!

          mark_order_as_paid!
          transaction_invoice = mark_payment_as_paid!

          invoice_payment.update!(transaction_invoice:)
        end
      end

      def invoice_payment
        @invoice_payment ||= ecom_payment.payments_invoice_payments.build(
          amount_cents:,
          currency_code:,
          invoice_number: invoice_number.presence,
          payer:,
          admin_user:,
        ).tap(&:generate_uid)
      end

      def payer
        @payer ||= Payments::Payer::CreateOrUpdateWithNoProcessorCommand.call!(
          email: payer_email,
          name: payer_name,
        )
      end

      def ecom_payment
        ecom_order.payment
      end

      def ecom_order
        if instance_variable_defined?(:@ecom_order)
          @ecom_order
        else
          @ecom_order = Ecom::Order.joins(:payment).find_by(ecom_payments: { id: ecom_payment_id })
        end
      end

      def ecom_payment_method
        ecom_payment.payment_method
      end

      def admin_user
        @admin_user = AdminUser.find(admin_user_id)
      end

      def semantic_error_attributes
        errors.attribute_names.map(&:to_s) - attribute_names.map(&:to_s) - self.class.dependent_params.map(&:to_s)
      end

      private

      def validate_invoice_payment
        return unless errors.empty?

        invoice_payment.valid?

        invoice_payment.errors.to_hash.each do |attribute, messages|
          messages.each { |message| errors.add(attribute, message) }
        end
      end

      def set_defaults
        self.amount_cents ||= [0, ecom_payment.unpaid_balance_cents].max
        self.payer_email ||= ecom_order.learner.primary_email&.address
        self.payer_name ||= ecom_order.learner.full_name
      end

      def mark_order_as_paid!
        return unless ecom_order.cart?

        Ecom::Order::PrePayCommand.call!(order: ecom_order, payment_method: ecom_payment_method, payer_name:, payer_email:)
        Ecom::Order::PayCommand.call!(order: ecom_order, payment_method: ecom_payment_method, payer_name:, payer_email:)
      end

      # @return [Payments::Transaction::Invoice]
      def mark_payment_as_paid!
        Payments::Transaction::Invoice::CreateCommand.call!(ecom_payment: ecom_order.payment, amount_cents:, admin_user:).tap do |transaction|
          Ecom::Payment::ApplyPaymentsTransactionCommand.call!(payment: ecom_order.payment, payments_transaction: transaction)
        end
      end
    end
  end
end
