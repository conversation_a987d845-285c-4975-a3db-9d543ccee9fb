@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  /* Design System Classes */
  .brand-navy {
    color: #242E4A;
  }

  .bg-brand-navy {
    background-color: #242E4A;
  }

  .border-brand-navy {
    border-color: #242E4A;
  }

  .font-barlow {
    font-family: Barlow, sans-serif;
  }

  .rounded-brand {
    border-radius: 7.88px;
  }

  /* Support Cards Container */
  .support-cards-container {
    max-width: 1118px;
    gap: 10px;
  }

  /* Support Card Component */
  .support-card {
    @apply bg-white border border-gray-200 w-full lg:w-[366px] hover:shadow-sm transition-shadow duration-200 cursor-pointer font-barlow rounded-brand;
    padding: 16px 20px;
    height: 116.92px;
    text-decoration: none;
  }

  /* Mobile-specific support card styling */
  @media (max-width: 768px) {
    .support-card {
      height: 103px !important;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
    }

    .support-card-content {
      align-items: center !important;
      text-align: center;
      width: 100%;
      justify-content: center !important;
    }

    .support-card-content .flex.items-center.mb-2 {
      justify-content: center !important;
      margin-bottom: 8px !important;
      flex-direction: row !important;
      align-items: center;
      gap: 8px;
    }

    .support-card-content .flex.items-center .ml-3 {
      margin-left: 8px !important;
      flex: none !important;
      text-align: center;
    }

    .support-card-content .flex.items-center .ml-3 .flex.items-center {
      justify-content: center !important;
      gap: 6px;
      align-items: center;
    }

    .support-card-title {
      text-align: center !important;
    }

    .support-card-description {
      text-align: center !important;
      margin-bottom: 0 !important;
    }
  }

  .support-card-title {
    @apply font-semibold;
    color: #242E4A;
    font-size: 24px;
    font-weight: 600;
    font-family: Barlow, sans-serif;
    line-height: 1.02; /* 102% converted to decimal */
    letter-spacing: -0.02em; /* -2% converted to ems */
  }

  .support-card-description {
    color: #242E4A !important;
    font-size: 16px !important;
    font-weight: 400 !important;
    font-family: Barlow, sans-serif !important;
    line-height: 1.2 !important; /* 120% converted to decimal */
    letter-spacing: 0 !important; /* 0% letter spacing */
    margin: 0 !important; /* Remove default paragraph margins */
  }

  .support-card-icon {
    @apply w-6 h-6;
  }

  .support-card-content {
    gap: 8px;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  /* Desktop gap between title and arrow */
  .support-card-content .ml-3 .flex.items-center {
    gap: 6px;
  }

  .support-card-arrow {
    width: 7.88px;
    height: 13.75px;
    margin-left: 6px;
    margin-top: 4px;
    /* Exact dimensions as specified: 7.875880718231201px × 13.750696182250977px */
    /* Micro-adjustments: 6px right, 4px down for better visual alignment */
  }

  /* HubSpot Chat Integration Styles */
  [data-controller="hubspot-chat"] {
    @apply transition-all duration-300 ease-in-out;
  }


  [data-controller="hubspot-chat"].loading {
    @apply opacity-70 pointer-events-none;
  }

  [data-controller="hubspot-chat"].loading::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 20px;
    width: 20px;
    height: 20px;
    margin-top: -10px;
    border: 2px solid #E3721F;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  /* Support FAQ Dropdown Styles */
  #content-tag-filter {
    /* Remove all browser default styling */
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;

    /* Ensure consistent background and positioning */
    background-color: white !important;

    /* Fix for mobile Safari dropdown alignment */
    background-attachment: scroll !important;

    /* Ensure proper text rendering */
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
  }

  #content-tag-filter:focus {
    /* Custom focus state to override browser defaults */
    outline: none !important;
    border-color: #242E4A !important;
    box-shadow: 0 0 0 2px rgba(36, 46, 74, 0.1) !important;
  }

  /* Option styling - targeting multiple browsers */
  #content-tag-filter option {
    color: #242E4A !important;
    background-color: white !important;
    padding: 8px 12px;
  }

  #content-tag-filter option:checked {
    background-color: #242E4A !important;
    color: white !important;
  }

  #content-tag-filter option:hover {
    background-color: #f8fafc !important;
  }

  /* Firefox specific fixes */
  @-moz-document url-prefix() {
    #content-tag-filter {
      background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="%23242E4A"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/></svg>') !important;
      background-position: right 8px center !important;
      background-repeat: no-repeat !important;
      background-size: 16px !important;
    }
  }

  /* Webkit specific fixes for iOS Safari */
  @supports (-webkit-touch-callout: none) {
    #content-tag-filter {
      /* Prevent zoom on focus in iOS */
      font-size: 16px !important;
    }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Hero Section Typography */
  .hero-title {
    @apply brand-navy font-barlow font-semibold text-center;
    font-weight: 600;
    letter-spacing: -0.02em;
  }

  /* Desktop Hero Typography */
  .hero-title-desktop {
    font-size: 48px;
    line-height: 91.41px;
  }

  /* Mobile Hero Typography */
  .hero-title-mobile {
    font-family: Barlow;
    font-weight: 600;
    font-size: 30.09px;
    line-height: 30.97px;
    letter-spacing: -0.02em;
    text-align: center;
    color: #242E4A;
  }

  /* Responsive Hero Title */
  .hero-title-responsive {
    @apply hero-title hero-title-mobile;
  }

  /* Tablet Hero Title */
  @media (min-width: 769px) and (max-width: 1023px) {
    .hero-title-responsive {
      font-size: 38px;
      line-height: 65px;
      text-align: center;
      margin: 0 auto;
    }
  }

  /* Desktop Hero Title */
  @media (min-width: 1024px) {
    .hero-title-responsive {
      @apply hero-title-desktop;
    }
  }

  /* Hero Description Typography - Exact Figma Specifications */
  .hero-description {
    font-family: 'Barlow', sans-serif;
    font-weight: 400;
    font-size: 18px;
    line-height: 39.4px;
    letter-spacing: 0%;
    text-align: center;
    color: #000000;
    width: 1060.629150390625px;
    max-width: 100%; /* Responsive fallback */
    margin: 0 auto;
  }

  /* Tablet responsive adjustments for hero description */
  @media (max-width: 1200px) and (min-width: 769px) {
    .hero-description {
      width: 85%;
      max-width: 900px;
    }
  }

  /* Mobile responsive adjustments for hero description */
  @media (max-width: 768px) {
    .hero-description {
      width: 90%;
      max-width: 1060px;
    }
  }

  @media (max-width: 768px) {
    .hero-title-responsive {
      width: 280px;
      margin: 0 auto;
    }

    .hero-description {
      font-family: Barlow;
      font-weight: 400;
      font-size: 15.93px;
      line-height: 22.12px;
      letter-spacing: 0%;
      text-align: center;
      color: #000000;
      width: 280px;
      margin: 0 auto;
    }

    .faq-title {
      font-family: Barlow !important;
      font-weight: 600 !important;
      font-size: 20px !important;
      line-height: 38px !important;
      letter-spacing: -0.02em !important;
      text-align: center !important;
      width: 360.38px !important;
      margin: 0 auto !important;
    }

  }

  /* Mobile and Tablet Enroll Now Button - Available for both mobile and tablet */
  .mobile-enroll-btn {
    display: inline-block;
    width: 141.62px;
    height: 29.97px;
    background-color: #E3721F;
    color: white;
    font-family: Barlow;
    font-weight: 500;
    font-size: 12.26px;
    line-height: 120%;
    letter-spacing: 0.04em;
    text-align: center;
    text-transform: uppercase;
    text-decoration: none;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    transition: background-color 0.2s ease;
  }

  .mobile-enroll-btn:hover {
    background-color: #d6651a;
  }

  /* FAQ Section Title Typography */
  .faq-title {
    font-family: Barlow, sans-serif;
    font-weight: 600;
    font-size: 33px;
    line-height: 38px;
    letter-spacing: -0.02em; /* -2% converted to ems */
    text-align: center;
    color: #242E4A;
  }

  /* Header CTA Button Dimensions */
  .header-cta-button {
    width: 141.62px;
    height: 29.97px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-family: Barlow, sans-serif;
    font-weight: 500;
    font-size: 14px;
    border-radius: 6px;
    text-decoration: none;
    transition: opacity 0.2s ease-in-out;
  }

  .header-cta-button:hover {
    opacity: 0.9;
  }

  /* Button container with 10px gap */
  .header-cta-container {
    display: flex;
    gap: 10px;
  }
}
