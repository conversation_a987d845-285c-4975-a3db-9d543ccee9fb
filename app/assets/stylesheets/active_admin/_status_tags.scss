$status_lightblue: #3288bd;
$status_lightgrayblue: #7f8b92;
$status_darkgrayblue: #274e73;

$status_darkgreen: #3d8427;
$status_lightgreen: #95b66d;
$status_brightgreen: #0dbc4b;

$status_lightred: #d53e4f;
$status_lightgrayred: #a56866;

$status_lightorange: #fdae61;
$status_darkorange: #7c320a;
$status_brightorange: #f46d43;

$status_lightyellow: #d2b76d;

$status_darkmagenta: #9e0142;

$status_darkpurple: #5e4fa2;
$status_ziplines_blue: #241451;

.status_tag {

    &.dark-green {
        background: $status_darkgreen;
    }

    &.light-green {
        background: $status_lightgreen;
    }

    &.bright-green {
        background: $status_brightgreen;
    }

    &.dark-orange {
        background: $status_darkorange;
    }

    &.light-orange {
        background: $status_lightorange;
    }

    &.bright-orange {
        background: $status_brightorange;
    }

    &.dark-purple {
        background: $status_darkpurple;
    }

    &.dark-magenta {
        background: $status_darkmagenta;
    }

    &.light-blue {
        background: $status_lightblue;
    }

    &.light-gray-blue {
        background: $status_lightgrayblue;
    }

    &.dark-gray-blue {
        background: $status_darkgrayblue;
    }

    &.light-red {
        background: $status_lightred;
    }

    &.light-gray-red {
        background: $status_lightgrayred;
    }

    &.light-yellow {
        background: $status_lightyellow;
    }

    &.ziplines-blue {
        background: $status_ziplines_blue;
    }


    &.error {
        background: $status_lightred;
        white-space: nowrap;
    }

    // Partner, Program, PartnerProgram
    &.active {
        background: $status_lightblue;
    }

    &.inactive {
        background: $status_lightgrayblue;
    }

    // Cohort
    &.created {
        background: $status_lightgrayblue;
    }

    &.opened {
        background: $status_darkgrayblue;
    }

    &.started {
        background: $status_darkgreen;
    }

    &.ended {
        background: $status_lightgreen;
    }

    &.extension_period_ended {
        background: $status_lightgrayred;
    }

    &.closed {
        background: $status_darkorange;
    }

    // Registration
    &.registration {

        // status
        &.created {
            background: $status_lightgrayblue;
        }

        &.confirmed {
            background: $status_darkgreen;
        }

        &.cancelled {
            background: $status_lightred;
        }

        &.cohort_picked {
            background: $status_darkmagenta;
        }

        &.section_picked {
            background: $status_darkpurple;
        }

    }

    // Enrollment
    &.enrollment {

        // status
        &.pending {
            background: $status_lightgrayblue;
        }

        &.active {
            background: $status_lightblue;
        }

        &.no_pass {
            background: $status_lightred;
        }

        &.pass {
            background: $status_lightgreen;
        }

        &.certificate_issued {
            background: $status_brightgreen;
        }

        &.unenrolled {
            background: $status_lightyellow;
        }

        &.dropped {
            background: $status_brightorange;
        }

        &.transferred {
            background: $status_lightorange;
        }

        &.withdrew {
            background: $status_darkmagenta;
        }

        &.paused {
            background: $status_darkpurple;
        }

        // risk levels
        &.on_track {
            background-color: $status_darkgreen;
        }

        &.low_risk {
            background-color: $status_darkorange;
        }

        &.high_risk {
            background-color: $status_lightred;
        }
    }

    // Finance::Booking
    &.revenue {
        background: $status_darkgreen;
    }

    &.refund {
        background: $status_darkpurple;
    }

    // Alert
    &.open {
        background: $status_lightblue;
    }

    &.resolved {
        background: $status_darkgreen;
    }

    &.dismissed {
        background: $status_lightgrayblue;
    }

    &.ecom_order {
        &.cart {
            background: $status_lightgrayblue;
        }

        &.paid {
            background: $status_darkgrayblue;
        }

        &.fulfilled {
            background: $status_darkgreen;
        }
    }

    &.ecom_payment {
        &.pending {
            background: $status_lightgrayblue;
        }

        &.paid {
            background: $status_darkgreen;
        }

        &.partial {
            background: $status_darkgrayblue;
        }

        &.refunded {
            background: $status_darkpurple;
        }

        &.failed {
            background: $status_darkmagenta;
        }
    }

    &.ecom_refund {
        &.pending {
            background: $status_brightorange;
        }

        &.partial {
            background: $status_darkgreen;
        }

        &.refunded {
            background: $status_darkpurple;
        }

        &.failed {
            background: $status_darkmagenta;
        }
    }

    &.payments_installment {
        &.active {
            background: $status_lightblue;
        }

        &.paid {
            background: $status_darkgrayblue;
        }

        &.unpaid {
            background: $status_darkpurple;
        }
    }

    &.payments_installment_plan {
        &.active {
            background: $status_lightblue;
        }

        &.past_due {
            background: $status_darkmagenta;
        }

        &.paid {
            background: $status_darkgrayblue;
        }

        &.unpaid {
            background: $status_darkpurple;
        }

        &.canceled {
            background: $status_lightgrayblue;
        }
    }

    &.payments_manual_payment {
        &.pending {
            background: $status_lightgrayblue;
        }

        &.invoiced {
            background: $status_lightblue;
        }

        &.paid {
            background: $status_darkpurple;
        }

        &.failed {
            background: $status_darkmagenta;
        }
    }

    &.external_content_code_use {
        &.created {
            background: $status_lightgrayblue;
        }

        &.delivered {
            background: $status_lightblue;
        }

        &.used {
            background: $status_darkgreen;
        }

        &.expired {
            background: $status_darkmagenta;
        }
    }

    &.external_content_code_upload {
        &.uploaded {
            background: $status_lightgrayblue;
        }

        &.processing {
            background: $status_lightblue;
        }

        &.processed {
            background: $status_darkgreen;
        }

        &.processed_with_errors {
            background: $status_brightorange;
        }

        &.failed {
            background: $status_darkmagenta;
        }
    }

    &.learning_delivery_employee {
        &.onboarding {
            background: $status_lightblue;
        }

        &.active {
            background: $status_brightgreen;
        }

        &.paused {
            background: $status_lightgrayblue;
        }

        &.deactivated {
            background: $status_lightred;
        }
    }

    &.learning_delivery_role {
        &.lsi {
            background: $status_lightorange;
        }

        &.lsa {
            background: $status_darkgrayblue;
        }

        &.g {
            background: $status_darkgreen;
        }
    }

    &.learning_delivery_risk_assessment {

        // risk types
        &.not_activated {
            background: $status_lightgrayblue;
        }

        &.no_recent_activity {
            background: $status_lightblue;
        }

        &.missing_assignments {
            background: $status_darkmagenta;
        }

        &.late_assignments {
            background: $status_darkorange;
        }

        // risk levels
        &.on_track {
            background-color: $status_darkgreen;
        }

        &.low_risk {
            background-color: $status_darkorange;
        }

        &.high_risk {
            background-color: $status_lightred;
        }
    }

    &.learning_delivery_activity {

        // activity types
        &.grade {
            background: $status_darkgreen;
        }

        &.evaluate {
            background: $status_darkorange;
        }

        &.task {
            background: $status_lightblue;
        }
    }

    &.learning_delivery_task_group {
        &.created {
            background: $status_lightgrayblue;
        }

        &.sent {
            background: $status_lightblue;
        }

        &.completed {
            background: $status_darkgreen;
        }
    }

    &.learning_delivery_task {
        &.created {
            background: $status_lightgrayblue;
        }

        &.assigned {
            background: $status_lightblue;
        }

        &.viewed {
            background: $status_darkgrayblue;
        }

        &.completed {
            background: $status_darkgreen;
        }

        &.skipped {
            background: $status_lightorange;
        }

        &.expired {
            background: $status_lightred;
        }
    }

    &.lms_submission_review {
        &.submitted {
            background: $status_lightgrayblue;
        }

        &.pdf_generated {
            background: $status_darkgrayblue;
        }

        &.auto_graded {
            background: $status_lightblue;
        }

        &.manual_review_needed {
            background: $status_darkorange;
        }

        &.graded {
            background: $status_darkpurple;
        }

        &.published {
            background: $status_darkgreen;
        }

        &.complete {
            background: $status_darkgreen;
        }

        &.incomplete {
            background: $status_lightred;
        }

        &.current {
            background: $status_darkgrayblue;
        }
    }

    &.lms_submission_exercise_review {
        &.submitted {
            background: $status_lightgrayblue;
        }

        &.auto_graded {
            background: $status_lightgreen;
        }

        &.manual_review_needed {
            background: $status_darkorange;
        }

        &.graded {
            background: $status_brightgreen;
        }

        &.low {
            background: $status_darkorange;
        }

        &.medium {
            background: $status_lightblue;
        }

        &.high {
            background: $status_darkgreen;
        }

        &.incomplete {
            background: $status_lightred;
        }

        &.needs_work {
            background: $status_lightorange;
        }

        &.good {
            background: $status_brightgreen;
        }

        &.exemplary {
            background: $status_darkgreen;
        }
    }

    &.lms_submission {
        &.unsubmitted {
            background: $status_lightgrayblue;
        }

        &.submitted {
            background: $status_lightblue;
        }

        &.pending_review {
            background: $status_darkorange;
        }

        &.graded {
            background: $status_brightgreen;
        }
    }

    &.submission {
        &.incomplete {
            background: $status_lightred;
        }

        &.complete {
            background: $status_darkgreen;
        }
    }

    &.assignment {
        &.unlocked {
            background: $status_darkgrayblue;
        }

        &.locked {
            background: $status_darkmagenta;
        }
    }

    &.lms_submission_review_training_evaluation {
        &.created {
            background: $status_lightgrayblue;
        }

        &.evaluated {
            background: $status_darkgreen;
        }

        &.auto_evaluated {
            background: $status_lightblue;
        }

        &.correct {
            background: $status_brightgreen;
        }

        &.incorrect {
            background: $status_lightred;
        }

        &.manual_review {
            background: $status_lightblue;
        }

        &.skipped {
            background: $status_lightyellow;
        }
    }


}

// Partner#dns_status
.col-dns_status,
.row-dns_status {
    .status_tag {
        &.disabled {
            background: $status_lightgrayblue;
        }

        &.pending {
            background: $status_lightorange;
        }

        &.connected {
            background: $status_darkgreen;
        }
    }
}

.row-section_style {
    .status_tag {
        &.dedicated {
            background: $status_brightorange;
        }

        &.commingled {
            background: $status_lightblue;
        }
    }
}

.row-limited_transfers_used {
    .status_tag.limited-transfer--exceeded {
        background: $status_lightred;
        font-weight: bold;
        padding: 0.8em 0.5em;
    }
}

.col-purpose,
.row-purpose {
    .status_tag {
        &.standard {
            background: $status_ziplines_blue;
        }

        &.test {
            background: $status_lightgrayblue;
        }
    }
}

.lms_submission,
.lms_submission_review,
#index_table_lms_submission_reviews {

    .row-grade,
    .col-grade,
    .col-state_hr_submission_grade {
        .status_tag {
            &.incomplete {
                background: $status_lightred;
            }

            &.complete {
                background: $status_darkgreen;
            }
        }
    }
}


.scope-group-training_status,
.scope-group-training_result {
    li {
        a {
            background-image: linear-gradient(180deg, #6f6f6f6a, #42424262);
            border-color: #878686;
            box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1), 0 1px 0 0 rgba(255, 255, 255, 0.2) inset;
            text-shadow: none;
            color: white;
            font-weight: 500;


            span {
                color: white !important;
                text-shadow: none;
                font-weight: 500;
            }
        }

        a:hover {
            filter: brightness(1.1);
            background-image: none !important;
            box-shadow: 0 3px 3px 0 rgba(0, 0, 0, 0.3) inset;
        }

        &.selected a:hover {
            filter: brightness(1.5);
            background-color: #333 !important;
            background-image: none !important;
            box-shadow: 0 3px 3px 0 rgba(0, 0, 0, 0.3) inset;
        }
    }

    .scope.created a {
        background: $status_lightgrayblue;
    }

    .scope.evaluated a {
        background: $status_darkgreen;
    }

    .scope.auto_evaluated a {
        background: $status_lightblue;
    }

    .scope.correct a {
        background: $status_darkgreen;
    }

    .scope.incorrect a {
        background: $status_darkmagenta;
    }

    .scope.manual_review a {
        background: $status_lightblue;
    }

    .scope.skipped a {
        background: $status_lightyellow;
    }
}