/*
 * This is a manifest file that'll be compiled into application.css, which will include all the files
 * listed below.
 *
 * Any CSS (and SCSS, if configured) file within this directory, lib/assets/stylesheets, or any plugin's
 * vendor/assets/stylesheets directory can be referenced here using a relative path.
 *
 * You're free to add application-wide styles to this file and they'll appear at the bottom of the
 * compiled file so the styles you add here take precedence over styles defined in any other CSS
 * files in this directory. Styles in this file should be added after the last require_* statement.
 * It is generally better to create a new file per style scope.
 *
 *= require_tree ./learning_delivery
 *= require ./application.tailwind
 *= require_self
 */

@import "font-awesome";

$bg-primary: #fafafa;
$bg-scrollbar: #dfdfdf;
$scrollbar-thumb: #969696;
$scrollbar-thumb-hover: #555;

body {
  font-family: "IBM Plex Sans", serif;
  overflow-x: hidden;
  padding: 0;
  margin: 0;
  background: $bg-primary;
}

.employee-profile-activity-scroll {
  max-height: 75vh;

  .btn-div a {
    white-space: nowrap;
  }

  overflow-y: auto;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: $bg-scrollbar;
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background: $scrollbar-thumb;
    border-radius: 10px;

    &:hover {
      background: $scrollbar-thumb-hover;
    }
  }
}

#accordion-collapse {
  [data-toggle-open-value="true"] {
    h2 {
      svg {
        transform: rotate(0deg);
      }
    }
  }

  [data-toggle-open-value="false"] {
    h2 {
      svg {
        transform: rotate(180deg);
      }
    }
  }
}

@media screen and (max-width: 1170px) {
  .btn-div a {
    padding: 10px 6px;
    font-size: 10px;
  }

  h4.live-div {
    font-size: 28px;
  }
}

.min-h-60 {
  min-height: 60px;
  ;
}

.calc-width-sidebar {
  width: calc(100% - 28px);
}

.calc.w-notification {
  width: calc(100% - 12px);
}

.calc.w-notification .content {
  width: calc(100% - 38px);
}

.w-90 {
  max-width: 417px;
  width: 90%;
}

.submitted-blue {
  background-color: #1D4ED8;
}

.pdf-generated-yellow {
  background-color: #F8C715;
}

.autograded-purple {
  background-color: #6B21A8;
}

.manual-review-needed-orange {
  background-color: #FF9735;
}

.graded-green {
  background-color: #6EB77C;
}

.published-green {
  background-color: #166534;
}

.main-content {
  max-height: calc(100vh - 60px);
  max-width: calc(100% - 56px);
  overflow-y: auto;
  margin-left: 56px;
  margin-top: 60px;
  position: relative;
  transition: margin-left 300ms ease;
}
body.admin-user-impersonation .main-content {
  margin-top: 90px;
  max-height: calc(100vh - 90px);
}

.shadow-custom {
  box-shadow: 0 4px 15px #262B4329;
}

tr.selected {
  background-color: #f3f4f6 !important;
}



.wrapper {
	background: #ccc;
	height: 250px;
	width: 280px;
	position: relative;
}

.wrapper .donut {
	position: absolute;
	left: 120px;
	top: -25px;
}

.donut-segment {
  animation-name: fill-in;
  animation-fill-mode: forwards;
  animation-duration: 2000ms;
}

@keyframes fill-in {
  from {
    stroke-dashoffset: 0;
  }
  to {
/*     opacity: 1; */
  }
}

.ct-donut-center {
  font-size: 10px;
  font-weight: bold;
}
