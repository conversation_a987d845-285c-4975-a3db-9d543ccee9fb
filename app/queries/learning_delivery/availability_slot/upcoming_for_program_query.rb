# frozen_string_literal: true

module LearningDelivery
  class AvailabilitySlot
    class UpcomingForProgramQuery < ApplicationQuery

      END_THRESHOLD_DURATION = 6.months

      attr_reader :programs, :scope

      # @param programs [Array<LearningDelivery::Program>] of programs to filter by
      # @param scope [ActiveRecord::Relation] of LearningDelivery::AvailabilitySlot
      # @return [ActiveRecord::Relation] of LearningDelivery::AvailabilitySlot
      def initialize(programs:, scope: LearningDelivery::AvailabilitySlot)
        @programs = Array.wrap(programs)
        @scope = scope
      end

      def relation
        scope
          .includes(:cohorts).references(:cohorts)
          .merge(cohort_scope)
          .where(date: start_threshold..end_threshold)
          .order(:date, :day_of_the_week, :start_time)
      end


      private

      def cohort_scope
        Cohort.where(program: programs).where(lms_opens_on: start_threshold..end_threshold)
      end

      def start_threshold
        Time.zone.today.beginning_of_month
      end

      def end_threshold
        END_THRESHOLD_DURATION.from_now.to_date
      end


    end
  end
end
