# frozen_string_literal: true

class Section
  class SearchQuery < ApplicationQuery

    attr_reader :scope, :string

    def initialize(string:, scope: nil)
      @scope = scope || Section.all
      @string = string
    end

    def relation
      words = string.gsub(/[^\s\w]/, '').split(/\s/).compact_blank
      words -= ['section', 'Section']

      scope.joins(:cohort, :program).select(
        "sections.*",
        words.map do |word| # rubocop:disable Style/StringConcatenation
          <<~SQL.squish
          (CASE WHEN cohorts.name ilike '%#{word}%' THEN 1 ELSE 0 END +
           CASE WHEN programs.abbreviation ilike '%#{word}%' THEN 1 ELSE 0 END +
           CASE WHEN sections.suffix ilike '%#{word}%' THEN 1 ELSE 0 END)
          SQL
        end.join(' + ') + ' as total_matches',
      ).where(
        words.map do |word|
          "(cohorts.name ilike :term_#{word} OR " \
            "programs.abbreviation ilike :term_#{word} OR sections.suffix ilike :term_#{word})"
        end.join(' AND '),
        words.to_h { |word| [:"term_#{word}", "%#{word}%"] },
      ).order(total_matches: :desc)
    end

  end
end
