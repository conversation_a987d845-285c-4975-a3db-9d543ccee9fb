# frozen_string_literal: true

module Lms
  class Submission
    class ForGraderQuery < ApplicationQuery
      attr_reader :params, :current_employee

      SORT_ORDER = 'lms_assignments.due_at, lms_assignments.id, lms_submissions.submitted_at, lms_submissions.id'

      def initialize(params:, current_employee: nil)
        @params = params
        @current_employee = current_employee
      end

      def base_filtered_relation
        @base_filtered_relation ||= begin
          query = base_query
          query = apply_grader_filter(query)
          apply_param_filters(query)
        end
      end

      def relation
        @relation ||= apply_quick_filters(base_filtered_relation)
      end

      def submission_ids
        @submission_ids ||= relation
          .group(SORT_ORDER)
          .pluck('lms_submissions.id')
      end

      private

      def base_query
        Lms::Submission
          .includes(:reviews, :learner, :comments, :module_template, :assignment, section: { cohort: :program })
          .joins(:reviews)
          .where('lms_submission_reviews.attempt = lms_submissions.attempt')
          .order(SORT_ORDER)
          .where(lms_submission_reviews: { training: false })
      end

      def apply_grader_filter(query)
        return query unless current_employee

        section_ids = current_employee.grader_sections.pluck(:id)
        query.joins(:enrollment)
          .where(enrollments: { section_id: section_ids })
      end

      def apply_param_filters(query)
        # Apply section filter
        query = query.joins(:enrollment).where(enrollments: { section_id: params[:section_id] }) if params[:section_id].present?

        # Apply assignment filter
        if params[:assignment_template_id].present?
          query = query.joins(:assignment).where(lms_assignments: { assignment_template_id: params[:assignment_template_id] })
        end

        # Apply learner filter
        query = query.joins(:enrollment).where(enrollments: { learner_id: params[:learner_id] }) if params[:learner_id].present?

        query
      end

      def apply_quick_filters(query)
        case params[:filter]
        when 'all'
          query
        when 'new_comments'
          query.where(id: submission_ids_with_unread_comments)
        when 'processing'
          query.where(lms_submission_reviews: { state: Lms::Submission::Review::AUTOGRADING_IN_PROGRESS_STATES })
        else
          query.where(lms_submission_reviews: { state: Lms::Submission::Review.states[:manual_review_needed] })
        end
      end

      def submission_ids_with_unread_comments
        @submission_ids_with_unread_comments ||= Lms::Submission::Comment
          .unread_from_learner
          .pluck(:submission_id)
          .uniq
      end
    end
  end
end
