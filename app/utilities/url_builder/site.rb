# frozen_string_literal: true

class UrlBuilder
  class Site < UrlBuilder

    attr_reader :partner_program, :bypass_proxy

    delegate :partner, :program, to: :partner_program
    delegate :proxy_host, to: :partner
    delegate :use_proxy_host?, to: :site_host_router_params_builder

    def initialize(partner_program:, bypass_proxy: false)
      @partner_program = partner_program
      @bypass_proxy = bypass_proxy
    end

    def call!(template:, **)
      params = partner_router_params.merge(
        program_slug: partner_program.program.slug,
        **,
      ).compact

      if use_proxy_host?
        params = params.merge(host: proxy_host) # ensure `host` is not overridden by a keyword argument

        proxy_routing(template:, params:)
      else
        params = {
          partner_identifier: partner.slug,
        }.merge(params)

        subdomain_routing(template:, params:)
      end
    end


    private

    def site_host_router_params_builder
      @site_host_router_params_builder ||= UrlBuilder::SiteHostRouterParamsBuilder.new(partner:, bypass_proxy:)
    end

    def partner_router_params
      site_host_router_params_builder.call!
    end

    def proxy_routing(template:, params:)
      case template.to_sym
      when :landing
        router.proxy_host_site_landing_url(params)
      when :syllabus
        router.proxy_host_site_syllabus_url(params)
      when :squeeze
        router.proxy_host_site_squeeze_url(params)
      when :registration
        router.proxy_host_new_site_registrations_url(params)
      when :get_to_know_you
        router.proxy_host_site_get_to_know_you_url(params.except(:program_slug))
      when :reimbursement
        router.proxy_host_site_reimbursement_url(params)
      when :support
        router.proxy_host_site_support_url(params)
      when :curriculum
        router.proxy_host_site_landing_url(**params, anchor: 'Curriculum')
      when :testimonials
        router.proxy_host_site_landing_url(**params, anchor: 'testimonials')
      when :create_registration
        router.proxy_host_create_site_registrations_url(params)
      when :create_syllabus_request
        router.proxy_host_create_site_syllabus_requests_url(params)
      when :payment
        router.new_site_payments_url(params.merge(program_slug: nil))
      when :receipt
        router.site_payment_confirmation_url(params.merge(program_slug: nil))
      when :order_confirm
        router.site_order_confirm_url(params.merge(program_slug: nil))
      when :blog
        "https://#{proxy_host}/blog"
      when :blog_category
        "https://#{proxy_host}/blog/category/#{program.slug}"
      else
        raise ArgumentError.new("Unknown template: #{template}")
      end
    end

    def subdomain_routing(template:, params:)
      case template.to_sym
      when :landing
        router.site_landing_url(params)
      when :syllabus
        router.site_syllabus_url(params)
      when :squeeze
        router.site_squeeze_url(params)
      when :registration
        router.new_site_registrations_url(params)
      when :get_to_know_you
        router.site_get_to_know_you_url(params.except(:program_slug))
      when :reimbursement
        router.site_reimbursement_url(params)
      when :support
        router.site_support_url(params)
      when :curriculum
        router.site_landing_url(**params, anchor: 'Curriculum')
      when :testimonials
        router.site_landing_url(**params, anchor: 'testimonials')
      when :create_registration
        router.create_site_registrations_url(params)
      when :create_syllabus_request
        router.create_site_syllabus_requests_url(params)
      when :payment
        router.new_site_payments_url(params.merge(program_slug: nil, partner_identifier: nil))
      when :receipt
        router.site_payment_confirmation_url(params.merge(program_slug: nil, partner_identifier: nil))
      when :order_confirm
        router.site_order_confirm_url(params.merge(program_slug: nil, partner_identifier: nil))
      when :blog_category
        "https://ziplines.com/#{partner.slug}/blog/category/#{program.slug}"
      else
        raise ArgumentError.new("Unknown template: #{template}")
      end
    end
  end
end
