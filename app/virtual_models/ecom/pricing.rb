# frozen_string_literal: true

module Ecom
  # list_cents - The price of the product (shown as striked out)
  # sales_cents - The price of the product (upfront_price or standard_price if paying with a payment plan)
  # promos_discount_cents - The amount remove from promo discount codes. Does not include list_discount.
  # actual_cents - The price a customer paid or will pay. This is the price after discount codes are applied.
  # total_cents - Same as actual_cents
  # recurring_payment_cents - The amount per installment / payment. (nil if payment_method is not a payment_plan, ie upfront)
  # initial_payment_cents - The amount due at checkout.  For upfront this is total_cents.  For payment_plans this is recurring_cents.
  # final_payment_cents - The amount due for the final payment (only applies to installments). Accounts for any rounding weirdness on installments.
  #   Ex.  1000/3 = 333.33 * 3 = 999.99.  So the recurring would be $333.33 and the final would be $333.34.
  # recurring_payment_count - The number of payments expected.  (1 if payment_method is not a payment_plan, ie upfront)
  # list_discount_cents - The amount saved off of the list price from the upfront_discount and standard_discount. Does not include promos_discounts.
  # total_discount_cents - The total amount saved from list_cents including the list_discount_cents and promos_discount_cents.

  class Pricing < ApplicationVirtualModel
    include StoredInCents

    MONEY_ATTRIBUTES = [
      :actual,
      :list,
      :list_discount,
      :total_discount,
      :sale,
      :promos_discount,
      :total,
      :recurring_payment,
      :initial_payment,
      :final_payment,
    ].freeze
    CENTS_ATTRIBUTES = MONEY_ATTRIBUTES.map { |attr| :"#{attr}_cents" }
    AMOUNT_ATTRIBUTES = MONEY_ATTRIBUTES.map { |attr| :"#{attr}_amount" }

    MONEY_ATTRIBUTES.each do |attr|
      attribute "#{attr}_cents", :integer

      stored_in_cents :"#{attr}_cents", virtual_attribute: true
    end

    attribute :currency_code, :string
    attribute :recurring_payment_count, :integer
    attribute :promos_discount_codes

    def savings_percent
      (list_amount - actual_amount).percentage_of(list_amount)
    end

    def sale_savings_percent
      (list_amount - sale_amount).percentage_of(list_amount)
    end
  end
end
