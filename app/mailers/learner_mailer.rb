# frozen_string_literal: true

class LearnerMailer < ApplicationMailer
  default bcc: %w[<EMAIL>],
    reply_to: -> { email_address_with_name(t('.shared.reply_to_email'), t('.shared.reply_to_name')) }

  def lms_open_event
    set_learner_and_enrollment_data
    @presenter = LmsOpenEventPresenter.new(learner: @learner, enrollment: @enrollment)

    mail(
      to: @learner.primary_email.address,
      reply_to: 'Learning Management <<EMAIL>>',
      subject: "Log in now: Your #{@enrollment.program.name} experience has arrived",
    ) do |format|
      format.html { render layout: 'learner_mailer' }
      format.text
    end
  end

  def password_reset
    @email = params[:email]
    @reset_token = params[:reset_token]

    mail(
      to: @email,
      reply_to: 'Learning Management <<EMAIL>>',
      subject: "Reset your LMS password now",
    ) do |format|
      format.html { render layout: 'learner_mailer' }
      format.text
    end
  end

  def unenroll_confirmation
    set_learner_and_enrollment_data

    mail(
      to: @learner.primary_email.address,
      subject: "#{@enrollment.program.name}: Confirmation of enrollment change",
    )
  end

  def drop_confirmation
    set_learner_and_enrollment_data

    mail(
      to: @learner.primary_email.address,
      subject: "#{@enrollment.program.name}: Confirmation of drop request",
    )
  end

  def withdraw_confirmation
    set_learner_and_enrollment_data

    mail(
      to: @learner.primary_email.address,
      subject: "#{@enrollment.program.name}: Confirmation of withdrawal",
    )
  end

  def involuntary_withdrawal_confirmation
    set_learner_and_enrollment_data

    mail(
      to: @learner.primary_email.address,
      subject: "#{@enrollment.program.name}: LMS Access Suspension",
    )
  end

  def transfer_confirmation
    set_learner_and_enrollment_data
    @presenter = TransferConfirmationPresenter.new(learner: @learner, enrollment: @enrollment)

    mail(
      to: @learner.primary_email.address,
      subject: "#{@enrollment.program.name}: Confirmation of transfer",
    )
  end

  def manual_registration_confirmation
    set_learner_and_registration_data
    # Alias @presenter as @shared_presenter to make it compatible with the learner_mailer layout
    # which expects @shared_presenter for partner branding (logo, cobranding text)
    @shared_presenter = @presenter

    mail(
      to: @learner.primary_email.address,
      subject: "Action Required for your course enrollment",
      from: 'Enrollment Services <<EMAIL>>',
      reply_to: 'Enrollment Services <<EMAIL>>',
    ) do |format|
      format.html { render layout: 'learner_mailer' }
      format.text
    end
  end

  def manual_registration_reminder
    set_learner_and_registration_data
    @shared_presenter = @presenter # For layout compatibility

    subject_text = "Reminder: Action Required for #{@registration.partner_program.program_name} " \
                   "#{@registration.partner_program.partner.theme.course_nomenclature} " \
                   "at #{@registration.partner_program.partner.formal_name}"

    mail(
      to: @learner.primary_email.address,
      subject: subject_text,
      from: 'Enrollment Services <<EMAIL>>',
      reply_to: 'Enrollment Services <<EMAIL>>',
    ) do |format|
      format.html { render layout: 'learner_mailer' }
      format.text
    end
  end

  def external_content_delivery
    set_learner_and_enrollment_data
    @material_set = params[:material_set]
    @body_text = params[:body_text]
    @subject = "Thank you for requesting your #{@material_set.name}"

    mail(
      to: @learner.primary_email.address,
      subject: @subject,
      reply_to: 'Learning Management <<EMAIL>>',
    )
  end

  def general_message
    to = params[:to]
    @subject = params[:subject]
    @body = params[:body]

    mail(
      to:,
      subject: @subject,
      reply_to: 'Learning Management <<EMAIL>>',
    )
  end

  private

  def set_learner_and_enrollment_data
    @learner = params[:learner]
    @enrollment = params[:enrollment]
    @shared_presenter = LearnerMailer::SharedPresenter.new(learner: @learner, enrollment: @enrollment)
  end

  def set_learner_and_registration_data
    @registration = params[:registration]
    @learner = @registration.learner
    @presenter = LearnerMailer::ManualRegistrationPresenter.new(registration: @registration)
  end
end
