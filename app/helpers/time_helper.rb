# frozen_string_literal: true

module TimeHelper
  include ActionView::Helpers::DateHelper

  # Displays time relative to now with custom thresholds
  #
  # @param datetime [DateTime, Time] The datetime to format
  # @param relative_past [ActiveSupport::Duration] Threshold for past relative display (default: 1 week ago)
  # @param relative_future [ActiveSupport::Duration] Threshold for future relative display (default: 1 week from now)
  # @param strftime [String] Format string for absolute dates (default: 'Mon, Dec 04 2025 12:23PM')
  # @return [String] Formatted time string
  #
  # Examples:
  #   relative_formatted_date_time(5.minutes.ago)         # => "5 minutes ago"
  #   relative_formatted_date_time(3.days.from_now)       # => "in 3 days"
  #   relative_formatted_date_time(2.weeks.ago)           # => "Mon, Dec 04 2025 12:23PM"
  # strftime format options:
  # %a - The abbreviated weekday name (“Sun”)
  # %A - The full weekday name (“Sunday”)
  # %b - The abbreviated month name (“Jan”)
  # %B - The full month name (“January”)
  # %c - The preferred local date and time representation
  # %d - Day of the month (01..31)
  # %H - Hour of the day, 24-hour clock (00..23)
  # %I - Hour of the day, 12-hour clock (01..12)
  # %j - Day of the year (001..366)
  # %m - Month of the year (01..12)
  # %M - Minute of the hour (00..59)
  # %p - Meridian indicator (“AM” or “PM”)
  # %S - Second of the minute (00..60)
  # %U - Week number of the current year, starting with the first Sunday as the first day of the first week (00..53)
  # %W - Week number of the current year, starting with the first Monday as the first day of the first week (00..53)
  # %w - Day of the week (Sunday is 0, 0..6)
  # %x - Preferred representation for the date alone, no time
  # %X - Preferred representation for the time alone, no date
  # %y - Year without a century (00..99)
  # %Y - Year with century
  # %Z - Time zone name %% - Literal “%’’ character t = Time.now t.strftime(“Printed on %m/%d/%Y”)
  #      #=> “Printed on 04/09/2003” t.strftime(“at %I:%M%p”) #=> “at 08:56AM”
  #
  def relative_formatted_date_time(
    datetime,
    relative_past: 1.week.ago,
    relative_future: 1.week.from_now,
    strftime: '%a, %b %d %Y %l:%M%p'
  )
    return '' if datetime.blank?

    datetime = datetime.to_time if datetime.respond_to?(:to_time)
    now = Time.current

    # If datetime is within the relative range, show relative time
    if datetime.between?(relative_past, relative_future)
      time_diff = (now - datetime).abs

      if time_diff < 30.seconds
        'now'
      elsif datetime < now
        "#{time_ago_in_words(datetime)} ago"
      else
        "in #{time_ago_in_words(datetime, include_seconds: false)}"
      end
    else
      datetime.strftime(strftime)
    end
  end
end
