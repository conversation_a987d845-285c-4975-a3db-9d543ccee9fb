# frozen_string_literal: true

class ZendeskClient < HttpClient
  Error = Class.new(HttpClient::Error)

  def headers
    {
      'Accept' => 'application/json',
      'Content-Type' => 'application/json',
      'Authorization' => "Basic #{auth_string}",
    }
  end

  def paginated_get(path, params: {}, headers: {}, results: [], &block)
    params = { per_page: 100 }.merge(params) unless params.key?(:per_page)

    next_page_url = nil

    result = get(path, params:, headers:) do |response|
      response_json = response.json
      next_page_url = response_json['next_page']
      block&.call(response)
    end

    page_results = result['results'] || result['articles'] || []
    results.push(*page_results)

    return results if next_page_url.blank?

    paginated_get(next_page_url, params: {}, headers: {}, results:, &block)
  end

  private

  def origin
    Rails.application.config.remote_resources[:zendesk][:api_origin]
  end

  def base_path
    "/api/v2"
  end

  def auth_string
    Base64.strict_encode64("#{auth_email}/token:#{api_key}")
  end

  def auth_email
    ENV.fetch("ZENDESK_AUTH_EMAIL", nil)
  end

  def api_key
    Rails.application.credentials.fetch(:ZENDESK_API_KEY)
  end
end
