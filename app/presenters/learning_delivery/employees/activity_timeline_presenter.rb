# frozen_string_literal: true

module LearningDelivery
  module Employees
    class ActivityTimelinePresenter < ApplicationPresenter

      attr_reader :employee

      def initialize(employee)
        @employee = employee
      end

      def activities_dates
        employee.activities
          .order(created_at: :desc)
          .pluck(:created_at)
          .map { |d| d.to_date.strftime('%a %d %b %Y') }
          .uniq
      end

      def activity_type_title_text_and_image(activity)
        return if activity.nil?

        if activity.grade?
          {
            text: "Graded",
            image_path: "learning_delivery/icons/activity-tab-assignment-star.svg",
          }
        else
          {
            text: "Completed",
            image_path: "learning_delivery/icons/activity-tab-assignment-star.svg",
          }
        end
      end

      def activity_full_title(activity)
        "#{activity_type_title_text_and_image(activity)[:text]} #{activity.title}"
      end

      def activities_at(date)
        return if date.nil?

        employee.activities.where(created_at: date.to_date.all_day)
      end

      def current?(date)
        return true if date.to_date == Time.zone.today

        false
      end

      def date_timing(date)
        new_date = DateTime.parse(date.to_s)
        return new_date.strftime("%-I:%M %p") if current?(date)


        "#{new_date.strftime('%A')} #{new_date.strftime('%I:%M %p')}"
      end

      def past_date(date)
        return if date.nil?

        Date.parse(date).strftime("%d %b %Y")
      end

      def boundry_class_for_date_record(date)
        " border-t border-border-gray-200" if date != activities_dates.first
      end

      def to_partial_path
        'learning_delivery/employees/activity_timeline'
      end
    end
  end
end
