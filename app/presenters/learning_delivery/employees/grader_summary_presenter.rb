# frozen_string_literal: true

module LearningDelivery
  module Employees
    class GraderSummaryPresenter < ApplicationPresenter
      attr_reader :employee

      def initialize(employee)
        @employee = employee
      end

      def submissions
        @submissions ||= employee.lms_submissions.active
      end

      def total_submissions
        employee.lms_submissions.count
      end

      def submitted_assignments_count
        @submitted_assignments_count ||= submissions.size
      end

      def graded_assignments_count
        @graded_assignments_count ||= submissions.graded.size
      end

      def pending_assignments_count
        submitted_assignments_count - graded_assignments_count
      end

      def submitted_assignments_percentage
        submitted_assignments_count.percentage_of(total_submissions, round: 0)
      end

      def graded_assignments_percentage
        graded_assignments_count.percentage_of(submitted_assignments_count, round: 0)
      end

      def pending_assignments_percentage
        pending_assignments_count.percentage_of(submitted_assignments_count, round: 0)
      end

      def to_partial_path
        'learning_delivery/employees/grader_summary'
      end
    end
  end
end
