# frozen_string_literal: true

module LearningDelivery
  module Sections
    class SectionItemPresenter < ApplicationPresenter

      attr_reader :section

      def initialize(section:)
        @section = section
      end

      def to_partial_path
        'learning_delivery/sections/section_item'
      end

      delegate :id, :uid, :cohort, :enrollments, to: :section
      delegate :name, to: :section, prefix: true
      delegate :count, to: :enrollments, prefix: true

      def program_name
        section.program&.name
      end

      def cohort_name
        cohort&.active_admin_display_name
      end

      def cohort_status
        cohort&.status&.titleize
      end

      def cohort_status_color_classes
        case cohort&.status
        when 'created', 'ended'
          'bg-gray-100 text-gray-800'
        when 'opened'
          'bg-blue-100 text-blue-800'
        when 'started'
          'bg-green-100 text-green-800'
        when 'extension_period_ended'
          'bg-orange-100 text-orange-800'
        when 'inactive'
          'bg-gray-100 text-gray-600'
        when 'closed'
          'bg-red-100 text-red-800'
        else
          'bg-gray-100 text-gray-700'
        end
      end

      def live_day_of_the_week
        section.live_day_of_the_week_name
      end

      def live_start_time
        Section::LiveDayTimeFormatter.new(section:).to_fs(format: :short)
      end

      def nps_score
        return if NpsScore::AggregateScoreCommand.call!(section.live_session_reviews.map(&:score)).blank?

        NpsScore::AggregateScoreCommand.call!(section.live_session_reviews.map(&:score)).round
      end

      def row_classes
        'section-row hover:bg-blue-50 transition-colors duration-200'
      end

      def status_badge_classes
        base_classes = 'py-1 px-3 rounded-full inline-block text-xs font-medium'
        "#{base_classes} #{cohort_status_color_classes}"
      end
    end
  end
end
