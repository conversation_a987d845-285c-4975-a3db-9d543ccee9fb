# frozen_string_literal: true

module LearningDelivery
  module Sections
    class IndexPresenter < ApplicationPresenter
      include Pagy::Backend

      attr_reader :employee, :params, :pagy_info

      def initialize(employee:, params: {})
        @employee = employee
        @params = params
      end

      def section_filter_options
        [
          ['Past Sections', 'past'],
          ['Active Sections', 'active'],
          ['Upcoming Sections', 'upcoming'],
        ]
      end

      def section_item_presenters
        return [] if sections.nil?

        sections.map { |section| SectionItemPresenter.new(section:) }
      end

      def pagination_presenter
        PaginationPresenter.new(pagy_info:)
      end

      private


      def sections
        @sections ||= begin
          per_page = params[:per_page].to_i.nonzero? || 5
          page = params[:page].to_i.nonzero? || 1
          @pagy_info, paginated_sections = pagy(filtered_sections_query, limit: per_page, page:)
          paginated_sections
        end
      end

      def filtered_sections_query
        @filtered_sections_query ||= apply_filters(base_sections_query)
      end

      def apply_filters(query)
        filtered = query

        case params[:section_filter]
        when 'past'
          filtered = filtered.joins(:cohort).where(cohorts: { ends_on: ...Date.current })
        when 'active'
          filtered = filtered.joins(:cohort).where(cohorts: { status: Cohort::ACTIVE_STATUSES })
        when 'upcoming'
          filtered = filtered.joins(:cohort).where('cohorts.starts_on > ?', Date.current)
        end

        filtered
      end

      def employee_section_ids
        @employee_section_ids ||= employee&.sections&.pluck(:id)
      end

      def base_sections_query
        Section.with_ids(employee_section_ids).includes(:program, :cohort, :enrollments)
      end
    end
  end
end
