# frozen_string_literal: true

module LearningDelivery
  module Tasks
    class IndexPresenter
      class LineItemPresenter < ApplicationPresenter
        include TimeHelper

        attr_reader :task, :params

        delegate :uid, :title, to: :task

        def initialize(task:, params:)
          @task = task
          @params = params
        end

        def to_partial_path
          'learning_delivery/tasks/index/line_item'
        end

        def task_type
          task.type&.demodulize || 'Task'
        end

        def description
          return '' if task.description.blank?

          # Truncate description for table display
          truncated = task.description.truncate(100)
          simple_format(truncated, {}, { sanitize: false })
        end

        def status_presenter
          StatusPresenter.new(task:)
        end

        def due_at
          return '' if task.due_at.blank?

          strftime = '%m/%d/%Y'
          formatted_date = relative_formatted_date_time(task.due_at, strftime:)

          if task.due_at < Time.current && task.in_progress?
            content_tag(:span, formatted_date, class: 'text-red-600 font-medium')
          else
            formatted_date
          end
        end

        def learner_name
          return '' unless task.resource.is_a?(<PERSON>rner)

          task.resource.full_name
        end

        def section_name
          return '' unless task.resource.is_a?(<PERSON><PERSON>)

          task.resource.enrollments.primary.first&.section&.name || ''
        end

        def show_path
          learning_delivery_task_path(task, params.except(:id))
        end

        def update_task_path
          learning_delivery_task_path(task.uid)
        end

        def selected?
          return false if params[:id].blank?

          params[:id].delete('-') == task.uid.delete('-')
        end

      end
    end
  end
end
