# frozen_string_literal: true

module LearningDelivery
  module Tasks
    module ResourcePresenter
      class EnrollmentPresenter < ApplicationPresenter
        include TimeHelper

        attr_reader :enrollment

        def initialize(enrollment:)
          @enrollment = enrollment
        end

        def to_partial_path
          'learning_delivery/tasks/resource/enrollment'
        end

        def learner
          @learner ||= enrollment.learner
        end

        def name
          learner.full_name
        end

        def email
          learner.primary_email_address || 'No email'
        end

        def section_name
          enrollment.section&.program_cohort_name || 'No section'
        end

        def section_link
          '#' # TODO: Update with actual section link when available
        end

        def enrollment_status
          enrollment&.status&.humanize || 'No enrollment'
        end

        def learner_link
          '#' # TODO: Update with actual learner link when available
        end

        def exist_reuested_on
          relative_formatted_date_time(enrollment.exit_requested_on)
        end

      end
    end
  end
end
