# frozen_string_literal: true

module LearningDelivery
  module Tasks
    class TaskModalPresenter < ApplicationPresenter
      include TimeHelper

      attr_reader :task_id, :params, :current_admin_user

      delegate :uid, :description, :reason, :recommendation,
        :owner, :assigned_by, :created_at, :assigned_at, :due_at,
        :completed_at, :viewed_at, :resource, :status, to: :task

      def initialize(task_id:, params: {}, current_admin_user: nil)
        @task_id = task_id
        @params = params
        @current_admin_user = current_admin_user
      end

      def task
        @task ||= LearningDelivery::Task.includes(:owner, :assigned_by, :comments).find_from_uid!(task_id)
      end

      def task_presenter
        @task_presenter ||= begin
          presenter_class = case task.type
          when 'LearningDelivery::Task::ReachOut'
            TypePresenter::ReachOutPresenter
          when 'LearningDelivery::Task::RemoveLearner'
            TypePresenter::RemoveLearnerPresenter
          when 'LearningDelivery::Task::Announcement'
            TypePresenter::AnnouncementPresenter
          when 'LearningDelivery::Task::FixMergedRemoteContact'
            TypePresenter::FixMergedRemoteContactPresenter
          else
            TypePresenter::FallbackPresenter
          end

          presenter_class.new(task:)
        end
      end

      def resource_presenter
        return nil unless task.resource

        case task.resource
        when RiskAssessment
          LearningDelivery::Tasks::ResourcePresenter::RiskAssessmentPresenter.new(risk_assessment: task.resource)
        when Section
          LearningDelivery::Tasks::ResourcePresenter::SectionPresenter.new(section: task.resource)
        when Learner
          LearningDelivery::Tasks::ResourcePresenter::LearnerPresenter.new(learner: task.resource)
        when Enrollment
          LearningDelivery::Tasks::ResourcePresenter::EnrollmentPresenter.new(enrollment: task.resource)
        else
          LearningDelivery::Tasks::ResourcePresenter::ObjectPresenter.new(resource: task.resource)
        end
      end

      def index_path
        learning_delivery_tasks_path(**params.except(:id))
      end

      def comments_presenter
        @comments_presenter ||= CommentsPresenter.new(task:, current_user: current_admin_user)
      end

      def owner_name
        task.owner&.full_name || 'Unassigned'
      end

      def assigned_by_name
        task.assigned_by&.full_name || 'System'
      end

      def formatted_created_at
        format_datetime(task.created_at)
      end

      def formatted_assigned_at
        return 'Not assigned' if task.assigned_at.blank?

        format_datetime(task.assigned_at)
      end

      def formatted_completed_at
        return nil if task.completed_at.blank?

        format_datetime(task.completed_at)
      end

      def formatted_viewed_at
        return nil if task.viewed_at.blank?

        format_datetime(task.viewed_at)
      end

      def formatted_description
        return '' if task.description.blank?

        simple_format(task.description)
      end

      def formatted_reason
        return '' if task.reason.blank?

        simple_format(task.reason)
      end

      def formatted_recommendation
        return '' if task.recommendation.blank?

        simple_format(task.recommendation)
      end

      def can_mark_complete?
        task.status != 'completed' && task.status != 'skipped'
      end

      def update_task_path
        learning_delivery_task_path(task)
      end

      private

      def format_datetime(datetime)
        relative_formatted_date_time(datetime, strftime: '%B %d, %Y at %I:%M %p')
      end
    end
  end
end
