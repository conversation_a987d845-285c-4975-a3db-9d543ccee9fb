# frozen_string_literal: true

module LearningDelivery
  module Tasks
    module TypePresenter
      class BasePresenter < ApplicationPresenter
        include TimeHelper

        attr_reader :task

        def initialize(task:)
          @task = task
        end

        def to_partial_path
          "learning_delivery/tasks/types/fallback"
        end

        def display_title
          task.display_type
        end

        def task_icon_class
          'fa-info px-2'
        end

        def task_icon_color_class
          'bg-orange-600'
        end

        def info_background_class
          'bg-orange-50'
        end

        def status_presenter
          StatusPresenter.new(task:)
        end

        def title
          simple_format(task.title)
        end

        def formatted_due_at
          return nil if task.due_at.blank?

          formatted_date = format_datetime(task.due_at)
          if task.completed_at.present?
            "#{formatted_date} (Completed on #{format_datetime(task.completed_at)})"
          elsif task.due_at < Time.current
            "#{formatted_date} (Overdue)"
          else
            formatted_date
          end
        end

        private

        def format_datetime(datetime)
          relative_formatted_date_time(datetime, strftime: '%B %d, %Y at %I:%M %p')
        end
      end
    end
  end
end
