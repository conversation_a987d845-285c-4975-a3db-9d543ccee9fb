# frozen_string_literal: true

module LearningDelivery
  class Task
    class ResolveDuplicateEnrollmentPresenter < BasePresenter
      PLACEHOLDER_TO_VALUE_METHOD_MAP = {
        '{LEARNER_NAME}' => :learner_name,
        '{LEARNER_EMAIL}' => :learner_email,
        '{LINK_TO_HUBSPOT_CONTACT}' => :link_to_hubspot_contact,
        '{COHORT_NAME}' => :cohort_name,
        '{DUPLICATE_ENROLLMENT_ID}' => :duplicate_enrollment_id,
      }.freeze

      VARIABLES = PLACEHOLDER_TO_VALUE_METHOD_MAP.keys.freeze

      attr_reader :resource, :duplicate_enrollment

      alias_method :enrollment, :resource
      alias_method :due_at, :due_date

      delegate :learner, :cohort, to: :enrollment

      def initialize(resource:, duplicate_enrollment:)
        super(resource:)
        @resource = resource
        @duplicate_enrollment = duplicate_enrollment
      end

      def placeholder_values
        PLACEHOLDER_TO_VALUE_METHOD_MAP
      end

      def task_template
        @task_template ||= LearningDelivery::TaskTemplate.find_by!(task_type: :resolve_duplicate_enrollment, sub_type: nil)
      end

      def learner_name
        @learner_name ||= learner.full_name
      end

      def learner_email
        @learner_email ||= learner.primary_email_address
      end

      def cohort_name
        @cohort_name ||= cohort.name
      end

      def duplicate_enrollment_id
        @duplicate_enrollment_id ||= duplicate_enrollment.uid
      end

      def link_to_hubspot_contact
        @link_to_hubspot_contact ||= resource.deal.remote_hubspot_contact&.remote_link
      end
    end
  end
end
