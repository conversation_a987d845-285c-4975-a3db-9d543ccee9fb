# frozen_string_literal: true

module LearningDelivery
  module Submissions
    class ShowPresenter
      class PaginationPresenter < ApplicationPresenter
        include Pagy::Backend

        attr_reader :params, :current_employee, :submission

        def initialize(submission:, params:, current_employee:)
          @submission = submission
          @params = params
          @current_employee = current_employee
        end

        def to_partial_path
          'learning_delivery/submissions/show/pagination'
        end

        def show_path_for(submission_id)
          UrlBuilder::LearningDelivery.learning_delivery_submission_path(id: submission_id, params:)
        end

        def total_submissions
          @total_submissions ||= submission_ids.count
        end

        def submission_id_for_page(page)
          index = page - 1
          submission_ids[index] if index >= 0 && index < submission_ids.count
        end

        def pagy_info
          @pagy_info ||= Pagy.new(
            count: submission_ids.count,
            page: current_position,
            limit: 1, # One submission per "page"
          )
        end

        def next_page_path
          return unless pagy_info.next

          show_path_for(submission_id_for_page(pagy_info.next))
        end

        def submission_ids
          @submission_ids ||= query.submission_ids
        end

        def current_position
          @current_position ||= [submission_ids.index(submission.id).to_i + 1, 1].max
        end

        def expected_page
          @expected_page ||= begin
            per_page = params[:per_page].presence || Pagy::DEFAULT[:limit]
            (current_position.to_f / per_page.to_i).ceil
          end
        end


        private

        def query
          @query ||= Lms::Submission::ForGraderQuery.new(
            params:,
            current_employee:,
          )
        end

      end
    end
  end
end
