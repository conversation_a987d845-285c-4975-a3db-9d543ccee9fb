# frozen_string_literal: true

module LearningDelivery
  module Submissions
    class ShowPresenter
      class HeaderPresenter < ApplicationPresenter
        attr_reader :submission, :current_employee

        delegate :url, to: :submission, prefix: true

        def initialize(submission:, current_employee: nil)
          @submission = submission
          @current_employee = current_employee
        end

        def to_partial_path
          'learning_delivery/submissions/show/header'
        end

        def student_name
          submission.learner.full_name
        end

        def assignment_name
          submission.assignment.name
        end

        def section_name
          submission.section.name
        end

        def week
          submission.cohort_week&.number
        end

        def status_presenter
          StatusPresenter.new(submission.current_review)
        end

        def show_admin_link?
          current_employee.nil?
        end

        def admin_submission_url
          UrlBuilder::Admin.admin_lms_submission_url(submission)
        end

      end
    end
  end
end
