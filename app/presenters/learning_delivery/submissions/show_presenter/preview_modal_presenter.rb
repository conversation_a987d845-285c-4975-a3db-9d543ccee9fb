# frozen_string_literal: true

module LearningDelivery
  module Submissions
    class ShowPresenter
      class PreviewModalPresenter < ApplicationPresenter

        attr_reader :review, :redirect_next_path

        delegate :comments_to_publish, :submission, to: :review, allow_nil: true


        def initialize(review:, redirect_next_path: nil)
          @review = review
          @redirect_next_path = redirect_next_path
        end

        def to_partial_path
          'learning_delivery/submissions/show/preview_modal'
        end

        def review_grade
          review&.grade_name
        end

        def comment_preview
          comments_to_publish&.gsub("\n\n", "\n<br>\n")&.html_safe
        end

        def review_grade_class
          review_grade_presenter.pill_css_class(review_grade)
        end

        def review_grade_background_color
          review_grade_presenter.background_color(review_grade)
        end

        def publish_path
          UrlBuilder::LearningDelivery.publish_learning_delivery_submission_path(
            id: submission.id,
          )
        end

        def save_status_id
          "preview_modal_#{review&.id}--button-save-status"
        end

        private

        def review_grade_presenter
          @review_grade_presenter ||= GradePresenter.new
        end

      end
    end
  end
end
