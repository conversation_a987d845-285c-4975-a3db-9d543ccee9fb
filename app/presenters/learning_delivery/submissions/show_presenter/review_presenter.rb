# frozen_string_literal: true

module LearningDelivery
  module Submissions
    class ShowPresenter
      class ReviewPresenter < ApplicationPresenter
        attr_reader :submission, :redirect_next_path, :current_employee

        delegate :id, to: :submission, prefix: true
        delegate :current_review, :publishable?, to: :submission

        def initialize(submission:, redirect_next_path: nil, current_employee: nil)
          @submission = submission
          @redirect_next_path = redirect_next_path
          @current_employee = current_employee
        end

        def to_partial_path
          'learning_delivery/submissions/show/review'
        end

        def dom_id
          "submission_#{submission.id}__review_#{submission.current_review&.id}"
        end

        def header_presenter
          HeaderPresenter.new(submission:, current_employee:)
        end

        def exercise_review_presenters
          return [] unless current_review&.exercise_reviews&.any?

          current_review.exercise_reviews
            .sort_by { |er| [er.exercise_config&.order.to_i, er.exercise_config&.id.to_i] }
            .compact
            .map do |exercise_review|
              ExerciseReviewPresenter.new(exercise_review:, submission:)
            end
        end

        def preview_modal_presenter
          PreviewModalPresenter.new(review: current_review, redirect_next_path:)
        end

        def review_update_path
          return unless current_review

          UrlBuilder::LearningDelivery.learning_delivery_submission_submission_review_path(
            submission_id: submission.id,
            id: review_id,
          )
        end

        def publish_path
          UrlBuilder::LearningDelivery.publish_learning_delivery_submission_path(
            id: submission.id,
          )
        end

        def review_grade
          current_review&.grade_name
        end

        def review_grade_presenter
          @review_grade_presenter ||= GradePresenter.new
        end

        def review_grade_class
          review_grade_presenter.pill_css_class(review_grade)
        end

        def grade_options_with_classnames
          Lms::Submission::Review.grades.map do |label, value|
            label = label.titleize
            class_names = review_grade_presenter.pill_css_class(label)
            [label, value, class_names]
          end
        end

        def display_overall_comment?
          current_review&.exercise_reviews&.count&.> 1
        end

        def review_comment
          current_review&.comment
        end

        def review_id
          current_review&.id
        end

        def save_status_id
          "#{dom_id}--save-status"
        end

        def button_save_status_id
          "#{dom_id}--button-save-status"
        end

        def editable_textarea_save_status_id
          "#{dom_id}--editable-textarea-save-status"
        end

      end
    end
  end
end
