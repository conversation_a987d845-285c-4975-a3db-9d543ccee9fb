# frozen_string_literal: true

module LearningDelivery
  module Submissions
    class IndexPresenter < ApplicationPresenter
      include Pagy::Backend

      attr_reader :params, :current_employee, :pagy_info

      def initialize(params:, current_employee: nil)
        @params = params
        @current_employee = current_employee
      end

      def line_item_presenters
        return [] if submissions.nil?

        submissions.map { |submission| LineItemPresenter.new(submission:, params:) }
      end

      def pagination_presenter
        PaginationPresenter.new(pagy_info: @pagy_info)
      end

      def filters_presenter
        FiltersPresenter.new(
          selected_section_id: params[:section_id],
          selected_assignment_template_id: params[:assignment_template_id],
          selected_learner_id: params[:learner_id],
          submissions: base_submissions_for_filters,
          submission_ids_with_unread_comments:,
          filter_type: params[:filter],
          current_employee:,
          selected_per_page: params[:per_page],
        )
      end

      def submissions_list
        @submissions_list ||= submissions.map(&:id)
      end

      private

      def submissions
        @submissions ||= begin
          @pagy_info, paginated_submissions = pagy(query.relation, limit: params[:per_page], page: params[:page])
          paginated_submissions
        end
      end

      def base_submissions_for_filters
        @base_submissions_for_filters ||= query.base_filtered_relation
      end

      def query
        @query ||= Lms::Submission::ForGraderQuery.new(
          params:,
          current_employee:,
        )
      end

      def submission_ids_with_unread_comments
        @submission_ids_with_unread_comments ||= Lms::Submission::Comment
          .unread_from_learner
          .pluck(:submission_id)
          .uniq
      end
    end
  end
end
