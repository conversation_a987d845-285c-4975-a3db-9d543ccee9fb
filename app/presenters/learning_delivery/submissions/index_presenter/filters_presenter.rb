# frozen_string_literal: true

module LearningDelivery
  module Submissions
    class IndexPresenter
      class FiltersPresenter < ApplicationPresenter
        attr_reader :submissions, :submission_ids_with_unread_comments, :selected_section_id,
          :selected_assignment_template_id, :selected_learner_id, :filter_type, :current_employee, :selected_per_page

        def initialize(submissions:, submission_ids_with_unread_comments:, selected_section_id:,
                       selected_assignment_template_id:, selected_learner_id: nil, filter_type: nil, current_employee: nil, selected_per_page: nil)
          @submissions = submissions
          @submission_ids_with_unread_comments = submission_ids_with_unread_comments
          @selected_section_id = selected_section_id
          @selected_assignment_template_id = selected_assignment_template_id
          @selected_learner_id = selected_learner_id
          @filter_type = filter_type
          @current_employee = current_employee
          @selected_per_page = selected_per_page
        end

        def to_partial_path
          'learning_delivery/submissions/index/filters'
        end

        def review_status_options
          Lms::Submission::Review.state_alt_name_to_ids.map do |name, id|
            [
              name,
              id,
            ]
          end
        end

        def section_options
          sections.sort_by(&:program_cohort_name).map do |section|
            [
              section.program_cohort_name,
              section.id,
            ]
          end
        end

        def assignment_template_options
          assignment_templates.map do |assignment_template|
            [
              assignment_template.full_name,
              assignment_template.id,
            ]
          end
        end

        def learner_options
          learners.map do |learner|
            [
              learner.full_name,
              learner.id,
            ]
          end
        end

        def filter_all?
          filter_type == 'all'
        end

        def filter_needs_review?
          !(filter_all? || filter_new_comments? || filter_processing?)
        end

        def filter_new_comments?
          filter_type == 'new_comments'
        end

        def filter_processing?
          filter_type == 'processing'
        end

        def show_processing_filter?
          current_employee.nil?
        end

        def needs_review_count
          @needs_review_count ||= submissions
            .where(lms_submission_reviews: { state: Lms::Submission::Review.states[:manual_review_needed] })
            .count
        end

        def new_comments_count
          @new_comments_count ||= submissions.where(id: submission_ids_with_unread_comments).count
        end

        def processing_count
          @processing_count ||= submissions
            .where(lms_submission_reviews: { state: Lms::Submission::Review::AUTOGRADING_IN_PROGRESS_STATES })
            .count
        end

        def selected_section
          @selected_section ||= sections.find { |s| s.id == selected_section_id.to_i } if selected_section_id.present?
        end

        def submissions_path_with_per_page
          learning_delivery_submissions_path(per_page: selected_per_page)
        end

        private

        def sections
          @sections ||=
            if current_employee
              current_employee.grader_sections&.active&.order(:name)
            else
              Section.active.order(:name)
            end
        end

        def assignment_templates
          @assignment_templates ||=
            Lms::AssignmentTemplate
              .joins(:module_template, assignments: [:section_assignments, :assignment_group])
              .where(lms_section_assignments: { section_id: selected_section&.id || sections.pluck(:id) })
              .merge(Lms::AssignmentGroup.playbook_assignments)
              .order('lms_module_templates.week_number, lms_module_templates.title, lms_assignment_templates.name')
              .uniq
        end

        def learners
          @learners ||= begin
            learner_ids = submissions.joins(:enrollment).pluck('enrollments.learner_id').uniq
            Learner.where(id: learner_ids).order(:first_name, :last_name)
          end
        end

      end
    end
  end
end
