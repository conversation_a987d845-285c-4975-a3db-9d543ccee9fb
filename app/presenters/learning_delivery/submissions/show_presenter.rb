# frozen_string_literal: true

module LearningDelivery
  module Submissions
    class ShowPresenter < ApplicationPresenter

      attr_reader :submission, :params, :current_employee

      delegate :current_review, :id, to: :submission
      delegate :comments_to_publish, to: :current_review
      delegate :id, to: :submission, prefix: true
      delegate :expected_page, to: :pagination_presenter

      def initialize(submission:, params:, current_employee: nil)
        @submission = submission
        @params = params
        @current_employee = current_employee
      end

      def to_partial_path
        'learning_delivery/submissions/show'
      end

      def index_path
        UrlBuilder::LearningDelivery.learning_delivery_submissions_path(params: params.except(:page).merge(page: expected_page))
      end

      def pagination_presenter
        @pagination_presenter ||= PaginationPresenter.new(
          submission:,
          params:,
          current_employee:,
        )
      end

      def comments_presenter
        @comments_presenter ||= CommentsPresenter.new(
          submission:,
          comments:,
        )
      end

      def review_presenter
        @review_presenter ||= ReviewPresenter.new(
          submission:,
          redirect_next_path: pagination_presenter.next_page_path,
          current_employee:,
        )
      end

      def exercise_review_comments
        return unless current_review&.exercise_reviews&.any?

        current_review.exercise_reviews.map(&:comment)
      end

      def comments
        @comments ||= submission.comments.includes(:author).order(created_at: :desc)
      end

    end
  end
end
