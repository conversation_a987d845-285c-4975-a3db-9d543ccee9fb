# frozen_string_literal: true

module LearningDelivery
  module Learners
    class LearnerRiskTypeElementDescriptionPresenter
      class LearnerMissingAssignmentsPresenter < ApplicationPresenter

        attr_reader :risk

        def initialize(risk:)
          @risk = risk
        end

        def cohort(id)
          @cohort = Cohort.find(id)
        end

        def cohort_url(cohort)
          @cohort_url ||= UrlBuilder::Admin.admin_cohort_url(cohort)
        end

        def assighnment_group_detail(name, date)
          dt = DateTime.parse(date.to_s)
          eastern_time = dt.in_time_zone("Eastern Time (US & Canada)")
          date_part = eastern_time.strftime("%B %-d")
          year_and_time_part = eastern_time.strftime("%Y, %-l:%M %p %Z")
          "#{date_part} #{name} #{year_and_time_part}"
        end

        def assignment_list(assignment_ids)
          assignment_ids.map { |id| Lms::Assignment.find(id) }
        end

        def accessed_at(date)
          return if date.blank?

          dt = DateTime.parse(date.to_s)
          eastern_time = dt.in_time_zone("Eastern Time (US & Canada)")
          formatted = eastern_time.strftime("%B %-d, %Y, %-l:%M %p")

          formatted
        end

        def status_color_classes(task)
          case task.status
          when 'skipped'
            'bg-orange-100 text-orange-700'
          when 'assigned'
            'bg-blue-100 text-blue-700'
          when 'completed'
            'bg-green-100 text-green-700'
          when 'expired'
            'bg-red-100 text-red-700'
          else # viewed, created, or unknown
            'bg-gray-100 text-gray-700'
          end
        end

        def status_dot_color(task)
          case task.status
          when 'skipped'
            'bg-dark-yellow-zip'
          when 'assigned'
            'bg-purple-zip'
          when 'completed'
            'bg-green-500'
          when 'expired'
            'bg-red-500'
          else # viewed, created, or unknown
            'bg-gray-500'
          end
        end

        def cell_base_classes
          'p-3 text-extra-dark-grey-zip text-sm font-normal border-b border-r border-tertiary-light-grey-zip'
        end

        def status_cell_classes
          "#{cell_base_classes} text-sm font-normal border-b border-r border-tertiary-light-grey-zip"
        end

        # Status badge styling
        def status_badge_classes(task)
          base_classes = 'py-1 px-4 rounded-full inline-block text-sm font-normal'
          "#{base_classes} #{status_color_classes(task)}"
        end

        def status_dot_classes(task)
          base_classes = 'w-2 h-2 p-1 rounded-full inline-block mr-2'
          "#{base_classes} #{status_dot_color(task)}"
        end

        def to_partial_path
          'learning_delivery/learners/learner_risk_type_element_description/learner_missing_assignments'
        end


      end
    end
  end
end
