# frozen_string_literal: true

module LearningDelivery
  module Learners
    class LearnerRiskTypeElementDescriptionPresenter < ApplicationPresenter

      attr_reader :risk

      def initialize(risk:)
        @risk = risk
      end

      def risk_type_details_presenter
        case risk.risk_type
        when "not_activated"
          LearnerCanvasNotActivatedPresenter.new
        when "missing_assignments"
          LearnerMissingAssignmentsPresenter.new(risk:)
        when "late_assignments"
          LearnerLateAssignmentsPresenter.new(risk:)
        else
          LearnerNoRecentActivityPresenter.new
        end
      end

      def to_partial_path
        'learning_delivery/employee/learners/learner_risk_info_list'
      end

    end
  end
end
