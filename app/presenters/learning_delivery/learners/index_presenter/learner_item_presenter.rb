# frozen_string_literal: true

module LearningDelivery
  module Learners
    class IndexPresenter
      class LearnerItemPresenter < ApplicationPresenter
        include ActionView::Helpers::DateHelper

        attr_reader :enrollment, :section

        delegate :learner, :risk_reason, to: :enrollment
        delegate :full_name, :primary_email_address, to: :learner

        def initialize(enrollment:, section:)
          @enrollment = enrollment
          @section = section
        end

        def date_ago(date)
          return if date.blank?

          "#{time_ago_in_words(date).gsub(/^about\s/, '')} ago"
        end

        def enrollment_status_text_and_colors
          case enrollment.status.to_s
          when "pending"
            {
              label: "Pending",
              dot_class: "bg-secondary-cricket-yellow-zip",
              bg_class: "bg-extra-light-yellow-zip",
            }
          when "active"
            {
              label: "Active",
              dot_class: "bg-green-zip",
              bg_class: "bg-green-50",
            }
          when "no_pass"
            {
              label: "No Pass",
              dot_class: "bg-red-zip",
              bg_class: "bg-functional-extra-light-red-zip",
            }
          when "pass"
            {
              label: "Pass",
              dot_class: "bg-dark-yellow-zip",
              bg_class: "bg-extra-light-yellow-zip",
            }
          when "certificate_issued"
            {
              label: "Certificate Issued",
              dot_class: "bg-purple-zip",
              bg_class: "bg-tertiary-light-grey-zip",
            }
          when "unenrolled"
            {
              label: "Unenrolled",
              dot_class: "bg-brand-primary-gray-zip",
              bg_class: "bg-functional-extra-light-grey-zip",
            }
          when "dropped"
            {
              label: "Dropped",
              dot_class: "bg-functional-warning-red-zip",
              bg_class: "bg-functional-extra-light-red-zip",
            }
          when "transferred"
            {
              label: "Transferred",
              dot_class: "bg-dark-yellow-zip",
              bg_class: "bg-extra-light-yellow-zip",
            }
          when "withdrew"
            {
              label: "Withdrew",
              dot_class: "bg-dark-yellow-zip",
              bg_class: "bg-extra-light-yellow-zip",
            }

          else
            {
              label: "Paused",
              dot_class: "bg-extra-dark-grey-zip",
              bg_class: "bg-text-focus-zip",
            }
          end
        end

        def risk_status_text_and_colors
          case enrollment.risk_level.to_s
          when "on_track"
            {
              label: "On Track",
              dot_class: "bg-green-zip",
              bg_class: "bg-tertiary-light-grey-zip",
            }
          when "low"
            {
              label: "Low Risk",
              dot_class: "bg-secondary-cricket-yellow-zip",
              bg_class: "bg-extra-light-yellow-zip",
            }
          else
            {
              label: "High Risk",
              dot_class: "bg-red-zip",
              bg_class: "bg-functional-extra-light-red-zip",
            }
          end
        end

        def to_partial_path
          'learning_delivery/learners/index/learner_item'
        end

      end
    end
  end
end
