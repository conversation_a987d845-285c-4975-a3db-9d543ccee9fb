# frozen_string_literal: true

module LearningDelivery
  module Learners
    class ShowPresenter < ApplicationPresenter
      include Pagy::Backend

      DEFAULT_PER_PAGE = 20

      attr_reader :section, :learner, :enrollment, :per_page, :page, :tab

      def initialize(learner:, section:, enrollment:, per_page:, page:, tab:)
        @section = section
        @learner = learner
        @enrollment = enrollment
        @per_page = per_page
        @page = page
        @tab = tab
      end

      def learner_profile_details_presenter
        ShowPresenter::LearnerProfileTabPresenter.new(learner:, section:, enrollment:)
      end

      def tab_options_presenter
        ShowPresenter::LearnerShowTabOptionsPresenter.new(learner:, section:, enrollment:)
      end

      def learner_avatar_presenter
        ShowPresenter::LearnerAvatarPresenter.new(learner:)
      end

      def learner_tab_item_presenter
        @presenter = case tab
        when "learner_tasks_list"
          ShowPresenter::LearnerTaskListPresenter.new(section:, page:, per_page:)
        when "learner_enrollment_status_history"
          ShowPresenter::LearnerEnrollmentStatusHistoryPresenter.new(enrollment:, page:, per_page:)
        when "learner_risk_info"
          ShowPresenter::LearnerRiskInfoPresenter.new(enrollment:)
        else
          ShowPresenter::LearnerAssignmentListPresenter.new(section:, page:, per_page:)
        end
      end

    end
  end
end
