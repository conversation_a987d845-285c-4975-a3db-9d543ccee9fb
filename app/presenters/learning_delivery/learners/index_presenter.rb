# frozen_string_literal: true

module LearningDelivery
  module Learners
    class IndexPresenter < ApplicationPresenter
      include Pagy::Backend

      DEFAULT_PER_PAGE = 20

      attr_reader :params, :section, :learner, :risk_value, :pagy_info, :current_admin_user

      def initialize(current_admin_user:, section: nil, learner: nil, params: {})
        @params = params
        @section = section
        @current_admin_user = current_admin_user
        @learner = learner
        @risk_value = params[:risk_value]
      end

      def learner_item_presenters
        return [] if enrollments.nil? || section.nil?

        enrollments.map { |enrollment| LearnerItemPresenter.new(enrollment:, section:) }
      end

      def pagination_presenter
        PaginationPresenter.new(pagy_info:)
      end

      def format_section_title(section)
        program_abbreviation = section.cohort.program.abbreviation
        "#{program_abbreviation} #{section.cohort.starts_on.strftime("%b '%y")} Section #{section.suffix}"
      end

      def section_options
        options = [['Select a Section', '']]
        options += if section.present?
          ([section] + sections_scope.where.not(id: section.id)).map do |s|
            [format_section_title(s), s.humanized_uid]
          end
        else
          sections_scope.map do |s|
            [format_section_title(s), s.humanized_uid]
          end
        end
        options
      end

      def student_options
        return [['Select User', '']] if section.nil?

        [['Select User', '']] + section.enrollments.map do |enrollment|
          [enrollment.learner.full_name, enrollment.learner.uid]
        end
      end

      def risk_status
        LearningDelivery::RiskAssessment.risk_level_alt_name_to_ids.to_a
      end


      private

      def sections_scope
        @sections_scope ||= begin
          scope = if current_admin_user.admin?
            Section.active
          else
            current_admin_user.learning_delivery_employee.advocate_sections.active
          end

          scope.ordered_by_start_and_name
        end
      end

      def per_page
        @per_page ||= params[:per_page].to_i.nonzero? || DEFAULT_PER_PAGE
      end

      def page
        @page ||= params[:page].to_i.nonzero? || 1
      end

      def enrollments
        return [] if section.nil?

        @enrollments ||= begin
          @pagy_info, paginated_enrollments = pagy(filtered_enrollments, limit: per_page, page:)
          paginated_enrollments
        end
      end

      def filtered_enrollments
        return Enrollment.none if section.nil?

        @filtered_enrollments ||= begin
          scope = if learner.present?
            learner.enrollments.where(section_id: section.id)
          else
            section.enrollments.primary
          end
          scope = scope.where(risk_level: risk_value) if risk_value.present?
          scope.order(risk_level: :desc, id: :asc)
        end
      end

    end
  end
end
