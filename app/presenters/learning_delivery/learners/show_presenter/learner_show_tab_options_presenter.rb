# frozen_string_literal: true

module LearningDelivery
  module Learners
    class ShowPresenter
      class LearnerShowTabOptionsPresenter < ApplicationPresenter
        attr_reader :learner, :section, :enrollment

        def initialize(learner:, section:, enrollment:)
          @learner = learner
          @section = section
          @enrollment = enrollment
        end

        def to_partial_path
          'learning_delivery/learners/show/learner_show_tab_options'
        end
      end
    end
  end
end
