# frozen_string_literal: true

module LearningDelivery
  module Learners
    class ShowPresenter
      class LearnerEnrollmentStatusHistoryPresenter < ApplicationPresenter
        include Pagy::Backend

        DEFAULT_PER_PAGE = 20
        attr_reader :enrollment, :per_page, :page, :pagy_info

        def initialize(enrollment:, per_page:, page:)
          @enrollment = enrollment
          @per_page = per_page
          @page = page
        end

        def pagination_presenter
          PaginationPresenter.new(pagy_info:)
        end

        def enrollment_status_list_item_presenters
          return [] if status_changes.nil?

          status_changes.map { |status| LearnerEnrollmentStatusHistoryItemPresenter.new(status:) }
        end

        def to_partial_path
          'learning_delivery/learners/show/learner_enrollment_status_history'
        end


        private

        def status_changes
          return [] if filtered_status_changes.nil?

          @per_page ||= per_page.to_i.nonzero? || DEFAULT_PER_PAGE
          @page ||= page.to_i.nonzero? || 1
          @status_changes ||= begin
            @pagy_info, paginated_status_changes = pagy(filtered_status_changes, limit: @per_page, page: @page)
            paginated_status_changes
          end
        end

        def filtered_status_changes
          return [] if enrollment.nil?

          @filtered_status_changes ||= enrollment.status_changes
            .where.not(status_was: nil)
            .where.not(reason: nil)
            .where.not(admin_user_id: nil)
        end
      end
    end
  end
end
