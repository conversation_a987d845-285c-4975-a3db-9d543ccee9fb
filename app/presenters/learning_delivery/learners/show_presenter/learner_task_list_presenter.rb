# frozen_string_literal: true

module LearningDelivery
  module Learners
    class ShowPresenter
      class LearnerTaskListPresenter < ApplicationPresenter
        include Pagy::Backend

        DEFAULT_PER_PAGE = 20
        attr_reader :section, :per_page, :page, :pagy_info

        def initialize(section:, per_page:, page:)
          @section = section
          @per_page = per_page
          @page = page
        end

        def pagination_presenter
          PaginationPresenter.new(pagy_info:)
        end

        def task_list_item_presenters
          return [] if tasks.nil?

          tasks.map { |task| LearnerTaskListItemPresenter.new(task:) }
        end

        def to_partial_path
          'learning_delivery/learners/show/learner_task_list'
        end


        private

        def tasks
          return [] if filtered_tasks.nil?

          @per_page ||= per_page.to_i.nonzero? || DEFAULT_PER_PAGE
          @page ||= page.to_i.nonzero? || 1
          @tasks ||= begin
            @pagy_info, paginated_tasks = pagy(filtered_tasks, limit: @per_page, page: @page)
            paginated_tasks
          end
        end

        def filtered_tasks
          return [] if section.nil?

          @filtered_tasks ||= section.learning_delivery_tasks
        end
      end
    end
  end
end
