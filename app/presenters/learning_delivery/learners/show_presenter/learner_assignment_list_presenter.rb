# frozen_string_literal: true

module LearningDelivery
  module Learners
    class ShowPresenter
      class LearnerAssignmentListPresenter < ApplicationPresenter
        include Pagy::Backend

        DEFAULT_PER_PAGE = 20
        attr_reader :section, :per_page, :page, :pagy_info

        def initialize(section:, per_page:, page:)
          @section = section
          @per_page = per_page
          @page = page
        end

        delegate :assignments, to: :section, prefix: true

        def pagination_presenter
          PaginationPresenter.new(pagy_info:)
        end

        def assignment_list_item_presenters
          return [] if assignments.nil? || section.nil?

          assignments.map { |assignment| LearnerAssignmentListItemPresenter.new(assignment:) }
        end

        def to_partial_path
          'learning_delivery/learners/show/learner_assignment_list'
        end

        private

        def assignments
          return [] if section.nil?

          return [] if filtered_assignments.nil?

          @per_page ||= per_page.to_i.nonzero? || DEFAULT_PER_PAGE
          @page ||= page.to_i.nonzero? || 1
          @assignments ||= begin
            @pagy_info, paginated_assignments = pagy(filtered_assignments, limit: @per_page, page: @page)
            paginated_assignments
          end
        end

        def filtered_assignments
          @filtered_assignments ||= section_assignments
        end

      end
    end
  end
end
