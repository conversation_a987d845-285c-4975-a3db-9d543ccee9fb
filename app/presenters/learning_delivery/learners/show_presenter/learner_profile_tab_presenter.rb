# frozen_string_literal: true

module LearningDelivery
  module Learners
    class ShowPresenter
      class LearnerProfileTabPresenter < ApplicationPresenter
        attr_reader :learner, :enrollment, :section

        def initialize(learner:, section:, enrollment:)
          @learner = learner
          @section = section
          @enrollment = enrollment
        end

        def learner_full_name(enrollment)
          enrollment.learner.full_name
        end

        def format_section_title(section)
          program_abbreviation = section.cohort.program.abbreviation
          "#{program_abbreviation} #{section.cohort.starts_on.strftime("%b '%y")} Section #{section.suffix}"
        end

        def learner_email(enrollment)
          enrollment.learner.primary_email_address
        end

        def manager_view?
          permission_level == :manager
        end

        def enrollment_status_text_and_colors(enrollment)
          case enrollment.status
          when "pending"
            {
              label: "Pending",
              dot_class: "bg-secondary-cricket-yellow-zip",
              bg_class: "bg-extra-light-yellow-zip",
            }
          when "active"
            {
              label: "Active",
              dot_class: "bg-green-zip",
              bg_class: "bg-green-50",
            }
          when "no_pass"
            {
              label: "No Pass",
              dot_class: "bg-red-zip",
              bg_class: "bg-functional-extra-light-red-zip",
            }
          when "pass"
            {
              label: "Pass",
              dot_class: "bg-dark-yellow-zip",
              bg_class: "bg-extra-light-yellow-zip",
            }
          when "certificate_issued"
            {
              label: "Certificate Issued",
              dot_class: "bg-purple-zip",
              bg_class: "bg-tertiary-light-grey-zip",
            }
          when "unenrolled"
            {
              label: "Unenrolled",
              dot_class: "bg-brand-primary-gray-zip",
              bg_class: "bg-functional-extra-light-grey-zip",
            }
          when "dropped"
            {
              label: "Dropped",
              dot_class: "bg-functional-warning-red-zip",
              bg_class: "bg-functional-extra-light-red-zip",
            }
          when "transferred"
            {
              label: "Transferred",
              dot_class: "bg-dark-yellow-zip",
              bg_class: "bg-extra-light-yellow-zip",
            }
          when "withdrew"
            {
              label: "Withdrew",
              dot_class: "bg-dark-yellow-zip",
              bg_class: "bg-extra-light-yellow-zip",
            }

          else
            {
              label: "Paused",
              dot_class: "bg-extra-dark-grey-zip",
              bg_class: "bg-text-focus-zip",
            }
          end
        end

        def risk_status_text_and_colors(enrollment)
          case enrollment.course_risk
          when "on_track"
            {
              label: "On Track",
              dot_class: "bg-green-zip",
              bg_class: "bg-tertiary-light-grey-zip",
            }
          when "low"
            {
              label: "Low Risk",
              dot_class: "bg-secondary-cricket-yellow-zip",
              bg_class: "bg-extra-light-yellow-zip",
            }
          when "moderate"
            {
              label: "Moderate Risk",
              dot_class: "bg-dark-yellow-zip",
              bg_class: "bg-extra-light-yellow-zip",
            }
          else
            {
              label: "High Risk",
              dot_class: "bg-red-zip",
              bg_class: "bg-functional-extra-light-red-zip",
            }
          end
        end

        def to_partial_path
          'learning_delivery/learners/show/learner_profile_tab'
        end

      end
    end
  end
end
