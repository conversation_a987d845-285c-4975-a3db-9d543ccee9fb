# frozen_string_literal: true

module LearningDelivery
  module Learners
    class ShowPresenter
      class LearnerRiskInfoPresenter < ApplicationPresenter
        attr_reader :enrollment

        def initialize(enrollment:)
          @enrollment = enrollment
        end

        def risk_type_breakdowns
          LearningDelivery::RiskAssessment.where(enrollment_id: enrollment.id)
        end

        def lms_last_activity(date)
          return if date.blank?

          dt = DateTime.parse(date.to_s)
          eastern_time = dt.in_time_zone("Eastern Time (US & Canada)")
          formatted = eastern_time.strftime("%B %-d, %Y, %-l:%M %p %Z")

          formatted
        end

        def accessed_at(date)
          return if date.blank?

          dt = DateTime.parse(date.to_s)
          eastern_time = dt.in_time_zone("Eastern Time (US & Canada)")
          formatted = eastern_time.strftime("%B %-d, %Y, %-l:%M %p")

          formatted
        end

        def to_partial_path
          'learning_delivery/learners/show/learner_risk_info'
        end
      end
    end
  end
end
