# frozen_string_literal: true

module LearningDelivery
  module Learners
    class ShowPresenter
      class LearnerEnrollmentStatusHistoryPresenter
        class LearnerEnrollmentStatusHistoryItemPresenter < ApplicationPresenter
          attr_reader :status

          def initialize(status:)
            @status = status
          end

          def admin_name(id)
            return if id.blank?

            AdminUser.find(id).full_name
          end

          def admin_name_presenter(id)
            return if id.nil?

            admin = AdminUser.find(id)
            ShowPresenter::LearnerAvatarPresenter.new(learner: admin, classes:["size-10", "rounded-full"])
          end

          def created_at(date)
            dt = DateTime.parse(date)
            eastern_time = dt.in_time_zone("Eastern Time (US & Canada)")
            date_part = eastern_time.strftime("%B %d")
            time_part = eastern_time.strftime("%I:%M %P %Z")

            { date: date_part, time: time_part }
          end

          def enrollment_status_text_and_colors(status)
            case status
            when "pending"
              {
                label: "Pending",
                text_class: "text-secondary-cricket-yellow-zip",
                bg_class: "bg-extra-light-yellow-zip",
              }
            when "active"
              {
                label: "Active",
                text_class: "text-green-500",
                bg_class: "bg-green-50",
              }
            when "no_pass"
              {
                label: "No Pass",
                text_class: "text-red-zip",
                bg_class: "bg-functional-extra-light-red-zip",
              }
            when "pass"
              {
                label: "Pass",
                text_class: "text-yellow-500",
                bg_class: "bg-extra-light-yellow-zip",
              }
            when "certificate_issued"
              {
                label: "Certificate Issued",
                text_class: "text-black",
                bg_class: "bg-tertiary-light-grey-zip",
              }
            when "unenrolled"
              {
                label: "Unenrolled",
                text_class: "text-brand-primary-gray-zip",
                bg_class: "bg-functional-extra-light-grey-zip",
              }
            when "dropped"
              {
                label: "Dropped",
                text_class: "text-red-500",
                bg_class: "bg-functional-extra-light-red-zip",
              }
            when "transferred"
              {
                label: "Transferred",
                text_class: "text-purple-zip",
                bg_class: "bg-purple-200",
              }
            when "withdrew"
              {
                label: "Withdrew",
                text_class: "text-dark-yellow-zip",
                bg_class: "bg-extra-light-yellow-zip",
              }

            else
              {
                label: "Paused",
                text_class: "text-green-800",
                bg_class: "bg-gray-100",
              }
            end
          end

          def to_partial_path
            'learning_delivery/learners/show/learner_enrollment_status_history/learner_enrollment_status_history_item'
          end
        end
      end
    end
  end
end
