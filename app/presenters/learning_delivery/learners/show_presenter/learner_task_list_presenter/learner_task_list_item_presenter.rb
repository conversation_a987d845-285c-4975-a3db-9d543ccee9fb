# frozen_string_literal: true

module LearningDelivery
  module Learners
    class ShowPresenter
      class LearnerTaskListPresenter
        class LearnerTaskListItemPresenter < ApplicationPresenter
          attr_reader :task

          def initialize(task:)
            @task = task
          end

          def due_date(date)
            return "—" if date.blank?

            date = DateTime.parse(date.to_s) unless date.respond_to?(:strftime)
            date.strftime("%B %-d, %Y")
          end

          def status_color_classes(task)
            case task.status
            when 'skipped'
              'bg-orange-100 text-orange-700'
            when 'assigned'
              'bg-blue-100 text-blue-700'
            when 'completed'
              'bg-green-100 text-green-700'
            when 'expired'
              'bg-red-100 text-red-700'
            else
              'bg-gray-100 text-gray-700'
            end
          end

          def status_dot_color(task)
            case task.status
            when 'skipped'
              'bg-dark-yellow-zip'
            when 'assigned'
              'bg-purple-zip'
            when 'completed'
              'bg-green-500'
            when 'expired'
              'bg-red-500'
            else
              'bg-gray-500'
            end
          end

          def cell_base_classes
            'p-3 text-extra-dark-grey-zip text-sm font-normal border-b border-r border-tertiary-light-grey-zip'
          end

          def status_cell_classes
            "#{cell_base_classes} text-sm font-normal border-b border-r border-tertiary-light-grey-zip"
          end

          def status_badge_classes(task)
            base_classes = 'py-1 px-4 rounded-full inline-block text-sm font-normal'
            "#{base_classes} #{status_color_classes(task)}"
          end

          def status_dot_classes(task)
            base_classes = 'w-2 h-2 p-1 rounded-full inline-block mr-2'
            "#{base_classes} #{status_dot_color(task)}"
          end

          def to_partial_path
            'learning_delivery/learners/show/learner_task_list/learner_task_list_item'
          end
        end
      end
    end
  end
end
