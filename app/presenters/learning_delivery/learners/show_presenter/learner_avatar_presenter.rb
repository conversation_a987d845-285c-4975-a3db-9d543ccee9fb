# frozen_string_literal: true

module LearningDelivery
  module Learners
    class ShowPresenter
      class LearnerAvatarPresenter < ApplicationPresenter

        COLORS = %w[
          bg-red-500
          bg-orange-500
          bg-amber-500
          bg-yellow-500
          bg-lime-500
          bg-green-500
          bg-emerald-500
          bg-teal-500
          bg-cyan-500
          bg-sky-500
          bg-blue-500
          bg-indigo-500
          bg-violet-500
          bg-purple-500
          bg-fuchsia-500
          bg-pink-500
          bg-rose-500
          bg-slate-500
        ].freeze

        attr_reader :learner, :classes

        delegate :headshot, to: :learner, private: true, allow_nil: true

        def initialize(learner:, classes: nil)
          @learner = learner
          @classes = Array.wrap(classes)
        end

        def to_partial_path
          'learning_delivery/learners/show/learner_avatar'
        end

        def css_classes
          (classes + generated_avatar_css).compact.join(' ')
        end

        def generated_avatar_css
          [generated_avatar_bg_color]
        end

        def generated_avatar_bg_color
          return if learner.blank?

          COLORS[(learner.first_name.first.ord + learner.last_name.first.ord) % COLORS.size]
        end

        def image_url
          return unless learner.present? && headshot.attached?

          headshot.cdn_url
        end

        def initials
          return if learner.nil?

          "#{learner.first_name.first}#{learner.last_name.first}"
        end

      end
    end
  end
end
