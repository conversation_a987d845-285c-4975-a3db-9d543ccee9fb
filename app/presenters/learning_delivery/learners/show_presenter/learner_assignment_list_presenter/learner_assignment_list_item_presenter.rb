# frozen_string_literal: true

module LearningDelivery
  module Learners
    class ShowPresenter
      class LearnerAssignmentListPresenter
        class LearnerAssignmentListItemPresenter < ApplicationPresenter
          attr_reader :assignment

          def initialize(assignment:)
            @assignment = assignment
          end

          def submission_date(date)
            return "—" if date.blank?

            date = DateTime.parse(date.to_s) unless date.respond_to?(:strftime)
            date.strftime("%B %-d, %Y")
          end

          def to_partial_path
            'learning_delivery/learners/show/learner_assignment_list/learner_assignment_list_item'
          end
        end
      end
    end
  end
end
