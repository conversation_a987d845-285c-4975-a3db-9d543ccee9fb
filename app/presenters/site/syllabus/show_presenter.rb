# frozen_string_literal: true

module Site
  module Syllabus
    class ShowPresenter < ApplicationPresenter

      attr_reader :partner_program, :discount_code_code

      delegate :partner, :program, :cobranding_text, :brand_logos_allowed?, :show_our_course_attracts_block?,
        :certificate_title, :certificate_type_name, :cohort_presenters,
        :course_nomenclature, :disclaimer_presenter, :enroll_url, :expandable_career_presenters, :footer_presenter, :expandable_so_you_can_presenters,
        :faqs_presenters, :header_banner_presenter, :instructors_presenter, :logo_on_primary_background, :program_duration_text, :logo_css,
        :shared_setup_presenter, :sample_certificate, :testimonials_presenter, :preview_video_url, :preview_video_image, :career_quote_image,
        :company_brands, :applied_technologies, :site_program_dataset, :site_partner_program_dataset, :program_duration_summary,
        :program_certificate_details, :certificate_details, :program_name, :program_short_name, :eva_banner_presenter,
        :cobranding_css, :savings_percent, :sale_savings_percent,
        :site_partner_dataset, :site_reimbursement_url, :admissions_schedule_url, :overrides, :offer_buy_now_pay_later?, :ab_testing_html,
        to: :partner_program_presenter
      delegate_pricing :actual_cents, :list_cents, :sale_cents, :promos_discount_cents, :recurring_actual_cents, to: :partner_program_presenter

      delegate :site_syllabus_page_template, :site_landing_page_template, to: :program

      delegate :custom_hero_text, :expandable_learnings, :our_students, :preview_video_blurb, :what_you_earn,
        :expandable_learnings_blurb, to: :site_syllabus_page_template

      delegate :syllabus, :instructors_headline, :instructors_blurb, to: :site_program_dataset
      delegate :expandable_career_blurb, to: :site_landing_page_template

      def initialize(partner_program:, discount_code_code: nil)
        @partner_program = partner_program
        @discount_code_code = discount_code_code
      end

      def meta_tags_presenter
        @meta_tags_presenter ||= Components::MetaTagsPresenter.new(partner_program:, page_template: site_syllabus_page_template)
      end

      def hero_text
        overrides.dig(:syllabus_page, :hero_text) || custom_hero_text || "#{program_name} #{course_nomenclature}".titlecase
      end

      def our_students_presenters
        options = {
          title_classes: 'text-2xl font-semibold mb-1',
        }
        our_students.map do |hash|
          title = hash.keys.first
          descriptions = hash.values.first
          Components::Utility::KeyPointsPresenter.new(title:, descriptions:, options:)
        end
      end

      def what_you_earn_presenters
        options = {
          title_classes: 'text-base font-semibold mt-6 mb-1',
        }
        what_you_earn.map do |hash|
          title = PartnerProgram::InterpolateCommand.call!(partner_program:, string: hash.keys.first)
          descriptions = PartnerProgram::InterpolateNestedArrayCommand.call!(partner_program:, array: hash.values.first)
          Components::Utility::KeyPointsPresenter.new(title:, descriptions:, options:)
        end
      end

      def expandable_curriculum_presenters
        expandable_learnings.map do |hash|
          title = hash["title"]
          list_items = hash["list_items"]
          playbook_assignment = hash['playbook_assignment']

          footer_items = if playbook_assignment
            { 'label' => 'Playbook assignment', 'text' => playbook_assignment }
          else
            hash['footer_items']
          end
          body = hash['body'].presence

          Components::Utility::ExpandableListPresenter.new(title:, list_items:, footer_items:, body:)
        end
      end

      def countdown_presenter
        @countdown_presenter ||= CountdownPresenter.new(partner_program:)
      end

      def headline_blurb
        PartnerProgram::InterpolateCommand.call!(partner_program:, string: site_syllabus_page_template.headline_blurb)
      end

      def our_learners_blurb
        sanitize(site_syllabus_page_template.our_learners_blurb, tags: DEFAULT_ALLOWED_TAGS, attributes: DEFAULT_ALLOWED_ATTRIBUTES)
      end

      def your_time_blurb
        sanitize(site_syllabus_page_template.your_time_blurb, tags: DEFAULT_ALLOWED_TAGS, attributes: DEFAULT_ALLOWED_ATTRIBUTES)
      end

      def preview_video_headline
        site_syllabus_page_template.preview_video_headline.presence || "Introduction to #{program_name}"
      end

      def canonical_url
        UrlBuilder::Site.new(partner_program:).call!(template: :syllabus)
      end

      def syllabus_url
        syllabus.cdn_url(params: { 'response-content-disposition' => "attachment;filename=#{program_name} Syllabus.pdf" })
      end

      def cache_key(*components)
        Digest::SHA2.hexdigest(
          (
            partner_program_presenter.cache_key_components +
            [site_syllabus_page_template.updated_at, site_landing_page_template.updated_at] +
            components
          ).flatten.join('-'),
        )
      end

      def step_3_title
        site_landing_page_template.step_3_title.presence || Site::Landing::ShowPresenter::DEFAULT_STEP3_TITLE
      end

      # @return [String, nil] The discount code name to apply for the registration, if any.
      def discount_code_name
        discount_code&.name
      end

      private

      def partner_program_presenter
        @partner_program_presenter ||= Site::Shared::PartnerProgramPresenter.new(partner_program:, promos_discount_codes: Array.wrap(discount_code))
      end

      # @return [Promos::DiscountCode, nil] The discount code object if it exists, otherwise nil.
      def discount_code
        return @discount_code if defined?(@discount_code)

        @discount_code ||= discount_code_code.presence && Promos::DiscountCode.code(discount_code_code).first
      end

    end
  end
end
