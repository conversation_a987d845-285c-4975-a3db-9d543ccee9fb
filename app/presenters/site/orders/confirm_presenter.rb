# frozen_string_literal: true

module Site
  module Orders
    class ConfirmPresenter < ApplicationPresenter
      delegate :experience_level_options, :aspiration_options,
        :current_experience_level_text, :partner_program, :partner, :program, :logo_on_primary_background,
        :addable_cohorts, :certificate_title, :disclaimer_presenter, :shared_setup_presenter, :savings_percent,
        :sale_savings_percent, :footer_presenter, :cobranding_text, :admissions_schedule_url, :certificate_details,
        :logo_css, :course_nomenclature, :certificate_nomenclature, :program_name, :partner_short_name,
        :program_duration_summary, :cobranding_css, :ab_testing_html,
        :single_cohort_with_single_section?, :program_duration, :available_section_uid, :section_options_exists?,
        :multiple_section_options_for?, :eva_enabled?, to: :new_registrations_presenter

      delegate :order, to: :order_item

      attr_reader :order_item, :registration, :new_registrations_presenter

      def initialize(order_item:)
        @order_item = order_item
        @registration = order_item.registration
        @new_registrations_presenter = Site::Registrations::NewPresenter.new(
          partner_program: @registration.partner_program,
          learner_cookie_info: {}, # Empty since we'll use existing registration data
          params_cohort_key: nil,
          params_section_uid: nil,
          params_registration_uid: nil,
          discount_code_code: nil,
        )
      end

      def review_mode?
        true
      end

      def email
        @email ||= registration.email.address
      end

      def first_name
        @first_name ||= registration.learner.first_name
      end

      def last_name
        @last_name ||= registration.learner.last_name
      end

      def phone
        @phone ||= registration.learner.phone&.local_number
      end

      def pre_select_cohort?(cohort_key:)
        registration.cohort.key == cohort_key
      end

      def pre_select_section?(section_uid:)
        registration.section.uid == section_uid
      end

      def pre_select_experience_level?(experience_level:)
        registration.experience_level.to_s.to_sym == experience_level
      end

      def pre_select_aspiration?(aspiration:)
        registration.aspiration.to_s.to_sym == aspiration
      end

      def cohort_readonly?
        registration.cohort_picked_status? || registration.section_picked_status?
      end

      def section_readonly?
        registration.section_picked_status?
      end

      def selected_cohort_name
        return nil unless cohort_readonly?

        registration.cohort.starts_on.strftime('%B (Starts %-m/%-d)')
      end

      def selected_section_name
        return nil unless section_readonly?

        Section::LiveDayTimeFormatter.new(section: registration.section).to_fs
      end

      def selected_cohort_key
        return nil unless cohort_readonly?

        registration.cohort.key
      end

      def selected_section_uid
        return nil unless section_readonly?

        registration.section.uid
      end

      def cohort_options
        return [[registration.cohort.key, registration.cohort.starts_on.strftime('%B (Starts %-m/%-d)')]] if cohort_readonly?

        new_registrations_presenter.cohort_options
      end

      def section_options(cohort:)
        if section_readonly?
          return [] unless registration.cohort == cohort

          [[registration.section.uid, Section::LiveDayTimeFormatter.new(section: registration.section).to_fs]]
        else
          new_registrations_presenter.section_options(cohort:)
        end
      end
    end
  end
end
