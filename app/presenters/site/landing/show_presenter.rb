# frozen_string_literal: true

module Site
  module Landing
    class ShowPresenter < ApplicationPresenter
      DEFAULT_CAREER_BLURB = "Get the support you need to meet your unique goals. Whether you're looking for a promotion or to start a new career,
                              we're here to help."
      DEFAULT_STEP3_TITLE = "Advance your career"

      attr_reader :partner_program, :learner_cookie_info, :discount_code_code

      delegate :partner, :program, :site_partner_program_dataset, :site_program_dataset, :cobranding_text,
        :brand_logos_allowed?, :show_our_course_attracts_block?,
        :certificate_title, :certificate_type_name, :cohort_presenters, :footer_presenter, :cobranding_css, :logo_css,
        :custom_disclaimer, :disclaimer_presenter, :enroll_url, :expandable_career_presenters, :expandable_so_you_can_presenters, :faqs_presenters,
        :header_banner_presenter, :instructors_presenter, :logo_on_primary_background, :program_duration_text, :shared_setup_presenter,
        :sample_certificate, :testimonials_presenter, :partner_theme, :course_nomenclature, :applied_technologies, :preview_video_url,
        :preview_video_image, :career_quote_image, :career_quote_alt_text, :company_brands, :program_certificate_details, :site_reimbursement_url,
        :savings_percent, :sale_savings_percent, :eva_banner_presenter,
        :admissions_schedule_url, :overrides, :program_name, :program_short_name, :offer_buy_now_pay_later?, :ab_testing_html,
        to: :partner_program_presenter
      delegate_pricing :list_cents, :actual_cents, :sale_cents, :promos_discount_cents, :recurring_actual_cents, to: :partner_program_presenter

      delegate :career_title, to: :program, prefix: true

      delegate :site_landing_page_template, to: :program

      delegate :custom_hero_text, :hero_image, :key_points, :secondary_headline, :discover_new_skills,
        :explore_options, :step_1_heading, to: :site_landing_page_template

      delegate :expandable_skill_map, :instructors_headline, :instructors_blurb, to: :site_program_dataset

      def initialize(partner_program:, learner_cookie_info:, discount_code_code: nil)
        @partner_program = partner_program
        @learner_cookie_info = learner_cookie_info
        @discount_code_code = discount_code_code
      end

      def meta_tags_presenter
        @meta_tags_presenter ||= Components::MetaTagsPresenter.new(partner_program:, page_template: site_landing_page_template)
      end

      def hero_text
        overrides.dig(:landing_page, :hero_text) || custom_hero_text || "#{program_short_name} #{partner_theme.course_nomenclature}".titlecase
      end

      def expandable_skill_presenters
        expandable_skill_map.map do |hash|
          title = hash.keys.first
          list_items = hash.values.first
          Components::Utility::ExpandableListPresenter.new(title:, list_items:)
        end
      end

      def syllabus_form_presenter(location:)
        Shared::SyllabusFormPresenter.new(partner_program:, location:, meta_tags_presenter:, template: 'landing', learner_cookie_info:)
      end

      def step_2_title
        PartnerProgram::InterpolateCommand.call!(
          partner_program:,
          string: overrides.dig(:landing_page, :step_2_title) || site_landing_page_template.step_2_title,
        )
      end

      def step_2_body
        interpolated_step_2_body = PartnerProgram::InterpolateCommand.call!(partner_program:, string: site_landing_page_template.step_2_body)
        simple_format(interpolated_step_2_body, {}, { sanitize_options: { tags: DEFAULT_ALLOWED_TAGS, attributes: DEFAULT_ALLOWED_ATTRIBUTES } })
      end

      def master_the_skills_of
        suffix = if program_career_title.present?
          program_career_title.with_indefinite_article
        else
          program_short_name
        end
        "Master the Skills of #{suffix}"
      end

      def banner_cta_header_text
        overrides.dig(:landing_page, :banner_cta_header_text) ||
          site_landing_page_template.banner_cta_header_text ||
          master_the_skills_of
      end

      def hero_subtext
        PartnerProgram::InterpolateCommand.call!(partner_program:, string: site_landing_page_template.hero_subtext)
      end

      def expandable_career_blurb
        site_landing_page_template.expandable_career_blurb.presence || DEFAULT_CAREER_BLURB
      end

      def step_3_title
        site_landing_page_template.step_3_title.presence || DEFAULT_STEP3_TITLE
      end

      def step_1_blurb
        PartnerProgram::InterpolateCommand.call!(partner_program:, string: site_landing_page_template.step_1_blurb)
      end

      def show_blog_link?
        overrides.dig(:landing_page, :show_blog_link) || false
      end

      def blog_url
        UrlBuilder::Site.new(partner_program:).call!(template: :blog_category)
      end

      def canonical_url
        if partner.ziplines?
          "https://www.ziplines.com/courses/#{program.slug}-course"
        else
          UrlBuilder::Site.new(partner_program:).call!(template: :landing)
        end
      end

      def cache_key(*components)
        Digest::SHA2.hexdigest(
          (
            partner_program_presenter.cache_key_components +
            [site_landing_page_template.updated_at] +
            components
          ).flatten.join('-'),
        )
      end

      def career_quote_image_classes
        site_program_dataset.overrides[:career_quote_image_classes].presence || {
          container: ProgramDataset::OVERRIDES_EXAMPLE.dig(:career_quote_image_classes, :container),
          image_tag: ProgramDataset::OVERRIDES_EXAMPLE.dig(:career_quote_image_classes, :image_tag),
        }
      end

      # @return [String, nil] The discount code name to apply for the registration, if any.
      def discount_code_name
        discount_code&.name
      end

      private

      def partner_program_presenter
        @partner_program_presenter ||= Site::Shared::PartnerProgramPresenter.new(partner_program:, promos_discount_codes: Array.wrap(discount_code))
      end

      # @return [Promos::DiscountCode, nil] The discount code object if it exists, otherwise nil.
      def discount_code
        return @discount_code if defined?(@discount_code)

        @discount_code ||= discount_code_code.presence && Promos::DiscountCode.code(discount_code_code).first
      end

    end
  end
end
