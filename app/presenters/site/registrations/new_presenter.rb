# frozen_string_literal: true

module Site
  module Registrations
    class NewPresenter < ApplicationPresenter

      attr_reader :partner_program, :params_cohort_key, :params_section_uid, :params_registration_uid, :learner_cookie_info, :discount_code_code

      delegate :partner, :program, :logo_on_primary_background, :addable_cohorts, :certificate_title, :disclaimer_presenter,
        :shared_setup_presenter, :savings_percent, :sale_savings_percent,
        :footer_presenter, :cobranding_text, :admissions_schedule_url, :certificate_details, :logo_css,
        :course_nomenclature, :certificate_nomenclature, :program_name, :partner_short_name, :program_duration_summary, :cobranding_css,
        :offer_buy_now_pay_later?, :ab_testing_html, to: :partner_program_presenter
      delegate_pricing :list_cents, :actual_cents, :sale_cents, :promos_discount_cents, :recurring_actual_cents, to: :partner_program_presenter

      delegate :uid, to: :partner_program, prefix: true

      delegate :program_short_name, :site_program_dataset, to: :partner_program

      delegate :overrides, to: :site_program_dataset, prefix: :program

      def initialize(partner_program:, learner_cookie_info:, params_cohort_key: nil, params_section_uid: nil, params_registration_uid: nil,
                     discount_code_code: nil)
        @partner_program = partner_program
        @params_cohort_key = params_cohort_key
        @params_section_uid = params_section_uid
        @params_registration_uid = params_registration_uid
        @learner_cookie_info = learner_cookie_info
        @discount_code_code = discount_code_code
      end

      def cohort_options
        @cohort_options ||= addable_cohorts.map do |cohort|
          [cohort.key, cohort.live_name]
        end
      end

      def section_options(cohort:)
        Cohort::SectionsToOfferCommand.call!(cohort:, partner:).map do |section|
          [section.uid, section.live_name(format: :default)]
        end
      end

      def experience_level_options
        [
          [:newbie, program_overrides.dig(:registration, :experience_level_question, :newbie).presence ||
            "I'm a newbie to #{program_short_name.downcase_with_inflections}",],
          [:some,
           program_overrides.dig(:registration, :experience_level_question, :some).presence ||
             "I have some #{program_short_name.downcase_with_inflections} experience",],
          [:current, program_overrides.dig(:registration, :experience_level_question, :current).presence ||
            current_experience_level_text,],
        ]
      end

      def current_experience_level_text
        if program.career_title.present?
          "I'm currently #{program.career_title.with_indefinite_article} by trade"
        else
          "I'm currently in #{program.career_industry.with_indefinite_article} role"
        end
      end

      def aspiration_options
        [
          [:begin, "Begin my professional career"],
          [:advance, "Advance in my current career"],
          [:change, "Change or re-enter my career"],
          [:other, "Other"],
        ]
      end

      def eva_enabled?
        partner.eva_enabled? && discount_code_code.blank?
      end

      # @return [String, nil] The discount code name to apply for the registration, if any.
      def discount_code_name
        discount_code&.name
      end

      def eva_type_options
        [
          [:veteran, "I am a Veteran"],
          [:alumni, "I am a #{partner.name} graduate"],
          [:employee, "I am a #{partner.name} employee"],
          ['', "None of these apply"],
        ]
      end

      def eva_discount
        partner.promos_eva_discount_code.discount.name
      end

      def program_duration
        "#{program.duration_in_weeks}-week"
      end

      def single_cohort?
        addable_cohorts.one?
      end

      def pre_select_cohort?(cohort_key:)
        single_cohort? || (params_cohort_key == cohort_key) || (learner_cookie_info['cohort_key'] == cohort_key)
      end

      def pre_select_section?(section_uid:)
        return (params_section_uid == section_uid) if params_section_uid.present?

        learner_cookie_info['section_uid'] == section_uid
      end

      def pre_select_experience_level?(experience_level:)
        return registration.experience_level.to_s.to_sym == experience_level if params_registration_uid.present?

        learner_cookie_info['experience_level']&.to_sym == experience_level
      end

      def pre_select_aspiration?(aspiration:)
        return registration.aspiration.to_s.to_sym == aspiration if params_registration_uid.present?

        learner_cookie_info['aspiration']&.to_sym == aspiration
      end

      def pre_select_eva_type?(eva_type:)
        return eva_type == '' if params_registration_uid.blank? || registration.eva_type.blank?

        registration.eva_type.to_s.to_sym == eva_type || learner_cookie_info['eva_type']&.to_sym == eva_type
      end

      def single_cohort_with_single_section?
        single_cohort? && section_options(cohort: addable_cohorts.first).one?
      end

      def section_options_exists?(cohort:)
        section_options(cohort:).any?
      end

      def multiple_section_options_for?(cohort:)
        section_options(cohort:).many?
      end

      def available_section_uid(cohort:)
        section_options(cohort:).first.first
      end

      def review_mode?
        false
      end

      def canonical_url
        UrlBuilder::Site.new(partner_program:).call!(template: :registration)
      end

      def email
        @email ||= registration&.email&.address || learner_cookie_info['email']
      end

      def first_name
        @first_name ||= learner&.first_name || learner_cookie_info['first_name']
      end

      def last_name
        @last_name ||= learner&.last_name || learner_cookie_info['last_name']
      end

      def phone
        @phone ||= learner&.phone&.local_number || learner_cookie_info['phone']
      end

      private

      def partner_program_presenter
        @partner_program_presenter ||= Site::Shared::PartnerProgramPresenter.new(partner_program:, promos_discount_codes: Array.wrap(discount_code))
      end

      def registration
        if instance_variable_defined?(:@registration)
          @registration
        else
          @registration = Registration.find_by(uid: params_registration_uid)
        end
      end

      def learner
        return nil if registration.blank?

        @learner ||= registration.learner
      end

      # @return [Promos::DiscountCode, nil] The discount code object if it exists, otherwise nil.
      def discount_code
        return @discount_code if defined?(@discount_code)

        @discount_code ||= discount_code_code.presence && Promos::DiscountCode.code(discount_code_code).first
      end
    end
  end
end
