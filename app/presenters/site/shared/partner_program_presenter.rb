# frozen_string_literal: true

module Site
  module Shared
    class PartnerProgramPresenter < ApplicationPresenter

      attr_reader :partner_program, :promos_discount_codes

      delegate :certificate_attributes, :partner, :program, :site_partner_program_dataset, :program_name, :program_short_name, to: :partner_program

      delegate :sample_certificate, :overrides, to: :site_partner_program_dataset

      delegate :site_partner_dataset, to: :partner

      delegate :site_program_dataset, :career_title, :name, :short_name, :cohorts, :admissions_schedule_url, to: :program

      delegate :brand_logos_allowed?, :show_our_course_attracts_block?,
        :logo_css, :cobranding_css, :squeeze_page_logo_css, :squeeze_page_cobranding_css,
        :custom_disclaimer, :custom_eva_banner_text, :ab_testing_html, to: :site_partner_dataset

      delegate :expandable_career_map, :expandable_so_you_can_map, :certificate_details,
        :preview_video_url, :preview_video_image, :career_quote_image, :applied_technologies,
        to: :site_program_dataset

      delegate :theme, :name, :formal_name, :short_name, to: :partner, prefix: true

      delegate :course_nomenclature, :certificate_nomenclature, :logo_on_primary_background, :cobranding_text, :logo, to: :partner_theme

      delegate_pricing :list_cents, :actual_cents, :sale_cents, :promos_discount_cents, to: :upfront_pricing
      delegate :savings_percent, :sale_savings_percent, to: :upfront_pricing

      delegate_pricing :actual_cents, to: :recurring_pricing, prefix: :recurring
      delegate :savings_percent, to: :recurring_pricing, prefix: :recurring

      def initialize(partner_program:, promos_discount_codes: nil)
        @partner_program = partner_program
        @promos_discount_codes = Array.wrap(promos_discount_codes)
      end

      def certificate_title
        "#{program_name} #{course_nomenclature}".titlecase
      end

      def certificate_type_name
        certificate_attributes[:certificate_type_name]
      end

      def cohort_presenters
        addable_cohorts.map do |cohort|
          Shared::CohortPresenter.new(cohort:, partner_program:)
        end
      end

      def disclaimer_presenter
        Components::CustomDisclaimerPresenter.new(disclaimer: custom_disclaimer)
      end

      def enroll_url
        @enroll_url ||= UrlBuilder::Site.new(partner_program:).call!(template: :registration)
      end

      def expandable_career_presenters
        expandable_career_map.map do |hash|
          title = hash.keys.first
          list_items = hash.values.first
          Components::Utility::ExpandableListPresenter.new(title:, list_items:)
        end
      end

      def expandable_so_you_can_presenters
        return if expandable_so_you_can_map.blank?

        expandable_so_you_can_map.map do |hash|
          title = hash.keys.first
          list_items = hash.values.first
          Components::Utility::ExpandableListPresenter.new(title:, list_items:)
        end
      end

      def faqs_presenters
        PartnerProgramDataset::BuildFaqPresentersCommand.call!(partner_program_dataset: site_partner_program_dataset)
      end

      def header_banner_presenter
        @header_banner_presenter ||= Components::HeaderBannerPresenter.new(partner_program:, pricing: upfront_pricing)
      end

      def instructors_presenter
        Components::Program::InstructorsPresenter.new(site_program_dataset:, site_partner_dataset:)
      end

      def program_duration_weeks
        ((program.duration + program_extended_content_duration) / 1.week).round
      end

      def program_duration_text
        pluralize(program_duration_weeks, 'week')
      end

      def program_duration_summary
        "#{program_duration_weeks}-Week #{course_nomenclature.titlecase}"
      end

      def testimonials_presenter
        Components::Program::TestimonialsPresenter.new(partner_program:)
      end

      def program_certificate_details
        partner = site_partner_dataset.certificate_name || partner_formal_name
        if site_program_dataset.overrides[:certificate_details_addendum].present?
          parts = [certificate_details, interpolate_certificate_details(site_program_dataset)].compact
          if parts.size > 1
            parts.join(' + ')
          else
            parts.first
          end
        else
          parts = [certificate_details, "#{certificate_nomenclature} from #{partner}"].compact
          if parts.size == 1
            "Earn your #{parts.first}"
          else
            parts.join(' + ')
          end
        end
      end

      def interpolate_certificate_details(site_program_dataset)
        PartnerProgram::InterpolateCommand.new(partner_program:, string: site_program_dataset.overrides[:certificate_details_addendum]).call!
      end

      def eva_banner_presenter
        Components::EvaBannerPresenter.new(partner:, enroll_url:, custom_eva_banner_text:)
      end

      def footer_presenter
        Components::FooterPresenter.new(partner:)
      end

      def site_reimbursement_url
        @site_reimbursement_url ||= UrlBuilder::Site.new(partner_program:).call!(template: :reimbursement)
      end

      def shared_setup_presenter
        Components::PartnerProgram::SharedSetupPresenter.new(partner_program:)
      end

      def company_brands
        @company_brands ||= CompanyBrand.ordered.all
      end

      def applied_technologies_presenter
        Components::Program::AppliedTechnologiesPresenter.new(site_program_dataset:)
      end

      def addable_cohorts
        @addable_cohorts ||= Program::AddableCohortsCommand.call!(program:, partner:)
      end

      def offer_buy_now_pay_later?
        partner.ecom_payment_methods.any?(&:buy_now_pay_later?)
      end

      def career_quote_alt_text
        site_program_dataset.career_quote_alt_text || 'Inspirational quotes about career advancement'
      end

      def cache_key_components
        [
          partner.updated_at,
          partner_theme.updated_at,
          site_partner_dataset.updated_at,
          program.updated_at,
          site_program_dataset.updated_at,
          site_program_dataset.testimonials.map(&:updated_at),
          site_program_dataset.applied_technologies.map(&:updated_at),
          site_program_dataset.instructors.map(&:updated_at),
          partner_program.updated_at,
          site_partner_program_dataset.updated_at,
          global_dataset.updated_at,
          addable_cohorts.map(&:updated_at),
          addable_cohorts.map(&:sections).flatten.map(&:updated_at),
          company_brands.map(&:updated_at),
          promos_discount_codes.map(&:updated_at),
        ]
      end

      private

      def upfront_pricing
        @upfront_pricing ||= Ecom::Pricing::DefaultForPartnerProgram.call!(partner_program:, promos_discount_codes:)
      end

      def recurring_pricing
        @recurring_pricing ||=
          Ecom::Pricing::DefaultForPartnerProgram.call!(
            partner_program:,
            payment_method: Ecom::PaymentMethod.find_by(kind: 'installments'),
            promos_discount_codes:,
          )
      end

      def program_extended_content_duration
        program_extended_content_duration_setting[program.key].to_i.weeks
      end

      def program_extended_content_duration_setting
        @program_extended_content_duration_setting ||=
          global_dataset&.program_extended_content_duration || {}
      end

      def global_dataset
        @global_dataset ||= Site::GlobalDataset.first
      end

    end
  end
end
