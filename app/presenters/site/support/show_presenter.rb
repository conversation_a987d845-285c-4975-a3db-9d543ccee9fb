# frozen_string_literal: true

module Site
  module Support
    class ShowPresenter < ApplicationPresenter
      attr_reader :partner_program, :section_id, :content_tags

      delegate :partner, :program, to: :partner_program
      delegate :site_partner_dataset, to: :partner
      delegate :logo_on_primary_background, :logo_css, :cobranding_text, :cobranding_css,
        :footer_presenter, :enroll_url, to: :partner_program_presenter

      def initialize(partner_program:, section_id: nil, content_tags: nil)
        @partner_program = partner_program
        @section_id = section_id
        @content_tags = content_tags
      end

      def shared_setup_presenter
        @shared_setup_presenter ||= Site::Components::PartnerProgram::SharedSetupPresenter.new(partner_program:)
      end

      def meta_tags_presenter
        @meta_tags_presenter ||= Site::Components::MetaTagsPresenter.new(
          partner_program:,
          page_template: support_page_template,
        )
      end

      def header_banner_presenter
        @header_banner_presenter ||= Site::Components::HeaderBannerPresenter.new(partner_program:)
      end

      def disclaimer_presenter
        @disclaimer_presenter ||= Site::Components::CustomDisclaimerPresenter.new(
          disclaimer: site_partner_dataset&.custom_disclaimer,
        )
      end

      def custom_footer_presenter
        @custom_footer_presenter ||= Site::Components::FooterPresenter.new(partner:)
      end

      def partner_program_presenter
        @partner_program_presenter ||= Site::Shared::PartnerProgramPresenter.new(partner_program:)
      end

      def show_custom_disclaimer?
        site_partner_dataset&.custom_disclaimer.present?
      end

      def canonical_url
        @canonical_url ||= UrlBuilder::Site.new(partner_program:).call!(template: :support)
      end

      def cache_key(*components)
        Digest::SHA2.hexdigest(
          (partner_program_presenter.cache_key_components + components).flatten.join('-'),
        )
      end

      def syllabus_url
        @syllabus_url ||= UrlBuilder::Site.new(partner_program:).call!(template: :syllabus)
      end

      def contact_email
        @contact_email ||= '<EMAIL>'
      end

      # Support options data for cards
      def support_options
        [
          {
            id: 'chat',
            title: 'Chat live',
            description: 'Quick questions? Get answers in real time.',
            icon: chat_icon,
            controller: 'hubspot-chat',
            action: 'click->hubspot-chat#openChat',
            data_attributes: { 'fallback-email' => contact_email },
            css_classes: 'support-card block',
          },
          {
            id: 'meeting',
            title: 'Book a meeting',
            description: 'Need detailed guidance? Book a 15-min call with an advisor.',
            icon: calendar_icon,
            controller: 'calendly',
            action: 'click->calendly#openPopup',
            url: program.admissions_schedule_url,
            data_attributes: { 'calendly-url-value' => program.admissions_schedule_url },
            css_classes: 'support-card block',
          },
          {
            id: 'email',
            title: 'Email us',
            description: 'Not urgent? We\'ll reply within 24 hours.',
            icon: email_icon,
            url: "mailto:#{contact_email}",
            css_classes: 'support-card block',
          },
        ]
      end

      def arrow_icon_svg
        path_data = 'M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z'
        %(<path fill-rule="evenodd" d="#{path_data}" clip-rule="evenodd"/>).html_safe
      end

      def arrow_image
        ActionController::Base.helpers.image_tag('site/support/arrow-icon.png', alt: 'Arrow', class: 'support-card-arrow')
      end

      def sanitized_article_body(article)
        body_content = article[:body].to_s

        # First interpolate variables (e.g., {REIMBURSEMENT_URL}) before sanitization
        interpolated_content = PartnerProgram::InterpolateCommand.call!(
          partner_program:,
          string: body_content,
        )

        # Then sanitize with ActionView's sanitize helper using safe tags and attributes
        sanitized_content = ActionController::Base.helpers.sanitize(
          interpolated_content,
          tags: %w[p br strong em ul ol li a h4 h5 h6 div span],
          attributes: %w[href target rel class],
          remove_empty_elements: true,
        )

        # Additional security: validate and clean href attributes
        cleaned_content = sanitized_content.gsub(/href\s*=\s*["']([^"']+)["']/i) do |match|
          url = ::Regexp.last_match(1).strip
          quote_char = match[-1] # Get the closing quote character

          # Allow only safe protocols
          if url.match?(/\A(https?|mailto):/i) || url.match?(%r{\A[#/]})
            # For external links, ensure they have proper attributes
            if url.match?(/\Ahttps?:/i) && url.exclude?(request_domain)
              "href=#{quote_char}#{url}#{quote_char} target=#{quote_char}_blank#{quote_char} rel=#{quote_char}noopener#{quote_char}"
            else
              match
            end
          else
            # Remove unsafe protocols
            'href="#"'
          end
        end

        cleaned_content.html_safe
      end

      def total_articles_count
        articles.size
      end

      def articles?
        articles.present?
      end

      def articles
        @articles ||= Zendesk::FetchArticlesCommand.call!(
          partner_program:,
        )
      end

      def article_tags(article)
        article.fetch(:content_tag_names, [])
      end

      def available_content_tags
        @available_content_tags ||= begin
          content_tag_mappings.keys.map do |tag_key|
            display_name = tag_key.humanize
            [display_name, tag_key]
          end.sort_by(&:first)
        end
      end

      private

      # Icon helpers - using provided SVG icons with partner secondary color
      def chat_icon
        chat_path = 'M11.5 10.8975C11.5 10.6009 11.588 10.3108 11.7528 10.0642C11.9177 9.81749 12.1519 9.62523 12.426 9.5117C12.7001 ' \
                    '9.39817 13.0017 9.36847 13.2927 9.42634C13.5836 9.48422 13.8509 9.62708 14.0607 9.83686C14.2705 10.0466 14.4133 10.3139 ' \
                    '14.4712 10.6049C14.5291 10.8959 14.4994 11.1975 14.3859 11.4715C14.2723 11.7456 14.0801 11.9799 13.8334 12.1447C13.5867 ' \
                    '12.3095 13.2967 12.3975 13 12.3975C12.6022 12.3975 12.2207 12.2395 11.9394 11.9582C11.6581 11.6769 11.5 11.2953 11.5 ' \
                    '10.8975ZM7.50003 12.3975C7.79671 12.3975 8.08671 12.3095 8.33339 12.1447C8.58006 11.9799 8.77232 11.7456 8.88585 ' \
                    '11.4715C8.99938 11.1975 9.02909 10.8959 8.97121 10.6049C8.91333 10.3139 8.77047 10.0466 8.56069 9.83686C8.35091 9.62708 ' \
                    '8.08364 9.48422 7.79267 9.42634C7.5017 9.36847 7.2001 9.39817 6.92601 9.5117C6.65192 9.62523 6.41765 9.81749 6.25283 ' \
                    '10.0642C6.08801 10.3108 6.00003 10.6009 6.00003 10.8975C6.00003 11.2953 6.15807 11.6769 6.43937 11.9582C6.72068 12.2395 ' \
                    '7.10221 12.3975 7.50003 12.3975ZM18.5 12.3975C18.7967 12.3975 19.0867 12.3095 19.3334 12.1447C19.5801 11.9799 19.7723 ' \
                    '11.7456 19.8859 11.4715C19.9994 11.1975 20.0291 10.8959 19.9712 10.6049C19.9133 10.3139 19.7705 10.0466 19.5607 9.83686C' \
                    '19.3509 9.62708 19.0836 9.48422 18.7927 9.42634C18.5017 9.36847 18.2001 9.39817 17.926 9.5117C17.6519 9.62523 17.4177 ' \
                    '9.81749 17.2528 10.0642C17.088 10.3108 17 10.6009 17 10.8975C17 11.2953 17.1581 11.6769 17.4394 11.9582C17.7207 12.2395 ' \
                    '18.1022 12.3975 18.5 12.3975ZM26 2.89752V18.8975C26 19.428 25.7893 19.9367 25.4142 20.3117C25.0392 20.6868 24.5305 ' \
                    '20.8975 24 20.8975H7.37503L3.30003 24.4175L3.28878 24.4263C2.92883 24.7315 2.47196 24.8985 2.00003 24.8975C1.70624 ' \
                    '24.8969 1.41614 24.832 1.15003 24.7075C0.804869 24.548 0.512902 24.2925 0.308985 23.9716C0.105069 23.6506 -0.00218259 ' \
                    '23.2778 3.36657e-05 22.8975V2.89752C3.36657e-05 2.36709 0.210747 1.85838 0.58582 1.48331C0.960893 1.10824 1.4696 ' \
                    '0.897522 2.00003 0.897522H24C24.5305 0.897522 25.0392 1.10824 25.4142 1.48331C25.7893 1.85838 26 2.36709 26 2.89752ZM24 ' \
                    '2.89752H2.00003V22.8975L6.34628 19.1475C6.52697 18.9882 6.75912 18.8994 7.00003 18.8975H24V2.89752Z'
        %(<svg class="support-card-icon w-8 h-8 text-s" fill="currentColor" viewBox="0 0 26 25" xmlns="http://www.w3.org/2000/svg">
          <path d="#{chat_path}"/>
        </svg>).html_safe
      end

      def calendar_icon
        calendar_path = 'M16.5 2.35736H14.25V1.60736C14.25 1.40845 14.171 1.21768 14.0303 1.07703C13.8897 0.936378 13.6989 0.857361 ' \
                        '13.5 0.857361C13.3011 0.857361 13.1103 0.936378 12.9697 1.07703C12.829 1.21768 12.75 1.40845 12.75 1.60736V2.35736H5.25V' \
                        '1.60736C5.25 1.40845 5.17098 1.21768 5.03033 1.07703C4.88968 0.936378 4.69891 0.857361 4.5 0.857361C4.30109 0.857361 ' \
                        '4.11032 0.936378 3.96967 1.07703C3.82902 1.21768 3.75 1.40845 3.75 1.60736V2.35736H1.5C1.10218 2.35736 0.720644 2.5154 ' \
                        '0.43934 2.7967C0.158035 3.07801 0 3.45954 0 3.85736V18.8574C0 19.2552 0.158035 19.6367 0.43934 19.918C0.720644 20.1993 ' \
                        '1.10218 20.3574 1.5 20.3574H16.5C16.8978 20.3574 17.2794 20.1993 17.5607 19.918C17.842 19.6367 18 19.2552 18 18.8574V' \
                        '3.85736C18 3.45954 17.842 3.07801 17.5607 2.7967C17.2794 2.5154 16.8978 2.35736 16.5 2.35736ZM3.75 3.85736V4.60736C3.75 ' \
                        '4.80627 3.82902 4.99704 3.96967 5.13769C4.11032 5.27834 4.30109 5.35736 4.5 5.35736C4.69891 5.35736 4.88968 5.27834 ' \
                        '5.03033 5.13769C5.17098 4.99704 5.25 4.80627 5.25 4.60736V3.85736H12.75V4.60736C12.75 4.80627 12.829 4.99704 12.9697 ' \
                        '5.13769C13.1103 5.27834 13.3011 5.35736 13.5 5.35736C13.6989 5.35736 13.8897 5.27834 14.0303 5.13769C14.171 4.99704 ' \
                        '14.25 4.80627 14.25 4.60736V3.85736H16.5V6.85736H1.5V3.85736H3.75ZM16.5 18.8574H1.5V8.35736H16.5V18.8574Z'
        %(<svg class="support-card-icon w-8 h-8 text-s" fill="currentColor" viewBox="0 0 18 21" xmlns="http://www.w3.org/2000/svg">
          <path d="#{calendar_path}"/>
        </svg>).html_safe
      end

      def email_icon
        email_path = 'M20.3944 10.7436L3.30281 0.988126C3.01451 0.825831 2.68361 0.755236 2.35418 0.785746C2.02475 0.816256 1.71244 ' \
                     '0.946423 1.45885 1.1589C1.20527 1.37139 1.02245 1.65609 0.934752 1.9751C0.847055 2.2941 0.858643 2.63225 0.967974 ' \
                     '2.9445L4.08414 12.1679L0.967974 21.3922C0.881252 21.6377 0.854668 21.9004 0.890451 22.1582C0.926234 22.4161 1.02334 ' \
                     '22.6616 1.17363 22.8742C1.32391 23.0867 1.52299 23.2602 1.75415 23.3799C1.98531 23.4996 2.24182 23.5621 2.50215 ' \
                     '23.5622C2.78497 23.5617 3.06286 23.4881 3.30891 23.3486L20.3924 13.5769C20.6443 13.4357 20.8542 13.2301 21.0005 12.981C' \
                     '21.1467 12.732 21.2241 12.4485 21.2246 12.1597C21.2251 11.8709 21.1488 11.5871 21.0034 11.3375C20.8581 11.0879 20.6489 ' \
                     '10.8815 20.3974 10.7395L20.3944 10.7436ZM2.50215 21.9345C2.50259 21.9304 2.50259 21.9263 2.50215 21.9223L5.52777 ' \
                     '12.9817H11.4549C11.6707 12.9817 11.8778 12.896 12.0304 12.7434C12.183 12.5907 12.2688 12.3837 12.2688 12.1679C12.2688 ' \
                     '11.952 12.183 11.745 12.0304 11.5924C11.8778 11.4397 11.6707 11.354 11.4549 11.354H5.52777L2.50825 2.41751C2.50725 ' \
                     '2.41175 2.50518 2.40623 2.50215 2.40123L19.5937 12.1506L2.50215 21.9345Z'
        %(<svg class="support-card-icon w-8 h-8 text-s" fill="currentColor" viewBox="0 0 22 24" xmlns="http://www.w3.org/2000/svg">
          <path d="#{email_path}"/>
        </svg>).html_safe
      end

      def request_domain
        # Extract domain from canonical URL for security checks
        @request_domain ||= begin
          uri = URI.parse(canonical_url)
          uri.host
        rescue URI::InvalidURIError
          'ziplines.com' # fallback domain
        end
      end

      def content_tag_mappings
        @content_tag_mappings ||= Zendesk::FilterAndFormatArticlesCommand.new(
          articles: [],
          partner_program:,
        ).content_tag_mappings
      end

      def support_page_template
        Struct.new(:page_title, :meta_title, :meta_description, :meta_keywords, :updated_at).new(
          'Support & FAQs',
          'Support & FAQs',
          "Get answers to your questions about {PROGRAM_NAME} {COURSE_NOMENCLATURE}",
          "support, help, faq, #{partner_program.program.name}",
          Time.current,
        )
      end
    end
  end
end
