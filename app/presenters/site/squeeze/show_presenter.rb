# frozen_string_literal: true

module Site
  module Squeeze
    class ShowPresenter < ApplicationPresenter

      attr_reader :partner_program, :learner_cookie_info

      delegate :partner, :program, :shared_setup_presenter, :brand_logos_allowed?, :show_our_course_attracts_block?,
        :disclaimer_presenter, :faqs_presenters, :sample_certificate, :company_brands, :logo, :program_short_name, :site_partner_dataset,
        :savings_percent, :footer_presenter, :applied_technologies,
        :cobranding_text, :testimonials_presenter, :overrides, :applied_technologies_presenter, :squeeze_page_logo_css, :squeeze_page_cobranding_css,
        :offer_buy_now_pay_later?, :ab_testing_html, :site_program_dataset, to: :partner_program_presenter
      delegate_pricing :list_cents, :actual_cents, :recurring_actual_cents, to: :partner_program_presenter

      delegate :site_squeeze_page_template, to: :partner_program

      delegate :points_info, :sneak_peek_blurb, :services, :skills, :learner_image, :career_option_images, :applied_technology_position,
        :custom_hero_text, :custom_hero_text_v_1, :custom_hero_title_v_1, :hero_blurb_jsonb, :hero_blurb_jsonb_v_1, to: :site_squeeze_page_template

      SNEAK_PEEK_HEADING = "Get a sneak peek"

      def initialize(partner_program:, learner_cookie_info:)
        @partner_program = partner_program
        @learner_cookie_info = learner_cookie_info
      end

      def meta_tags_presenter
        @meta_tags_presenter ||= Components::MetaTagsPresenter.new(partner_program:, page_template: site_squeeze_page_template)
      end

      def syllabus_form_presenter(location:)
        options = {
          submit_button_classes: 'border-p bg-p text-white px-8 py-2 uppercase font-medium w-full cursor-pointer ' \
                                 'hover:underline disabled:cursor-not-allowed',
          heading_classes: nil,
          headline_text: nil,
          email_input_placeholder: "Enter Your Email*",
          phone_input_placeholder: "Phone Number",
        }

        Shared::SyllabusFormPresenter.new(partner_program:, location:, meta_tags_presenter:, template: 'squeeze', options:, learner_cookie_info:)
      end

      def syllabus_form_presenter_v_1(location:)
        options = {
          submit_button_classes: 'border-p bg-black text-white px-8 py-2 uppercase font-medium w-full cursor-pointer text-sbg ' \
                                 'hover:underline disabled:cursor-not-allowed',
          heading_classes: nil,
          headline_text: nil,
          email_input_placeholder: "Email*",
          phone_input_placeholder: "Phone (optional)",
        }

        Shared::SyllabusFormPresenterV1.new(partner_program:, location:, meta_tags_presenter:, template: 'squeeze', options:,
          learner_cookie_info:,
        )
      end

      def global_dataset
        @global_dataset ||= GlobalDataset.first
      end

      def skills_presenters
        skills.map do |hash|
          title = PartnerProgram::InterpolateCommand.call!(partner_program:, string: hash.keys.first)
          descriptions = PartnerProgram::InterpolateNestedArrayCommand.call!(partner_program:, array: hash.values.first)
          Components::Utility::KeyPointsPresenter.new(title:, descriptions:, points_icon: :check, options: { icon_options: { 'stroke-width' => 4 } })
        end
      end

      def services_presenters
        services.map do |hash|
          title = PartnerProgram::InterpolateCommand.call!(partner_program:, string: hash.keys.first)
          descriptions = PartnerProgram::InterpolateNestedArrayCommand.call!(partner_program:, array: hash.values.first)
          Components::Utility::KeyPointsPresenter.new(title:, descriptions:, points_icon: :check, options: { icon_options: { 'stroke-width' => 4 } })
        end
      end

      def hero_text
        overrides.dig(:squeeze_page, :hero_text) || custom_hero_text || "Become a Certified #{program_short_name}"
      end

      def hero_text_v_1
        overrides.dig(:squeeze_page, :custom_hero_text_v_1) || custom_hero_text_v_1 || "Become a Certified #{program_short_name}"
      end

      def hero_blurb_presenters
        hero_blurb_jsonb.map do |hash|
          title = PartnerProgram::InterpolateCommand.call!(partner_program:, string: hash.keys.first)
          descriptions = PartnerProgram::InterpolateNestedArrayCommand.call!(partner_program:, array: hash.values.first)
          Components::Utility::KeyPointsPresenter.new(
            title:,
            descriptions:,
            points_icon: :check,
            options: {
              icon_options: { 'stroke-width' => 4 },
              title_classes: 'text-lg xl:text-lg md:text-md',
            },
          )
        end
      end

      def hero_blurb_presenters_v_1
        hero_blurb_jsonb_v_1.map do |hash|
          descriptions = PartnerProgram::InterpolateNestedArrayCommand.call!(partner_program:, array: hash.values)
          Components::Utility::KeyPointsPresenterV1.new(
            descriptions:,
          )
        end
      end

      def applied_technologies_after_skills?
        applied_technology_position.in?(['after_skills'])
      end

      def applied_technologies_after_services?
        applied_technology_position.in?(['after_services'])
      end

      def canonical_url
        if partner.ziplines?
          "https://www.ziplines.com/courses/#{program.slug}-course"
        else
          UrlBuilder::Site.new(partner_program:).call!(template: :landing)
        end
      end

      def sneak_peek_heading
        site_program_dataset.overrides.dig(:squeeze, :sneak_peek_heading) || SNEAK_PEEK_HEADING
      end

      def cache_key(*components)
        Digest::SHA2.hexdigest(
          (partner_program_presenter.cache_key_components + [
            site_squeeze_page_template.updated_at,
            career_option_images.map(&:updated_at),
          ] + components).flatten.join('-'),
        )
      end


      private

      def partner_program_presenter
        @partner_program_presenter ||= Site::Shared::PartnerProgramPresenter.new(partner_program:)
      end
    end
  end
end
