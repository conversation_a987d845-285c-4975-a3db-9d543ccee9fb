# frozen_string_literal: true

module Remote
  module Hu<PERSON>pot
    class Deal
      class EnrollmentPresenter < ApplicationPresenter
        PRICE_CHANGE_2025_DATE = Date.new(2025, 8, 12)

        EVA_TYPE_HUBSPOT_VALUES = {
          employee: 'emp',
          alumni: 'alumni',
          veteran: 'vet',
        }.with_indifferent_access.freeze

        STATUS_HUBSPOT_VALUES = {
          no_pass: 'No Pass',
          pass: 'Pass',
          certificate_issued: 'Pass',
          unenrolled: 'Unenroll',
          dropped: 'Drop',
          withdrew: 'Withdraw',
          paused: 'Involuntary withdrawal',
        }.with_indifferent_access.freeze

        REFUND_REASON_HUBSPOT_VALUES = {
          drop_unenroll_withdrawal: 'drop_withdraw',
          chargeback: 'chargeback',
          post_transaction_adjustment: 'post_trans_adj',
          duplicate_payment: 'Duplicate Payment',
          other: 'other_refund_reason',
        }.with_indifferent_access.freeze

        attr_reader :enrollment

        delegate :deal, :ecom_order, :ecom_order_item, :registration, :remote_canvas_enrollment, :transfers, to: :enrollment
        delegate :payment, to: :ecom_order
        delegate :payment_method, :payments_transactions, :payments_transaction_charges, :payments_transaction_refunds,
          :refunds, :payments_installment_plan, to: :payment
        delegate :variant, :finance_contract, to: :ecom_order_item

        def initialize(enrollment:)
          @enrollment = enrollment
        end

        def properties
          Time.use_zone(CRM_TIME_ZONE) do
            {
              lms_type: 'student',
              enrollee_type:,
              cc_processing_fee:,
              learner_payment_type:,
              learner_payment_process:,
              learner_status:,
              extended_until:,
              became_paid_enrollee: became_paid_enrollee_at.to_date,
              small_business_target: false,
              **financial_properties,
              finance_relationship: registration.finance_relationship.name,
              third_party_entity: registration.third_party_entity,
              partner_brand_fee__: partner_brand_fee,
              **exit_status_change_properties,
              **transfer_properties,
              lms_learner_access_reinstatement_date:,
              learner_pass_date:,
              **certificate_properties,
              learner_activation_status:,
            }
          end
        end

        def certificate_properties
          {
            certificate_update_date:,
            certificate_url: enrollment.certificate_url,
          }
        end

        def learner_pass_date
          if pass_status_change
            pass_status_change.created_at.in_time_zone(CRM_TIME_ZONE).to_date
          else
            # fallback for migrated data
            certificate_update_date
          end
        end


        private

        def certificate_update_date
          return unless enrollment.certificate_issued_at

          enrollment.certificate_issued_at.in_time_zone(CRM_TIME_ZONE).to_date
        end

        def bookings
          payment.total_amount
        end

        def used_promo_code
          return nil if deal_closed_won_by_clas?

          ecom_order.promos_discount_codes.map(&:humanized_code).join('; ')
        end

        def became_paid_enrollee_at
          @became_paid_enrollee_at ||= deal.closed_at.in_time_zone(CRM_TIME_ZONE)
        end

        def reversal_month_range
          # refunds in same month as enrollment are reversals
          became_paid_enrollee_at.all_month
        end

        def non_reversal_period
          # refunds outside the enrollment month are non reversals
          became_paid_enrollee_at.next_month.beginning_of_month..
        end

        def cash_reversal
          refunded(during: reversal_month_range)
        end

        def cash_non_reversal
          refunded(during: non_reversal_period)
        end

        def collected(during:)
          payments_transaction_charges.select { |t| during.cover? t.transacted_at }.sum(&:amount_amount)
        end

        def refunded(during:)
          -1 * payments_transaction_refunds.select { |t| during.cover? t.transacted_at }.sum(&:amount_amount)
        end

        def nett_collected(during:)
          payments_transactions.select { |t| during.cover? t.created_at }.sum(&:amount_amount)
        end

        def bookings_reversal
          if refunds.last&.reason_post_transaction_adjustment?
            cash_reversal
          else
            bookings_reversal_for_non_adjustments
          end
        end

        def bookings_reversal_for_non_adjustments
          if refunded(during: reversal_month_range).positive?
            bookings - nett_collected(during: reversal_month_range)
          else
            0
          end
        end

        def enrollee_type
          return nil if deal_closed_won_by_clas?

          if registration.reenrolled_from
            're-enroll'
          elsif registration.eva_type
            EVA_TYPE_HUBSPOT_VALUES[registration.eva_type]
          else
            'stand'
          end
        end

        def learner_payment_process
          return nil if deal_closed_won_by_clas?

          if payment_method.offer_on_site? || payment_method.learner_manual?
            'greenfig_online'
          elsif payment_method.partner_invoice?
            'partner_po_check'
          elsif payment_method.learner_invoice?
            'greenfig_corp_po_invoice'
          else
            'other'
          end
        end

        def cc_processing_fee
          return nil if deal_closed_won_by_clas?

          if payment_method.offer_on_site? || payment_method.learner_manual?
            finance_contract.processing_fee_carve_out_rate
          else
            0
          end
        end

        def learner_payment_type
          return nil if deal_closed_won_by_clas?

          if payment_method.kind == 'installments'
            'Installment'
          elsif payment_method.kind == 'buy_now_pay_later'
            'Affirm'
          else
            'Full Payment'
          end
        end

        def learner_status
          status = STATUS_HUBSPOT_VALUES[enrollment.status].to_s
          status = 'Extension' if enrollment.extendable? && enrollment.extended?
          status
        end

        def extended_until
          return '' unless enrollment.extended?

          enrollment.extended_until.in_time_zone(CRM_TIME_ZONE).to_date
        end

        def latest_status_change
          @latest_status_change ||= enrollment.status_changes.last
        end

        def pass_status_change
          enrollment.status_changes.detect(&:status_became_pass?)
        end

        def drop_withdraw_reason
          return '' unless paused_or_exited?

          reason = latest_status_change.reason
          reason.in?(Enrollment::ExitCommand::REASONS) ? reason : 'Other'
        end

        def lms_learner_access_reinstatement_date
          return '' unless enrollment.active_status? && latest_status_change.status_was.in?(Enrollment::EXIT_STATUSES)

          latest_status_change_date
        end

        def latest_status_change_date
          @latest_status_change_date ||= latest_status_change.created_at.in_time_zone(CRM_TIME_ZONE).to_date
        end

        def lms_learner_access_restriction_date
          return '' unless paused_or_exited?

          remote_canvas_enrollment ? '' : latest_status_change_date
        end

        def learner_drop_withdraw_date
          return '' unless paused_or_exited?

          enrollment.paused_status? ? latest_status_change_date : enrollment.exit_requested_on
        end

        def exit_status_change_properties
          {
            drop_withdraw_reason:,
            learner_drop_withdraw_date:,
            lms_learner_access_restriction_date:,
          }
        end

        # learner_payment_ - display name course_price
        #     Meant to be the price for rev share (partner share + principal share + processing fee carve out)
        #     Usually upfront_price - discounts (for manual enrollments, it's just the price paid)
        def financial_properties
          # We conditionally return nil for certain properties. Sending nil allows us to not overwrite the value on Hubspot
          {
            amount: deal_closed_won_by_clas? ? nil : bookings,
            bookings____: deal_closed_won_by_clas? ? nil : bookings,
            used_promo_code:,
            learner_payment_: course_price,
            learner_payment_collected: payment.paid_balance_amount,
            payment_overdue_since_date:,
            **financial_properties_for_refunds,
          }
        end

        def partner_brand_fee
          return nil if deal_closed_won_by_clas?

          finance_contract.fee_rate
        end

        def upfront_payment_method
          if instance_variable_defined?(:@upfront_payment_method)
            @upfront_payment_method
          else
            @upfront_payment_method = Ecom::PaymentMethod.find_by(kind: :upfront)
          end
        end

        def upfront_pricing
          @upfront_pricing ||= Ecom::Pricing::BuildFromOrderCommand.call!(order: ecom_order, payment_method: upfront_payment_method)
        end

        def upfront_cents
          @upfront_cents ||= upfront_pricing.sale_cents
        end

        def upfront_discount
          @upfront_discount ||= ecom_order.promos_discounts.sum do |discount|
            Promos::Discount::CalculateCentsOffCommand.call!(subtotal_cents: upfront_cents, discount:)
          end
        end

        def course_price
          # return nil to not overwrite the value on Hubspot
          # this is to primarily not overwrite the Course price for Enrollments created in CLAS
          return nil if deal.closed_at.in_time_zone(CRM_TIME_ZONE).to_date.before?(PRICE_CHANGE_2025_DATE) || deal_closed_won_by_clas?

          if payment_method.manual_pricing?
            bookings
          else
            (upfront_cents - upfront_discount).sdiv(100.0, round: 2)
          end
        end

        def payment_overdue_since_date
          return "" if payments_installment_plan.blank?
          return "" unless payments_installment_plan.past_due? || payments_installment_plan.unpaid?

          installment_due_at.in_time_zone('Pacific Time (US & Canada)').to_date
        end

        def installment_due_at
          @installment_due_at ||= payments_installment_plan.installments.active.first.due_at
        end

        def financial_properties_for_refunds
          if refunds.any?
            {
              financial_revenue_reverse: bookings_reversal,
              financial_cash_reversal: cash_reversal,
              refund____: cash_non_reversal,
              payment_refund_reason:,
            }
          else
            {
              financial_revenue_reverse: 0,
              financial_cash_reversal: 0,
              refund____: 0,
              payment_refund_reason: '',
            }
          end
        end

        def paused_or_exited?
          enrollment.status.in?(Enrollment::EXIT_STATUSES)
        end

        def payment_refund_reason
          return unless refunds.any?

          REFUND_REASON_HUBSPOT_VALUES[refunds.last.reason].to_s
        end

        def transfer_properties
          {
            is_transfer: ever_been_limited_transfer?,
            learner_transfer_original_cohort:,
          }
        end

        def first_limited_transfer
          return @first_limited_transfer if defined?(@first_limited_transfer)

          @first_limited_transfer = transfers.where(limited: true).first
        end

        def ever_been_limited_transfer?
          first_limited_transfer.present?
        end

        def learner_transfer_original_cohort
          return unless first_limited_transfer

          first_limited_transfer.transferred_from.cohort.starts_on.strftime('%B_%Y').downcase
        end

        def deal_closed_won_by_clas?
          @deal_closed_won_by_clas ||= deal.closed_won_by_clas?
        end

        def learner_activation_status
          return unless not_activated_risk_assessment

          if not_activated_risk_assessment.high_risk?
            'not_activated'
          elsif not_activated_risk_assessment.on_track?
            'activated'
          end
        end

        def not_activated_risk_assessment
          @not_activated_risk_assessment ||= enrollment.learning_delivery_risk_assessments.not_activated.take
        end
      end
    end
  end
end
