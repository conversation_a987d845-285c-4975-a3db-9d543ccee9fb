import { Controller } from "@hotwired/stimulus"

/**
 * This Stimulus controller handles saving content via a button click.
 * It provides a save button that, when clicked, sends a request to a specified URL to save the content.
 * The save operation can be performed using different HTTP methods (default is PATCH).
 * It updates the UI to show the status of the save operation, such as "Saving...", "Saved", or "Error saving" in the saveStatusTarget.
 * Currently it does not send any data with the request, but it can be extended to include additional data if needed.
 */
export default class extends Controller {
  static values = {
    saveUrl: String,
    method: String,
    saveStatusId: String,
    data: Object,
    redirectUrl: String,
  };

  save() {
    this.saveStatus("Saving...")
    
    const requestOptions = {
      method: this.methodValue || "PATCH",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "X-CSRF-Token": document.querySelector('meta[name="csrf-token"]').getAttribute('content')
      }
    }
    
    // Add data to request body if provided
    if (this.hasDataValue && this.dataValue) {
      requestOptions.body = JSON.stringify(this.dataValue)
    }
    
    fetch(this.saveUrlValue, requestOptions).then(response => {
      if (response.ok) {
        this.saveStatus("Saved")
        if (this.hasRedirectUrlValue && this.redirectUrlValue) {
          window.Turbo.visit(this.redirectUrlValue)
        } else {
          window.location.reload()
        }
      } else {
        return response.json().then(json => {
          this.saveStatus("Error saving")
          console.error(json.errors || "Failed to save")
        })
      }
    }).catch(error => {
      this.saveStatus("Error saving")
      console.error("Error saving content:", error);
    });
  }


  saveStatus(message) {
    if (this.hasSaveStatusIdValue) {
      const saveStatusElement = document.getElementById(this.saveStatusIdValue)
      if (saveStatusElement) {
        saveStatusElement.textContent = message

        if (message === "Saved") {
          setTimeout(() => {
            saveStatusElement.textContent = ""
          }, 2000)
        }
      }
    }
  }
}