import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = ["trigger", "content", "icon"];

  connect() {
    this.isOpen = false;
    this.animating = false;

    // Initialize proper ARIA attributes
    this.initializeAccessibility();

    // Handle keyboard navigation
    this.bindKeyboardEvents();
  }

  disconnect() {
    this.unbindKeyboardEvents();
  }

  initializeAccessibility() {
    try {
      // Ensure proper ARIA setup
      if (this.hasTriggerTarget) {
        this.triggerTarget.setAttribute("aria-expanded", "false");
        this.triggerTarget.setAttribute("role", "button");

        // Generate unique IDs if not present
        if (!this.triggerTarget.id) {
          this.triggerTarget.id = `accordion-trigger-${Math.random().toString(36).substring(2, 11)}`;
        }

        if (this.hasContentTarget && !this.contentTarget.id) {
          this.contentTarget.id = `accordion-content-${Math.random().toString(36).substring(2, 11)}`;
        }

        // Set up aria-controls relationship
        if (this.hasContentTarget) {
          this.triggerTarget.setAttribute(
            "aria-controls",
            this.contentTarget.id,
          );
          this.contentTarget.setAttribute(
            "aria-labelledby",
            this.triggerTarget.id,
          );
        }
      }
    } catch (error) {
      console.error("Failed to initialize accordion accessibility:", error);
    }
  }

  bindKeyboardEvents() {
    this.keydownHandler = this.handleKeydown.bind(this);
    if (this.hasTriggerTarget) {
      this.triggerTarget.addEventListener("keydown", this.keydownHandler);
    }
  }

  unbindKeyboardEvents() {
    if (this.keydownHandler && this.hasTriggerTarget) {
      this.triggerTarget.removeEventListener("keydown", this.keydownHandler);
    }
  }

  handleKeydown(event) {
    // Handle Enter and Space keys for accessibility
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault();
      this.toggle();
    }
  }

  toggle() {
    if (this.animating) return;

    try {
      if (this.isOpen) {
        this.close();
      } else {
        this.open();
      }
    } catch (error) {
      console.error("Failed to toggle accordion:", error);
      this.handleError();
    }
  }

  open() {
    if (this.animating || this.isOpen) return;

    try {
      this.animating = true;
      this.isOpen = true;

      // Update ARIA attributes
      if (this.hasTriggerTarget) {
        this.triggerTarget.setAttribute("aria-expanded", "true");
      }

      // Calculate height before expanding
      if (this.hasContentTarget) {
        this.contentTarget.style.display = "block";
        const height = this.contentTarget.scrollHeight;
        this.contentTarget.style.display = "";

        // Expand content with smooth animation
        this.contentTarget.style.maxHeight = height + "px";
        this.contentTarget.classList.remove("max-h-0");
      }

      // Rotate icon
      if (this.hasIconTarget) {
        this.iconTarget.classList.add("transform", "rotate-180");
      }

      // Reset animation flag after transition
      setTimeout(() => {
        this.animating = false;
        // Set height to auto for dynamic content
        if (this.hasContentTarget) {
          this.contentTarget.style.maxHeight = "none";
        }
      }, 300);
    } catch (error) {
      this.animating = false;
      console.error("Failed to open accordion:", error);
      this.handleError();
    }
  }

  close() {
    if (this.animating || !this.isOpen) return;

    try {
      this.animating = true;
      this.isOpen = false;

      // Update ARIA attributes
      if (this.hasTriggerTarget) {
        this.triggerTarget.setAttribute("aria-expanded", "false");
      }

      // Set explicit height before collapsing for smooth animation
      if (this.hasContentTarget) {
        this.contentTarget.style.maxHeight =
          this.contentTarget.scrollHeight + "px";

        // Force reflow
        this.contentTarget.offsetHeight;

        // Collapse content
        this.contentTarget.style.maxHeight = "0px";
        this.contentTarget.classList.add("max-h-0");
      }

      // Reset icon
      if (this.hasIconTarget) {
        this.iconTarget.classList.remove("transform", "rotate-180");
      }

      // Reset animation flag after transition
      setTimeout(() => {
        this.animating = false;
      }, 300);
    } catch (error) {
      this.animating = false;
      console.error("Failed to close accordion:", error);
      this.handleError();
    }
  }

  // Public method to force close accordion regardless of current state
  forceClose() {
    if (this.animating) {
      // If animating, wait and try again
      setTimeout(() => this.forceClose(), 50);
      return;
    }

    try {
      this.isOpen = false;
      this.animating = false;

      // Update ARIA attributes
      if (this.hasTriggerTarget) {
        this.triggerTarget.setAttribute("aria-expanded", "false");
      }

      // Force collapse content immediately without animation
      if (this.hasContentTarget) {
        this.contentTarget.style.maxHeight = "0px";
        this.contentTarget.classList.add("max-h-0");
      }

      // Reset icon
      if (this.hasIconTarget) {
        this.iconTarget.classList.remove("transform", "rotate-180");
      }
    } catch (error) {
      console.error("Failed to force close accordion:", error);
      this.handleError();
    }
  }

  handleError() {
    // Reset to a safe state if something goes wrong
    this.animating = false;

    try {
      if (this.hasContentTarget) {
        this.contentTarget.style.maxHeight = "";
        this.contentTarget.classList.remove("max-h-0");
      }

      if (this.hasTriggerTarget) {
        this.triggerTarget.setAttribute(
          "aria-expanded",
          this.isOpen ? "true" : "false",
        );
      }


      if (this.hasIconTarget) {
        this.iconTarget.classList.remove("transform", "rotate-180");
      }
    } catch (resetError) {
      console.error("Failed to reset accordion state:", resetError);
    }
  }
}
