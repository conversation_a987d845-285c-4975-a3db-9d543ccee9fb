
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["segment"]

  connect() {
    const segments = this.segmentTargets
    const firstSegmentOffset = parseInt(segments[0].getAttribute("stroke-dashoffset"), 10)
    let preSegmentsTotalLength = parseFloat(segments[0].dataset.per)

    for (let i = 0; i < segments.length; i++) {
      const segment = segments[i]
      const percent = parseFloat(segment.dataset.per)
      const strokeDasharray = `${percent} ${100 - percent}`
      segment.style.strokeDasharray = strokeDasharray

      if (i !== 0) {
        const strokeDashoffset = `${100 - preSegmentsTotalLength + firstSegmentOffset}`
        segment.style.strokeDashoffset = strokeDashoffset
        preSegmentsTotalLength += percent
      }
    }
  }
}
