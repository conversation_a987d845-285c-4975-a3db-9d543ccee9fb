import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static values = {
    url: String
  }

  connect() {
    window.addEventListener('load', () => {
      const urlParams = new URLSearchParams(window.location.search)
      if (urlParams.get('modal') === '1') {
        this.triggerCalendly()
      }
    })
  }

  // Handle click to open Calendly popup
  openPopup(event) {
    event.preventDefault()

    // Ensure Calendly is available
    if (typeof window.Calendly !== 'undefined' && window.Calendly.initPopupWidget) {
      try {
        window.Calendly.initPopupWidget({ url: this.urlValue })
      } catch (error) {
        console.error('Failed to open Calendly widget:', error)
        // Fallback: navigate to Calendly URL
        window.open(this.urlValue, '_blank', 'noopener,noreferrer')
      }
    } else {
      // Fallback if Calendly script not loaded
      window.open(this.urlValue, '_blank', 'noopener,noreferrer')
    }

    return false
  }

  triggerCalendly() {
    this.element.click()
  }
}
