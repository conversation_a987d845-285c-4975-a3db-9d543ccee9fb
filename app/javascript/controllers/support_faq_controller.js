import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = [
    "filterSelect",
    "filterContainer",
    "articlesContainer",
    "faqItem",
    "noResults",
    "loadingState",
    "errorState",
  ];
  static values = {
    total: Number,
  };

  connect() {
    this.originalArticles = Array.from(this.faqItemTargets);
    this.isLoading = false;

    // Cache DOM elements for performance
    this.cachedElements = new Map();
    this.cacheElements();

    // Initialize from URL parameters
    this.initializeFromURL();
  }

  disconnect() {
    // Cleanup if needed
  }

  cacheElements() {
    this.originalArticles.forEach((item, index) => {
      this.cachedElements.set(index, {
        item: item,
        tags: item.dataset.tags ? item.dataset.tags.split(",") : [],
      });
    });
  }

  initializeFromURL() {
    try {
      const url = new URL(window.location);
      const filterParam = url.searchParams.get("content_tags");

      if (filterParam && filterParam !== "all") {
        if (this.hasFilterSelectTarget) {
          this.filterSelectTarget.value = filterParam;
          this.currentFilter = filterParam;
          this.filterArticles();
        }
      }
    } catch (error) {
      console.warn("Failed to initialize from URL parameters:", error);
    }
  }

  filterByTag(event) {
    try {
      this.currentFilter = event.currentTarget.value;
      this.collapseAllAccordions();
      this.filterArticles();
    } catch (error) {
      console.error("Filter failed:", error);
      this.handleError("Filter functionality is temporarily unavailable");
    }
  }

  filterArticles() {
    if (this.isLoading) return;

    try {
      this.setLoadingState(true);
      let visibleCount = 0;

      this.cachedElements.forEach((cached) => {
        // Check filter match
        const filterMatch =
          this.currentFilter === "all" ||
          cached.tags.includes(this.currentFilter);

        if (filterMatch) {
          cached.item.style.display = "block";
          cached.item.setAttribute("aria-hidden", "false");
          visibleCount++;
        } else {
          cached.item.style.display = "none";
          cached.item.setAttribute("aria-hidden", "true");
        }
      });

      // Show/hide no results message
      if (visibleCount === 0) {
        this.noResultsTarget.classList.remove("hidden");
        this.noResultsTarget.setAttribute("aria-hidden", "false");
      } else {
        this.noResultsTarget.classList.add("hidden");
        this.noResultsTarget.setAttribute("aria-hidden", "true");
      }

      // Update URL with current filter
      this.updateURL();

      // Announce results to screen readers
      this.announceResults(visibleCount);
    } catch (error) {
      console.error("Filter failed:", error);
      this.handleError("Unable to filter articles");
    } finally {
      this.setLoadingState(false);
    }
  }

  updateURL() {
    try {
      if (this.currentFilter !== "all") {
        const url = new URL(window.location);
        url.searchParams.set("content_tags", this.currentFilter);
        window.history.replaceState({}, "", url);
      } else {
        const url = new URL(window.location);
        url.searchParams.delete("content_tags");
        window.history.replaceState({}, "", url);
      }
    } catch (error) {
      console.warn("Failed to update URL:", error);
    }
  }

  announceResults(count) {
    // Create or update screen reader announcement
    let announcement = document.getElementById("filter-results-announcement");
    if (!announcement) {
      announcement = document.createElement("div");
      announcement.id = "filter-results-announcement";
      announcement.setAttribute("aria-live", "polite");
      announcement.setAttribute("aria-atomic", "true");
      announcement.className = "sr-only";
      document.body.appendChild(announcement);
    }

    const message =
      count === 0
        ? "No articles found matching your criteria"
        : `${count} article${count === 1 ? "" : "s"} found`;

    announcement.textContent = message;
  }

  setLoadingState(loading) {
    this.isLoading = loading;

    if (loading) {
      this.filterContainerTarget?.setAttribute("aria-busy", "true");
    } else {
      this.filterContainerTarget?.setAttribute("aria-busy", "false");
    }
  }

  collapseAllAccordions() {
    try {
      // Find all accordion controllers within the FAQ items
      let collapsedCount = 0;
      this.faqItemTargets.forEach((faqItem) => {
        const accordionController =
          this.application.getControllerForElementAndIdentifier(
            faqItem,
            "accordion",
          );
        if (accordionController && accordionController.forceClose) {
          // Use the accordion's forceClose method for reliable closing
          accordionController.forceClose();
          collapsedCount++;
        }
      });

      // Log if no accordion controllers were found for debugging
      if (collapsedCount === 0) {
        console.warn("No accordion controllers found for collapsing");
      }
    } catch (error) {
      console.error("Failed to collapse accordions:", error);
    }
  }

  handleError(message) {
    // Create or update error announcement
    let errorElement = document.getElementById("filter-error-announcement");
    if (!errorElement) {
      errorElement = document.createElement("div");
      errorElement.id = "filter-error-announcement";
      errorElement.setAttribute("aria-live", "assertive");
      errorElement.setAttribute("role", "alert");
      errorElement.className = "sr-only";
      document.body.appendChild(errorElement);
    }

    errorElement.textContent = message;

    // Log error for debugging
    console.error("Support FAQ Error:", message);
  }
}
