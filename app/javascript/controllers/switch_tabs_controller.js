
import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = ["tab"]
  static values = {
    activeClass: String,
    inactiveClass: String
  }

  connect() {
    const urlParams = new URLSearchParams(window.location.search);
    const activeTabParam = urlParams.get("tab");
    let activeTab = this.tabTargets.find(tab => {
      return tab.href.includes(`tab=${activeTabParam}`);
    });
    if (!activeTab) {
      activeTab = this.tabTargets[0];
    }

    this.selectTab(activeTab);
  }

  selectTab(tab) {
    this.tabTargets.forEach(tab => {
      tab.classList.remove(...this.activeClassValue.split(" "));
      tab.classList.add(...this.inactiveClassValue.split(" "));
    });

    tab.classList.remove(...this.inactiveClassValue.split(" "));
    tab.classList.add(...this.activeClassValue.split(" "));
  }
}




