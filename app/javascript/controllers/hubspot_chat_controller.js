import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static values = {
    maxRetries: { type: Number, default: 10 },
    retryDelay: { type: Number, default: 500 },
  };

  connect() {
    this.retryCount = 0;
  }

  // Main action to open the chat widget
  openChat(event) {
    event.preventDefault();

    // Add visual feedback immediately
    this.element.classList.add("loading");

    // Try to open the widget with retry logic
    this.tryOpenWidget();
  }

  tryOpenWidget() {
    if (this.isHubSpotReady()) {
      this.openHubSpotWidget();
    } else if (this.retryCount < this.maxRetriesValue) {
      this.retryCount++;
      setTimeout(() => this.tryOpenWidget(), this.retryDelayValue);
    } else {
      console.error("Failed to open HubSpot chat");
    }
  }

  isHubSpotReady() {
    return (
      typeof window.HubSpotConversations !== "undefined" &&
      window.HubSpotConversations.widget &&
      typeof window.HubSpotConversations.widget.open === "function"
    );
  }

  openHubSpotWidget() {
    try {
      window.HubSpotConversations.widget.open();
      this.handleSuccess();
    } catch (error) {
      console.error("Failed to open HubSpot chat:", error);
    }
  }

  handleSuccess() {
    // Reset visual state
    this.element.classList.remove("loading");

    // Optional: Fire a custom event for analytics
    this.dispatch("opened", { detail: { source: "support_page" } });
  }

  // Alternative method using URL hash
  openChatWithHash(event) {
    event.preventDefault();

    // This method appends #hs-chat-open to URL which auto-opens the widget
    const currentUrl = new URL(window.location);
    currentUrl.hash = "hs-chat-open";
    window.history.replaceState({}, "", currentUrl);

    // Manually trigger if needed
    if (this.isHubSpotReady()) {
      try {
        window.HubSpotConversations.widget.open();
      } catch (error) {
        console.error("Failed to open HubSpot chat via hash method:", error);
      }
    }
  }
}
