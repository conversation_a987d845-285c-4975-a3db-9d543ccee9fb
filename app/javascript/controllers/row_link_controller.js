import { Controller } from "@hotwired/stimulus"

// Stimulus controller for making table rows clickable with Turbo frame support
// it advances URL without reloading the page, coupled with turbo_stream replace in the rails controller
// rows should have .row class and data attributes for row-link controller
export default class extends Controller {
  static values = { url: String }
  static targets = ["row"]

  navigate(event) {
    let currentSelectedRow = document.querySelector('.row.selected')
    if (currentSelectedRow) { currentSelectedRow.classList.remove('selected');}
    this.rowTarget.classList.add('selected');

    // Don't navigate if clicking on a link or button
    if (event.target.closest('a, button')) {
      return
    }
    
    if (this.hasUrlValue) {
      fetch(this.urlValue, {
        headers: { 
          'Accept': 'text/vnd.turbo-stream.html',
          'Turbo-Frame': 'task_modal'
        },
      })
      .then(response => response.text())
      .then(html => {
        Turbo.renderStreamMessage(html);
        history.pushState(null, '', this.urlValue);
      })
      .catch(error => console.error('Error:', error));
    } 
  }
}