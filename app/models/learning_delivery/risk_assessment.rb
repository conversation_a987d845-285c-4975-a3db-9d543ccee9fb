# frozen_string_literal: true

# == Schema Information
#
# Table name: learning_delivery_risk_assessments
#
#  id            :bigint           not null, primary key
#  assessed_at   :datetime         not null
#  risk_details  :jsonb            not null
#  risk_level    :integer          default("on_track"), not null
#  risk_type     :integer          not null
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  enrollment_id :bigint           not null
#
# Indexes
#
#  idx_learning_delivery_risk_assessments_on_enrollment_and_type  (enrollment_id,risk_type) UNIQUE
#  index_learning_delivery_risk_assessments_on_enrollment_id      (enrollment_id)
#
# Foreign Keys
#
#  fk_rails_...  (enrollment_id => enrollments.id) ON DELETE => cascade
#
module LearningDelivery
  class RiskAssessment < ApplicationRecord
    include HasStatus

    self.authorized_resource_group = 'Learning Delivery'

    has_paper_trail

    has_status :risk_type
    has_status :risk_level

    belongs_to :enrollment

    has_one :learner, through: :enrollment

    # Code elsewhere (ie Enrollment.highest_risk_assessment) higher risk is higher int value
    RISK_LEVEL_DEFINITION = {
      on_track: [0, 'On Track'],
      low_risk: [1, 'Low Risk'],
      high_risk: [2, 'High Risk'],
    }.freeze

    rich_enum risk_level: RISK_LEVEL_DEFINITION, alt: 'name'

    rich_enum risk_type: {
      not_activated: [0, 'Canvas account not activated'],
      missing_assignments: [1, 'Missing assignments'],
      late_assignments: [2, 'Late assignments'],
      no_recent_activity: [3, 'No recent activity'],
    }, alt: 'name'

    validates :risk_type, presence: true
    validates :risk_type, uniqueness: { scope: :enrollment_id }
    validates :risk_level, presence: true
    validates :risk_details, presence: true
    validates :assessed_at, presence: true
  end
end
