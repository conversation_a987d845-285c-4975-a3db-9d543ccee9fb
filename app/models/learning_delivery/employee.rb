# frozen_string_literal: true

# == Schema Information
#
# Table name: learning_delivery_employees
#
#  id                      :bigint           not null, primary key
#  availability_notes      :text
#  canvas_registration_url :string
#  company_email           :string
#  company_profile_url     :string
#  first_name              :string           not null
#  joined_on               :date
#  last_name               :string           not null
#  linked_in_url           :string
#  max_concurrent_sections :integer
#  notes                   :text
#  on_website              :boolean          default(FALSE), not null
#  personal_email          :string           not null
#  sis_key                 :string
#  status                  :integer          default("onboarding"), not null
#  time_zone               :string           not null
#  uid                     :string           not null
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  admin_user_id           :bigint
#
# Indexes
#
#  index_learning_delivery_employees_on_admin_user_id   (admin_user_id)
#  index_learning_delivery_employees_on_company_email   (company_email) UNIQUE
#  index_learning_delivery_employees_on_personal_email  (personal_email) UNIQUE
#  index_learning_delivery_employees_on_sis_key         (sis_key) UNIQUE
#  index_learning_delivery_employees_on_status          (status)
#  index_learning_delivery_employees_on_type            (type)
#  index_learning_delivery_employees_on_uid             (uid) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (admin_user_id => admin_users.id)
#
module LearningDelivery
  class Employee < ApplicationRecord
    include HasUid
    include TimeZoneHelper
    include HasStatus

    self.ignored_columns += %w[type]

    self.authorized_resource_group = 'Learning Delivery'

    has_uid prefix: 'LD-E'
    has_status

    ASSIGNABLE_STATUSES = %w[onboarding active].freeze

    scope :personal_email, ->(email) { where(personal_email: email&.downcase) }
    scope :company_email, ->(email) { where(company_email: email&.downcase) }
    scope :with_sections, -> { includes(:instructor_sections, :advocate_sections, :grader_sections) }
    scope :with_live_sessions, -> { includes(:leader_live_sessions, :moderator_live_sessions) }
    scope :with_live_sesssion_reviews, -> { includes(leader_live_sessions: :reviews, moderator_live_sessions: :reviews) }
    scope :advocates, -> { joins(:employee_roles).where(learning_delivery_employee_roles: { role_id: LearningDelivery::Role.advocate&.id }) }
    scope :instructors, -> { joins(:employee_roles).where(learning_delivery_employee_roles: { role_id: LearningDelivery::Role.instructor&.id }) }
    scope :graders, -> { joins(:employee_roles).where(learning_delivery_employee_roles: { role_id: LearningDelivery::Role.grader&.id }) }

    has_status :abbreviation

    has_one_attached :headshot
    has_one_attached :resume

    belongs_to :admin_user, optional: true

    has_many :competencies, dependent: :destroy, class_name: 'LearningDelivery::Competency', inverse_of: false
    has_many :primary_competencies, -> { where(level: :primary) }, class_name: 'LearningDelivery::Competency', inverse_of: false
    has_many :secondary_competencies, -> { where(level: :secondary) }, class_name: 'LearningDelivery::Competency', inverse_of: false

    has_many :programs, through: :competencies
    has_many :primary_programs, through: :primary_competencies, source: :program
    has_many :secondary_programs, through: :secondary_competencies, source: :program

    has_many :instructor_sections, class_name: 'Section', foreign_key: 'instructor_id', inverse_of: :instructor
    has_many :advocate_sections, class_name: 'Section', foreign_key: 'advocate_id', inverse_of: :advocate
    has_many :grader_sections, class_name: 'Section', foreign_key: 'grader_id', inverse_of: :grader


    has_many :leader_live_sessions, class_name: 'LiveSession', foreign_key: 'leader_id', inverse_of: :leader
    has_many :moderator_live_sessions, class_name: 'LiveSession', foreign_key: 'moderator_id', inverse_of: :moderator

    has_many :employee_roles, dependent: :destroy, class_name: 'LearningDelivery::EmployeeRole'
    has_many :roles, through: :employee_roles, class_name: 'LearningDelivery::Role'

    has_many :availabilities, class_name: 'LearningDelivery::Availability', dependent: :destroy
    has_many :availability_slots, through: :availabilities, class_name: 'LearningDelivery::AvailabilitySlot'

    has_many :activities, class_name: 'LearningDelivery::Activity', dependent: :destroy

    has_many :lms_submissions, through: :grader_sections, class_name: 'Lms::Submission', source: :lms_submissions

    accepts_nested_attributes_for :competencies, allow_destroy: true

    rich_enum status: {
      onboarding: [0, 'Onboarding'],
      active: [1, 'Active'],
      paused: [2, 'Paused'],
      deactivated: [3, 'Deactived'],
    }, alt: 'name'

    validates :first_name, presence: true
    validates :last_name, presence: true
    validates :personal_email, format: { with: URI::MailTo::EMAIL_REGEXP }, presence: true, uniqueness: true
    validates :company_email, format: { with: URI::MailTo::EMAIL_REGEXP }, allow_blank: true, uniqueness: true
    validates :max_concurrent_sections, inclusion: { in: [nil, 1, 2] }, allow_nil: true
    validates :time_zone, inclusion: { in: ActiveSupport::TimeZone.all.map(&:name) }, allow_blank: true
    validates :joined_on, presence: true
    validates :status, presence: true

    before_validation :set_sis_key, on: :create

    def personal_email=(new_email)
      self[:personal_email] = new_email&.downcase
    end

    def company_email=(new_email)
      self[:company_email] = new_email&.downcase
    end

    def primary_email
      company_email.presence || personal_email
    end

    def full_name
      [first_name, last_name].join(' ')
    end

    def sections
      (instructor_sections + advocate_sections + grader_sections).uniq
    end

    def live_sessions
      (leader_live_sessions + moderator_live_sessions).uniq
    end

    def live_session_reviews
      live_sessions.flat_map(&:reviews)
    end

    def active_admin_display_name
      full_name
    end

    def score
      rand(0...5.0).round(1)
    end

    def instructor?
      roles.include?(Role.instructor)
    end

    def advocate?
      roles.include?(Role.advocate)
    end

    def grader?
      roles.include?(Role.grader)
    end

    def employee_current_availabilities
      availability_slots.where("EXTRACT(MONTH FROM date) = ?", Date.current.month)&.map(&:day_of_the_week)
    end

    private

    def set_sis_key
      self.sis_key ||= humanized_uid
    end
  end
end
