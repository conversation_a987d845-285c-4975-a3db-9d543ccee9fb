# frozen_string_literal: true

# == Schema Information
#
# Table name: learning_delivery_activities
#
#  id            :bigint           not null, primary key
#  activity_type :integer          not null
#  description   :text
#  metadata      :json
#  target_type   :string
#  title         :string           not null
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  employee_id   :bigint           not null
#  target_id     :bigint
#
# Indexes
#
#  idx_on_activity_type_employee_id_783bac81e2        (activity_type,employee_id)
#  idx_on_employee_id_created_at_b1ebf93a5d           (employee_id,created_at)
#  index_learning_delivery_activities_on_employee_id  (employee_id)
#  index_learning_delivery_activities_on_target       (target_type,target_id)
#
# Foreign Keys
#
#  fk_rails_...  (employee_id => learning_delivery_employees.id)
#
module LearningDelivery
  class Activity < ApplicationRecord
    include HasStatus

    self.authorized_resource_group = 'Learning Delivery'

    has_status :activity_type

    rich_enum activity_type: {
      grade: [0, 'Graded Submission'],
      task: [2, 'Completed Task'],
    }, alt: 'name'

    belongs_to :employee, class_name: 'LearningDelivery::Employee'
    belongs_to :target, polymorphic: true, optional: true

    validates :activity_type, presence: true
    validates :title, presence: true
  end
end
