# frozen_string_literal: true

# == Schema Information
#
# Table name: learning_delivery_task_templates
#
#  id             :bigint           not null, primary key
#  description    :text
#  reason         :text
#  recommendation :text
#  sub_type       :string
#  task_type      :integer          default("reach_out"), not null
#  title          :string
#  uid            :string
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#
# Indexes
#
#  index_task_templates_on_task_type_and_sub_type  (task_type,sub_type) UNIQUE
#
module LearningDelivery
  class TaskTemplate < ApplicationRecord
    include HasUid

    self.authorized_resource_group = 'Learning Delivery'

    has_uid prefix: 'LD-TT'

    rich_enum task_type: {
      reach_out: [0, 'Reach Out'],
      announcement: [1, 'Announcement'],
      remove_learner: [2, 'Remove Learner'],
      fix_merged_remote_contact: [3, 'Fix Merged Remote Contact'],
      general: [4, 'General'],
      resolve_duplicate_enrollment: [5, 'Resolve Duplicate Enrollment'],
    }, alt: 'name'

    validates :task_type, presence: true
    validates :sub_type, uniqueness: { scope: :task_type }
  end
end
