# frozen_string_literal: true

# == Schema Information
#
# Table name: remote_resources
#
#  id               :bigint           not null, primary key
#  core_record_type :string           not null
#  fetched_at       :datetime
#  key              :string           default(""), not null
#  published_at     :datetime
#  type             :string           not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  core_record_id   :bigint           not null
#
# Indexes
#
#  index_remote_resources_on_core_record                   (core_record_type,core_record_id)
#  index_remote_resources_on_type_and_key_and_core_record  (type,key,core_record_type,core_record_id) UNIQUE
#
module Remote
  module Hubspot
    class Product < Remote::Resource
      include BaseActions

      delegate :partner, :program, :site_pages, to: :core_record
      delegate :theme, to: :partner, prefix: true
      delegate :theme, to: :program, prefix: true

      alias_method :partner_program, :core_record

      cattr_reader(:alternate_key_property) { 'hs_sku' }
      def alternate_key
        env_id = case Rails.env
        when 'production'  then ''
        when 'staging'     then 'STG'
        when 'development' then 'DEV'
        else 'TEST'
        end
        [env_id, program.key, partner.slug]
          .map(&:strip).compact_blank!.join('::')
      end

      def remote_publishable?
        super &&
          [partner_theme, program_theme, partner.remote_hubspot_deal_partner_channel,
           program.remote_hubspot_deal_active_program,
           program.remote_hubspot_deal_product_program,
           program.remote_hubspot_deal_program_name_abbreviated,].all?(&:present?)
      end

      def default_payload
        {
          properties: {
            # Partner Attributes
            **partner_branding_elements_properties,
            partner_brand_fee__: partner.finance_relationships.default.first.active_contract.fee_rate,
            payment_installments: partner.enabled_payment_methods.any? { |m| m.ecom_payment_method.installments? },
            payment_affirm: buy_now_pay_later_enabled?,
            # Program Attributes
            program_name_abbreviated: program.remote_hubspot_deal_program_name_abbreviated.key,
            program_career_title: program.remote_hubspot_deal_program_career_title&.key.to_s, # optional for some programs
            active_program: program.remote_hubspot_deal_active_program.key,
            product_program: program.remote_hubspot_deal_product_program.key,
            # PartnerProgram attributes
            partner_program_name: partner_program.program_name,
            **pricing_properties,
            # Hubspot Product Attributes
            name: partner_program.name,
            hs_sku: alternate_key,
            hs_product_type: "service",
          },
        }.with_indifferent_access
      end

      def nomenclature_payload
        {
          properties: {
            'partner_course_nomenclature' => partner_theme.course_nomenclature,
            'partner_cert_nomenclature' => partner_theme.certificate_nomenclature,
          },
        }.with_indifferent_access
      end

      def partner_branding_elements_payload
        {
          properties: partner_branding_elements_properties,
        }.with_indifferent_access
      end

      def partner_branding_elements_properties
        {
          partner_channel: partner.remote_hubspot_contact_partner_channel.key,
          partner_brand_slug: partner.slug,
          partner_formal_name: partner.formal_name,
          partner_short_name: partner.short_name,
          **partner_theme_properties,
          **partner_program_url_properties,
          **nomenclature_payload[:properties],
        }.with_indifferent_access
      end

      def partner_theme_payload
        {
          properties: partner_theme_properties,
        }.with_indifferent_access
      end

      def partner_theme_properties
        {
          partner_from_email: partner_theme.from_email,
          partner_reply_email: partner_theme.reply_to_email,
          partner_primary_color: partner_theme.primary_color,
          partner_secondary_color: partner_theme.secondary_color,
          partner_font_color_primary_background: partner_theme.font_color_on_primary_background,
          partner_font_color_secondary_background: partner_theme.font_color_on_secondary_background,
          partner_primary_logo: partner_theme.logo.permanent_url,
          partner_logo_primary_background: partner_theme.logo_on_primary_background.permanent_url,
          partner_primary_color_pixel: partner_theme.primary_color_pixel.permanent_url,
          cobranding_text: partner_theme.cobranding_text.to_s,
        }.with_indifferent_access
      end

      def partner_program_url_properties
        {
          partner_gtku_url: site_pages.get_to_know_you_template.first.url,
          partner_product_reimbursement_url: site_pages.reimbursement_template.first.url,
          partner_product_landing_url: site_pages.landing_template.first.url,
          short_landing_url: site_pages.landing_template.first.short_url,
          partner_product_syllabus_url: site_pages.syllabus_template.first.url,
          short_syllabus_url: site_pages.syllabus_template.first.short_url,
          partner_product_enroll_url: registration_url,
          short_enroll_url: registration_short_url,
          partner_product_curriculum_anchor: site_pages.curriculum_template.first.url,
          short_infosession_anchor_url: site_pages.curriculum_template.first.short_url,
          short_squeeze_url: site_pages.squeeze_template.first.short_url,
          partner_product_calendly_landing_url: site_pages.landing_template.first.url(modal: 1),
          reviews_anchor: site_pages.landing_template.first.url(anchor: 'testimonials'),
          # In Core we don't accept payments without a registration, so send users to the registration page by default
          partner_product_payment_url: registration_url,
          short_payment_url: registration_short_url,
        }.with_indifferent_access
      end

      def pricing_payload
        { properties: pricing_properties }.with_indifferent_access
      end

      def remote_link
        I18n.t('remote_link', scope: i18n_scope, app_origin:, portal:, remote_label: alternate_key)
      end

      private

      def pricing
        @pricing ||= %i[upfront installments buy_now_pay_later].to_h do |kind|
          pricing = Ecom::Pricing::DefaultForPartnerProgram.call!(partner_program:, payment_method: Ecom::PaymentMethod.find_by(kind:))
          [kind, pricing]
        end
      end

      def registration_url
        site_pages.registration_template.first.url
      end

      def registration_short_url
        site_pages.registration_template.first.short_url
      end

      def installment_amount_per_month
        "#{pricing[:installments].recurring_payment_currency.sub(/\.00$/, '')}/month"
      end

      def buy_now_pay_later_amount_per_month
        "#{pricing[:buy_now_pay_later].recurring_payment_currency_ceiled}/month"
      end

      def buy_now_pay_later_enabled?
        partner.enabled_payment_methods.any? { |m| m.ecom_payment_method.buy_now_pay_later? }
      end

      def pricing_properties
        {
          partner_product_standard_price: pricing[:upfront].actual_currency_rounded,
          partner_product_standard_price_discount: pricing[:upfront].total_discount_currency_rounded,
          n3mos_installment: installment_amount_per_month,
          affirm_as_low_as: buy_now_pay_later_enabled? ? buy_now_pay_later_amount_per_month : '',
          price: pricing[:upfront].actual_amount,
        }.with_indifferent_access
      end
    end
  end
end
