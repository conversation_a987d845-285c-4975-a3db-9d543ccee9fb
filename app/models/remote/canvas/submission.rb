# frozen_string_literal: true

# == Schema Information
#
# Table name: remote_resources
#
#  id               :bigint           not null, primary key
#  core_record_type :string           not null
#  fetched_at       :datetime
#  key              :string           default(""), not null
#  published_at     :datetime
#  type             :string           not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  core_record_id   :bigint           not null
#
# Indexes
#
#  index_remote_resources_on_core_record                   (core_record_type,core_record_id)
#  index_remote_resources_on_type_and_key_and_core_record  (type,key,core_record_type,core_record_id) UNIQUE
#
module Remote
  module Canvas
    class Submission < Remote::Resource
      include BaseActions

      has_remote_parent 'Remote::Canvas::Section'

      delegate :cohort, :section, :assignment, :enrollment, to: :core_record
      delegate :learner, to: :enrollment
      delegate :grader, to: :section
      delegate :remote_canvas_course, to: :cohort
      delegate :remote_canvas_enrollment, to: :enrollment
      delegate :remote_canvas_assignment, to: :assignment
      delegate :remote_canvas_user, to: :learner

      def default_payload
        {
          submission: {
            posted_grade: core_record.grade,
          },
        }
      end

      def remote_update(json = default_payload, params: { as_user_id: grader.remote_canvas_employee.key }, &)
        super
      end

      def remote_link
        I18n.t('remote.canvas.submission.remote_link', app_origin:, course_key: remote_canvas_course.key,
          assignment_key: remote_canvas_assignment.key, user_key: remote_canvas_user.key,
        )
      end

      undef remote_create
      undef remote_delete
      undef remote_publish
      undef remote_find_by_alternate_key
      undef remote_fetch

      def remote_resource_path
        "/courses/#{remote_canvas_course.key}/assignments/#{remote_canvas_assignment.key}/submissions/#{remote_canvas_user.key}"
      end

      alias_method :remote_find_path, :remote_resource_path
      alias_method :remote_update_path, :remote_resource_path
    end
  end
end
