# frozen_string_literal: true

# == Schema Information
#
# Table name: remote_resources
#
#  id               :bigint           not null, primary key
#  core_record_type :string           not null
#  fetched_at       :datetime
#  key              :string           default(""), not null
#  published_at     :datetime
#  type             :string           not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  core_record_id   :bigint           not null
#
# Indexes
#
#  index_remote_resources_on_core_record                   (core_record_type,core_record_id)
#  index_remote_resources_on_type_and_key_and_core_record  (type,key,core_record_type,core_record_id) UNIQUE
#
module Remote
  module Canvas
    class Assignment < Remote::Resource
      include BaseActions

      has_remote_parent 'Remote::Canvas::Course'

      def remote_publishable?
        false
      end

      def default_payload
        {
          assignment:
            {
              name: core_record.name,
            },
        }
      end

      def alternate_key
        core_record.name
      end

      def alternate_key_property
        'name'
      end

      def alternate_key_from_response(response)
        response.dig(alternate_key_property).split(':', 2).last.strip
      end

      def remote_find_by_alternate_key
        # unlike other canvas objects, there is no sis_*_id for enrollments
        # so we need to query by user_id and section_id
        path = ['/courses', remote_parent.key, 'assignments'].join('/')
        response = client.get(path, params: { search_term: alternate_key })
        response.first
      end

      def remote_link
        I18n.t('remote.canvas.assignment.remote_link', app_origin:, course_key: remote_parent.key, key:)
      end

      def remote_list_assignment_overrides
        client.paginated_get("/courses/#{remote_parent.key}/assignments/#{key}/overrides", params: { per_page: 50 })
      end

      undef remote_update
      undef remote_delete
      undef remote_publish

      private

      def remote_resource_path
        "/courses/#{remote_parent.key}/assignments/#{key}"
      end

      alias_method :remote_find_path, :remote_resource_path
    end
  end
end
