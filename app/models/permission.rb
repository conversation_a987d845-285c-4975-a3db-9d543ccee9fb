# frozen_string_literal: true

# == Schema Information
#
# Table name: permissions
#
#  id             :bigint           not null, primary key
#  level          :integer          not null
#  name           :string           not null
#  resource_group :string           not null
#  resource_type  :string
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  resource_id    :integer
#
# Indexes
#
#  idx_on_resource_group_resource_type_resource_id_70b222d81b  (resource_group,resource_type,resource_id) UNIQUE
#
class Permission < ApplicationRecord
  self.authorized_resource_group = 'Manage Roles'

  has_many :admin_user_permissions, dependent: :destroy
  has_many :admin_users, -> { distinct }, through: :admin_user_permissions
  has_many :role_permissions, dependent: :destroy
  has_many :roles, through: :role_permissions
  belongs_to :resource, polymorphic: true, optional: true

  before_create :set_name

  RESOURCE_GROUPS = [
    'Admin',
    'Admin - Impersonation',
    'Alerts',
    'Base',
    'Customers',
    'Development',
    'External Content',
    'Finance',
    'Grant',
    'Learning Delivery',
    'Learning Delivery - Management',
    'Learning Delivery - Impersonation',
    'LMS',
    'Manage Roles',
    'Order Management',
    'Other Platforms',
    'Operational Finance',
    'Partner Contracts',
    'Product Catalog',
    'Promos',
    'Site',
  ].freeze

  rich_enum level: {
    r: [0, 'Read'],
    rw: [10, 'Read/Write'],
    rwd: [20, 'Read/Write/Delete'],
  }, prefix: false, alt: 'name'

  def self.resource_groups
    RESOURCE_GROUPS
  end

  def name_for_sidebar
    name
  end

  def read?
    level.include?('r')
  end

  def write?
    level.include?('w')
  end

  def delete?
    level.include?('d')
  end

  def set_name
    self.name = "#{resource_group}: #{level_name}"
  end
end
