# frozen_string_literal: true

# == Schema Information
#
# Table name: promos_referrers
#
#  id            :bigint           not null, primary key
#  code          :string           not null
#  uid           :string           not null
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  enrollment_id :bigint           not null
#
# Indexes
#
#  index_promos_referrers_on_code           (code) UNIQUE
#  index_promos_referrers_on_enrollment_id  (enrollment_id)
#  index_promos_referrers_on_uid            (uid) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (enrollment_id => enrollments.id)
#
module Promos
  class Referrer < ApplicationRecord
    include HasUid

    self.authorized_resource_group = 'Promos'

    has_uid prefix: 'P-R'

    belongs_to :enrollment
    has_one :learner, through: :enrollment
    has_one :partner_program, through: :enrollment
    has_one :partner, through: :partner_program
    has_one :program, through: :partner_program

    has_many :syllabus_requests, foreign_key: 'promos_referrer_id', inverse_of: :promos_referrer
    has_many :registrations, foreign_key: 'promos_referrer_id', inverse_of: :promos_referrer
    has_many :primary_enrollments, through: :registrations, source: :primary_enrollment


    before_validation :standardize_code

    validates :code, presence: true, uniqueness: true

    scope :code, ->(a_code) { where(code: standardize_code(a_code)) }

    class << self
      def standardize_code(code)
        return nil if code.nil?

        code.upcase
      end
    end

    private

    def standardize_code
      self.code = self.class.standardize_code(code) if code.present?
    end
  end
end
