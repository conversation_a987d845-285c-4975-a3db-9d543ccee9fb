# frozen_string_literal: true

# == Schema Information
#
# Table name: site_partner_datasets
#
#  id                             :bigint           not null, primary key
#  ab_testing_html                :text
#  back_to_name                   :string
#  brand_logos_allowed            :boolean          default(TRUE), not null
#  certificate_name               :string
#  cobranding_css                 :string
#  custom_disclaimer              :text
#  custom_eva_banner_text         :string
#  custom_footer_text             :text
#  faqs                           :jsonb            not null
#  home_url                       :string
#  logo_css                       :string
#  overrides                      :jsonb            not null
#  show_brand_logos_heading       :boolean          default(TRUE), not null
#  show_our_course_attracts_block :boolean          default(TRUE), not null
#  squeeze_page_cobranding_css    :string
#  squeeze_page_logo_css          :string
#  created_at                     :datetime         not null
#  updated_at                     :datetime         not null
#  partner_id                     :bigint           not null
#
# Indexes
#
#  index_site_partner_datasets_on_partner_id  (partner_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (partner_id => partners.id) ON DELETE => cascade
#
module Site
  class PartnerDataset < ApplicationRecord
    self.authorized_resource_group = 'Site'

    OVERRIDES_EXAMPLE = {
      landing_page: {
        step_2_partner_name: 'Ziplines Education Inc.',
      },
    }.freeze


    belongs_to :partner

    validates :partner, uniqueness: true
    validates :brand_logos_allowed, inclusion: { in: [true, false] }
    validates :show_our_course_attracts_block, inclusion: { in: [true, false] }

    before_validation :set_default_faqs
    before_save :set_default_overrides

    def overrides
      @overrides ||= super.with_indifferent_access
    end

    private

    def set_default_faqs
      self.faqs = Array.wrap(faqs)
    end

    def set_default_overrides
      self.overrides = {} if overrides.nil?
    end
  end
end
