# frozen_string_literal: true

# == Schema Information
#
# Table name: site_syllabus_page_templates
#
#  id                         :bigint           not null, primary key
#  custom_hero_text           :string
#  expandable_learnings       :jsonb            not null
#  expandable_learnings_blurb :string           not null
#  headline_blurb             :string           not null
#  instructors_blurb          :string           not null
#  meta_description           :text
#  meta_title                 :string
#  our_learners_blurb         :text             not null
#  our_students               :jsonb            not null
#  preview_video_blurb        :string           not null
#  preview_video_headline     :string
#  what_you_earn              :jsonb            not null
#  your_time_blurb            :text             not null
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  program_id                 :bigint           not null
#
# Indexes
#
#  index_site_syllabus_page_templates_on_program_id  (program_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (program_id => programs.id) ON DELETE => cascade
#
module Site
  class SyllabusPageTemplate < ApplicationRecord
    self.authorized_resource_group = 'Site'

    belongs_to :program

    with_options presence: true do
      validates :headline_blurb
      validates :expandable_learnings
      validates :our_students
      validates :preview_video_blurb
      validates :expandable_learnings_blurb
      validates :instructors_blurb
      validates :our_learners_blurb
      validates :your_time_blurb
      validates :what_you_earn
    end

    validates :program, uniqueness: true
  end
end
