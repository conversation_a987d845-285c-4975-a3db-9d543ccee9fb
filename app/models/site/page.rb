# frozen_string_literal: true

# == Schema Information
#
# Table name: site_pages
#
#  id                 :bigint           not null, primary key
#  short_url          :string
#  template           :integer          not null
#  url                :string           not null
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  partner_program_id :bigint           not null
#
# Indexes
#
#  index_site_pages_on_partner_program_id               (partner_program_id)
#  index_site_pages_on_template_and_partner_program_id  (template,partner_program_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (partner_program_id => partner_programs.id) ON DELETE => cascade
#
module Site
  class Page < ApplicationRecord
    self.authorized_resource_group = 'Site'

    belongs_to :partner_program

    rich_enum template: {
      landing: [1, 'Landing'],
      syllabus: [2, 'Syllabus'],
      squeeze: [3, 'Squeeze'],
      registration: [4, 'Registration'],
      get_to_know_you: [5, 'Get to Know You'],
      reimbursement: [6, 'Reimbursement'],
      curriculum: [7, 'Curriculum'],
      testimonials: [8, 'Testimonial'],
      support: [9, 'Support'],
    }, alt: 'name', suffix: true

    validates :url, presence: true
    validates :template, presence: true, uniqueness: { scope: :partner_program }

    def url(anchor: nil, **query_params)
      return super() if anchor.nil? && query_params.empty?

      uri = URI.parse(url)
      uri.fragment = anchor if anchor
      uri.query = query_params.to_query if query_params.present?
      uri.to_s
    end
  end
end
