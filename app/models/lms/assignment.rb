# frozen_string_literal: true

# == Schema Information
#
# Table name: lms_assignments
#
#  id                      :bigint           not null, primary key
#  due_at                  :datetime
#  grading_type            :integer          not null
#  html_url                :text
#  lock_at                 :datetime
#  name                    :string           not null
#  published               :boolean          default(FALSE), not null
#  required                :boolean          default(FALSE), not null
#  status                  :integer
#  unlock_at               :datetime
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  assignment_template_id  :bigint
#  lms_assignment_group_id :bigint
#  lms_module_id           :bigint
#
# Indexes
#
#  idx_assignments_sorting                           (due_at,id)
#  index_lms_assignments_on_assignment_template_id   (assignment_template_id)
#  index_lms_assignments_on_lms_assignment_group_id  (lms_assignment_group_id)
#  index_lms_assignments_on_lms_module_id            (lms_module_id)
#
# Foreign Keys
#
#  fk_rails_...  (lms_assignment_group_id => lms_assignment_groups.id)
#  fk_rails_...  (lms_module_id => lms_modules.id)
#
module Lms
  class Assignment < ApplicationRecord
    self.authorized_resource_group = 'LMS'

    belongs_to :lms_module, class_name: 'Lms::Module', inverse_of: :assignments, optional: true
    belongs_to :assignment_group, class_name: 'Lms::AssignmentGroup', foreign_key: :lms_assignment_group_id, inverse_of: :assignments
    belongs_to :assignment_template, class_name: 'Lms::AssignmentTemplate', optional: true

    has_one :cohort, through: :assignment_group

    has_many :section_assignments, class_name: 'Lms::SectionAssignment', dependent: :destroy, foreign_key: :lms_assignment_id, inverse_of: :assignment
    has_many :sections, through: :section_assignments
    has_many :submissions, class_name: 'Lms::Submission', foreign_key: :lms_assignment_id, inverse_of: :assignment, dependent: :destroy
    has_many :accepted_submission_types, class_name: 'Lms::Submission::AcceptedType', dependent: :destroy, inverse_of: :assignment

    has_one :program, through: :cohort
    has_one :grading_config, through: :assignment_template

    scope :with_due_date, -> { where.not(due_at: nil) }
    scope :requires_grading, -> { where(required: true).where.not(grading_type: :not_graded) }
    scope :impacts_outcome, -> { requires_grading.with_due_date }
    scope :optional, -> { where(required: false).or(where(grading_type: :not_graded)) }

    scope :accepts_online_url_or_upload, lambda {
      joins(:accepted_submission_types).where(lms_submission_accepted_types: { name: %i[online_url online_upload] })
    }

    delegate :section_weeks, to: :lms_module, allow_nil: true

    rich_enum grading_type: {
      pass_fail: [0, 'Pass/Fail'],
      percent: [1, 'Percent'],
      letter_grade: [2, 'Letter Grade'],
      gpa_scale: [3, 'GPA Scale'],
      points: [4, 'Points'],
      not_graded: [5, 'Not Graded'],
    }

    rich_enum status: {
      locked: [0, 'Locked'],
      unlocked: [1, 'Unlocked'],
      past_due: [2, 'Past Due'],
    }

    def cohort_week
      section_weeks&.first&.cohort_week
    end
  end
end
