# frozen_string_literal: true

# == Schema Information
#
# Table name: lms_submission_exercise_reviews
#
#  id                                              :bigint           not null, primary key
#  auto_graded_at                                  :datetime         not null
#  autograder_confidence                           :integer
#  comment                                         :text
#  extracted_work                                  :text
#  grade                                           :integer
#  manually_reviewed_at                            :datetime
#  state                                           :integer          default("submitted"), not null
#  title                                           :string
#  created_at                                      :datetime         not null
#  updated_at                                      :datetime         not null
#  manually_reviewed_by_id(References admin_users) :bigint
#  review_id                                       :bigint           not null
#
# Indexes
#
#  idx_on_manually_reviewed_by_id_b37a61481e           (manually_reviewed_by_id)
#  index_lms_submission_exercise_reviews_on_review_id  (review_id)
#
# Foreign Keys
#
#  fk_rails_...  (manually_reviewed_by_id => admin_users.id)
#  fk_rails_...  (review_id => lms_submission_reviews.id)
#
module Lms
  class Submission
    class ExerciseReview < ApplicationRecord
      include HasStatus

      has_paper_trail

      self.authorized_resource_group = 'LMS'

      has_status :state
      has_status :grade
      has_status :autograder_confidence

      rich_enum state: {
        submitted: [0, 'Submitted'],
        auto_graded: [1, 'Auto Graded'],
        manual_review_needed: [2, 'Manual Review Needed'],
        graded: [3, 'Graded'],
      }, alt: 'name'

      rich_enum autograder_confidence: {
        low: [0, 'Low'],
        medium: [1, 'Medium'],
        high: [2, 'High'],
      }, alt: 'name', prefix: true

      rich_enum grade: {
        incomplete: [1, 'Incomplete'],
        needs_work: [2, 'Needs Work'],
        good: [3, 'Good'],
        exemplary: [4, 'Exemplary'],
      }, alt: 'name'

      belongs_to :review, class_name: 'Lms::Submission::Review'
      belongs_to :manually_reviewed_by, class_name: 'AdminUser', optional: true
      has_one :submission, through: :review

      has_one :exercise_config, ->(record) { where(lms_exercise_configs: { title: record.title }) }, through: :review, source: :exercise_configs

      delegate :grading_instructions, :requires_manual_review?, to: :exercise_config, allow_nil: true

      validates :state, presence: true

      # This object is created right after auto-grading, so auto_graded_at is the same as created_at
      before_create :set_auto_graded_at

      private

      def set_auto_graded_at
        self.auto_graded_at ||= created_at
      end
    end
  end
end
