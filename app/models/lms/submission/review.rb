# frozen_string_literal: true

# == Schema Information
#
# Table name: lms_submission_reviews
#
#  id                                                 :bigint           not null, primary key
#  attempt                                            :integer          default(0)
#  auto_graded_at                                     :datetime
#  auto_grader_grade(Grade assigned by the AI Grader) :integer
#  comment                                            :text
#  grade                                              :integer
#  graded_at                                          :datetime
#  manually_reviewed_at                               :datetime
#  pdf_generated_at                                   :datetime
#  published_at                                       :datetime
#  score                                              :integer
#  state                                              :integer          default("submitted"), not null
#  training                                           :boolean          default(FALSE), not null
#  uid                                                :string           not null
#  created_at                                         :datetime         not null
#  updated_at                                         :datetime         not null
#  manually_reviewed_by_id(References admin_users)    :bigint
#  submission_id                                      :bigint           not null
#
# Indexes
#
#  index_lms_submission_reviews_on_manually_reviewed_by_id    (manually_reviewed_by_id)
#  index_lms_submission_reviews_on_state                      (state)
#  index_lms_submission_reviews_on_submission_id_and_attempt  (submission_id,attempt) UNIQUE
#  index_lms_submission_reviews_on_training                   (training)
#  index_lms_submission_reviews_on_uid                        (uid) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (manually_reviewed_by_id => admin_users.id)
#  fk_rails_...  (submission_id => lms_submissions.id)
#
module Lms
  class Submission
    class Review < ApplicationRecord
      include HasUid
      include HasStatus

      has_paper_trail

      self.authorized_resource_group = 'LMS'

      has_uid prefix: 'LMS-SR'
      has_status :state
      has_status :grade
      has_status :auto_grader_grade

      GRADE_OPTIONS = {
        incomplete: [0, 'Incomplete'],
        complete: [1, 'Complete'],
      }.freeze

      rich_enum state: {
        submitted: [0, 'Submitted'],
        pdf_generated: [1, 'PDF Generated'],
        auto_graded: [2, 'Auto Graded'],
        manual_review_needed: [3, 'Manual Review Needed'],
        graded: [4, 'Graded'],
        published: [5, 'Published'],
        stale: [6, 'Stale'],
      }, alt: 'name'

      AUTOGRADING_IN_PROGRESS_STATES = [
        states[:submitted],
        states[:pdf_generated],
        states[:auto_graded],
        states[:graded],
      ].freeze

      rich_enum grade: GRADE_OPTIONS, alt: 'name'
      rich_enum auto_grader_grade: GRADE_OPTIONS, alt: 'name', suffix: true

      belongs_to :submission, class_name: 'Lms::Submission'
      belongs_to :manually_reviewed_by, class_name: 'AdminUser', optional: true
      has_many :exercise_reviews, class_name: 'Lms::Submission::ExerciseReview', dependent: :destroy
      has_one :training_evaluation, class_name: 'Lms::Submission::Review::TrainingEvaluation', dependent: :destroy

      has_one :enrollment, through: :submission
      has_one :assignment, through: :submission
      has_one :lms_module, through: :assignment
      has_one :assignment_template, through: :assignment
      has_one :grading_config, through: :assignment_template
      has_one :grading_assistant, through: :grading_config
      has_one :learner, through: :enrollment
      has_one :cohort, through: :enrollment
      has_one :section, through: :enrollment
      has_one :program, through: :enrollment
      has_one :grader, through: :section


      has_many :exercise_configs, through: :grading_config

      delegate :cohort_week, to: :assignment
      delegate :lms_url, to: :submission

      has_one_attached :submission_export

      validates :state, presence: true
      validates :submission_id, uniqueness: { scope: :attempt }
      validates :submission_export, content_type: ['application/pdf']

      scope :mismatched_grades, lambda {
        joins(:submission)
          .where(
            "(lms_submission_reviews.auto_grader_grade = ? AND lms_submissions.grade != ?) OR
             (lms_submission_reviews.auto_grader_grade = ? AND lms_submissions.grade != ?)",
            Review.auto_grader_grades[:incomplete], Submission::INCOMPLETE,
            Review.auto_grader_grades[:complete], Submission::COMPLETE,
          )
      }

      scope :auto_grader_overridden, lambda {
        live.where("lms_submission_reviews.grade IS NOT NULL AND auto_grader_grade IS NOT NULL AND lms_submission_reviews.grade != auto_grader_grade")
      }

      scope :auto_grader_accepted, lambda {
        live.where("lms_submission_reviews.grade IS NOT NULL AND auto_grader_grade IS NOT NULL AND lms_submission_reviews.grade = auto_grader_grade")
      }

      scope :auto_grader_blank, lambda {
        live.where(auto_grader_grade: nil)
      }

      scope :training, -> { where(training: true) }
      scope :live, -> { where(training: false) }
      scope :current, lambda {
        joins(:submission).where('lms_submission_reviews.attempt = lms_submissions.attempt')
      }

      def comments_to_publish
        CombineCommentsCommand.call!(review: self)
      end

      def current?
        submission.attempt == attempt
      end
    end
  end
end
