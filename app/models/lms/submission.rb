# frozen_string_literal: true

# == Schema Information
#
# Table name: lms_submissions
#
#  id                :bigint           not null, primary key
#  attempt           :integer
#  grade             :string
#  graded_at         :datetime
#  score             :decimal(, )
#  seconds_late      :integer
#  state             :integer          default("unsubmitted"), not null
#  submission_type   :integer
#  submitted_at      :datetime
#  url               :text
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  enrollment_id     :bigint           not null
#  graded_by_id      :bigint
#  lms_assignment_id :bigint           not null
#
# Indexes
#
#  index_lms_submissions_on_assignment_and_enrollment  (enrollment_id,lms_assignment_id) UNIQUE
#  index_lms_submissions_on_graded_by_id               (graded_by_id)
#  index_lms_submissions_on_lms_assignment_id          (lms_assignment_id)
#
# Foreign Keys
#
#  fk_rails_...  (enrollment_id => enrollments.id)
#  fk_rails_...  (graded_by_id => learning_delivery_employees.id)
#  fk_rails_...  (lms_assignment_id => lms_assignments.id)
#
module Lms
  class Submission < ApplicationRecord
    include HasStatus

    has_paper_trail

    self.authorized_resource_group = 'LMS'

    COMPLETE = 'complete'
    INCOMPLETE = 'incomplete'

    has_status :state

    # We also have an .active scope which is anything submitted or later.
    # And inactive is unsubmitted.
    rich_enum state: {
      unsubmitted: [0, 'Unsubmitted'],
      submitted: [1, 'Submitted'],
      pending_review: [2, 'Pending Review'],
      graded: [3, 'Graded'],
    }, alt: 'name'

    rich_enum submission_type: {
      undefined: [-1, 'Undefined'],
      online_text_entry: [0, 'Online Text Entry'],
      online_url: [1, 'Online URL'],
      online_upload: [2, 'Online Upload'],
      media_recording: [3, 'Media Recording'],
      basic_lti_launch: [4, 'Basic LTI Launch'],
      student_annotation: [5, 'Student Annotation'],
      external_tool: [6, 'External Tool'],
    }, alt: 'name'

    belongs_to :enrollment
    belongs_to :assignment, class_name: 'Lms::Assignment', foreign_key: :lms_assignment_id, inverse_of: :submissions
    belongs_to :graded_by, class_name: 'LearningDelivery::Employee', optional: true

    has_one :learner, through: :enrollment
    has_one :cohort, through: :enrollment
    has_one :section, through: :enrollment
    has_one :program, through: :enrollment
    has_one :current_review, ->(submission) { where(attempt: submission.attempt) }, class_name: 'Lms::Submission::Review', inverse_of: :submission
    has_many :reviews, class_name: 'Lms::Submission::Review', dependent: :destroy, inverse_of: :submission
    has_many :exercise_reviews, through: :reviews, class_name: 'Lms::Submission::ExerciseReview'
    has_many :comments, class_name: 'Lms::Submission::Comment', dependent: :destroy, inverse_of: :submission
    has_many :accepted_submission_types, through: :assignment

    has_one :lms_module, through: :assignment
    has_one :assignment_template, through: :assignment
    has_one :assignment_group, through: :assignment
    has_one :grading_config, through: :assignment_template
    has_one :module_template, through: :assignment_template

    scope :assignment_accepts_online_url_or_upload, -> { joins(:assignment).merge(Lms::Assignment.accepts_online_url_or_upload).distinct }
    scope :marked_complete_without_url, -> { assignment_accepts_online_url_or_upload.where(grade: :complete, url: nil) }
    scope :ungraded_with_url, -> { assignment_accepts_online_url_or_upload.where(grade: nil, submitted_at: ..2.days.ago).where.not(url: nil) }
    scope :active, -> { where(state: [:submitted, :pending_review, :graded]) }
    scope :inactive, -> { where(state: :unsubmitted) }

    delegate :cohort_week, to: :assignment

    validates :state, presence: true
    validates :submission_type, presence: true
    validates :graded_at, presence: true, if: :graded?
    validates :lms_assignment_id, uniqueness: { scope: :enrollment_id }

    def submitted?
      state.in?(%w[submitted pending_review graded])
    end

    def completed?
      # just looking at grades because sometimes states remains submitted
      grade == COMPLETE
    end

    def lms_url
      [
        Rails.application.config.remote_resources[:canvas][:api_origin],
        remote_canvas_submission.remote_resource_path,
      ].join('/').to_s
    rescue
      ''
    end

    def grader_url
      UrlBuilder::LearningDelivery.learning_delivery_submission_url(id:)
    end

    def publishable?
      current_review && current_review.published_at.nil? && !current_review.training?
    end
  end
end
