# frozen_string_literal: true

# == Schema Information
#
# Table name: lms_grading_configs
#
#  id                                      :bigint           not null, primary key
#  ai_blank_template_file_key              :string
#  ai_blank_template_vector_store_file_key :string
#  ai_vector_store_key                     :string
#  uid                                     :string           not null
#  created_at                              :datetime         not null
#  updated_at                              :datetime         not null
#  grading_assistant_id                    :bigint           not null
#
# Indexes
#
#  index_lms_grading_configs_on_grading_assistant_id  (grading_assistant_id)
#  index_lms_grading_configs_on_uid                   (uid) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (grading_assistant_id => lms_grading_assistants.id)
#
module Lms
  class GradingConfig < ApplicationRecord
    include HasUid

    has_paper_trail

    self.authorized_resource_group = 'LMS'

    has_uid prefix: 'LMS-GC'

    belongs_to :grading_assistant, class_name: 'Lms::GradingAssistant'
    has_many :exercise_configs, lambda {
      order(order: :asc, id: :asc)
    }, class_name: 'Lms::ExerciseConfig', dependent: :destroy, inverse_of: :grading_config
    has_many :assignment_templates, class_name: 'Lms::AssignmentTemplate', dependent: :nullify

    has_many :module_templates, through: :assignment_templates, class_name: 'Lms::ModuleTemplate'
    has_many :programs, through: :module_templates

    has_one_attached :blank_template

    accepts_nested_attributes_for :exercise_configs, allow_destroy: true, reject_if: :all_blank

    delegate :ai_assistant, to: :grading_assistant

    validates :blank_template, content_type: ['application/pdf']

    def ai_vector_store
      @ai_vector_store ||= AI::VectorStore.new(id: ai_vector_store_key) if ai_vector_store_key.present?
    end

    def ai_vector_store=(ai_vector_store)
      self.ai_vector_store_key = ai_vector_store&.id
    end

    def blank_template_filename
      "#{humanized_uid}_blank_template.pdf"
    end

    def ai_blank_template_file
      @ai_blank_template_file ||= AI::File.new(id: ai_blank_template_file_key) if ai_blank_template_file_key.present?
    end

    def ai_blank_template_file=(ai_file)
      self.ai_blank_template_file_key = ai_file&.id
    end

    def ai_blank_template_vector_store_file
      return if ai_blank_template_vector_store_file_key.blank?

      @ai_blank_template_vector_store_file ||= AI::VectorStoreFile.new(id: ai_blank_template_vector_store_file_key)
    end

    def ai_blank_template_vector_store_file=(ai_vector_store_file)
      self.ai_blank_template_vector_store_file_key = ai_vector_store_file&.id
    end

  end
end
