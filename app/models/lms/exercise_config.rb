# frozen_string_literal: true

# == Schema Information
#
# Table name: lms_exercise_configs
#
#  id                     :bigint           not null, primary key
#  grading_instructions   :text
#  order                  :integer          default(0), not null
#  requires_manual_review :boolean          default(FALSE), not null
#  title                  :string           not null
#  uid                    :string           not null
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  grading_config_id      :bigint           not null
#
# Indexes
#
#  index_lms_exercise_configs_on_grading_config_id  (grading_config_id)
#  index_lms_exercise_configs_on_uid                (uid) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (grading_config_id => lms_grading_configs.id)
#
module Lms
  class ExerciseConfig < ApplicationRecord
    include HasUid

    has_paper_trail

    self.authorized_resource_group = 'LMS'

    has_uid prefix: 'LMS-EC'

    belongs_to :grading_config, class_name: 'Lms::GradingConfig'

    validates :title, presence: true
    validates :requires_manual_review, inclusion: { in: [true, false] }
  end
end
