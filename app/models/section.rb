# frozen_string_literal: true

# == Schema Information
#
# Table name: sections
#
#  id                   :bigint           not null, primary key
#  chat_join_url        :string
#  chat_workspace_key   :string
#  conferencing_url     :string
#  live_day_of_the_week :integer          not null
#  live_end_time        :time             not null
#  live_start_time      :time             not null
#  suffix               :string           not null
#  uid                  :string           not null
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  advocate_id          :bigint
#  cohort_id            :bigint           not null
#  grader_id            :bigint
#  instructor_id        :bigint
#  partner_id           :bigint
#
# Indexes
#
#  index_sections_on_advocate_id         (advocate_id)
#  index_sections_on_chat_workspace_key  (chat_workspace_key)
#  index_sections_on_cohort_id           (cohort_id)
#  index_sections_on_grader_id           (grader_id)
#  index_sections_on_instructor_id       (instructor_id)
#  index_sections_on_partner_id          (partner_id)
#  index_sections_on_uid                 (uid) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (advocate_id => learning_delivery_employees.id)
#  fk_rails_...  (cohort_id => cohorts.id) ON DELETE => cascade
#  fk_rails_...  (grader_id => learning_delivery_employees.id)
#  fk_rails_...  (instructor_id => learning_delivery_employees.id)
#  fk_rails_...  (partner_id => partners.id)
#
class Section < ApplicationRecord
  include HasUid

  self.authorized_resource_group = 'Base'

  LIVE_SESSION_DEFAULT_DURATION = 2.hours

  has_uid prefix: 'S'

  rich_enum live_day_of_the_week: {
    sunday: [0, 'Sunday'],
    monday: [1, 'Monday'],
    tuesday: [2, 'Tuesday'],
    wednesday: [3, 'Wednesday'],
    thursday: [4, 'Thursday'],
    friday: [5, 'Friday'],
    saturday: [6, 'Saturday'],
  }, prefix: true, alt: 'name'

  scope :dedicated_to, ->(partner) { where(partner:) }
  scope :commingled, -> { where(partner: nil) }
  scope :with_ids, ->(ids) { ids.present? ? where(id: ids) : all }

  scope :program_cohort_suffix_cont, ->(string) { SearchQuery.relation(string:) }
  scope :active, -> { joins(:cohort).where(cohorts: { status: Cohort::ACTIVE_STATUSES }) }
  scope :ordered_by_start_and_name, -> { joins(cohort: :program).order('cohorts.starts_on', 'programs.abbreviation', 'sections.suffix') }

  belongs_to :cohort
  belongs_to :partner, optional: true
  belongs_to :instructor, class_name: 'LearningDelivery::Employee', optional: true
  belongs_to :advocate, class_name: 'LearningDelivery::Employee', optional: true
  belongs_to :grader, class_name: 'LearningDelivery::Employee', optional: true

  has_one :program, through: :cohort
  has_one :ecom_variant,
    -> { where(partner: nil) },
    class_name: 'Ecom::Variant',
    dependent: :destroy,
    inverse_of: :section

  has_many :enrollments
  has_many :registrations
  has_many :weeks, -> { ordered }, class_name: 'Section::Week', dependent: :destroy, inverse_of: :section
  has_many :live_sessions, through: :weeks
  has_many :live_session_reviews, through: :live_sessions, class_name: 'LiveSession::Review', source: :reviews
  has_many :cohort_weeks, through: :cohort, source: :weeks
  has_many :section_assignments, class_name: 'Lms::SectionAssignment', dependent: :destroy
  has_many :assignments, through: :section_assignments, class_name: 'Lms::Assignment'

  has_many :section_availability_slots, class_name: 'LearningDelivery::SectionAvailabilitySlot', dependent: :destroy
  has_many :availability_slots, through: :section_availability_slots, class_name: 'LearningDelivery::AvailabilitySlot'
  has_many :learning_delivery_tasks, class_name: 'LearningDelivery::Task', dependent: :nullify

  has_many :lms_submissions, through: :enrollments, class_name: 'Lms::Submission', source: :lms_submissions

  accepts_nested_attributes_for :ecom_variant, update_only: true
  accepts_nested_attributes_for :weeks, allow_destroy: true

  validates_associated :ecom_variant

  validates :live_day_of_the_week, presence: true
  validates :live_start_time, presence: true
  validates :live_end_time, presence: true
  validates :suffix, presence: true

  before_validation :set_suffix, :set_end_time
  before_destroy :ensure_at_least_one_section

  delegate :active?, to: :cohort

  class << self
    def ransackable_attributes(_auth_object = nil)
      super + %w[email_address_cont]
    end
  end

  # @format [Symbol] `format` from `LiveDayTimeFormatter`. Note that default is `:short`, not `:default` from `LiveDayTimeFormatter`.
  def live_name(format: :short)
    LiveDayTimeFormatter.new(section: self).to_fs(format:)
  end

  def name
    "Section #{suffix}"
  end

  def full_name
    "#{name} - #{live_name} (#{enrollments.size})"
  end

  def active_admin_display_name
    "#{name} - #{live_name}"
  end

  def program_cohort_name
    "#{program.abbreviation.upcase} - #{cohort.active_admin_display_name} #{name}"
  end

  def program_cohort_name_with_instructor
    [program_cohort_name, instructor&.full_name].compact.join(' - ')
  end

  def program_cohort_name_with_advocate
    [program_cohort_name, advocate&.full_name].compact.join(' - ')
  end

  def commingled?
    partner.nil?
  end

  def starts_on
    weeks.first&.starts_on
  end

  def lms_modules
    modules = weeks.includes(:direct_lms_modules, cohort_week: :lms_modules)
      .flat_map(&:lms_modules)

    return Lms::Module.none if modules.empty?

    Lms::Module.where(id: modules.map(&:id)).order(:position)
  end

  private

  def set_suffix
    self.suffix ||= cohort&.sections&.count.to_i + 1
  end

  def set_end_time
    return if live_start_time.blank?

    self.live_end_time ||= live_start_time + LIVE_SESSION_DEFAULT_DURATION
  end

  def ensure_at_least_one_section
    return if cohort.sections.size > 1

    errors.add(:base, 'Cannot delete the last section of a cohort')
    throw :abort
  end
end
