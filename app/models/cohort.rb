# frozen_string_literal: true

# == Schema Information
#
# Table name: cohorts
#
#  id                       :bigint           not null, primary key
#  add_period_ends_on       :date             not null
#  add_period_starts_on     :date
#  drop_period_ends_on      :date             not null
#  ends_on                  :date             not null
#  extension_period_ends_on :date             not null
#  key                      :string           not null
#  lms_closes_on            :date             not null
#  lms_opens_on             :date             not null
#  name                     :string           not null
#  starts_on                :date             not null
#  status                   :integer          default("created"), not null
#  transfer_period_ends_on  :date             not null
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#  program_id               :bigint           not null
#
# Indexes
#
#  index_cohorts_on_key         (key) UNIQUE
#  index_cohorts_on_name        (name)
#  index_cohorts_on_program_id  (program_id)
#  index_cohorts_on_status      (status)
#
# Foreign Keys
#
#  fk_rails_...  (program_id => programs.id) ON DELETE => cascade
#

# There are 3 virtual statuses for a Cohort:
#   * Active - Cohort is currently running and students can still complete course work and get a pass/no pass
#   * Add Period Active - Students can enroll in the cohort
#   * LMS Open - Students can access the LMS
class Cohort < ApplicationRecord
  self.authorized_resource_group = 'Base'

  # ordered list of date attributes and their corresponding statuses
  STATUS_DATE_TRIGGERS = [
    { date_attribute: nil, status: :created, type: :starts },
    { date_attribute: :lms_opens_on, status: :opened, type: :starts },
    { date_attribute: :starts_on, status: :started, type: :starts },
    { date_attribute: :ends_on, status: :ended, type: :ends },
    { date_attribute: :extension_period_ends_on, status: :extension_period_ended, type: :ends },
    { date_attribute: :lms_closes_on, status: :closed, type: :ends },
  ].freeze

  LMS_OPENED_STATUSES = %i[ opened started ended extension_period_ended inactive ].freeze

  ACTIVE_STATUSES = %i[ opened started ended ].freeze

  # All end dates for a cohort cutover at 12am localtime the next day.  Ex add_period_ends_on is today, we switch over at 12am localtime the next day
  CUTOVER_HOUR = 0

  scope :active, -> { where(status: ACTIVE_STATUSES) }
  scope :add_period_active, lambda {
    date = Time.zone.now.hour < CUTOVER_HOUR ? Time.zone.yesterday : Time.zone.yesterday + 1
    where(add_period_starts_on: ..date, add_period_ends_on: date...)
  }
  scope :lms_open, -> { where(status: LMS_OPENED_STATUSES) }

  belongs_to :program

  has_many :sections, dependent: :destroy
  has_many :enrollments, through: :sections
  has_many :primary_enrollments, -> { where(primary: true) }, through: :sections, source: :enrollments
  has_many :weeks, -> { ordered }, class_name: 'Cohort::Week', dependent: :destroy, inverse_of: :cohort
  has_many :assignment_groups, class_name: 'Lms::AssignmentGroup', dependent: :destroy
  has_many :assignments, class_name: 'Lms::Assignment', through: :assignment_groups
  has_many :live_sessions, through: :sections
  has_many :live_session_reviews, through: :sections
  has_many :lms_modules, class_name: 'Lms::Module', inverse_of: :cohort
  has_many :lms_submissions, through: :enrollments, class_name: 'Lms::Submission', source: :lms_submissions

  accepts_nested_attributes_for :sections

  validates :status, presence: true
  validates :key, presence: true, uniqueness: true
  validates :name, presence: true

  validates :add_period_starts_on, comparison: { less_than_or_equal_to: :add_period_ends_on }, allow_nil: true
  validates :add_period_ends_on, presence: true, comparison: { greater_than_or_equal_to: :add_period_starts_on }
  validates :drop_period_ends_on, presence: true, comparison: { greater_than_or_equal_to: :starts_on }
  validates :ends_on, presence: true, comparison: { greater_than_or_equal_to: :starts_on }
  validates :extension_period_ends_on, presence: true, comparison: { greater_than_or_equal_to: :ends_on }
  validates :lms_closes_on, presence: true, comparison: { greater_than_or_equal_to: :lms_opens_on }
  validates :lms_opens_on, presence: true, comparison: { less_than_or_equal_to: :lms_closes_on }
  validates :starts_on, presence: true, comparison: { less_than_or_equal_to: :ends_on }
  validates :transfer_period_ends_on, presence: true, comparison: { greater_than_or_equal_to: :add_period_ends_on }

  before_validation :set_key, :set_name

  rich_enum status: {
    created: [0, 'Created'],
    opened: [1, 'Opened'], # LMS Opened
    started: [2, 'Started'], # Cohort Started
    ended: [3, 'Ended'], # Cohort Ended
    extension_period_ended: [4, 'Extension Period Ended'], # Extension Period Ended
    inactive: [5, 'Inactive'], # Cohort Inactive
    closed: [6, 'Closed'], # LMS Closed
  }, alt: 'name'

  scope :name_cont, lambda { |string|
    string.gsub(/[^\s\w]/, '').split(/\s/).compact_blank.inject(all) do |rel, word|
      rel.where('name ilike :term', term: "%#{word}%")
    end
  }

  def active?
    status.to_sym.in?(ACTIVE_STATUSES)
  end

  def week_number
    return if !active?

    (Time.zone.today - lms_opens_on).sdiv(7).to_i
  end

  def lms_open?
    LMS_OPENED_STATUSES.include?(status.to_sym)
  end

  def lms_closed?
    !lms_open?
  end

  def add_period_active?
    (add_period_starts_on.at_beginning_of_day...(add_period_ends_on + 1).at_beginning_of_day.change(hour: CUTOVER_HOUR)).cover?(Time.zone.now)
  end

  def starting_month(format: :default)
    case format
    when :numeric
      starts_on.strftime('%Y-%m')
    when :default
      starts_on.strftime('%b, %Y')
    else
      raise ArgumentError.new("Unknown format: #{format}")
    end
  end

  def active_admin_display_name
    starting_month
  end

  def grader
    return unless sections.all? { |s| s.grader.present? && s.grader == sections.first.grader }

    @grader ||= sections.first.grader
  end

  def live_name(format: :default)
    case format
    when :long
      [program.name, starts_on.strftime('%b %-d, %Y')].join(' - ')
    when :default, :short
      starts_on.strftime('%B (Starts %-m/%-d)')
    else
      raise ArgumentError.new("Unknown format: #{format}")
    end
  end

  private

  def set_key
    return if program.blank? || starts_on.blank?

    self.key ||= begin
      base_key = [program.key, starts_on.strftime('%m%d%y')].join('_')
      test_key = base_key

      2.step.inject(test_key) do |key, i|
        break key if self.class.where(program:, key:).empty?

        "#{base_key}_#{i}"
      end
    end
  end

  def set_name
    return if program.blank? || starts_on.blank?

    self.name ||= [program.name, starting_month].join(' - ')
  end
end
