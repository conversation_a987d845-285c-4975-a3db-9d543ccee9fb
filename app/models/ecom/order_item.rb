# frozen_string_literal: true

# == Schema Information
#
# Table name: ecom_order_items
#
#  id                  :bigint           not null, primary key
#  currency_code       :string           default("USD"), not null
#  price_cents         :integer          default(0), not null
#  uid                 :string
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  finance_contract_id :bigint
#  order_id            :bigint           not null
#  registration_id     :bigint
#  variant_id          :bigint           not null
#
# Indexes
#
#  index_ecom_order_items_on_finance_contract_id  (finance_contract_id)
#  index_ecom_order_items_on_order_id             (order_id)
#  index_ecom_order_items_on_registration_id      (registration_id)
#  index_ecom_order_items_on_uid                  (uid) UNIQUE
#  index_ecom_order_items_on_variant_id           (variant_id)
#
# Foreign Keys
#
#  fk_rails_...  (finance_contract_id => finance_contracts.id)
#  fk_rails_...  (order_id => ecom_orders.id) ON DELETE => cascade
#  fk_rails_...  (registration_id => registrations.id) ON DELETE => cascade
#  fk_rails_...  (variant_id => ecom_variants.id) ON DELETE => cascade
#
module Ecom
  class OrderItem < ApplicationRecord
    include HasUid
    include StoredInCents

    self.authorized_resource_group = 'Order Management'

    stored_in_cents :price_cents

    has_uid prefix: 'OI'

    belongs_to :order, class_name: 'Ecom::Order', inverse_of: :order_items
    belongs_to :variant, class_name: 'Ecom::Variant', inverse_of: :order_items
    belongs_to :registration, inverse_of: :ecom_order_item, optional: true
    belongs_to :finance_contract, class_name: 'Finance::Contract', inverse_of: :order_items, optional: true

    has_one :partner, through: :order
    has_one :section, through: :variant
    has_one :cohort, through: :variant
    has_one :program, through: :variant
    has_one :product, through: :variant
    has_one(
      :partner_program,
      lambda {
        select(
          'partner_programs.*', # NOTE: This is necessary for preloading the association.
        ).from(
          <<-SQL.squish,
            (
              SELECT partner_programs.*, ecom_order_items.id AS order_item_id
              FROM ecom_order_items
              JOIN ecom_orders ON ecom_orders.id = ecom_order_items.order_id
              JOIN ecom_variants ON ecom_variants.id = ecom_order_items.variant_id
              JOIN sections ON sections.id = ecom_variants.section_id
              JOIN cohorts ON cohorts.id = sections.cohort_id
              JOIN partner_programs ON partner_programs.partner_id = ecom_orders.partner_id AND partner_programs.program_id = cohorts.program_id
            ) AS partner_programs
          SQL
        )
      },
      class_name: 'PartnerProgram',
      inverse_of: false,
    )
    has_one :primary_enrollment, -> { primary }, class_name: 'Enrollment', inverse_of: false, foreign_key: :ecom_order_item_id
    has_one :finance_revenue_booking, class_name: 'Finance::Booking::Revenue', inverse_of: false, foreign_key: :ecom_order_item_id
    has_one :payment, through: :order, class_name: 'Ecom::Payment', inverse_of: :order_items

    has_many :enrollments, foreign_key: :ecom_order_item_id, inverse_of: :ecom_order_item
    has_many :finance_bookings, class_name: 'Finance::Booking', inverse_of: :ecom_order_item

    validates :price_cents, presence: true, numericality: { greater_than_or_equal_to: 0, only_integer: true }

    def fulfilled?
      primary_enrollment.present?
    end

    def partner_program
      if instance_variable_defined?(:@partner_program)
        @partner_program
      else
        @partner_program = PartnerProgram.find_by(partner: order.partner, program:)
      end
    end

  end
end
