# frozen_string_literal: true

# == Schema Information
#
# Table name: partners
#
#  id                          :bigint           not null, primary key
#  abbreviation                :string
#  certifier                   :integer          default("none"), not null
#  currency_code               :string           default("USD"), not null
#  dns_status                  :integer          default("disabled"), not null
#  ecom_payment_providers      :string           default(["APPLE_PAY", "GOOGLE_PAY"]), not null, is an Array
#  formal_name                 :string           not null
#  name                        :string           not null
#  proxy_host                  :string
#  section_style               :integer          default("commingled"), not null
#  short_name                  :string           not null
#  slug                        :string           not null
#  status                      :integer          default("active"), not null
#  time_zone                   :string           default("UTC"), not null
#  uid                         :string           not null
#  created_at                  :datetime         not null
#  updated_at                  :datetime         not null
#  address_id                  :bigint
#  promos_eva_discount_code_id :bigint
#
# Indexes
#
#  index_partners_on_abbreviation                 (abbreviation) UNIQUE WHERE (abbreviation IS NOT NULL)
#  index_partners_on_address_id                   (address_id)
#  index_partners_on_name                         (name) UNIQUE
#  index_partners_on_promos_eva_discount_code_id  (promos_eva_discount_code_id)
#  index_partners_on_proxy_host                   (proxy_host)
#  index_partners_on_slug                         (slug) UNIQUE
#  index_partners_on_uid                          (uid) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (address_id => addresses.id)
#  fk_rails_...  (promos_eva_discount_code_id => promos_discount_codes.id)
#
class Partner < ApplicationRecord
  include HasUid # HasSlug should come after, as we want the default to_param to be slug
  include HasSlug

  self.authorized_resource_group = 'Base'

  has_uid prefix: 'P'
  has_slug from: :name

  rich_enum certifier: { none: [0, 'None'], partner: [1, 'Partner'], third_party: [2, 'Third Party'] }, prefix: true, alt: 'name'
  rich_enum status: { active: [1, 'Active'], inactive: [0, 'Inactive'] }, alt: 'name'
  rich_enum dns_status: {
    disabled: [0, 'Disabled'], pending: [1, 'Pending'], connected: [2, 'Connected'], failed: [3, 'Failed'],
  }, prefix: 'dns', alt: 'name'
  rich_enum section_style: { commingled: [0, 'Commingled'], dedicated: [1, 'Dedicated'] }, alt: 'name', suffix: 'sections'

  scope :abbreviation, ->(abbr) { where(abbreviation: abbr.upcase) }
  scope :with_site_data, -> { includes(:site_partner_dataset, :theme) }

  belongs_to :promos_eva_discount_code, class_name: 'Promos::DiscountCode', optional: true, inverse_of: :eva_partners
  belongs_to :address, optional: true

  has_one :theme, dependent: :destroy, inverse_of: :partner

  has_one :default_partner_program, lambda {
    merge(PartnerProgram.default).merge(PartnerProgram.viewable_on_site)
  }, class_name: "PartnerProgram", inverse_of: false
  has_one :default_program, through: :default_partner_program, class_name: 'Program', source: :program, inverse_of: false
  has_one :certificate_theme
  has_one :site_partner_dataset, class_name: 'Site::PartnerDataset', dependent: :destroy, inverse_of: :partner

  has_many :partner_programs
  has_many :programs, through: :partner_programs
  has_many :ecom_orders, class_name: "Ecom::Order", inverse_of: :partner, dependent: :destroy
  has_many :enabled_payment_methods, class_name: 'EnabledPaymentMethod', inverse_of: :partner, dependent: :destroy
  has_many(
    :ecom_payment_methods,
    class_name: 'Ecom::PaymentMethod',
    source: :ecom_payment_method,
    through: :enabled_payment_methods,
    dependent: :destroy,
  )
  has_many :finance_relationships, class_name: 'Finance::Relationship', inverse_of: :partner, dependent: :destroy
  has_many :finance_contracts, class_name: 'Finance::Contract', through: :finance_relationships, inverse_of: :partner, source: :contracts
  has_many :sections


  accepts_nested_attributes_for :theme, update_only: true
  accepts_nested_attributes_for :certificate_theme, allow_destroy: true
  accepts_nested_attributes_for :ecom_payment_methods
  accepts_nested_attributes_for :finance_relationships
  accepts_nested_attributes_for :address, allow_destroy: true

  before_validation :set_dns_status, :sanitize_proxy_host, :remove_blank_ecom_payment_providers, :upcase_abbreviation

  validates :name, presence: true, uniqueness: true
  validates :short_name, presence: true
  validates :formal_name, presence: true
  validates :abbreviation, uniqueness: true, allow_blank: true
  validates :currency_code, currency_code: true

  validate :matching_promos_eva_discount_code_currency_code
  validate :ecom_payment_providers_has_valid_keys


  def eva_enabled?
    promos_eva_discount_code.present?
  end

  def active_admin_display_name
    name
  end

  def gf?
    name.downcase.in? %w[greenfig ziplines]
  end

  def ziplines?
    slug == 'ziplines'
  end

  private

  def set_dns_status
    if proxy_host.blank?
      self.dns_status = :disabled
    elsif dns_status == 'disabled'
      self.dns_status = :pending
    end
  end

  def sanitize_proxy_host
    return if proxy_host.blank?

    self.proxy_host = proxy_host&.strip&.sub('https://', '')&.sub('http://', '')&.delete_suffix('/')
  end

  def matching_promos_eva_discount_code_currency_code
    if promos_eva_discount_code.blank? ||
        !promos_eva_discount_code.discount.is_a?(Promos::Discount::AmountOff) ||
        currency_code == promos_eva_discount_code.discount.currency_code
      return
    end

    errors.add(:promos_eva_discount_code_id, "discount currency code for amount off must match the currency code")
  end

  def ecom_payment_providers_has_valid_keys
    return if ecom_payment_providers.blank?

    invalid_keys = ecom_payment_providers - Ecom::PaymentProvider::KEYS
    return if invalid_keys.empty?

    errors.add(:ecom_payment_providers, "contains invalid payment provider keys: #{invalid_keys.join(', ')}")
  end

  def remove_blank_ecom_payment_providers
    self.ecom_payment_providers = ecom_payment_providers.compact_blank! if ecom_payment_providers.is_a?(Array)
  end

  def upcase_abbreviation
    self.abbreviation = abbreviation.upcase if abbreviation.present?
  end
end
