# frozen_string_literal: true

class ApplicationRecord < ActiveRecord::Base
  include ActsAsRansackable
  include ResourceGroupable
  include RichEnums

  primary_abstract_class

  strip_attributes collapse_spaces: true

  expose_all_attributes_to_ransack
  expose_all_scopes_to_ransack
  expose_all_associations_to_ransack

  def self.inherited(subclass)
    super
    subclass.include HasRemoteResources if subclass.name.in? Remote::CORE_RECORD_TYPES
  end
end
