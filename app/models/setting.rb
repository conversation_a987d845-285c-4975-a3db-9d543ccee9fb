# frozen_string_literal: true

# == Schema Information
#
# Table name: settings
#
#  id         :bigint           not null, primary key
#  key        :string           not null
#  name       :string           not null
#  value      :jsonb            not null
#  uid        :string           not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
# Indexes
#
#  index_settings_on_key  (key) UNIQUE
#  index_settings_on_uid  (uid) UNIQUE
#
class Setting < ApplicationRecord
  include HasUid
  include ResourceGroupable

  has_paper_trail

  self.authorized_resource_group = 'Admin'

  has_uid prefix: 'GS'

  validates :name, presence: true
  validates :key, presence: true, uniqueness: true

  before_validation :set_key, on: :create

  attr_readonly :key

  class << self

    # @param key [String, Symbol] The key of the setting to retrieve.
    # @param path [Array<String, Symbol>] The path to the value within the JSONB column.
    # Example:
    #   If setting.value = { discount: { code: 'SUMMER', amount: 100_00 } }
    #   Setting.value_for(:discount, :code) => 'SUMMER'
    #   Setting.value_for(:discount, :amount) => 100_00
    #   Setting.value_for(:discount) => { code: 'SUMMER', amount: 100_00 }
    #   Setting.value_for(:non_existent_key) => {}
    def value_for(key, *path)
      setting = find_by(key:)
      return {} unless setting&.value

      if path.empty?
        setting.value
      else
        setting.value.dig(*path.map(&:to_s))
      end
    end
  end


  private

  def set_key
    self.key = name.parameterize.underscore if name.present? && key.blank?
  end
end
