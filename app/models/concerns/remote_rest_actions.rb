# frozen_string_literal: true

module RemoteRestActions
  extend ActiveSupport::Concern

  # client and remote_collection_path to be configured after this module is included

  def client
    raise NotImplementedError.new("Define client in including class/module")
  end
  delegate(*HttpClient::SUPPORTED_HTTP_METHODS, to: :client)

  def remote_create(json = default_payload, params: nil, &)
    post(remote_create_path, json:, params:, &)
  end

  def remote_find(params: nil, &)
    get(remote_find_path, params:, &)
  end

  def remote_update(json = default_payload, params: nil, &)
    put(remote_update_path, json:, params:, &)
  end

  def remote_delete(json: nil, params: nil, &)
    delete(remote_delete_path, json:, params:, &)
  end

  private

  def must_have!(kwargs)
    # raises if required keyword arguments are missing
    blank_args = kwargs.select { |_, v| v.blank? }
    raise ArgumentError.new("Given arguments must have a value: #{blank_args.keys.to_sentence}") if blank_args.any?

    kwargs # truthy
  end
end
