# frozen_string_literal: true

# == Schema Information
#
# Table name: enrollments
#
#  id                        :bigint           not null, primary key
#  certificate_issued_at     :datetime
#  certificate_url           :string
#  certifier                 :integer          default("none"), not null
#  clas_cohort_admission_key :integer
#  course_risk               :integer
#  course_risk_reviewed_at   :datetime
#  exit_requested_on         :date
#  extended_until            :date
#  extension_reason          :string
#  last_lms_activity_at      :datetime
#  primary                   :boolean          default(TRUE), not null
#  risk_level                :integer          default("on_track"), not null
#  status                    :integer          default("pending"), not null
#  uid                       :string           not null
#  created_at                :datetime         not null
#  updated_at                :datetime         not null
#  deal_id                   :bigint
#  ecom_order_item_id        :bigint           not null
#  extended_by_id            :bigint
#  learner_id                :bigint           not null
#  partner_program_id        :bigint           not null
#  registration_id           :bigint           not null
#  section_id                :bigint           not null
#
# Indexes
#
#  idx_enrollments_section_join                    (section_id,id)
#  index_enrollments_on_clas_cohort_admission_key  (clas_cohort_admission_key) UNIQUE
#  index_enrollments_on_deal_id                    (deal_id)
#  index_enrollments_on_ecom_order_item_id         (ecom_order_item_id)
#  index_enrollments_on_extended_by_id             (extended_by_id)
#  index_enrollments_on_learner_id                 (learner_id)
#  index_enrollments_on_partner_program_id         (partner_program_id)
#  index_enrollments_on_registration_id            (registration_id)
#  index_enrollments_on_status                     (status)
#  index_enrollments_on_uid                        (uid) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (deal_id => deals.id)
#  fk_rails_...  (ecom_order_item_id => ecom_order_items.id)
#  fk_rails_...  (extended_by_id => admin_users.id)
#  fk_rails_...  (learner_id => learners.id)
#  fk_rails_...  (partner_program_id => partner_programs.id)
#  fk_rails_...  (registration_id => registrations.id)
#  fk_rails_...  (section_id => sections.id)
#
class Enrollment < ApplicationRecord
  include HasUid
  include SetAsPrimary
  include HasStatus

  self.authorized_resource_group = 'Customers'

  has_uid prefix: 'E'

  has_status
  has_status :risk_level

  has_paper_trail

  STATUS_REASON_REQUIRED = %w[no_pass unenroll dropped withdrew paused].freeze

  rich_enum certifier: Partner.certifiers.merge(Partner.certifier_names) { |_k, int, name| [int, name] }, prefix: true, alt: 'name'
  rich_enum status: {
    pending: [0, 'Pending'],
    active: [1, 'Active'],
    no_pass: [2, 'No Pass'],
    pass: [3, 'Pass'],
    certificate_issued: [4, 'Certificate Issued'],
    unenrolled: [5, 'Unenrolled'],
    dropped: [6, 'Dropped'],
    transferred: [7, 'Transferred'],
    withdrew: [8, 'Withdrew'],
    paused: [9, 'Paused'],
  }, suffix: true, alt: 'name'
  rich_enum course_risk: {
    on_track: [0, 'On-Track'],
    low: [1, 'Low Risk'],
    moderate: [2, 'Moderate Risk'],
    high: [3, 'High Risk'],
  }, suffix: true, alt: 'name'

  rich_enum risk_level: LearningDelivery::RiskAssessment::RISK_LEVEL_DEFINITION, alt: 'name'


  POST_LEARNER_STATUSES = %w[no_pass pass certificate_issued].freeze

  USER_EXIT_STATUSES = %w[unenrolled dropped withdrew].freeze
  SYSTEM_ONLY_EXIT_STATUS = %w[paused].freeze
  RETAINED_STATUSES = %w[pending active no_pass pass certificate_issued].freeze
  EXIT_STATUSES = (USER_EXIT_STATUSES + SYSTEM_ONLY_EXIT_STATUS).freeze

  scope :post_learner, -> { where(status: POST_LEARNER_STATUSES) }
  scope :exited, -> { where(status: [*USER_EXIT_STATUSES, 'transferred']) }
  scope :primary, -> { where(primary: true) }
  scope :gradeable, -> { where(primary: true, status: :active, extended_until: [nil, Time.current..]) }
  scope :fully_paid, -> { joins(:ecom_payment).where(ecom_payments: { status: :paid }) }
  scope :re_enrolls, lambda {
    primary
      .joins(:registration).where(registrations: { reenrolled_from_id: 1.. })
      .joins(:ecom_order).where(ecom_orders: { purpose: :standard })
  }
  scope :re_enrolled_at_no_cost, lambda {
    re_enrolls.joins(:ecom_payment).where(ecom_payments: { total_cents: 0 })
  }
  scope :re_enrolled_at_discount, lambda {
    re_enrolls.joins(:ecom_payment).where(ecom_payments: { total_cents: 1.. })
  }

  # Enrollments still in the section (not exited or transfered or paused)
  scope :retained, -> { where(status: RETAINED_STATUSES) }

  belongs_to :ecom_order_item, class_name: "Ecom::OrderItem", inverse_of: false
  belongs_to :learner
  belongs_to :partner_program
  belongs_to :deal
  belongs_to :registration
  belongs_to :section
  belongs_to :extended_by, class_name: 'AdminUser', optional: true, inverse_of: :extended_enrollments

  has_one :reenrolled_to, class_name: 'Registration', foreign_key: :reenrolled_from_id, inverse_of: :reenrolled_from

  has_one :finance_contract, through: :ecom_order_item
  has_one :finance_relationship, through: :finance_contract, source: :relationship
  has_many :finance_bookings, through: :ecom_order_item

  # The transfer where this enrollment is the `transferred_from`, so this describes where this enrollment was transferred to.
  has_one :to_transfer, class_name: "Enrollment::Transfer", dependent: :destroy, inverse_of: :transferred_from
  has_one :transferred_to, class_name: "Enrollment", through: :to_transfer

  # The transfer where this enrollment is the `transferred_to`, so this describes where this enrollment was transferred from.
  has_one :from_transfer, class_name: "Enrollment::Transfer", dependent: :destroy, inverse_of: :transferred_to
  has_one :transferred_from, class_name: "Enrollment", through: :from_transfer

  has_many :transfers, class_name: "Enrollment::Transfer", foreign_key: :registration_id, primary_key: :registration_id, inverse_of: false

  has_one :ecom_order, through: :ecom_order_item, class_name: 'Ecom::Order', source: :order
  has_one :ecom_payment, through: :ecom_order, class_name: 'Ecom::Payment', source: :payment

  has_one :cohort, through: :section
  has_one :program, through: :partner_program
  has_one :partner, through: :partner_program
  has_one :promos_referrer, through: :registration
  has_one :remote_canvas_user, through: :learner

  has_many :status_changes, class_name: 'Enrollment::StatusChange', dependent: :destroy

  has_many :external_content_material_sets, class_name: 'ExternalContent::MaterialSet', through: :program
  has_many :external_content_code_uses, class_name: 'ExternalContent::CodeUse'

  has_many :live_session_reviews, class_name: 'LiveSession::Review'

  has_many :learning_delivery_risk_assessments, class_name: 'LearningDelivery::RiskAssessment',
    dependent: :destroy, inverse_of: :enrollment
  has_one :highest_risk_assessment, -> { order(risk_level: :desc) },
    class_name: 'LearningDelivery::RiskAssessment', inverse_of: :enrollment
  has_many :lms_submissions, class_name: 'Lms::Submission'
  has_many :lms_submission_comments, through: :lms_submissions, source: :comments

  set_as_primary :primary, owner_key: :ecom_order_item

  delegate :eva_type, :eva_type_name, :third_party_entity, to: :registration
  delegate :time_zone, to: :partner_program
  delegate :purpose, to: :ecom_order

  validates :certificate_issued_at, presence: true, if: :certificate_url?
  validates :clas_cohort_admission_key, uniqueness: true, allow_nil: true


  def transfer?
    transferred_from.present?
  end

  def extended?
    extended_until.present?
  end

  def extendable?
    active_status? || pending_status?
  end

  def canvas_status
    remote_canvas_enrollment.present? ? 'active' : 'inactive'
  end

  # @return [StatusChange] The most recent StatusChange where the status_became the given status.
  # @param status_became [Symbol] The status to search for.
  def latest_status_change_for(status_became)
    status_changes.where(status_became:).last
  end

  def not_canvas_enrolled_yet?
    !status_changes.exists?(status_became: 'active')
  end

  def risk_reason
    return if risk_level == 'on_track'

    highest_risk_assessment&.risk_type_name
  end
end
