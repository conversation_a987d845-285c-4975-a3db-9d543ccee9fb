# frozen_string_literal: true

# == Schema Information
#
# Table name: registrations
#
#  id                      :bigint           not null, primary key
#  aspiration              :integer
#  clas_external_key       :string
#  clas_key                :integer
#  eva_type                :integer
#  experience_level        :integer
#  notes                   :text
#  status                  :integer          default("confirmed"), not null
#  third_party_entity      :string
#  tracking_key            :string
#  uid                     :string           not null
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  admin_user_id           :bigint
#  deal_id                 :bigint
#  email_id                :bigint           not null
#  finance_relationship_id :bigint
#  learner_id              :bigint           not null
#  partner_program_id      :bigint           not null
#  promos_referrer_id      :bigint
#  reenrolled_from_id      :bigint
#  section_id              :bigint           not null
#  site_ad_tracking_id     :bigint
#
# Indexes
#
#  index_registrations_on_admin_user_id            (admin_user_id)
#  index_registrations_on_clas_external_key        (clas_external_key) UNIQUE
#  index_registrations_on_clas_key                 (clas_key) UNIQUE
#  index_registrations_on_deal_id                  (deal_id)
#  index_registrations_on_email_id                 (email_id)
#  index_registrations_on_finance_relationship_id  (finance_relationship_id)
#  index_registrations_on_learner_id               (learner_id)
#  index_registrations_on_partner_program_id       (partner_program_id)
#  index_registrations_on_promos_referrer_id       (promos_referrer_id)
#  index_registrations_on_reenrolled_from_id       (reenrolled_from_id)
#  index_registrations_on_section_id               (section_id)
#  index_registrations_on_site_ad_tracking_id      (site_ad_tracking_id)
#  index_registrations_on_status                   (status)
#  index_registrations_on_uid                      (uid) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (admin_user_id => admin_users.id)
#  fk_rails_...  (deal_id => deals.id)
#  fk_rails_...  (email_id => emails.id)
#  fk_rails_...  (finance_relationship_id => finance_relationships.id)
#  fk_rails_...  (learner_id => learners.id)
#  fk_rails_...  (partner_program_id => partner_programs.id)
#  fk_rails_...  (promos_referrer_id => promos_referrers.id)
#  fk_rails_...  (reenrolled_from_id => enrollments.id)
#  fk_rails_...  (section_id => sections.id)
#  fk_rails_...  (site_ad_tracking_id => site_ad_trackings.id)
#
class Registration < ApplicationRecord
  include HasUid
  include HasStatus

  self.authorized_resource_group = 'Customers'

  has_uid prefix: 'R'
  has_status

  rich_enum experience_level: { newbie: [0, 'Newbie'], some: [1, 'Some'], current: [2, 'Current'] }, prefix: true, alt: 'name'
  rich_enum aspiration: { other: [0, 'Other'], begin: [1, 'Begin'], advance: [2, 'Advance'], change: [3, 'Change'] }, prefix: true, alt: 'name'
  rich_enum eva_type: { employee: [1, 'Employee'], veteran: [2, 'Veteran'], alumni: [3, 'Alumni'] }, prefix: true, alt: 'name'
  rich_enum status: {
    created: [0, 'Created'],
    confirmed: [1, 'Confirmed'],
    cancelled: [2, 'Cancelled'],
    cohort_picked: [3, 'Cohort Picked'],
    section_picked: [4, 'Section Picked'],
  }, suffix: true, alt: 'name'

  def confirmable?
    status.in?(%w[created cohort_picked section_picked])
  end

  belongs_to :email
  belongs_to :learner
  belongs_to :section
  belongs_to :partner_program
  belongs_to :deal, optional: true
  belongs_to :finance_relationship, class_name: 'Finance::Relationship'
  belongs_to :admin_user, optional: true # If an Admin created the registration on behalf of the learner
  belongs_to :site_ad_tracking, class_name: 'Site::AdTracking', optional: true, inverse_of: :registration
  belongs_to :reenrolled_from, class_name: 'Enrollment', optional: true, inverse_of: :reenrolled_to
  belongs_to :promos_referrer, class_name: 'Promos::Referrer', optional: true

  has_one :cohort, through: :section
  has_one :program, through: :partner_program
  has_one :partner, through: :partner_program
  has_one :ecom_order_item, inverse_of: :registration, class_name: 'Ecom::OrderItem', dependent: :destroy
  has_one :ecom_order, through: :ecom_order_item, class_name: 'Ecom::Order', source: :order
  has_one :ecom_payment, through: :ecom_order, class_name: 'Ecom::Payment', source: :payment

  has_many :enrollments, dependent: :destroy
  has_one :primary_enrollment, -> { merge(Enrollment.primary) }, class_name: "Enrollment", inverse_of: false


  delegate :address, to: :email, prefix: true, allow_nil: true
  delegate :first_name, :last_name, to: :learner, allow_nil: true

  accepts_nested_attributes_for :site_ad_tracking

  validates :clas_key, uniqueness: { allow_nil: true }
  validate :eva_enabled, on: :create
  validates :email_address, format: {
    with: /\A([a-z]*\s*)*<*([^@\s]+)@((?:[-a-z0-9]+\.)+[a-z]{2,})>*\z/,
    message: "is not valid",
  }

  class << self
    def ransackable_attributes(_auth_object = nil)
      super + %w[email_address_cont]
    end
  end

  def ecom_variant
    @ecom_variant ||= Ecom::Variant.where(section:, partner:).first || section&.ecom_variant
  end


  private

  def eva_enabled
    return if eva_type.blank?

    errors.add(:eva_type, 'is not allowed for this institution') unless partner.eva_enabled?
  end
end
