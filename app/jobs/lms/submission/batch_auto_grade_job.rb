# frozen_string_literal: true

module Lms
  class Submission
    class BatchAutoGradeJob < BaseSidekiqJob
      def perform
        active_sections_with_graders.find_each do |section|
          submissions = find_submitted_submissions(section)

          submissions.find_each do |submission|
            next if submission.completed?

            Lms::Submission::AutoGradeJob.perform_async(submission.id, false)
          end
        end
      end

      private

      def active_sections_with_graders
        @active_sections_with_graders ||= Section.active.where.not(grader_id: nil)
      end

      def find_submitted_submissions(section)
        Lms::Submission
          .submitted
          .joins(:enrollment)
          .where(enrollments: { section_id: section.id })
          .where.not(
            id: Lms::Submission::Review.where('lms_submission_reviews.attempt = lms_submissions.attempt')
              .select(:submission_id),
          )
      end
    end
  end
end
