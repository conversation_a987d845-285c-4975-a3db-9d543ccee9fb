# frozen_string_literal: true

module Lms
  class Submission
    class InactiveReviewSlackAlertJob < BaseCronJob
      scout_apm_sample_rate 0.25

      INACTIVE_DURATION_THRESHOLD = 4.hours
      BATCH_SIZE = 100

      EXCLUDED_STATES = %w[published manual_review_needed stale].freeze

      def perform
        return unless feature_enabled?

        inactive_reviews.find_each(batch_size: BATCH_SIZE) do |review|
          Alert::OpenCommand::InactiveReview.call!(review:)
        end
      end

      private

      def feature_enabled?
        FeatureFlag.enabled?(:alert_inactive_review)
      end

      def inactive_reviews
        Lms::Submission::Review
          .where(training: false)
          .where.not(state: EXCLUDED_STATES)
          .where(lms_submission_reviews: { updated_at: ...INACTIVE_DURATION_THRESHOLD.ago })
      end
    end
  end
end
