# frozen_string_literal: true

module Lms
  class Submission
    class Comment
      class FetchBySectionJob < BaseSidekiqJob
        DEFAULT_LOOKBACK = 4.hours

        # @param section_id [Integer]
        # @param updated_since_iso8601 [String, nil]
        def perform(section_id, updated_since_iso8601 = nil)
          section = Section.find(section_id)

          updated_since = updated_since_iso8601.present? ? Time.zone.parse(updated_since_iso8601) : DEFAULT_LOOKBACK.ago

          Lms::Submission::Comment::FetchBySectionCommand.call!(
            section:,
            updated_since:,
          )
        end
      end
    end
  end
end
