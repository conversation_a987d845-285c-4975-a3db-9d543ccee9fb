# frozen_string_literal: true

class Section
  class FetchSubmissionsJob < BaseCronJob
    DEFAULT_LOOKBACK = 12.hours

    attr_reader :states

    def perform(states = nil)
      @states = states

      sections.find_each do |section|
        Section::FetchSubmissionsCommand.call!(
          section:,
          states:,
          submitted_since:,
          graded_since:,
        )

        Lms::Submission::Comment::FetchBySectionJob.perform_async(section.id)
      end
    end

    private

    def sections
      Section.joins(:cohort)
        .where(cohorts: { status: Section::FetchSubmissionsCommand::SUBMISSION_SYNCABLE_COHORT_STATUSES })
        .where.associated(:remote_canvas_section)
    end

    def submitted_since
      DEFAULT_LOOKBACK.ago if states == ['submitted']
    end

    def graded_since
      DEFAULT_LOOKBACK.ago if states == ['graded']
    end
  end
end
