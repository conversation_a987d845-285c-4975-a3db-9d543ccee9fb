# frozen_string_literal: true

require 'rails_helper'

describe Payments::Transaction::Charge::CreateCommand do
  describe '#call!' do
    let(:scenario) { PaidLearnerScenario.create(partner_args: [currency_code:]) }
    let(:currency_code) { Ecom::Currency::VALID_CODES.sample }
    let(:event) { create(:payments_event, :unprocessed) }
    let(:processor) { :stripe }
    let(:payer_processor_key) { payments_payer.processor_key }
    let(:transaction_processor_key) { "ch_#{rand}" }
    let(:installment_plan_processor_key) { nil }
    let(:manual_payment) { nil }
    let(:payments_payer) { scenario.payments_payer }
    let(:ecom_order) { scenario.ecom_order }
    let(:ecom_payment) { scenario.ecom_payment }
    let(:ecom_payment_method) { scenario.ecom_payment_method }
    let(:amount_cents) { scenario.ecom_payment.total_cents }
    let(:transacted_at) { Time.zone.now - rand(1..1000).seconds }
    let!(:apply_payments_transaction_command_class) { class_spy(Ecom::Payment::ApplyPaymentsTransactionCommand, call!: nil).as_stubbed_const }
    let(:command) do
      described_class.new(
        event:,
        processor:,
        payer_processor_key:,
        transaction_processor_key:,
        installment_plan_processor_key:,
        manual_payment:,
        ecom_order:,
        ecom_payment_method:,
        amount_cents:,
        transacted_at:,
      )
    end

    def expect_valid_charge
      charge = call!

      expect(apply_payments_transaction_command_class).to have_received(:call!).with(
        payment: ecom_payment,
        payments_transaction: charge,
      )
      expect(charge).to be_a(Payments::Transaction::Charge)
      expect(charge.ecom_payment).to eq(ecom_payment)
      expect(charge.currency_code).to eq(currency_code)
      expect(charge.amount_cents).to eq(amount_cents)
      expect(charge.transacted_at).to be_within(1.second).of(transacted_at)
      expect(charge.processor_key).to eq(transaction_processor_key)
      expect(charge.processor).to eq(processor.to_s)
      expect(charge.payer.processor_key).to eq(payer_processor_key)
    end

    subject(:call!) { command.call! }

    context 'with an unknown processor' do
      let(:processor) { :unknown }

      it 'raises an error' do
        expect { call! }.to raise_error(ArgumentError, /Processor is not supported: unknown/)
      end
    end

    context 'when the event already has a transaction' do
      let(:event) { create(:payments_event, payments_transaction: build(:payments_transaction, :charge)) }

      it 'raises an error' do
        expect { call! }.to raise_error(ArgumentError, /Event already has a transaction/)
      end
    end

    context 'when the event transactions is already processed' do
      let(:event) { create(:payments_event, :processed) }

      it 'raises an error' do
        expect { call! }.to raise_error(ArgumentError, /Event transactions is already processed/)
      end
    end

    it 'creates a charge transaction for the event' do
      expect { call! }.to change { Payments::Transaction::Charge.count }.by(1)

      expect_valid_charge
    end

    context 'when the payment method is installments' do
      let(:ecom_payment_method) { create(:ecom_payment_method, kind: 'installments') }
      let(:installment_plan_processor_key) { "sub_#{FFaker::UUID.uuidv4}" }

      it 'creates an installment plan for the charge' do
        expect { call! }.to change { Payments::InstallmentPlan.count }.by(1)

        expect(ecom_order.payment.payments_installment_plan.processor_key).to eq(installment_plan_processor_key)

        expect_valid_charge
      end

      context 'with existing installment plan' do
        let!(:installment_plan) { create(:payments_installment_plan, processor_key: installment_plan_processor_key, ecom_payment:) }

        it 'uses the existing installment plan' do
          expect { call! }.not_to(change { Payments::InstallmentPlan.count })

          expect(ecom_order.payment.payments_installment_plan).to eq(installment_plan)

          expect_valid_charge
        end

        context 'with canceled installment plan' do
          before { installment_plan.update!(status: 'canceled', canceled_at: Time.current, canceled_by_admin_user: create(:admin_user)) }

          context 'with a manual payment' do
            let(:manual_payment) { create(:payments_manual_payment, ecom_payment:) }

            it 'processes the manual payment' do
              expect { call! }.to change { manual_payment.reload.status }.to('paid')

              expect(manual_payment.transaction_charge).to be_a(Payments::Transaction::Charge)
              expect(installment_plan.reload.status).to eq('canceled') # remains canceled

              expect_valid_charge
            end
          end
        end
      end
    end

    context 'with a manual payment' do
      let(:manual_payment) { create(:payments_manual_payment, ecom_payment:) }

      it 'processes the manual payment' do
        expect { call! }.to change { manual_payment.reload.status }.to('paid')

        expect(manual_payment.transaction_charge).to be_a(Payments::Transaction::Charge)

        expect_valid_charge
      end
    end
  end
end
