# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Deal::SwitchPartnerCommand, type: :command do
  around do |example|
    Timecop.freeze(Time.current.beginning_of_hour) do
      example.run
    end
  end

  let(:source_partner) { create(:partner, :configured, proxy_host: 'source-domain.example.com') }
  let(:target_partner) { create(:partner, :configured, proxy_host: 'target-domain.example.com') }
  let(:program) { create(:program) }
  let!(:source_partner_program) { create(:partner_program, partner: source_partner, program:) }
  let!(:target_partner_program) { create(:partner_program, partner: target_partner, program:) }
  let!(:deal) { create(:deal, :open, partner_program: source_partner_program) }
  let!(:registration) do
    create(:registration, deal:, partner_program: source_partner_program, email: deal.email)
  end
  let!(:order) { create(:ecom_order, partner: source_partner) }
  let!(:order_item) { create(:ecom_order_item, order:, registration:, finance_contract: registration.finance_relationship.contracts.first) }
  let!(:syllabus_request) { create(:syllabus_request, deal:, email: deal.email, partner_program: source_partner_program) }
  let(:finance_relationship) { create(:finance_relationship, partner: target_partner, default: true) }
  let(:finance_contract) { create(:finance_contract, relationship: finance_relationship) }

  before do
    allow(PartnerProgram).to receive(:find_by!)
      .with(partner: target_partner, program:)
      .and_return(target_partner_program)

    allow_any_instance_of(described_class)
      .to receive(:finance_relationship)
      .and_return(finance_relationship)

    allow(finance_relationship)
      .to receive(:active_contract)
      .and_return(finance_contract)
  end

  describe '.callable?' do
    subject(:callable) { described_class.callable?(deal:, target_partner:) }

    context 'when deal is closed won' do
      let(:deal) { create(:deal, :closed_won, partner: source_partner, program:) }

      it 'is not callable' do
        expect(callable).to be false
        expect(deal.errors[:status]).to include('must not be closed won')
      end
    end

    context 'when target partner does not offer the program' do
      let(:other_program) { create(:program) }
      let(:target_partner) { create(:partner, programs: [other_program]) }

      it 'is not callable' do
        expect(callable).to be false
        expect(deal.errors[:partner].first).to include("#{target_partner.name} does not offer program")
      end
    end

    context 'when target partner is the same as source partner' do
      it 'is not callable' do
        expect(described_class.callable?(deal:, target_partner: source_partner)).to be false
        expect(deal.errors[:partner]).to include('must not be same partner')
      end
    end

    context 'when all validations pass' do
      it 'is callable' do
        expect(callable).to be true
      end
    end
  end

  describe '#call!' do
    let(:command) { described_class.new(deal:, target_partner:) }
    let!(:update_short_payment_url_command) do
      instance_double(Ecom::Order::UpdateShortPaymentUrlCommand, call!: true)
    end

    before do
      allow(Ecom::Order::UpdateShortPaymentUrlCommand)
        .to receive(:new)
        .and_return(update_short_payment_url_command)
    end

    context "when the customer does not have an Open Deal for the target partner program" do
      it 'updates the deal with the new partner program' do
        expect { command.call! }
          .to change { deal.reload.partner_program }
          .from(source_partner_program).to(target_partner_program)
      end

      it 'updates the registration with the new partner program and finance relationship' do
        command.call!
        registration.reload
        expect(registration.partner_program).to eq(target_partner_program)
        expect(registration.finance_relationship).to eq(finance_relationship)
      end

      it 'updates the order with the new partner' do
        expect { command.call! }
          .to change { order.reload.partner }
          .from(source_partner).to(target_partner)
      end

      it 'updates the syllabus request with the new partner program' do
        expect { command.call! }
          .to change { syllabus_request.reload.partner_program }
          .from(source_partner_program).to(target_partner_program)
      end

      it 'updates the order item with the new finance contract' do
        expect { command.call! }
          .to change { order_item.reload.finance_contract.partner }
          .from(source_partner).to(target_partner)
        expect(order_item.reload.finance_contract).to eq(finance_contract)
      end

      context 'when domain changes' do
        let(:target_partner) { create(:partner, proxy_host: 'new-domain.example.com') }

        it 'updates the short payment URL' do
          command.call!
          expect(Ecom::Order::UpdateShortPaymentUrlCommand)
            .to have_received(:new)
            .with(order:)
        end
      end

      context 'when domain does not change' do
        let(:target_partner) { create(:partner, proxy_host: source_partner.proxy_host) }

        it 'does not update the short payment URL' do
          command.call!
          expect(Ecom::Order::UpdateShortPaymentUrlCommand)
            .not_to have_received(:new)
        end
      end

      context 'when publish is false' do
        let(:command) { described_class.new(deal:, target_partner:, publish: false) }

        before { allow(Deal::PublishToHubspotJob).to receive(:perform_async) }

        it 'does not publish to HubSpot' do
          command.call!
          expect(Deal::PublishToHubspotJob).not_to have_received(:perform_async)
        end
      end

      context 'when publish is true' do
        before { allow(Deal::PublishToHubspotJob).to receive(:perform_async) }

        it 'publishes to HubSpot' do
          command.call!
          expect(Deal::PublishToHubspotJob)
            .to have_received(:perform_async)
            .with(deal.id)
        end
      end
    end

    context 'when the customer has an Open Deal for the target partner program' do
      let(:deal) { create(:deal, :open, partner_program: source_partner_program) }
      let!(:existing_deal) { create(:deal, :open, partner_program: target_partner_program, email: deal.email) }
      let!(:command) { described_class.new(deal:, target_partner:) }

      describe '#unpublish_remote_hubspot_deal_later' do
        let(:remote_hubspot_deal) { instance_double(Remote::Hubspot::Deal, key: 'test-deal-key') }
        let(:job) { class_double(Remote::Hubspot::Deal::UnpublishJob) }

        before do
          allow(deal).to receive(:remote_hubspot_deal).and_return(remote_hubspot_deal)
          stub_const('Remote::Hubspot::Deal::UnpublishJob', job)
          allow(job).to receive(:perform_async)
        end

        it 'enqueues the UnpublishJob with the remote deal key' do
          command.send(:unpublish_remote_hubspot_deal_later)
          expect(job).to have_received(:perform_async).with('test-deal-key')
        end

        it 'does not enqueue job if no remote hubspot deal exists' do
          allow(deal).to receive(:remote_hubspot_deal).and_return(nil)
          command.send(:unpublish_remote_hubspot_deal_later)
          expect(job).not_to have_received(:perform_async)
        end
      end

      describe '#destroy_deal' do
        it 'destroys the deal' do
          expect { command.call! }
            .to change { Deal.exists?(command.deal.id) }
            .from(true).to(false)
        end
      end

      describe '#update_deal' do
        let!(:now) { Time.current }

        before do
          travel_to(now) do
            deal.update!(
              last_became_prospect_at: 2.days.ago,
              last_became_registrant_at: 1.day.ago,
              revived_at: 12.hours.ago,
              stage: 'prospect',
            )
          end
        end

        it 'merges deal attributes when target deal exists', :aggregate_failures do
          command.send(:update_deal)

          existing_deal.reload
          expect(existing_deal.last_became_prospect_at).to be_within(1.second).of(2.days.ago)
          expect(existing_deal.last_became_registrant_at).to be_within(1.second).of(1.day.ago)
          expect(existing_deal.revived_at).to be_within(1.second).of(12.hours.ago)
          expect(existing_deal.stage).to eq('prospect')
        end

        it 'does not update the partner program since the deal was already existing with the correct partner program' do
          expect { command.call! }
            .not_to(change { existing_deal.reload.partner_program })
        end
      end
    end
  end
end
