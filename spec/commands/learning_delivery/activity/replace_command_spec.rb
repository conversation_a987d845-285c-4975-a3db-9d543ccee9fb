# frozen_string_literal: true

require 'rails_helper'

module LearningDelivery
  class Activity
    describe ReplaceCommand, type: :command do
      let(:employee) { create(:learning_delivery_employee, :advocate) }
      let(:attributes) do
        {
          'activity_type' => 'grade',
          'title' => FFaker::Lorem.sentence,
          'description' => FFaker::Lorem.sentence,
          'metadata' => { 'submissions_count' => 5, 'duration_minutes' => 30 },
        }
      end
      let(:command) { described_class.new(employee:, attributes:) }

      describe '.callable?' do
        subject(:callable?) { described_class.callable?(employee:, attributes:) }

        context 'when employee is a LearningDelivery::Employee' do
          context 'when activity_type is valid' do
            it 'returns true' do
              expect(callable?).to be(true)
            end
          end

          context 'when activity_type is invalid' do
            let(:attributes) do
              {
                'activity_type' => 'invalid_type',
                'title' => FFaker::Lorem.sentence,
              }
            end

            it 'returns false' do
              expect(callable?).to be(false)
            end
          end

          context 'when activity_type is missing' do
            let(:attributes) do
              {
                'title' => FFaker::Lorem.sentence,
              }
            end

            it 'returns false' do
              expect(callable?).to be(false)
            end
          end
        end

        context 'when employee is not a LearningDelivery::Employee' do
          let(:employee) { create(:admin_user) }

          it 'returns false' do
            expect(callable?).to be(false)
          end
        end

        context 'when employee is nil' do
          let(:employee) { nil }

          it 'returns false' do
            expect(callable?).to be(false)
          end
        end
      end

      describe '#call!' do
        subject(:call!) { command.call! }

        context 'when command is callable' do
          it 'creates a new activity' do
            expect { call! }.to change { LearningDelivery::Activity.count }.by(1)
          end

          it 'returns the created activity' do
            activity = call!

            expect(activity).to be_a(LearningDelivery::Activity)
            expect(activity).to be_persisted
            expect(activity.employee).to eq(employee)
            expect(activity.activity_type).to eq('grade')
            expect(activity.title).to eq(attributes['title'])
            expect(activity.description).to eq(attributes['description'])
            expect(activity.metadata).to eq(attributes['metadata'])
          end

          context 'with different activity types' do
            LearningDelivery::Activity.activity_types.each_key do |activity_type|
              context "when activity_type is #{activity_type}" do
                let(:attributes) do
                  {
                    'activity_type' => activity_type,
                    'title' => "#{activity_type.capitalize} completed",
                    'description' => "Employee completed #{activity_type} work",
                  }
                end

                it 'creates activity with correct type' do
                  activity = call!

                  expect(activity.activity_type).to eq(activity_type)
                  expect(activity.title).to eq(attributes['title'])
                end
              end
            end
          end

          context 'with target association' do
            let(:section) { create(:section) }
            let(:attributes) do
              {
                'activity_type' => 'task',
                'title' => FFaker::Lorem.sentence,
                'target' => section,
              }
            end

            it 'creates activity with target association' do
              activity = call!

              expect(activity.target).to eq(section)
            end
          end

          context 'with different employee roles' do
            [:advocate, :grader, :instructor].each do |role|
              context "when employee is #{role}" do
                let(:employee) { create(:learning_delivery_employee, role) }

                it 'creates activity for the employee' do
                  activity = call!

                  expect(activity.employee).to eq(employee)
                  expect(activity).to be_persisted
                end
              end
            end
          end
        end

        context 'when command is not callable' do
          context 'when employee is not a LearningDelivery::Employee' do
            let(:employee) { create(:admin_user) }

            it 'returns nil without creating activity' do
              expect { call! }.not_to(change { LearningDelivery::Activity.count })
              expect(call!).to be_nil
            end
          end

          context 'when activity_type is invalid' do
            let(:attributes) do
              {
                'activity_type' => 'invalid_type',
                'title' => FFaker::Lorem.sentence,
              }
            end

            it 'returns nil without creating activity' do
              expect { call! }.not_to(change { LearningDelivery::Activity.count })
              expect(call!).to be_nil
            end
          end

          context 'when employee is nil' do
            let(:employee) { nil }

            it 'returns nil without creating activity' do
              expect { call! }.not_to(change { LearningDelivery::Activity.count })
              expect(call!).to be_nil
            end
          end
        end

        context 'when validation fails' do
          let(:attributes) do
            {
              'activity_type' => 'grade',
              'title' => nil, # Required field missing
            }
          end

          it 'raises validation error' do
            expect { call! }.to raise_error(ActiveRecord::RecordInvalid)
          end

          it 'does not create activity' do
            expect { call! }.not_to(change { LearningDelivery::Activity.count })
          rescue ActiveRecord::RecordInvalid
            # Expected error, continue test
          end
        end
      end

      describe '#callable?' do
        subject(:callable?) { command.callable? }

        context 'when employee is a LearningDelivery::Employee and activity_type is valid' do
          it 'returns true' do
            expect(callable?).to be(true)
          end
        end

        context 'when employee is not a LearningDelivery::Employee' do
          let(:employee) { create(:admin_user) }

          it 'returns false' do
            expect(callable?).to be(false)
          end
        end

        context 'when activity_type is invalid' do
          let(:attributes) do
            {
              'activity_type' => 'invalid_type',
              'title' => FFaker::Lorem.sentence,
            }
          end

          it 'returns false' do
            expect(callable?).to be(false)
          end
        end

        context 'when employee is nil' do
          let(:employee) { nil }

          it 'returns false' do
            expect(callable?).to be(false)
          end
        end
      end

      describe '#initialize' do
        it 'sets employee and attributes' do
          expect(command.employee).to eq(employee)
          expect(command.attributes).to eq(attributes)
        end
      end
    end
  end
end
