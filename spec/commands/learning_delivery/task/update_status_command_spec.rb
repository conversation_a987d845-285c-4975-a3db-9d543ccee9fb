# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LearningDelivery::Task::UpdateStatusCommand do
  let(:task) { create(:learning_delivery_task, :created) }
  let(:admin_user) { create(:admin_user) }
  let(:employee) { create(:learning_delivery_employee, admin_user:) }
  let(:command) { described_class.new(task:, new_status:, user: employee.admin_user) }

  describe '#call' do
    context 'transitioning to viewed' do
      let(:new_status) { :viewed }

      context 'from valid states (created, assigned)' do
        it 'updates status and sets viewed_at timestamp' do
          freeze_time do
            result = nil
            expect { result = command.call }.to change { task.reload.status }.to('viewed')
              .and change { task.viewed_at }.to(Time.current)
            expect(result).to be true
          end
        end

        it 'works from assigned status' do
          task.update!(status: :assigned)
          expect { command.call }.to change { task.reload.status }.to('viewed')
        end
      end

      context 'from invalid states' do
        it 'rejects transition from completed/skipped/expired/viewed' do
          %i[completed skipped expired viewed].each do |status|
            task.update!(status:)
            expect { command.call }.not_to(change { task.reload.status })
            expect(command.call).to be false
          end
        end
      end
    end

    context 'transitioning to completed' do
      let(:new_status) { :completed }

      it 'updates status and sets completed_at timestamp' do
        freeze_time do
          result = nil
          expect { result = command.call }.to change { task.reload.status }.to('completed')
            .and change { task.completed_at }.to(Time.current)
          expect(result).to be true
        end
      end

      it 'creates a learning delivery activity' do
        expect do
          command.call
        end.to change { LearningDelivery::Activity.count }.by(1)

        activity = LearningDelivery::Activity.last
        expect(activity.activity_type).to eq('task')
        expect(activity.employee).to eq(employee)
        expect(activity.target).to eq(task)
      end
    end

    context 'transitioning to other statuses' do
      it 'updates status without setting timestamps' do
        %i[assigned skipped expired].each do |status|
          task = create(:learning_delivery_task, :created)
          command = described_class.new(task:, new_status: status)

          result = nil
          expect { result = command.call }.to change { task.reload.status }.to(status.to_s)
          expect(result).to be true
        end
      end
    end

    context 'with string status parameter' do
      it 'accepts string status parameters' do
        command = described_class.new(task:, new_status: 'viewed')
        expect { command.call }.to change { task.reload.status }.to('viewed')
      end
    end

    context 'error handling' do
      let(:new_status) { :viewed }

      it 'raises exception when task update fails' do
        allow(task).to receive(:update!).and_raise(ActiveRecord::RecordInvalid.new(task))
        expect { command.call }.to raise_error(ActiveRecord::RecordInvalid)
      end
    end
  end
end
