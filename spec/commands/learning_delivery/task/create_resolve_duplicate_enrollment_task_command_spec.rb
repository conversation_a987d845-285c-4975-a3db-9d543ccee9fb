# frozen_string_literal: true

require 'rails_helper'

module LearningDelivery
  class Task
    describe CreateResolveDuplicateEnrollmentTaskCommand do
      describe '#call!' do
        let(:enrollment) { create(:enrollment) }
        let(:duplicate_enrollment) { create(:enrollment, learner: enrollment.learner, section_args: { cohort: enrollment.cohort }) }

        subject(:call!) do
          described_class.call!(
            enrollment:,
            duplicate_enrollment:,
          )
        end

        let!(:task_template) do
          LearningDelivery::TaskTemplate.find_or_initialize_by(task_type: :resolve_duplicate_enrollment, sub_type: nil).tap do |template|
            template.title = 'Resolve Duplicate Enrollment: {LEARNER_NAME}'
            template.description = 'Learner {LEARNER_NAME} ({LEARNER_EMAIL}) has a duplicate enrollment for cohort {COHORT_NAME}.'
            template.recommendation = 'Please review both enrollments. <PERSON><PERSON> may have wanted to enroll ' \
                                      'another person. Contact the learner to resolve the issue.'
            template.reason = 'Duplicate enrollment detected during checkout process.'
            template.save!
          end
        end

        context 'when both enrollments are valid duplicates' do
          let!(:owner) { create(:admin_user, email: described_class::DEFAULT_OWNER_EMAIL) }
          let(:task) { instance_double(LearningDelivery::Task::ResolveDuplicateEnrollment) }

          it 'creates a duplicate enrollment task with the correct owner' do
            allow(LearningDelivery::Task::ResolveDuplicateEnrollment).to receive(:create!).and_return(task)

            call!

            expect(LearningDelivery::Task::ResolveDuplicateEnrollment).to have_received(:create!).with(
              hash_including(owner:),
            ).once
          end
        end

        context 'when the enrollments are for different learners' do
          before do
            duplicate_enrollment.update!(learner: create(:learner))
          end

          it 'does not create a task' do
            allow(LearningDelivery::Task::ResolveDuplicateEnrollment).to receive(:create!).and_return(true)

            call!

            expect(LearningDelivery::Task::ResolveDuplicateEnrollment).not_to have_received(:create!)
          end
        end

        context 'when the enrollments are for different cohorts' do
          before do
            new_cohort = create(:cohort)
            duplicate_enrollment.section.update!(cohort: new_cohort)
          end

          it 'does not create a task' do
            allow(LearningDelivery::Task::ResolveDuplicateEnrollment).to receive(:create!).and_return(true)

            call!

            expect(LearningDelivery::Task::ResolveDuplicateEnrollment).not_to have_received(:create!)
          end
        end

        context 'when one of the enrollments is not primary' do
          before do
            allow(duplicate_enrollment).to receive(:primary?).and_return(false)
          end

          before do
            duplicate_enrollment.section.update!(cohort: enrollment.section.cohort)
            allow(LearningDelivery::Task::ResolveDuplicateEnrollment).to receive(:create!).and_return(true)
          end

          it 'does not create a task' do
            call!

            expect(LearningDelivery::Task::ResolveDuplicateEnrollment).not_to have_received(:create!)
          end
        end

        context 'when the enrollments are the same' do
          let(:duplicate_enrollment) { enrollment }

          it 'does not create a task' do
            allow(LearningDelivery::Task::ResolveDuplicateEnrollment).to receive(:create!).and_return(true)

            call!

            expect(LearningDelivery::Task::ResolveDuplicateEnrollment).not_to have_received(:create!)
          end
        end
      end
    end
  end
end
