# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LearningDelivery::RiskAssessment::Rule::NotActivatedRule, type: :command do
  let(:starts_on) { 1.week.ago }
  let(:lms_opens_on) { 2.weeks.ago }
  let(:enrollment) { create(:enrollment, section_args: { cohort_args: { starts_on:, lms_opens_on: } }) }

  subject(:rule) { described_class.new(enrollment:) }

  before do
    freeze_time

    # Default to enrollment created before cohort starts for most tests
    allow(enrollment).to receive(:created_at).and_return(starts_on - 1.day)
  end

  describe '#call!' do
    context 'when before standard activation threshold' do
      let(:starts_on) { Time.current - described_class::STANDARD_ACTIVATION_THRESHOLD + 1.day }

      it 'returns nil' do
        expect(rule.call!).to be_nil
      end
    end

    context 'when enrollment was created after cohort start' do
      let(:starts_on) { 3.days.ago }

      context 'when within grace period' do
        before do
          allow(enrollment).to receive(:created_at).and_return(12.hours.ago)
        end

        it 'returns nil because the grace period has not expired' do
          expect(rule.call!).to be_nil
        end
      end

      context 'when outside grace period' do
        before do
          allow(enrollment).to receive_messages(created_at: 30.hours.ago)
        end

        it 'returns high risk assessment' do
          result = rule.call!

          expect(result.risk_level).to eq('high_risk')
          expect(result.risk_type).to eq(:not_activated)
        end
      end
    end

    context 'when already assessed as on track' do
      before do
        create(:learning_delivery_risk_assessment,
          enrollment:,
          risk_type: :not_activated,
          risk_level: :on_track,
        )
      end

      it 'returns nil' do
        expect(rule.call!).to be_nil
      end
    end

    context 'when user has logged in after course opened' do
      before do
        enrollment.update!(last_lms_activity_at: 5.days.ago)
      end

      it 'returns on track assessment' do
        result = rule.call!

        expect(result.risk_level).to eq('on_track')
        expect(result.risk_type).to eq(:not_activated)
        expect(result.risk_details).to include(last_login: 5.days.ago)
      end
    end

    context 'when user has not logged in' do
      it 'returns high risk assessment' do
        result = rule.call!

        expect(result.risk_level).to eq('high_risk')
        expect(result.risk_type).to eq(:not_activated)
        expect(result.risk_details).to include(last_login: nil)
      end
    end

    context 'when user has logged in before course opened' do
      before do
        enrollment.update!(last_lms_activity_at: 15.days.ago)
      end

      it 'returns high risk assessment' do
        result = rule.call!

        expect(result.risk_level).to eq('high_risk')
        expect(result.risk_type).to eq(:not_activated)
        expect(result.risk_details).to include(last_login: 15.days.ago)
      end
    end
  end
end
