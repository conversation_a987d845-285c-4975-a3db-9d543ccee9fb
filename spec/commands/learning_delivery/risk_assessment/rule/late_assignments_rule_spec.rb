# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LearningDelivery::RiskAssessment::Rule::LateAssignmentsRule, type: :command do
  let(:remote_canvas_user) { create(:remote_canvas_user) }
  let(:learner) { remote_canvas_user.core_record }
  let(:enrollment) { create(:enrollment, learner:, section_args: { cohort_args: :started }) }
  let(:section) { enrollment.section }
  let(:cohort) { enrollment.cohort }
  let!(:assignment_group) { create(:lms_assignment_group, cohort:) }

  subject(:rule) { described_class.new(enrollment:) }

  before do
    freeze_time
  end

  # Define a shared context for an activated enrollment
  shared_context 'with activated enrollment' do
    before do
      create(:learning_delivery_risk_assessment,
        :not_activated,
        :on_track,
        enrollment:,
        assessed_at: 1.day.ago,
      )
    end
  end

  describe '#call!' do
    context 'when not assessable' do
      # Don't include activated enrollment context here since we're testing when not assessable

      it 'returns nil' do
        expect(rule.call!).to be_nil
      end
    end

    context 'with no late assignments' do
      include_context 'with activated enrollment'

      before do
        overdue_assignment = create(:lms_assignment, :impacts_outcome, :overdue, assignment_group:)
        create(:lms_section_assignment, section:, assignment: overdue_assignment)
        create(:lms_submission,
          :submitted_on_time,
          assignment: overdue_assignment,
          enrollment:,
        )
      end

      it 'returns on track assessment' do
        result = rule.call!

        expect(result.risk_level).to eq('on_track')
        expect(result.risk_type).to eq(:late_assignments)
        expect(result.risk_details).to include(late_assignments_count: 0)
        expect(result.risk_details[:late_assignment_ids]).to be_empty
      end
    end

    context 'with late assignments below low risk threshold' do
      include_context 'with activated enrollment'

      let!(:late_assignment) do
        assignment = create(:lms_assignment, :impacts_outcome, :overdue, assignment_group:)
        create(:lms_section_assignment, section:, assignment:)
        create(:lms_submission,
          :submitted_late,
          assignment:,
          enrollment:,
        )
        assignment
      end

      it 'returns on track assessment' do
        result = rule.call!

        expect(result.risk_level).to eq('on_track')
        expect(result.risk_type).to eq(:late_assignments)
        expect(result.risk_details).to include(late_assignments_count: 1)
        expect(result.risk_details[:late_assignment_ids]).to contain_exactly(late_assignment.id)
      end
    end

    context 'with late assignments at low risk threshold' do
      include_context 'with activated enrollment'

      let!(:late_assignments) do
        (1..2).map do |_i|
          assignment = create(:lms_assignment, :impacts_outcome, :overdue, assignment_group:)
          create(:lms_section_assignment, section:, assignment:)
          due_date = assignment.due_at
          create(:lms_submission,
            assignment:,
            enrollment:,
            state: :submitted,
            submitted_at: due_date + 1.day,
          )
          assignment
        end
      end

      it 'returns low risk assessment' do
        result = rule.call!

        expect(result.risk_level).to eq('low_risk')
        expect(result.risk_type).to eq(:late_assignments)
        expect(result.risk_details).to include(late_assignments_count: 2)
        expect(result.risk_details[:late_assignment_ids]).to match_array(late_assignments.map(&:id))
      end
    end

    context 'with late assignments below high risk threshold' do
      include_context 'with activated enrollment'

      let!(:late_assignments) do
        (1..3).map do |_i|
          assignment = create(:lms_assignment, :impacts_outcome, :overdue, assignment_group:)
          create(:lms_section_assignment, section:, assignment:)
          due_date = assignment.due_at
          create(:lms_submission,
            assignment:,
            enrollment:,
            state: :submitted,
            submitted_at: due_date + 1.day,
          )
          assignment
        end
      end

      it 'returns low risk assessment' do
        result = rule.call!

        expect(result.risk_level).to eq('low_risk')
        expect(result.risk_type).to eq(:late_assignments)
        expect(result.risk_details).to include(late_assignments_count: 3)
        expect(result.risk_details[:late_assignment_ids]).to match_array(late_assignments.map(&:id))
      end
    end

    context 'with late assignments at high risk threshold' do
      include_context 'with activated enrollment'

      let!(:late_assignments) do
        (1..4).map do |_i|
          assignment = create(:lms_assignment, :impacts_outcome, :overdue, assignment_group:)
          create(:lms_section_assignment, section:, assignment:)
          due_date = assignment.due_at
          create(:lms_submission,
            assignment:,
            enrollment:,
            state: :submitted,
            submitted_at: due_date + 1.day,
          )
          assignment
        end
      end

      it 'returns high risk assessment' do
        result = rule.call!

        expect(result.risk_level).to eq('high_risk')
        expect(result.risk_type).to eq(:late_assignments)
        expect(result.risk_details).to include(late_assignments_count: 4)
        expect(result.risk_details[:late_assignment_ids]).to match_array(late_assignments.map(&:id))
      end
    end

    context 'with some on-time and some late submissions' do
      include_context 'with activated enrollment'

      let!(:late_assignments) do
        (1..2).map do |_i|
          assignment = create(:lms_assignment, :impacts_outcome, :overdue, assignment_group:)
          create(:lms_section_assignment, section:, assignment:)
          due_date = assignment.due_at
          create(:lms_submission,
            assignment:,
            enrollment:,
            state: :submitted,
            submitted_at: due_date + 1.day,
          )
          assignment
        end
      end

      let!(:on_time_assignment) do
        assignment = create(:lms_assignment, :impacts_outcome, :overdue, assignment_group:)
        create(:lms_section_assignment, section:, assignment:)
        create(:lms_submission,
          :submitted_on_time,
          assignment:,
          enrollment:,
        )
        assignment
      end

      it 'returns low risk assessment with correct late assignments' do
        result = rule.call!

        expect(result.risk_level).to eq('low_risk')
        expect(result.risk_type).to eq(:late_assignments)
        expect(result.risk_details).to include(late_assignments_count: 2)
        expect(result.risk_details[:late_assignment_ids]).to match_array(late_assignments.map(&:id))
        expect(result.risk_details[:late_assignment_ids]).not_to include(on_time_assignment.id)
      end
    end

    context 'with some future and some late submissions' do
      include_context 'with activated enrollment'

      let!(:late_assignment) do
        assignment = create(:lms_assignment, :impacts_outcome, :overdue, assignment_group:)
        create(:lms_section_assignment, section:, assignment:)
        create(:lms_submission,
          :submitted_late,
          assignment:,
          enrollment:,
        )
        assignment
      end

      let!(:future_assignment) do
        assignment = create(:lms_assignment, :impacts_outcome, assignment_group:)
        create(:lms_section_assignment, section:, assignment:)
        create(:lms_submission,
          assignment:,
          enrollment:,
          state: :submitted,
          submitted_at: Time.current,
        )
        assignment
      end

      it 'only counts late submissions for past-due assignments' do
        result = rule.call!

        expect(result.risk_level).to eq('on_track')
        expect(result.risk_type).to eq(:late_assignments)
        expect(result.risk_details).to include(late_assignments_count: 1)
        expect(result.risk_details[:late_assignment_ids]).to contain_exactly(late_assignment.id)
        expect(result.risk_details[:late_assignment_ids]).not_to include(future_assignment.id)
      end
    end
  end

  describe '#assessable?' do
    context 'when the learner is not activated' do
      let(:enrollment) do
        create(:enrollment, learner:, section_args: { cohort_args: :started })
      end
      let(:rule) { described_class.new(enrollment:) }
      let(:section) { enrollment.section }
      let(:cohort) { enrollment.cohort }
      let(:assignment_group) { create(:lms_assignment_group, cohort:) }

      before do
        # Create an overdue assignment but do NOT create a not_activated assessment
        assignment = create(:lms_assignment, :impacts_outcome, :overdue, assignment_group:)
        create(:lms_section_assignment, section:, assignment:)
      end

      it 'returns false' do
        expect(rule.send(:assessable?)).to be false
      end
    end

    context 'when there are no overdue assignments but learner is activated' do
      include_context 'with activated enrollment'

      it 'returns false' do
        expect(rule.send(:assessable?)).to be false
      end
    end

    context 'when there are overdue assignments and the learner is activated' do
      include_context 'with activated enrollment'

      before do
        assignment = create(:lms_assignment, :impacts_outcome, :overdue, assignment_group:)
        create(:lms_section_assignment, section:, assignment:)
      end

      it 'returns true' do
        expect(rule.send(:assessable?)).to be true
      end
    end
  end
end
