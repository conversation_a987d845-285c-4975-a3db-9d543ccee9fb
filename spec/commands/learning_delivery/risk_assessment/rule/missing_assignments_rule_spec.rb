# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LearningDelivery::RiskAssessment::Rule::MissingAssignmentsRule, type: :command do
  let(:remote_canvas_user) { create(:remote_canvas_user) }
  let(:learner) { remote_canvas_user.core_record }
  let(:enrollment) { create(:enrollment, learner:, section_args: { cohort_args: :started }) }
  let(:section) { enrollment.section }
  let(:cohort) { enrollment.cohort }
  let!(:assignment_group) { create(:lms_assignment_group, cohort:) }

  subject(:rule) { described_class.new(enrollment:) }

  before do
    freeze_time
  end

  # Define a shared context for an activated enrollment
  shared_context 'with activated enrollment' do
    before do
      create(:learning_delivery_risk_assessment,
        :not_activated,
        :on_track,
        enrollment:,
        assessed_at: 1.day.ago,
      )
    end
  end

  describe '#call!' do
    context 'when not assessable' do
      it 'returns nil' do
        # No assignments due means assessable? will return false
        expect(rule.call!).to be_nil
      end
    end

    context 'with no missing assignments' do
      include_context 'with activated enrollment'

      before do
        assignment = create(:lms_assignment, :impacts_outcome, :overdue, assignment_group:)

        create(:lms_section_assignment, section:, assignment:)

        create(:lms_submission, assignment:, enrollment:, state: :submitted, submitted_at: 1.day.ago)
      end

      it 'returns on track assessment' do
        result = rule.call!

        expect(result.risk_level).to eq('on_track')
        expect(result.risk_type).to eq(:missing_assignments)
        expect(result.risk_details).to include(missing_assignments_count: 0)
        expect(result.risk_details[:missing_assignment_ids]).to be_empty
      end
    end

    context 'with missing assignments below low risk threshold' do
      include_context 'with activated enrollment'

      before do
        # Create a submitted overdue assignment to ensure assessable? returns true
        # but this won't count as a missing assignment since it's been submitted
        overdue_submitted = create(:lms_assignment, :impacts_outcome, :overdue, assignment_group:)
        create(:lms_section_assignment, section:, assignment: overdue_submitted)
        create(:lms_submission, enrollment:, assignment: overdue_submitted, state: :submitted, submitted_at: 1.day.ago)

        non_graded = create(:lms_assignment, :impacts_outcome, :not_graded, assignment_group:)
        create(:lms_section_assignment, section:, assignment: non_graded)

        no_due_date = create(:lms_assignment, :no_due_date, required: true, assignment_group:)
        create(:lms_section_assignment, section:, assignment: no_due_date)

        future_assignment = create(:lms_assignment, :impacts_outcome, assignment_group:)
        create(:lms_section_assignment, section:, assignment: future_assignment)
      end

      it 'returns on track assessment' do
        result = rule.call!

        expect(result.risk_level).to eq('on_track')
        expect(result.risk_type).to eq(:missing_assignments)
        expect(result.risk_details).to include(missing_assignments_count: 0)
        expect(result.risk_details[:missing_assignment_ids]).to be_empty
      end
    end

    context 'with missing assignments at low risk threshold' do
      include_context 'with activated enrollment'

      let!(:assignment) do
        assignment = create(:lms_assignment, :impacts_outcome, :overdue, assignment_group:)
        create(:lms_section_assignment, section:, assignment:)
        assignment
      end

      it 'returns low risk assessment' do
        result = rule.call!

        expect(result.risk_level).to eq('low_risk')
        expect(result.risk_type).to eq(:missing_assignments)
        expect(result.risk_details).to include(missing_assignments_count: 1)
        expect(result.risk_details[:missing_assignment_ids]).to contain_exactly(assignment.id)
      end
    end

    context 'with missing assignments below high risk threshold' do
      include_context 'with activated enrollment'

      let!(:assignments) do
        (1..2).map do |_i|
          assignment = create(:lms_assignment, :impacts_outcome, :overdue, assignment_group:)
          create(:lms_section_assignment, section:, assignment:)
          assignment
        end
      end

      it 'returns low risk assessment' do
        result = rule.call!

        expect(result.risk_level).to eq('low_risk')
        expect(result.risk_type).to eq(:missing_assignments)
        expect(result.risk_details).to include(missing_assignments_count: 2)
        expect(result.risk_details[:missing_assignment_ids]).to match_array(assignments.map(&:id))
      end
    end

    context 'with missing assignments at high risk threshold' do
      include_context 'with activated enrollment'

      let!(:assignments) do
        (1..3).map do |_i|
          assignment = create(:lms_assignment, :impacts_outcome, :overdue, assignment_group:)
          create(:lms_section_assignment, section:, assignment:)
          assignment
        end
      end

      it 'returns high risk assessment' do
        result = rule.call!

        expect(result.risk_level).to eq('high_risk')
        expect(result.risk_type).to eq(:missing_assignments)
        expect(result.risk_details).to include(missing_assignments_count: 3)
        expect(result.risk_details[:missing_assignment_ids]).to match_array(assignments.map(&:id))
      end
    end

    context 'with some submitted assignments' do
      include_context 'with activated enrollment'

      let!(:missing_assignments) do
        (1..2).map do |_i|
          assignment = create(:lms_assignment, :impacts_outcome, :overdue, assignment_group:)
          create(:lms_section_assignment, section:, assignment:)
          assignment
        end
      end

      let!(:submitted_assignment) do
        assignment = create(:lms_assignment, :impacts_outcome, :overdue, assignment_group:)
        create(:lms_section_assignment, section:, assignment:)
        create(:lms_submission, assignment:, enrollment:, state: :submitted, submitted_at: 1.day.ago)
        assignment
      end

      it 'returns low risk assessment with correct missing assignments' do
        result = rule.call!

        expect(result.risk_level).to eq('low_risk')
        expect(result.risk_type).to eq(:missing_assignments)
        expect(result.risk_details).to include(missing_assignments_count: 2)
        expect(result.risk_details[:missing_assignment_ids]).to match_array(missing_assignments.map(&:id))
        expect(result.risk_details[:missing_assignment_ids]).not_to include(submitted_assignment.id)
      end
    end

    context 'with some overdue and some future assignments' do
      include_context 'with activated enrollment'

      let!(:overdue_assignment) do
        assignment = create(:lms_assignment, :impacts_outcome, :overdue, assignment_group:)
        create(:lms_section_assignment, section:, assignment:)
        assignment
      end

      let!(:future_assignments) do
        (1..3).map do |_i|
          assignment = create(:lms_assignment, :impacts_outcome, assignment_group:)
          create(:lms_section_assignment, section:, assignment:)
          assignment
        end
      end

      it 'only counts overdue assignments in risk calculation' do
        result = rule.call!

        expect(result.risk_level).to eq('low_risk')
        expect(result.risk_type).to eq(:missing_assignments)
        expect(result.risk_details).to include(missing_assignments_count: 1)
        expect(result.risk_details[:missing_assignment_ids]).to contain_exactly(overdue_assignment.id)

        future_assignments.each do |future_assignment|
          expect(result.risk_details[:missing_assignment_ids]).not_to include(future_assignment.id)
        end
      end
    end
  end

  describe '#assessable?' do
    context 'when there are no overdue assignments' do
      it 'returns false' do
        expect(rule.send(:assessable?)).to be false
      end
    end

    context 'when there are overdue assignments' do
      include_context 'with activated enrollment'

      before do
        # Create an overdue assignment to make assignments_due exist
        assignment = create(:lms_assignment, :impacts_outcome, :overdue, assignment_group:)
        create(:lms_section_assignment, section:, assignment:)
      end

      it 'returns true' do
        expect(rule.send(:assessable?)).to be true
      end
    end
  end
end
