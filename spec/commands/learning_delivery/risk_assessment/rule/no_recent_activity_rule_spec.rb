# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LearningDelivery::RiskAssessment::Rule::NoRecentActivityRule, type: :command do
  let(:enrollment) { create(:enrollment, section_args: { cohort_args: :ended }) }
  let(:cohort) { enrollment.cohort }
  before do
    freeze_time
  end

  subject(:rule) { described_class.new(enrollment:) }

  describe '#call!' do
    context 'when user has logged in after course opened' do
      before do
        enrollment.update!(last_lms_activity_at: 5.days.ago)
      end

      it 'returns on track assessment' do
        result = rule.call!

        expect(result.risk_level).to eq('on_track')
        expect(result.risk_type).to eq(:no_recent_activity)
        expect(result.risk_details).to include(last_login: 5.days.ago)
      end
    end

    context 'when user has not logged in' do
      it 'returns high risk assessment' do
        result = rule.call!

        expect(result.risk_level).to eq('high_risk')
        expect(result.risk_type).to eq(:no_recent_activity)
        expect(result.risk_details).to include(last_login: nil)
      end
    end

    context 'when user has logged in before course opened' do
      before do
        enrollment.update!(last_lms_activity_at: 15.days.ago)
      end

      it 'returns high risk assessment' do
        result = rule.call!

        expect(result.risk_level).to eq('high_risk')
        expect(result.risk_type).to eq(:no_recent_activity)
        expect(result.risk_details).to include(last_login: 15.days.ago)
      end
    end
  end

  describe '#assessable?' do
    context 'when current time is after the threshold and no recent on track assessment' do
      before do
        allow(Time).to receive(:current).and_return(cohort.starts_on + 11.days)
        allow(enrollment.learning_delivery_risk_assessments.no_recent_activity.on_track).to receive(:exists?).and_return(false)
      end

      it 'returns true' do
        expect(rule.send(:assessable?)).to be true
      end
    end

    context 'when current time is before the threshold' do
      before do
        allow(Time).to receive(:current).and_return(cohort.starts_on + 9.days)
      end

      it 'returns false' do
        expect(rule.send(:assessable?)).to be false
      end
    end

    context 'when there is a recent on track assessment' do
      before do
        create(:learning_delivery_risk_assessment, :no_recent_activity, :on_track, enrollment:, assessed_at: 5.days.ago)
      end

      it 'returns false' do
        expect(rule.send(:assessable?)).to be false
      end
    end
  end
end
