# frozen_string_literal: true

require 'rails_helper'

module LearningDelivery
  class RiskAssessment
    RSpec.describe AssessCommand, type: :command do
      subject(:command) { described_class.new(enrollment:) }

      let(:enrollment) { create(:enrollment, status: "active", section_args: { cohort_args: :started }) }
      before { allow(enrollment).to receive(:learning_delivery_risk_assessments).and_return([risk_assessment_1, risk_assessment_2]) }

      let(:risk_assessment_1) { create(:learning_delivery_risk_assessment, :not_activated, :on_track, enrollment:) }
      let(:risk_assessment_2) { create(:learning_delivery_risk_assessment, :no_recent_activity, :high_risk, enrollment:) }

      before do
        allow(ReplaceCommand).to receive(:call!)

        # Default rule output for tests that don't specify their own
        on_track_output = Rule::RuleOutput.new(
          risk_level: :on_track,
          risk_type: :not_activated,
          risk_details: {},
        )

        AssessCommand::RULES.each do |rule_class|
          allow(rule_class).to receive(:call!).and_return(on_track_output)
        end
      end

      describe '#call!' do
        it 'calls the appropriate rule for each risk type' do
          on_track_output = Rule::RuleOutput.new(
            risk_level: :on_track,
            risk_type: :not_activated,
            risk_details: {},
          )

          low_risk_output = Rule::RuleOutput.new(
            risk_level: :low_risk,
            risk_type: :no_recent_activity,
            risk_details: {},
          )

          allow(AssessCommand::RULES.first).to receive(:call!).and_return(on_track_output)
          allow(AssessCommand::RULES.last).to receive(:call!).and_return(low_risk_output)

          command.call!
          expect(AssessCommand::RULES).to all(have_received(:call!).with(enrollment:))
        end

        it 'stops processing rules after finding a high risk' do
          high_risk_output = Rule::RuleOutput.new(
            risk_level: :high_risk,
            risk_type: :not_activated,
            risk_details: {},
          )

          allow(AssessCommand::RULES.first).to receive(:call!).and_return(high_risk_output)
          allow(AssessCommand::RULES.last).to receive(:call!)

          command.call!
          expect(AssessCommand::RULES.first).to have_received(:call!).with(enrollment:)
          expect(AssessCommand::RULES.last).not_to have_received(:call!)
        end

        it 'calls the ReplaceCommand with the correct attributes' do
          command.call!
          expect(ReplaceCommand).to have_received(:call!).exactly(AssessCommand::RULES.count).times
        end

        it 'updates the enrollment risk level' do
          allow(Enrollment::ReplaceCommand).to receive(:call!)
          command.call!
          expect(Enrollment::ReplaceCommand).to have_received(:call!).with(enrollment:, attributes: { risk_level: 'high_risk' })
        end

        context 'when publishing is enabled' do
          subject(:command) { described_class.new(enrollment:, publish: true) }

          it 'calls publish_to_hubspot_later!' do
            allow(command).to receive(:publish_to_hubspot_later!)
            command.call!
            expect(command).to have_received(:publish_to_hubspot_later!)
          end
        end

        context 'when publishing is disabled' do
          subject(:command) { described_class.new(enrollment:, publish: false) }

          it 'does not call publish_to_hubspot_later!' do
            allow(command).to receive(:publish_to_hubspot_later!)
            command.call!
            expect(command).not_to have_received(:publish_to_hubspot_later!)
          end
        end
      end

      describe '#highest_risk_level' do
        let(:highest_risk_level) { command.send(:highest_risk_level) }

        shared_examples 'returns the highest risk level' do |expected_risk_level|
          it "returns #{expected_risk_level}" do
            risk_assessment_2.update!(risk_level: expected_risk_level)
            expect(highest_risk_level).to eq(expected_risk_level)
          end
        end

        context 'when there is at least one high risk' do
          it_behaves_like 'returns the highest risk level', 'high_risk'
        end

        context 'when there are only low and no risks' do
          it_behaves_like 'returns the highest risk level', 'low_risk'
        end

        context 'when there are only on track risks' do
          it_behaves_like 'returns the highest risk level', 'on_track'
        end
      end
    end
  end
end
