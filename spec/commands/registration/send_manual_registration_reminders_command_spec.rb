# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Registration::SendManualRegistrationRemindersCommand, type: :command do
  around do |example|
    Time.use_zone(described_class::TIME_ZONE) { example.run }
  end

  def create_registration_with_transaction(status:, transacted_at:, with_enrollment: false)
    registration = create(:registration, status:, ecom_order_item_count: 1)
    payment = create(:ecom_payment, order: registration.ecom_order)
    create(:payments_transaction, ecom_payment: payment, transacted_at:)

    create(:enrollment, registration:, primary: true) if with_enrollment

    registration
  end

  describe '#call!' do
    let(:command) { described_class.new }
    let(:mailer_delivery) { instance_double(ActionMailer::MessageDelivery, deliver_later: true) }

    before do
      allow(<PERSON><PERSON><PERSON>ailer).to receive_messages(
        with: LearnerMailer,
        manual_registration_reminder: mailer_delivery,
      )
    end

    context 'with eligible registrations' do
      let!(:registration_1_day_ago) do
        create_registration_with_transaction(
          status: :created,
          transacted_at: 1.day.ago.beginning_of_day + 8.hours,
        )
      end
      let!(:registration_2_days_ago) do
        create_registration_with_transaction(
          status: :created,
          transacted_at: 2.days.ago.beginning_of_day + 10.hours,
        )
      end

      it 'sends reminders for eligible registrations at all intervals' do
        command.call!

        expect(LearnerMailer).to have_received(:with).with(registration: registration_1_day_ago)
        expect(LearnerMailer).to have_received(:with).with(registration: registration_2_days_ago)
      end

      it 'handles errors gracefully without stopping the batch' do
        allow(command).to receive(:send_reminder_email) do |registration|
          raise StandardError.new('Email error') if registration == registration_1_day_ago
        end

        allow(ErrorReporter).to receive(:report)

        expect { command.call! }.not_to raise_error

        expect(ErrorReporter).to have_received(:report).once
      end
    end

    describe 'REMINDER_THRESHOLDS' do
      it 'contains the correct reminder thresholds' do
        expect(described_class::REMINDER_THRESHOLDS).to eq([1.day, 2.days])
      end

      it 'is frozen' do
        expect(described_class::REMINDER_THRESHOLDS).to be_frozen
      end
    end
  end

  describe '#eligible_registrations_for_interval' do
    let(:command) { described_class.new }
    let(:interval) { 1.day.ago.all_day }

    it 'includes registrations with correct statuses and transaction times' do
      created_reg = create_registration_with_transaction(
        status: :created,
        transacted_at: interval.begin + 1.hour,
      )

      cohort_picked_reg = create_registration_with_transaction(
        status: :cohort_picked,
        transacted_at: interval.begin + 2.hours,
      )

      section_picked_reg = create_registration_with_transaction(
        status: :section_picked,
        transacted_at: interval.end - 1.hour,
      )

      result = command.send(:eligible_registrations_for_interval, interval)

      expect(result).to include(created_reg)
      expect(result).to include(cohort_picked_reg)
      expect(result).to include(section_picked_reg)
    end

    it 'excludes registrations with incorrect statuses' do
      confirmed_reg = create_registration_with_transaction(
        status: :confirmed,
        transacted_at: interval.begin + 1.hour,
      )

      canceled_reg = create_registration_with_transaction(
        status: :cancelled,
        transacted_at: interval.begin + 2.hours,
      )

      result = command.send(:eligible_registrations_for_interval, interval)

      expect(result).not_to include(confirmed_reg)
      expect(result).not_to include(canceled_reg)
    end

    it 'excludes registrations with transaction times outside the interval' do
      reg_before_interval = create_registration_with_transaction(
        status: :created,
        transacted_at: interval.begin - 1.hour,
      )

      reg_after_interval = create_registration_with_transaction(
        status: :created,
        transacted_at: interval.end + 1.hour,
      )

      result = command.send(:eligible_registrations_for_interval, interval)

      expect(result).not_to include(reg_before_interval)
      expect(result).not_to include(reg_after_interval)
    end

    it 'handles registrations with multiple transactions correctly by checking if the first transaction is within the interval' do
      registration = create_registration_with_transaction(
        status: :created,
        transacted_at: interval.begin + 1.hour,
      )

      create(:payments_transaction, ecom_payment: registration.ecom_payment, transacted_at: interval.end + 1.day)

      result = command.send(:eligible_registrations_for_interval, interval)

      expect(result).to include(registration)
    end

    it 'uses the earliest transaction date for determining eligibility' do
      registration_with_first_txn_outside_interval = create_registration_with_transaction(
        status: :created,
        transacted_at: interval.begin - 1.hour,
      )

      # Create later transaction inside the interval
      create(:payments_transaction, ecom_payment: registration_with_first_txn_outside_interval.ecom_payment, transacted_at: interval.begin + 1.hour)

      result = command.send(:eligible_registrations_for_interval, interval)

      expect(result).not_to include(registration_with_first_txn_outside_interval)
    end
  end
end
