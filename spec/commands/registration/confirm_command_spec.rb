# frozen_string_literal: true

require 'rails_helper'

class Registration
  describe ConfirmCommand do
    describe '#call!' do
      let(:registration) { create(:registration, status: registration_status) }
      let(:registration_status) { :created }
      let(:command) { described_class.new(registration:) }
      let!(:conditional_fulfill_command_class) { class_spy(Ecom::Order::ConditionalFulfillCommand, call!: nil).as_stubbed_const }
      let!(:process_revenue_command_class) { class_spy(Ecom::OrderItem::ProcessRevenueCommand, call!: nil).as_stubbed_const }

      subject(:call!) { command.call! }

      context 'when registration is in created status' do
        let(:scenario) { PaidLearnerScenario.create }
        let(:registration) { scenario.registration.tap { |r| r.update!(status: :created) } }
        let!(:payments_transaction) { create(:payments_transaction, :charge, ecom_payment: scenario.ecom_payment) }

        it 'updates the registration status to confirmed' do
          expect { call! }.to change { registration.reload.status }.from('created').to('confirmed')
        end

        it 'calls ProcessRevenueCommand with the order item and latest transaction' do
          call!
          order_item = registration.ecom_order.order_items.first
          expected_payments_transaction = order_item.payment.payments_transactions.order(:created_at).last
          expect(process_revenue_command_class).to have_received(:call!).with(
            ecom_order_item: order_item,
            payments_transaction: expected_payments_transaction,
          )
        end

        it 'calls ConditionalFulfillCommand with the ecom order' do
          call!
          expect(conditional_fulfill_command_class).to have_received(:call!).with(order: registration.ecom_order)
        end

        it 'returns the registration' do
          expect(call!).to eq(registration)
        end
      end

      context 'when registration is already confirmed' do
        let(:registration_status) { :confirmed }

        it 'does not change the registration status' do
          expect { call! }.not_to(change { registration.reload.status })
        end

        it 'does not call ProcessRevenueCommand' do
          call!
          expect(process_revenue_command_class).not_to have_received(:call!)
        end

        it 'does not call ConditionalFulfillCommand' do
          call!
          expect(conditional_fulfill_command_class).not_to have_received(:call!)
        end

        it 'returns the registration' do
          expect(call!).to eq(registration)
        end
      end

      context 'when registration is not in created status' do
        let(:scenario) { PaidLearnerScenario.create }
        let(:registration) { scenario.registration.tap { |r| r.update!(status: :cancelled) } }
        let!(:payments_transaction) { create(:payments_transaction, :charge, ecom_payment: scenario.ecom_payment) }

        it 'raises an ArgumentError' do
          expect { call! }.to raise_error(ArgumentError, 'Registration must be in confirmable status to be confirmed')
        end

        it 'does not change the registration status' do
          expect do
            begin
              call!
            rescue
              nil
            end
          end.not_to(change { registration.reload.status })
        end

        it 'does not call ProcessRevenueCommand' do
          begin
            call!
          rescue
            nil
          end
          expect(process_revenue_command_class).not_to have_received(:call!)
        end

        it 'does not call ConditionalFulfillCommand' do
          begin
            call!
          rescue
            nil
          end
          expect(conditional_fulfill_command_class).not_to have_received(:call!)
        end
      end

      context 'with registration that has an ecom order' do
        let(:scenario) { PaidLearnerScenario.create }
        let(:registration) { scenario.registration.tap { |r| r.update!(status: :created) } }
        let(:ecom_order) { registration.ecom_order }
        let!(:payments_transaction) { create(:payments_transaction, :charge, ecom_payment: scenario.ecom_payment) }

        it 'calls ProcessRevenueCommand with the order item and latest transaction' do
          call!
          order_item = registration.ecom_order.order_items.first
          expected_payments_transaction = order_item.payment.payments_transactions.order(:created_at).last
          expect(process_revenue_command_class).to have_received(:call!).with(
            ecom_order_item: order_item,
            payments_transaction: expected_payments_transaction,
          )
        end

        it 'calls ConditionalFulfillCommand with the correct order' do
          call!
          expect(conditional_fulfill_command_class).to have_received(:call!).with(order: ecom_order)
        end

        it 'updates the registration status to confirmed' do
          expect { call! }.to change { registration.reload.status }.from('created').to('confirmed')
        end
      end

      context 'when ProcessRevenueCommand raises an error' do
        let(:scenario) { PaidLearnerScenario.create }
        let(:registration) { scenario.registration.tap { |r| r.update!(status: :created) } }
        let!(:payments_transaction) { create(:payments_transaction, :charge, ecom_payment: scenario.ecom_payment) }

        before do
          allow(process_revenue_command_class).to receive(:call!).and_raise(StandardError, 'Revenue booking failed')
        end

        it 'rolls back the registration status change due to transaction' do
          expect do
            begin
              call!
            rescue
              nil
            end
          end.not_to(change { registration.reload.status })
        end

        it 'propagates the error' do
          expect { call! }.to raise_error(StandardError, 'Revenue booking failed')
        end

        it 'does not call ConditionalFulfillCommand' do
          begin
            call!
          rescue
            nil
          end
          expect(conditional_fulfill_command_class).not_to have_received(:call!)
        end
      end

      context 'when ConditionalFulfillCommand raises an error' do
        let(:scenario) { PaidLearnerScenario.create }
        let(:registration) { scenario.registration.tap { |r| r.update!(status: :created) } }
        let!(:payments_transaction) { create(:payments_transaction, :charge, ecom_payment: scenario.ecom_payment) }

        before do
          allow(conditional_fulfill_command_class).to receive(:call!).and_raise(StandardError, 'Order fulfillment failed')
        end

        it 'rolls back the registration status change due to transaction' do
          expect do
            begin
              call!
            rescue
              nil
            end
          end.not_to(change { registration.reload.status })
        end

        it 'propagates the error' do
          expect { call! }.to raise_error(StandardError, 'Order fulfillment failed')
        end
      end

      context 'with multiple registrations' do
        let(:first_scenario) { PaidLearnerScenario.create }
        let(:second_scenario) { PaidLearnerScenario.create }
        let(:first_registration) { first_scenario.registration.tap { |r| r.update!(status: :created) } }
        let(:second_registration) { second_scenario.registration.tap { |r| r.update!(status: :created) } }
        let!(:first_payments_transaction) { create(:payments_transaction, :charge, ecom_payment: first_scenario.ecom_payment) }
        let!(:second_payments_transaction) { create(:payments_transaction, :charge, ecom_payment: second_scenario.ecom_payment) }
        let(:first_command) { described_class.new(registration: first_registration) }
        let(:second_command) { described_class.new(registration: second_registration) }

        it 'handles each registration independently' do
          expect { first_command.call! }.to change { first_registration.reload.status }.from('created').to('confirmed')
          expect { second_command.call! }.to change { second_registration.reload.status }.from('created').to('confirmed')

          expect(conditional_fulfill_command_class).to have_received(:call!).twice
          expect(process_revenue_command_class).to have_received(:call!).twice
        end
      end

      describe 'edge cases' do
        context 'when registration has no ecom order item' do
          let(:registration) { create(:registration, status: :created) }

          before do
            allow(registration).to receive(:ecom_order_item).and_return(nil)
          end

          it 'raises an error when trying to access ecom_order_item.payment' do
            expect { call! }.to raise_error(NoMethodError)
          end

          it 'rolls back the registration status change due to transaction' do
            begin
              call!
            rescue
              nil
            end
            expect(registration.reload.status).to eq('created')
          end
        end

        context 'when registration update fails' do
          let(:scenario) { PaidLearnerScenario.create }
          let(:registration) { scenario.registration.tap { |r| r.update!(status: :created) } }
          let!(:payments_transaction) { create(:payments_transaction, :charge, ecom_payment: scenario.ecom_payment) }

          before do
            allow(registration).to receive(:update!).and_raise(ActiveRecord::RecordInvalid)
          end

          it 'raises the validation error' do
            expect { call! }.to raise_error(ActiveRecord::RecordInvalid)
          end

          it 'does not call ProcessRevenueCommand' do
            begin
              call!
            rescue
              nil
            end
            expect(process_revenue_command_class).not_to have_received(:call!)
          end

          it 'does not call ConditionalFulfillCommand' do
            begin
              call!
            rescue
              nil
            end
            expect(conditional_fulfill_command_class).not_to have_received(:call!)
          end
        end
      end
    end
  end
end
