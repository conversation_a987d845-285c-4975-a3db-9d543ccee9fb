# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Zendesk::FormatArticleCommand, type: :model do
  let(:sample_article_data) do
    {
      'id' => 123,
      'title' => 'Test Article (Sample)',
      'body' => '<p>Test content with {REIMBURSEMENT_URL}</p>',
      'position' => 1,
      'html_url' => 'https://example.zendesk.com/article/123',
      'updated_at' => '2024-01-01T12:00:00Z',
      'draft' => false,
      'label_names' => ['Global'],
      'content_tag_ids' => ['tag_123', 'tag_456'],
    }
  end

  let(:command) { described_class.new(article_data: sample_article_data) }

  describe '#call!' do
    it 'returns formatted article hash' do
      result = command.call!

      expect(result).to be_a(Hash)
      expect(result).to include(:id, :title, :body, :position, :url, :updated_at, :published, :label_names, :content_tag_ids, :content_tag_names)
    end

    it 'preserves article ID' do
      result = command.call!
      expect(result[:id]).to eq(123)
    end

    it 'removes parenthetical content from title' do
      result = command.call!
      expect(result[:title]).to eq('Test Article')
    end

    it 'converts position to integer' do
      result = command.call!
      expect(result[:position]).to eq(1)
    end

    it 'preserves URL and updated_at' do
      result = command.call!
      expect(result[:url]).to eq('https://example.zendesk.com/article/123')
      expect(result[:updated_at]).to eq('2024-01-01T12:00:00Z')
    end

    it 'determines published status correctly' do
      result = command.call!
      expect(result[:published]).to be true
    end

    context 'with draft article' do
      let(:sample_article_data) do
        {
          'id' => 124,
          'title' => 'Draft Article',
          'body' => '<p>Draft content</p>',
          'position' => 2,
          'html_url' => 'https://example.zendesk.com/article/124',
          'updated_at' => '2024-01-01T12:00:00Z',
          'draft' => true,
          'label_names' => ['Global'],
          'content_tag_ids' => [],
        }
      end

      it 'marks draft articles as not published' do
        result = command.call!
        expect(result[:published]).to be false
      end
    end
  end

  describe '#published?' do
    it 'returns true for non-draft articles' do
      expect(command.published?).to be true
    end

    context 'with draft article' do
      let(:sample_article_data) do
        super().merge('draft' => true)
      end

      it 'returns false for draft articles' do
        expect(command.published?).to be false
      end
    end
  end

  describe 'title formatting' do
    it 'removes parenthetical content' do
      article_data = sample_article_data.merge('title' => 'Main Title (Additional Info)')
      command = described_class.new(article_data:)

      result = command.call!
      expect(result[:title]).to eq('Main Title')
    end

    it 'handles multiple parentheses' do
      article_data = sample_article_data.merge('title' => 'Title (Info 1) (Info 2)')
      command = described_class.new(article_data:)

      result = command.call!
      expect(result[:title]).to eq('Title')
    end

    it 'handles empty title' do
      article_data = sample_article_data.merge('title' => '')
      command = described_class.new(article_data:)

      result = command.call!
      expect(result[:title]).to eq('')
    end

    it 'handles nil title' do
      article_data = sample_article_data.merge('title' => nil)
      command = described_class.new(article_data:)

      result = command.call!
      expect(result[:title]).to eq('')
    end
  end

  describe 'body handling' do
    it 'preserves original body content' do
      result = command.call!
      expect(result[:body]).to eq('<p>Test content with {REIMBURSEMENT_URL}</p>')
    end

    it 'handles empty body' do
      article_data = sample_article_data.merge('body' => '')
      command = described_class.new(article_data:)

      result = command.call!
      expect(result[:body]).to eq('')
    end

    it 'handles nil body' do
      article_data = sample_article_data.merge('body' => nil)
      command = described_class.new(article_data:)

      result = command.call!
      expect(result[:body]).to eq('')
    end
  end

  describe 'content tag names extraction' do
    let(:content_tag_names_mapping) do
      {
        'pricing' => 'tag_123',
        'certifications' => 'tag_456',
      }
    end

    let(:command) do
      described_class.new(
        article_data: sample_article_data,
        content_tag_names_mapping:,
      )
    end

    it 'extracts content tag names from mappings' do
      result = command.call!
      expect(result[:content_tag_names]).to contain_exactly('pricing', 'certifications')
    end

    context 'with no content tag mappings' do
      let(:command) { described_class.new(article_data: sample_article_data) }

      it 'returns empty array for content_tag_names' do
        result = command.call!
        expect(result[:content_tag_names]).to eq([])
      end
    end

    context 'with article having no content_tag_ids' do
      let(:sample_article_data_no_tags) do
        sample_article_data.merge('content_tag_ids' => [])
      end

      let(:command) do
        described_class.new(
          article_data: sample_article_data_no_tags,
          content_tag_names_mapping:,
        )
      end

      it 'returns empty array for content_tag_names' do
        result = command.call!
        expect(result[:content_tag_names]).to eq([])
      end
    end

    context 'with unmapped content tag IDs' do
      let(:sample_article_data_unmapped) do
        sample_article_data.merge('content_tag_ids' => ['tag_123', 'unmapped_tag_999'])
      end

      let(:command) do
        described_class.new(
          article_data: sample_article_data_unmapped,
          content_tag_names_mapping:,
        )
      end

      it 'only returns names for mapped tags' do
        result = command.call!
        expect(result[:content_tag_names]).to eq(['pricing'])
      end
    end
  end
end
