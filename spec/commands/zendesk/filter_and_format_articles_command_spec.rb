# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Zendesk::FilterAndFormatArticlesCommand, type: :model do
  let(:scenario) { LandingScenario.create }
  let(:partner_program) { scenario.partner_program }

  let(:sample_articles) do
    [
      {
        'id' => 123,
        'title' => 'Test Article (Sample)',
        'body' => '<p>Test content with {REIMBURSEMENT_URL}</p>',
        'position' => 1,
        'html_url' => 'https://example.zendesk.com/article/123',
        'updated_at' => '2024-01-01T12:00:00Z',
        'draft' => false,
        'label_names' => ['Global'],
        'content_tag_ids' => ['tag_123'],
      },
      {
        'id' => 124,
        'title' => 'Program Article',
        'body' => '<p>Program specific content</p>',
        'position' => 2,
        'html_url' => 'https://example.zendesk.com/article/124',
        'updated_at' => '2024-01-02T12:00:00Z',
        'draft' => false,
        'label_names' => ["program:#{scenario.program.abbreviation}"],
        'content_tag_ids' => [],
      },
      {
        'id' => 125,
        'title' => 'Draft Article',
        'body' => '<p>Draft content</p>',
        'position' => 3,
        'html_url' => 'https://example.zendesk.com/article/125',
        'updated_at' => '2024-01-03T12:00:00Z',
        'draft' => true,
        'label_names' => ['Global'],
        'content_tag_ids' => [],
      },
    ]
  end

  let(:command) { described_class.new(articles: sample_articles, partner_program:) }


  describe '#initialize' do
    it 'initializes with articles and partner_program' do
      expect(command.articles).to eq(sample_articles)
      expect(command.partner_program).to eq(partner_program)
    end
  end

  describe '#call!' do
    context 'with empty articles' do
      let(:empty_command) { described_class.new(articles: [], partner_program:) }

      it 'returns empty array' do
        expect(empty_command.call!).to eq([])
      end
    end

    context 'with blank articles' do
      let(:nil_command) { described_class.new(articles: nil, partner_program:) }

      it 'returns empty array' do
        expect(nil_command.call!).to eq([])
      end
    end

    context 'with valid articles', vcr: 'commands/zendesk/filter_and_format_articles_command' do
      it 'filters and formats articles' do
        result = command.call!

        expect(result).to be_an(Array)
        expect(result.size).to eq(2) # Only published articles should be returned

        # Check that draft articles are filtered out
        expect(result.none? { |article| article[:id] == 125 }).to be true

        # Check articles are properly formatted
        first_article = result.find { |article| article[:id] == 123 }
        expect(first_article).to include(:id, :title, :body, :position, :url, :updated_at)
        expect(first_article[:title]).to eq('Test Article') # Parenthetical content removed
      end

      it 'sorts articles by position' do
        # Reverse the sample articles to test sorting
        reversed_articles = sample_articles.reverse
        reversed_command = described_class.new(articles: reversed_articles, partner_program:)
        allow(reversed_command).to receive(:content_tag_mappings).and_return({})

        result = reversed_command.call!

        # Should still be in position order (1, 2)
        expect(result.first[:position]).to eq(1)
        expect(result.second[:position]).to eq(2)
      end

      it 'filters articles by labels correctly' do
        result = command.call!

        # Should include Global and program-specific articles
        expect(result.any? { |article| article[:id] == 123 }).to be true # Global label
        expect(result.any? { |article| article[:id] == 124 }).to be true # Program label
      end
    end
  end

  describe '#content_tag_mappings', vcr: 'commands/zendesk/filter_and_format_articles_command' do
    it 'fetches and caches content tag mappings' do
      allow(Rails.cache).to receive(:fetch).and_call_original

      result = command.content_tag_mappings

      expect(result).to be_a(Hash)
      expect(Rails.cache).to have_received(:fetch).with(
        'zendesk_content_tags_all',
        hash_including(namespace: 'zendesk', expires_in: 1.hour, skip_nil: true),
      )
    end
  end

  describe 'article filtering' do
    context 'with label filtering' do
      it 'includes articles with Global label' do
        global_article = sample_articles.find { |a| a['label_names'].include?('Global') }
        expect(command.send(:filter_articles_by_labels, [global_article])).to include(global_article)
      end

      it 'includes articles with program-specific labels' do
        program_label = "program:#{scenario.program.abbreviation}"
        program_article = sample_articles.find { |a| a['label_names'].include?(program_label) }
        expect(command.send(:filter_articles_by_labels, [program_article])).to include(program_article)
      end

      context 'with partner abbreviation' do
        before { scenario.partner.update!(abbreviation: 'TEST') }

        it 'includes articles with partner-specific labels' do
          partner_article = {
            'id' => 126,
            'label_names' => ['partner:TEST'],
          }

          result = command.send(:filter_articles_by_labels, [partner_article])
          expect(result).to include(partner_article)
        end

        it 'includes articles with partner-program combination labels' do
          partnerprogram_article = {
            'id' => 127,
            'label_names' => ["partnerprogram:TEST-#{scenario.program.abbreviation}"],
          }

          result = command.send(:filter_articles_by_labels, [partnerprogram_article])
          expect(result).to include(partnerprogram_article)
        end
      end

      it 'excludes articles without matching labels' do
        non_matching_article = {
          'id' => 999,
          'label_names' => ['UnknownLabel'],
        }

        result = command.send(:filter_articles_by_labels, [non_matching_article])
        expect(result).to be_empty
      end

      it 'handles case insensitive label matching' do
        global_article = {
          'id' => 999,
          'label_names' => ['global'], # lowercase
        }

        result = command.send(:filter_articles_by_labels, [global_article])
        expect(result).to include(global_article)
      end
    end

    context 'with publish status filtering' do
      it 'excludes draft articles' do
        draft_article = sample_articles.find { |a| a['draft'] == true }
        expect(command.send(:published?, draft_article)).to be false
      end

      it 'includes published articles' do
        published_article = sample_articles.find { |a| a['draft'] == false }
        expect(command.send(:published?, published_article)).to be true
      end

      it 'handles articles without draft field' do
        article_without_draft = { 'id' => 999 }
        expect(command.send(:published?, article_without_draft)).to be true
      end
    end
  end

  describe 'delegate methods' do
    it 'delegates partner to partner_program' do
      expect(command.partner).to eq(partner_program.partner)
    end

    it 'delegates program to partner_program' do
      expect(command.program).to eq(partner_program.program)
    end
  end

  describe 'private methods' do
    describe '#program_abbreviation' do
      it 'returns program abbreviation' do
        expect(command.send(:program_abbreviation)).to eq(scenario.program.abbreviation)
      end
    end

    describe '#partner_abbreviation' do
      context 'when partner has abbreviation' do
        before { scenario.partner.update!(abbreviation: 'TEST') }

        it 'returns partner abbreviation' do
          expect(command.send(:partner_abbreviation)).to eq('TEST')
        end
      end

      context 'when partner has no abbreviation' do
        before { scenario.partner.update!(abbreviation: nil) }

        it 'returns nil' do
          expect(command.send(:partner_abbreviation)).to be_nil
        end
      end
    end
  end
end
