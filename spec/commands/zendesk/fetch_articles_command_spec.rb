# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Zendesk::FetchArticlesCommand, type: :model do
  let(:scenario) { LandingScenario.create }
  let(:partner_program) { scenario.partner_program }


  let(:command) { described_class.new(partner_program:) }


  describe '#initialize' do
    it 'initializes with partner_program' do
      expect(command.partner_program).to eq(partner_program)
    end

    it 'uses default section ID when not provided' do
      expect(command.articles_section_id).to eq(described_class::DEFAULT_FAQ_SECTION_ID)
    end

    it 'allows custom section ID' do
      custom_command = described_class.new(partner_program:, articles_section_id: '12345')
      expect(custom_command.articles_section_id).to eq('12345')
    end
  end

  describe '#call!' do
    around do |example|
      VCR.use_cassette 'commands/zendesk/fetch_articles_command', allow_playback_repeats: true do
        example.run
      end
    end

    it 'returns processed articles' do
      result = command.call!

      expect(result).to be_an(Array)
      expect(result).not_to be_empty
      expect(result.first).to have_key(:id)
      expect(result.first).to have_key(:title)
      expect(result.first).to have_key(:body)
    end

    it 'makes API request and processes articles' do
      result = command.call!
      expect(result).to be_an(Array)
    end

    it 'uses custom section ID when provided', vcr: 'commands/zendesk/fetch_articles_command_custom' do
      custom_command = described_class.new(partner_program:, articles_section_id: '12345')
      result = custom_command.call!
      expect(result).to be_an(Array)
    end
  end

  describe 'caching behavior', vcr: { cassette_name: 'commands/zendesk/fetch_articles_command', record: :new_episodes } do
    before do
      Rails.cache.clear
    end

    it 'uses Rails cache with correct parameters' do
      allow(Rails.cache).to receive(:fetch).and_call_original

      command.call!

      expect(Rails.cache).to have_received(:fetch).at_least(:once).with(
        kind_of(String),
        hash_including(
          namespace: 'zendesk',
          expires_in: 1.hour,
          skip_nil: true,
        ),
      )
    end

    it 'returns cached results on subsequent calls' do
      first_result = command.call!
      second_result = command.call!

      expect(second_result).to eq(first_result)
    end

    it 'generates different cache keys for different partner programs' do
      other_scenario = LandingScenario.create
      other_command = described_class.new(partner_program: other_scenario.partner_program)

      first_cache_key = command.send(:cache_key)
      second_cache_key = other_command.send(:cache_key)

      expect(first_cache_key).not_to eq(second_cache_key)
    end

    it 'generates different cache keys for different section IDs' do
      custom_command = described_class.new(partner_program:, articles_section_id: '99999')

      default_cache_key = command.send(:cache_key)
      custom_cache_key = custom_command.send(:cache_key)

      expect(default_cache_key).not_to eq(custom_cache_key)
    end
  end

  describe 'constants' do
    it 'has correct default FAQ section ID' do
      expect(described_class::DEFAULT_FAQ_SECTION_ID).to eq(38840552319899)
    end

    it 'has correct cache TTL' do
      expect(described_class::CACHE_TTL).to eq(1.hour)
    end
  end

  describe 'private methods' do
    describe '#cache_key' do
      it 'generates consistent cache keys' do
        first_key = command.send(:cache_key)
        second_key = command.send(:cache_key)
        expect(first_key).to eq(second_key)
      end

      it 'includes partner program UID in cache key' do
        cache_key = command.send(:cache_key)
        expect(cache_key).to be_a(String)
        expect(cache_key.length).to eq(16) # SHA256 truncated to 16 chars
      end
    end
  end
end
