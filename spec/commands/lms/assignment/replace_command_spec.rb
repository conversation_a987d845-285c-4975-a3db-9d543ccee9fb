# frozen_string_literal: true

require 'rails_helper'

module Lms
  class Assignment
    RSpec.describe ReplaceCommand do
      subject(:call!) { described_class.call!(lms_assignment:, attributes:) }

      let(:assignment_group) { create(:lms_assignment_group) }
      let(:lms_assignment) { create(:lms_assignment, assignment_group:, name: 'Old Name', required: false, published: false) }

      let(:attributes) do
        {
          name: 'New Name',
          grading_type: 'points',
          html_url: 'http://example.com/assignment',
          published: true,
          required: true,
          status: 'unlocked',
          due_at: 2.days.from_now,
          lock_at: 3.days.from_now,
          unlock_at: 1.day.ago,
        }
      end

      it 'updates and persists the assignment, returning it' do
        result = call!

        expect(result).to eq(lms_assignment)
        expect(lms_assignment.reload).to have_attributes(
          name: 'New Name',
          grading_type: 'points',
          html_url: 'http://example.com/assignment',
          published: true,
          required: true,
          status: 'unlocked',
          due_at: be_present,
          lock_at: be_present,
          unlock_at: be_present,
        )
      end

      context 'with invalid attributes' do
        let(:attributes) { { name: nil } }

        it 'raises and does not persist changes' do
          expect { call! }.to raise_error(ActiveRecord::NotNullViolation)
          expect(lms_assignment.reload.name).to eq('Old Name')
        end
      end
    end
  end
end
