# frozen_string_literal: true

require 'rails_helper'

module Lms
  class Assignment
    describe ReplaceFromCanvasAttributesCommand do
      subject(:command) do
        described_class.new(
          cohort:,
          canvas_attributes:,
        )
      end

      let(:cohort) { create(:cohort) }
      let!(:remote_canvas_course) { create(:remote_canvas_course, core_record: cohort) }
      let(:program) { cohort.program }
      let(:module_template) { create(:lms_module_template, program:) }
      let(:lms_module) { create(:lms_module, cohort:, module_template:) }
      let(:assignment_group) { create(:lms_assignment_group, cohort:, name: 'Assignments') }
      let(:lms_assignment) { create(:lms_assignment, lms_module:, assignment_group:) }
      let!(:remote_canvas_assignment) { create(:remote_canvas_assignment, core_record: lms_assignment, key: base_attributes['id']) }
      let(:canvas_client) { instance_double(CanvasClient) }

      let(:base_attributes) do
        {
          'id' => '123',
          'name' => 'Module: Assignment Name',
          'grading_type' => 'points',
          'html_url' => 'http://example.com',
          'published' => true,
          'omit_from_final_grade' => false,
          'due_at' => 1.day.from_now.iso8601,
          'lock_at' => 2.days.from_now.iso8601,
          'unlock_at' => 1.day.ago.iso8601,
          'has_overrides' => false,
          'assignment_group_id' => '456',
          'lms_module' => lms_module,
          'lms_assignment_group' => assignment_group,
        }
      end
      let(:canvas_attributes) { base_attributes }

      before do
        allow(CanvasClient).to receive(:new).and_return(canvas_client)
        allow(canvas_client).to receive(:get)
          .with("/courses/#{cohort.remote_canvas_course.key}/assignment_groups/456")
          .and_return({ 'name' => 'Assignments' })
      end

      describe '#call!' do
        context 'when replacing an existing assignment' do
          it 'updates the assignment attributes' do
            command.call!

            expect(lms_assignment.reload).to have_attributes(
              name: 'Assignment Name',
              grading_type: 'points',
              html_url: 'http://example.com',
              published: true,
              required: true,
              status: 'unlocked',
              due_at: be_present,
              lock_at: be_present,
              unlock_at: be_present,
            )
          end

          it 'calls Lms::Assignment::ReplaceCommand with expected attributes' do
            allow(Lms::Assignment::ReplaceCommand).to receive(:call!).and_call_original

            command.call!

            expect(Lms::Assignment::ReplaceCommand).to have_received(:call!).with(
              lms_assignment:,
              attributes: hash_including(
                name: 'Assignment Name',
                grading_type: 'points',
                html_url: 'http://example.com',
                published: true,
                required: true,
                status: 'unlocked',
                due_at: kind_of(String),
                lock_at: kind_of(String),
                unlock_at: kind_of(String),
                assignment_template: instance_of(Lms::AssignmentTemplate),
              ),
            ).once
          end
        end

        context 'when creating a new assignment' do
          let(:canvas_attributes) { base_attributes.merge('id' => 'new_assignment_id') }

          it 'creates a new LMS assignment' do
            expect { command.call! }.to change { Lms::Assignment.count }.by(1)
            expect(command.lms_assignment).to have_attributes(
              name: 'Assignment Name',
              grading_type: 'points',
              html_url: 'http://example.com',
              published: true,
              required: true,
              status: 'unlocked',
              due_at: be_present,
              lock_at: be_present,
              unlock_at: be_present,
              lms_module:,
              assignment_group:,
            )
          end
        end

        it 'parses the name correctly' do
          command.call!
          expect(lms_assignment.reload.name).to eq('Assignment Name')
        end

        it 'updates the remote canvas assignment' do
          command.call!

          expect(lms_assignment.remote_canvas_assignment.key).to eq('123')
        end

        context 'with assignment template association' do
          it 'finds or creates an assignment template and associates it with the assignment' do
            expect { command.call! }.to change { AssignmentTemplate.count }.by(1)

            assignment_template = lms_assignment.reload.assignment_template
            expect(assignment_template).to be_present
            expect(assignment_template).to have_attributes(
              name: 'Assignment Name',
              module_template:,
            )
          end

          context 'when a matching assignment template already exists' do
            let!(:existing_assignment_template) do
              create(:lms_assignment_template,
                name: 'Assignment Name',
                module_template:,
              )
            end

            it 'reuses the existing assignment template' do
              expect { command.call! }.not_to(change { AssignmentTemplate.count })
              expect(lms_assignment.reload.assignment_template).to eq(existing_assignment_template)
            end
          end

          context 'when assignment is not associated with a module' do
            let(:lms_module) { nil }

            it 'creates a new assignment template without a module template' do
              expect { command.call! }.to change { AssignmentTemplate.count }.by(1)
              expect(lms_assignment.reload.assignment_template).to have_attributes(
                module_template: nil,
              )
            end
          end
        end

        context 'when assignment has overrides' do
          let(:canvas_attributes) { base_attributes.merge('has_overrides' => true) }
          let(:assign_sections_command) { instance_spy(Lms::Assignment::AssignCohortSectionsCommand) }

          before do
            allow(Lms::Assignment::AssignCohortSectionsCommand).to receive(:new)
              .with(cohort:, lms_assignment:)
              .and_return(assign_sections_command)
          end

          it 'does not call AssignCohortSectionsCommand' do
            command.call!
            expect(assign_sections_command).not_to have_received(:call!)
          end
        end

        context 'when assignment has no overrides' do
          let(:assign_sections_command) { instance_spy(Lms::Assignment::AssignCohortSectionsCommand) }

          before do
            allow(Lms::Assignment::AssignCohortSectionsCommand).to receive(:new)
              .with(cohort:, lms_assignment:)
              .and_return(assign_sections_command)
          end

          it 'calls AssignCohortSectionsCommand' do
            command.call!
            expect(assign_sections_command).to have_received(:call!)
          end
        end
      end

      describe 'assignment status' do
        context 'when assignment is past due' do
          let(:canvas_attributes) { base_attributes.merge('due_at' => 1.day.ago.iso8601) }

          it 'sets status to past_due' do
            command.call!
            expect(lms_assignment.reload.status).to eq('past_due')
          end
        end

        context 'when assignment is locked' do
          context 'when unlock_at is in future' do
            let(:canvas_attributes) { base_attributes.merge('unlock_at' => 1.day.from_now.iso8601) }

            it 'sets status to locked' do
              command.call!
              expect(lms_assignment.reload.status).to eq('locked')
            end
          end

          context 'when lock_at is in past' do
            let(:canvas_attributes) { base_attributes.merge('lock_at' => 1.day.ago.iso8601) }

            it 'sets status to locked' do
              command.call!
              expect(lms_assignment.reload.status).to eq('locked')
            end
          end
        end

        context 'when assignment is not locked or past due' do
          it 'sets status to unlocked' do
            command.call!
            expect(lms_assignment.reload.status).to eq('unlocked')
          end
        end
      end

      describe 'required status' do
        context 'when assignment is not graded' do
          let(:canvas_attributes) { base_attributes.merge('grading_type' => 'not_graded') }

          it 'sets required to false' do
            command.call!
            expect(lms_assignment.reload.required).to be false
          end
        end

        context 'when assignment is omitted from final grade' do
          let(:canvas_attributes) { base_attributes.merge('omit_from_final_grade' => true) }

          it 'sets required to false' do
            command.call!
            expect(lms_assignment.reload.required).to be false
          end
        end

        context 'when assignment is graded and not omitted from final grade' do
          it 'sets required to true' do
            command.call!
            expect(lms_assignment.reload.required).to be true
          end
        end
      end

      describe '#update_accepted_submission_types' do
        context 'when submission_types are present' do
          let(:canvas_attributes) { base_attributes.merge('submission_types' => ['online_upload', 'online_text_entry']) }

          it 'calls UpdateAcceptedSubmissionTypesCommand with the correct parameters' do
            allow(Lms::Assignment::UpdateAcceptedSubmissionTypesCommand).to receive(:call!).and_call_original

            command.call!

            expect(Lms::Assignment::UpdateAcceptedSubmissionTypesCommand).to have_received(:call!).with(
              assignment: lms_assignment,
              submission_types: ['online_upload', 'online_text_entry'],
            ).once
          end
        end

        context 'when submission_types are not present' do
          let(:canvas_attributes) { base_attributes.except('submission_types') }

          it 'does not call UpdateAcceptedSubmissionTypesCommand' do
            allow(Lms::Assignment::UpdateAcceptedSubmissionTypesCommand).to receive(:call!)

            command.call!

            expect(Lms::Assignment::UpdateAcceptedSubmissionTypesCommand).not_to have_received(:call!)
          end
        end
      end
    end
  end
end
