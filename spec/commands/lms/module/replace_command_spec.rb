# frozen_string_literal: true

require 'rails_helper'

module Lms
  class Module
    describe ReplaceCommand, type: :command do
      let(:cohort) { create(:cohort) }
      let!(:remote_canvas_course) { create(:remote_canvas_course, core_record: cohort) }
      let!(:week) { create(:cohort_week, cohort:, number: 0) }
      let(:lms_module) { create(:lms_module, week:, cohort:) }
      let(:program) { cohort.program }

      let(:attributes) do
        {
          'id' => 'canvas_module_123',
          'name' => 'Week 1: Introduction to Ruby',
          'position' => 1,
          'unlock_at' => 1.week.from_now.iso8601,
          'items' => [
            {
              'id' => 'assignment_1',
              'title' => 'Ruby Basics',
              'type' => 'Assignment',
            },
          ],
        }
      end

      describe '#call!' do
        subject(:command) { described_class.new(cohort:, attributes:, lms_module:) }

        before do
          allow(Lms::Module::FetchRemoteAssignmentsData).to receive(:call!)
            .and_return([{ 'id' => 'assignment_1', 'title' => 'Ruby Basics' }])
          allow(Lms::Assignment::ReplaceFromCanvasAttributesCommand).to receive(:call!)
        end

        context 'when updating an existing module' do
          let!(:remote_canvas_module) { create(:remote_canvas_module, core_record: lms_module) }

          it 'updates the module attributes' do
            command.call!

            expect(lms_module.reload).to have_attributes(
              title: 'Introduction to Ruby',
              title_prefix: 'Week 1',
              unlock_at: Time.zone.parse(attributes['unlock_at']),
              position: 1,
              cohort:,
            )
          end

          it 'updates the remote canvas module key' do
            command.call!

            expect(lms_module.remote_canvas_module.reload.key).to eq('canvas_module_123')
          end

          it 'calls the Lms::Assignment::ReplaceFromCanvasAttributesCommand for each assignment' do
            command.call!

            expect(Lms::Assignment::ReplaceFromCanvasAttributesCommand).to have_received(:call!)
              .with(
                cohort:,
                canvas_attributes: { 'id' => 'assignment_1', 'title' => 'Ruby Basics', 'lms_module' => lms_module },
              )
          end

          context 'with module template association' do
            it 'finds or creates a module template and associates it with the module' do
              expect { command.call! }.to change { ModuleTemplate.count }.by(1)

              module_template = lms_module.reload.module_template
              expect(module_template).to be_present
              expect(module_template).to have_attributes(
                title: 'Introduction to Ruby',
                title_prefix: 'Week 1',
                week_number: 0,
                program:,
              )
            end

            context 'when a matching module template already exists' do
              let!(:existing_module_template) do
                create(:lms_module_template,
                  title: 'Introduction to Ruby',
                  title_prefix: 'Week 1',
                  week_number: 0,
                  program:,
                )
              end

              it 'reuses the existing module template' do
                expect { command.call! }.not_to(change { ModuleTemplate.count })
                expect(lms_module.reload.module_template).to eq(existing_module_template)
              end
            end
          end
        end

        context 'when creating a new module' do
          let(:lms_module) { nil }

          it 'creates a new module with correct attributes' do
            command.call!

            expect(command.lms_module).to have_attributes(
              title: 'Introduction to Ruby',
              title_prefix: 'Week 1',
              unlock_at: Time.zone.parse(attributes['unlock_at']),
              position: 1,
              cohort:,
            )
          end

          it 'creates a new remote canvas module' do
            command.call!

            expect(command.lms_module.remote_canvas_module).to be_present
            expect(command.lms_module.remote_canvas_module.key).to eq('canvas_module_123')
          end

          it 'creates and associates a module template' do
            expect { command.call! }.to change { ModuleTemplate.count }.by(1)

            expect(command.lms_module.module_template).to be_present
            expect(command.lms_module.module_template).to have_attributes(
              title: 'Introduction to Ruby',
              title_prefix: 'Week 1',
              week_number: 0,
              program:,
            )
          end
        end

        context 'when the week cannot be found' do
          let(:attributes) { { 'id' => 'canvas_module_123', 'position' => 999, 'name' => 'Invalid Week' } }

          it 'raises an ActiveRecord::RecordNotFound error' do
            command.call!

            expect(command.lms_module.week).to be_nil
          end
        end

        context 'when the transaction fails' do
          before do
            allow(lms_module).to receive(:update!).and_raise(ActiveRecord::RecordInvalid)
          end

          it 'rolls back all changes' do
            expect { command.call! }.to raise_error(ActiveRecord::RecordInvalid)
            expect(lms_module.reload.title).not_to eq('Introduction to Ruby')
          end
        end

        context 'when dealing with the last module' do
          let!(:additional_week) { create(:cohort_week, cohort:, number: 1) }
          let(:attributes) do
            {
              'id' => 'canvas_module_123',
              'name' => 'Week 2: Advanced Ruby',
              'position' => 2,
              'unlock_at' => 1.week.from_now.iso8601,
              'items' => [],
            }
          end

          it 'uses the last week number when it is the last module' do
            command.call!

            expect(command.send(:week_number)).to eq(1)
            expect(command.send(:last_module?)).to be_truthy
            expect(command.lms_module.week).to eq(additional_week)
          end
        end

        context 'when dealing with a regular module' do
          let!(:additional_weeks) do
            [
              create(:cohort_week, cohort:, number: 1),
              create(:cohort_week, cohort:, number: 2),
              create(:cohort_week, cohort:, number: 3),
            ]
          end
          let(:attributes) do
            {
              'id' => 'canvas_module_123',
              'name' => 'Week 2: Intermediate Ruby',
              'position' => 2,
              'unlock_at' => 1.week.from_now.iso8601,
              'items' => [],
            }
          end

          it 'uses the position as week number for non-last modules' do
            command.call!

            expect(command.send(:week_number)).to eq(2)
            expect(command.send(:last_module?)).to be_falsey
            expect(command.lms_module.week).to eq(additional_weeks.second)
          end
        end
      end
    end
  end
end
