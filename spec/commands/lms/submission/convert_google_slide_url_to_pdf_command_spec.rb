# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Lms::Submission::ConvertGoogleSlideUrlToPdfCommand do
  include ActiveSupport::Testing::TimeHelpers

  let(:presentation_id) { '12345' }
  let(:google_slide_url) { "https://docs.google.com/presentation/d/#{presentation_id}/edit" }
  let(:submission) { create(:lms_submission, :submitted, url: google_slide_url) }
  let(:current_review) { create(:lms_submission_review, submission:) }
  let(:command) { described_class.new(submission:) }

  before do
    allow(submission).to receive(:current_review).and_return(current_review)
    allow_any_instance_of(Google::Apis::DriveV3::DriveService).to receive(:export_file).and_return(true)
  end

  describe 'valid_google_slide_url?' do
    context 'when the URL is valid' do
      it 'returns true' do
        expect(command.send(:valid_google_slide_url?)).to be true
      end
    end

    context 'when the URL is invalid' do
      before { allow(submission).to receive(:url).and_return('invalid_url') }

      it 'returns false' do
        expect(command.send(:valid_google_slide_url?)).to be false
      end
    end
  end

  describe '#call!' do
    context 'when the URL is valid' do
      it 'parses the correct presentation_id' do
        expect(command.send(:presentation_id)).to eq(presentation_id)
      end

      context 'when the PDF is not already attached' do
        it 'attaches a PDF to the current review' do
          expect { command.call! }.to change { current_review.submission_export.attached? }.from(false).to(true)
        end

        it 'updates the review status to pdf_generated and the pdf_generated_at timestamp' do
          frozen_time = Time.zone.local(2024, 4, 29, 12, 0, 0)
          travel_to(frozen_time) do
            command.call!
            expect(current_review.reload.state).to eq('pdf_generated')
            expect(current_review.pdf_generated_at).to eq(frozen_time)
          end
        end
      end

      context 'when pdf is attached' do
        let(:current_review) { create(:lms_submission_review, :pdf_generated, submission:) }
        let!(:original_attachment) { current_review.submission_export.attachment }

        it 'does not re-attach the PDF' do
          expect { command.call! }.not_to(change { current_review.submission_export.attached? })
          expect(current_review.submission_export.attachment.id).to eq(original_attachment.id)
        end

        it 'does not update the review status' do
          current_review.graded!
          command.call!
          expect(current_review.reload.state).not_to eq('pdf_generated')
        end

        context 'when force is true' do
          let(:command) { described_class.new(submission:, force: true) }

          it 'reattaches the PDF' do
            command.call!
            expect(current_review.submission_export.attachment.id).not_to eq(original_attachment.id)
          end

          it 'updates the review status to pdf_generated' do
            command.call!
            expect(current_review.reload.state).to eq('pdf_generated')
          end
        end
      end

      context 'when the google slide is inaccessible' do
        before do
          allow_any_instance_of(Google::Apis::DriveV3::DriveService).to receive(:export_file).and_raise(
            Google::Apis::ClientError.new('Invalid request'),
          )
          allow(ErrorReporter).to receive(:report)
        end

        it 'does not attach a PDF' do
          expect { command.call! }.not_to(change { current_review.submission_export.attached? })
          expect(ErrorReporter).to have_received(:report)
        end

        it 'does not update the review status' do
          command.call!
          expect(current_review.reload.state).not_to eq('pdf_generated')
        end
      end
    end

    context 'when the URL is invalid' do
      before { allow(submission).to receive(:url).and_return('invalid_url') }

      it 'does not attach a PDF' do
        expect { command.call! }.not_to(change { current_review.submission_export.attached? })
      end

      it 'does not update the review status' do
        command.call!
        expect(current_review.reload.state).not_to eq('pdf_generated')
      end
    end

    context 'when there is no current review' do
      let(:current_review) { nil }

      it 'creates a new review and attaches the PDF' do
        expect { command.call! }.to change { submission.reviews.count }.by(1)
        new_review = command.current_review
        expect(new_review.submission_export.attached?).to be true
        expect(new_review.state).to eq('pdf_generated')
        expect(new_review.pdf_generated_at).to be_present
        expect(new_review.attempt).to eq(submission.attempt)
        expect(new_review.training).to be false
      end

      context 'when training is true' do
        let(:command) { described_class.new(submission:, training: true) }

        it 'creates a new review with training set to true' do
          expect { command.call! }.to change { submission.reviews.count }.by(1)
          new_review = command.current_review
          expect(new_review.training).to be true
        end
      end
    end
  end
end
