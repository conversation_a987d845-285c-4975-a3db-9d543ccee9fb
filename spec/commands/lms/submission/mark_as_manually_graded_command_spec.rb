# frozen_string_literal: true

require 'rails_helper'

module Lms
  class Submission
    RSpec.describe MarkAsManuallyGradedCommand, type: :command do
      let(:current_user) { create(:admin_user) }
      let!(:grader) { create(:learning_delivery_employee, admin_user: current_user) }
      let(:review) { create(:lms_submission_review, :manual_review_needed) }
      let(:submission) { review.submission }
      let(:command) { described_class.new(submission:, current_user:) }

      before do
        allow(current_user).to receive(:learning_delivery_employee).and_return(grader)
      end

      describe '#call!' do
        it 'marks the review as manually graded' do
          command.call!
          review.reload

          expect(review.manually_reviewed_at).to be_present
          expect(review.manually_reviewed_by).to eq(current_user)
          expect(review.state).to eq('graded')
          expect(review.graded_at).to eq(review.manually_reviewed_at)
        end

        it 'creates a learning delivery activity' do
          expect do
            command.call!
          end.to change { LearningDelivery::Activity.count }.by(1)

          activity = LearningDelivery::Activity.last
          expect(activity.activity_type).to eq('grade')
          expect(activity.employee).to eq(grader)
          expect(activity.target).to eq(review)
        end

        context 'when an error occurs' do
          before do
            allow(submission.current_review).to receive(:save!).and_raise(StandardError, "Database error")
          end

          it 'raises the error' do
            expect { command.call! }.to raise_error(StandardError, "Database error")
          end
        end
      end
    end
  end
end
