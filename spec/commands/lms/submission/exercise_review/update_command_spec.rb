# frozen_string_literal: true

require 'rails_helper'

module Lms
  class Submission
    class ExerciseReview
      RSpec.describe UpdateCommand, type: :command do
        let(:user) { create(:admin_user) }
        let(:exercise_review) { create(:lms_submission_exercise_review, state: :auto_graded) }
        let(:comment) { 'Great job!' }
        let(:grade) { 3 }
        let(:attributes) { { grade:, comment: } }

        subject(:command) do
          described_class.new(
            exercise_review:,
            attributes:,
            current_user: user,
          )
        end

        describe '#call!' do
          context 'when updating grade and comment' do
            it 'updates the grade and comment' do
              command.call!
              exercise_review.reload
              expect(exercise_review[:grade]).to eq(Lms::Submission::ExerciseReview.grades.key(grade))
              expect(exercise_review.comment).to eq(comment)
            end

            it 'raises an exception when update fails' do
              allow_any_instance_of(Lms::Submission::ExerciseReview).to receive(:update!).and_raise(ActiveRecord::RecordInvalid.new(exercise_review))
              expect { command.call! }.to raise_error(ActiveRecord::RecordInvalid)
            end
          end

          context 'when updating manually_reviewed_at and manually_reviewed_by fields' do
            before do
              allow(Time).to receive(:current).and_return(Time.zone.local(2025, 6, 1, 12, 0, 0))
            end

            it 'updates manually_reviewed_at to current time' do
              command.call!
              exercise_review.reload
              expect(exercise_review.manually_reviewed_at).to eq(Time.zone.local(2025, 6, 1, 12, 0, 0))
            end

            it 'updates manually_reviewed_by to current user' do
              command.call!
              exercise_review.reload
              expect(exercise_review.manually_reviewed_by).to eq(user)
            end

            it 'sets the state to graded' do
              command.call!
              exercise_review.reload
              expect(exercise_review.graded?).to be true
            end

            it 'raises an exception when save fails during mark_as_reviewed' do
              allow_any_instance_of(Lms::Submission::ExerciseReview).to receive(:update!).and_return(true)
              allow_any_instance_of(Lms::Submission::ExerciseReview).to receive(:save!).and_raise(ActiveRecord::RecordInvalid.new(exercise_review))
              expect { command.call! }.to raise_error(ActiveRecord::RecordInvalid)
            end
          end

          context 'when mark_as_reviewed is true' do
            let(:title) { 'New Title' }
            let(:attributes) { { grade:, comment:, title:, state: 'submitted', mark_as_reviewed: true } }

            it 'updates all attributes' do
              original_state = exercise_review.state
              command.call!
              exercise_review.reload
              expect(exercise_review[:grade]).to eq(Lms::Submission::ExerciseReview.grades.key(grade))
              expect(exercise_review.comment).to eq(comment)
              expect(exercise_review.title).to eq(title)
              expect(exercise_review.manually_reviewed_at).to be_present
              expect(exercise_review.state).not_to eq(original_state) # State changes due to mark_as_reviewed
            end
          end

          context 'when mark_as_reviewed is false' do
            let(:attributes) { { grade:, comment:, mark_as_reviewed: false } }
            let(:exercise_review) do
              create(:lms_submission_exercise_review,
                state: :graded,
                manually_reviewed_at: Time.current,
                manually_reviewed_by: user,
              )
            end

            it 'returns state to manuel_review_needed and clears out the manually reviewed fields' do
              command.call!
              exercise_review.reload
              expect(exercise_review.manually_reviewed_at).to be_nil
              expect(exercise_review.manually_reviewed_by).to be_nil
              expect(exercise_review.manual_review_needed?).to be true
            end

            it 'does not update grade or comment' do
              original_grade = exercise_review.grade
              original_comment = exercise_review.comment
              command.call!
              exercise_review.reload
              expect(exercise_review.grade).to eq(original_grade)
              expect(exercise_review.comment).to eq(original_comment)
            end

            it 'raises an exception when save fails during undo_mark_as_reviewed' do
              allow_any_instance_of(Lms::Submission::ExerciseReview).to receive(:save!).and_raise(ActiveRecord::RecordInvalid.new(exercise_review))
              expect { command.call! }.to raise_error(ActiveRecord::RecordInvalid)
            end
          end

          context 'when transaction fails' do
            it 'raises the exception' do
              allow(ActiveRecord::Base).to receive(:transaction).and_raise(StandardError, 'Transaction failed')
              expect { command.call! }.to raise_error(StandardError, 'Transaction failed')
            end
          end
        end
      end
    end
  end
end
