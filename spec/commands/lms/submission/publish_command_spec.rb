# frozen_string_literal: true

require 'rails_helper'

module Lms
  class Submission
    RSpec.describe PublishCommand, type: :command do
      let(:training) { false }
      let(:review) { create(:lms_submission_review, :manual_review_needed, training:) }
      let(:submission) { review.submission }
      let(:remote_canvas_submission) { create(:remote_canvas_submission, core_record: submission, key: 'canvas_submission_123') }
      let(:command) { described_class.new(submission:) }
      let(:comment_create_command) { instance_double(Lms::Submission::Comment::CreateCommand) }

      before do
        allow(submission).to receive(:remote_canvas_submission).and_return(remote_canvas_submission)
        allow(remote_canvas_submission).to receive(:remote_update)
        allow(Lms::Submission::Comment::CreateCommand).to receive(:new).and_return(comment_create_command)
        allow(comment_create_command).to receive(:call!)
      end

      describe '#call!' do
        context "when publishing fails" do
          before do
            allow_any_instance_of(described_class).to receive(:publish_comment).and_raise(StandardError, "Remote error")
          end

          it 'raises error' do
            expect { command.call! }.to raise_error(StandardError, "Remote error")
          end
        end

        context 'when review is already published' do
          let(:review) { create(:lms_submission_review, :published) }

          it 'returns early and does not publish' do
            command.call!
            expect(remote_canvas_submission).not_to have_received(:remote_update)
            expect(Lms::Submission::Comment::CreateCommand).not_to have_received(:new)
            expect(comment_create_command).not_to have_received(:call!)
          end
        end

        context 'when review is a training review' do
          let(:training) { true }

          it 'returns early and does not publish' do
            command.call!
            expect(remote_canvas_submission).not_to have_received(:remote_update)
            expect(Lms::Submission::Comment::CreateCommand).not_to have_received(:new)
            expect(comment_create_command).not_to have_received(:call!)
          end
        end

        context 'when review is not published and not training' do
          let(:grader) { create(:learning_delivery_employee, admin_user: review.manually_reviewed_by) }

          before do
            allow(submission.current_review).to receive(:grader).and_return(grader)
          end

          it 'publishes the grade to Canvas' do
            command.call!
            expect(remote_canvas_submission).to have_received(:remote_update)
          end

          it 'marks the review as published' do
            command.call!
            review.reload

            expect(review.published_at).not_to be_nil
            expect(review.state).to eq('published')
          end

          it 'marks the submission as graded' do
            command.call!
            expect(submission.state).to eq('graded')
            expect(submission.grade).to eq(review.grade)
            expect(submission.graded_at).not_to be_nil
            expect(submission.graded_by).to eq(grader)
          end

          it 'updates submission before calling remote update' do
            allow(submission).to receive(:update!).and_call_original
            command.call!

            expect(submission).to have_received(:update!).ordered
            expect(remote_canvas_submission).to have_received(:remote_update).ordered
          end

          context 'when review has comments to publish' do
            let(:review) { create(:lms_submission_review, :with_manual_review, training:) }
            let(:comments_to_publish) { "This is a great submission!" }

            before do
              allow(submission.current_review).to receive(:comments_to_publish).and_return(comments_to_publish)
              allow(review.manually_reviewed_by).to receive(:learning_delivery_employee).and_return(grader)
            end

            it 'creates a submission comment and publishes it' do
              command.call!
              expect(Lms::Submission::Comment::CreateCommand).to have_received(:new).with(
                submission:,
                author: grader,
                text: comments_to_publish,
              )
              expect(comment_create_command).to have_received(:call!)
            end
          end

          context 'when review has comments to publish and review has no manually reviewed by' do
            let(:review) { create(:lms_submission_review, :graded) }
            let(:grader) { create(:learning_delivery_employee, admin_user: review.manually_reviewed_by) }
            let(:comments_to_publish) { "This is a great submission!" }

            before do
              allow(submission.current_review).to receive_messages(comments_to_publish:, grader:)
            end

            it 'creates a submission comment and publishes it' do
              command.call!
              expect(Lms::Submission::Comment::CreateCommand).to have_received(:new).with(
                submission:,
                author: grader,
                text: comments_to_publish,
              )
              expect(comment_create_command).to have_received(:call!)
            end
          end

          context 'when review has no comments to publish' do
            before { allow(submission.current_review).to receive(:comments_to_publish).and_return(nil) }

            it 'does not create a submission comment' do
              command.call!

              expect(Lms::Submission::Comment::CreateCommand).not_to have_received(:new)
              expect(comment_create_command).not_to have_received(:call!)
            end
          end
        end
      end
    end
  end
end
