# frozen_string_literal: true

require 'rails_helper'

module Lms
  class Submission
    class Comment
      RSpec.describe DeleteCommand, type: :command do
        let(:admin_user) { create(:admin_user) }
        let(:employee) { create(:learning_delivery_employee, admin_user:) }
        let(:submission) { create(:lms_submission) }
        let(:comment) { create(:lms_submission_comment, submission:, author: employee) }
        let(:command) { described_class.new(comment:) }
        let(:remote_canvas_submission_comment) { instance_double(Remote::Canvas::SubmissionComment, remote_resource_path: '/path') }

        describe '#call!' do
          before do
            allow(comment).to receive(:remote_canvas_submission_comment).and_return(remote_canvas_submission_comment)
            allow(Remote::UnpublishCommand).to receive(:call!)
          end

          it 'deletes the comment' do
            comment
            expect { command.call! }.to change { submission.comments.count }.by(-1)

            expect { comment.reload }.to raise_error(ActiveRecord::RecordNotFound)
          end

          it 'unpublishes the comment from <PERSON>vas' do
            command.call!
            expect(Remote::UnpublishCommand).to have_received(:call!).with(remote_resource: remote_canvas_submission_comment)
          end


          context 'when comment has no remote canvas submission comment' do
            before do
              allow(comment).to receive(:remote_canvas_submission_comment).and_return(nil)
              allow(Remote::UnpublishCommand).to receive(:call!)
            end

            it 'does not attempt to unpublish' do
              command.call!
              expect(Remote::UnpublishCommand).not_to have_received(:call!)
            end
          end

          context 'when transaction fails' do
            before do
              allow(comment).to receive(:destroy!).and_raise(ActiveRecord::RecordNotDestroyed)
            end

            it 'rolls back the transaction' do
              expect do
                expect { command.call! }.to raise_error(ActiveRecord::RecordNotDestroyed)
              end.not_to(change { submission.comments.count })
            end
          end
        end
      end
    end
  end
end
