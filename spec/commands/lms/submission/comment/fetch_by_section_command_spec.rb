# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Lms::Submission::Comment::FetchBySectionCommand do
  describe '#call!' do
    let(:cohort) { create(:cohort, key: 'course_1') }
    let(:section) { create(:section, :configured, cohort:) }
    let!(:remote_canvas_course) { create(:remote_canvas_course, core_record: cohort) }
    let!(:remote_canvas_section) { create(:remote_canvas_section, core_record: section, key: 'SEC1') }

    let(:updated_since) { 4.hours.ago }

    let(:canvas_client) { instance_double(CanvasClient) }

    before do
      allow(CanvasClient).to receive(:new).and_return(canvas_client)
    end

    context 'when GraphQL returns submissions with comments and matching local submissions exist' do
      let(:graphql_response) do
        {
          'data' => {
            'course' => {
              'submissionsConnection' => {
                'nodes' => [
                  {
                    '_id' => 'sub_1',
                    'commentsConnection' => {
                      'nodes' => [
                        { '_id' => 'cmt_1', 'comment' => 'Nice work', 'attempt' => 1, 'author' => { '_id' => 'user_1' } },
                        { '_id' => 'cmt_2', 'comment' => 'Fix please', 'attempt' => 2, 'author' => { '_id' => 'user_2' } },
                      ],
                    },
                  },
                  {
                    '_id' => 'sub_2',
                    'commentsConnection' => { 'nodes' => [] },
                  },
                ],
              },
            },
          },
        }
      end

      let(:ecom_order_item_args) { [:fulfilled, order_args: [:with_payment, mapped_payment_args: [:paid]]] }
      let!(:enrollment) { create(:enrollment, section:, ecom_order_item_args:) }
      let!(:assignment) { create(:lms_assignment) }
      let!(:lms_submission) { create(:lms_submission, enrollment:, assignment:) }
      let!(:remote_canvas_submission) { create(:remote_canvas_submission, core_record: lms_submission, key: 'sub_1') }

      it 'calls ReplaceCommand for each comment on matched submissions and scopes query by section' do
        allow(canvas_client).to receive(:graphql).and_return(graphql_response)
        allow(Lms::Submission::Comment::ReplaceCommand).to receive(:call!)

        described_class.new(section:, updated_since:).call!

        expect(canvas_client).to have_received(:graphql).with(
          hash_including(
            query: include('query RecentSubmissionComments'),
            variables: hash_including(
              sisCourseId: 'can_course_1',
              sectionIds: ['SEC1'],
              updatedSince: satisfy { |v| Time.zone.parse(v).between?(updated_since - 1.second, updated_since + 1.second) },
            ),
          ),
        )

        expect(Lms::Submission::Comment::ReplaceCommand).to have_received(:call!).with(
          submission: lms_submission,
          attributes: include(
            'id' => 'cmt_1',
            'comment' => 'Nice work',
            'attempt' => 1,
            'author_id' => 'user_1',
          ),
        )

        expect(Lms::Submission::Comment::ReplaceCommand).to have_received(:call!).with(
          submission: lms_submission,
          attributes: include(
            'id' => 'cmt_2',
            'comment' => 'Fix please',
            'attempt' => 2,
            'author_id' => 'user_2',
          ),
        )
      end
    end

    context 'when no matching local submissions are found' do
      let(:graphql_response) do
        {
          'data' => {
            'course' => {
              'submissionsConnection' => {
                'nodes' => [
                  {
                    '_id' => 'unknown_sub',
                    'commentsConnection' => {
                      'nodes' => [
                        { '_id' => 'cmt_1', 'comment' => 'Hello', 'attempt' => 1, 'author' => { '_id' => 'user_1' } },
                      ],
                    },
                  },
                ],
              },
            },
          },
        }
      end

      it 'does not call ReplaceCommand' do
        allow(canvas_client).to receive(:graphql).and_return(graphql_response)
        allow(Lms::Submission::Comment::ReplaceCommand).to receive(:call!)

        described_class.new(section:, updated_since:).call!

        expect(Lms::Submission::Comment::ReplaceCommand).not_to have_received(:call!)
      end
    end
  end
end
