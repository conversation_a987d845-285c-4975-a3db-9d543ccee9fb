# frozen_string_literal: true

require 'rails_helper'

module Lms
  class Submission
    class Comment
      RSpec.describe CreateCommand, type: :command do
        let(:admin_user) { create(:admin_user) }
        let(:employee) { create(:learning_delivery_employee, admin_user:) }
        let(:learner) { create(:learner) }
        let(:submission) { create(:lms_submission) }
        let(:text) { "This is a test comment" }
        let(:command) { described_class.new(submission:, author: employee, text:) }
        let(:learner_command) { described_class.new(submission:, author: learner, text:) }
        let(:remote_canvas_submission_comment) { instance_double(Remote::Canvas::SubmissionComment, remote_resource_path: '/path') }

        describe '#call!' do
          before do
            allow_any_instance_of(Lms::Submission::Comment).to receive(:build_remote_canvas_submission_comment).and_return(
               remote_canvas_submission_comment,
             )
            allow(Remote::PublishCommand).to receive(:call!)
          end

          context 'when creating a comment' do
            it 'creates a comment with the correct attributes' do
              expect do
                command.call!
              end.to change { submission.comments.count }.by(1)

              comment = submission.comments.last
              expect(comment.text).to eq(text)
              expect(comment.author).to eq(employee)
              expect(comment.attempt).to eq(submission.attempt)
              expect(comment.read_at).to be_present
            end

            it 'returns the created comment' do
              result = command.call!
              expect(result).to eq(command.comment)
              expect(result).to be_a(Lms::Submission::Comment)
            end

            context 'when author is a learner' do
              it 'creates a comment without read_at' do
                expect do
                  learner_command.call!
                end.to change { submission.comments.count }.by(1)

                comment = submission.comments.last
                expect(comment.text).to eq(text)
                expect(comment.author).to eq(learner)
                expect(comment.read_at).to be_nil
              end
            end
          end

          it 'publishes the comment' do
            command.call!
            expect(Remote::PublishCommand).to have_received(:call!).with(remote_resource: remote_canvas_submission_comment)
          end

          context 'when transaction fails' do
            before do
              allow_any_instance_of(Lms::Submission::Comment).to receive(:save!).and_raise(ActiveRecord::RecordInvalid)
            end

            it 'rolls back the transaction' do
              expect do
                expect { command.call! }.to raise_error(ActiveRecord::RecordInvalid)
              end.not_to(change { submission.comments.count })
            end
          end
        end
      end
    end
  end
end
