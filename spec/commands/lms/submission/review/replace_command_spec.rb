# frozen_string_literal: true

require 'rails_helper'

module Lms
  class Submission
    class Review
      RSpec.describe ReplaceCommand, type: :command do
        let(:review) { create(:lms_submission_review, :auto_graded, grade: :incomplete) }
        let(:grade) { 1 } # complete
        let(:comment) { "New Comment" }
        let(:attributes) { { grade:, comment: } }
        let(:current_user) { create(:admin_user) }
        let(:command) { described_class.new(review:, attributes:, current_user:) }

        describe '#call!' do
          it 'updates the review with the provided attributes' do
            command.call!

            expect(review.reload.complete?).to be true
            expect(review.comment).to eq(comment)
          end

          it 'raises an error if the update fails' do
            allow_any_instance_of(Lms::Submission::Review).to receive(:update!).and_raise(ActiveRecord::RecordInvalid)

            expect { command.call! }.to raise_error(ActiveRecord::RecordInvalid)
          end
        end
      end
    end
  end
end
