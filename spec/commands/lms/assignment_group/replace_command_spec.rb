# frozen_string_literal: true

require 'rails_helper'

module Lms
  class AssignmentGroup
    describe ReplaceCommand do
      let(:cohort) { create(:cohort) }
      let(:assignments_data) do
        [
          {
            'id' => 'assignment1_id',
            'name' => 'Assignment 1',
            'short_name' => 'Assign 1',
            'position' => 1,
          },
          {
            'id' => 'assignment2_id',
            'name' => 'Assignment 2',
            'short_name' => 'Assign 2',
            'position' => 2,
          },
        ]
      end
      let(:attributes) do
        {
          'name' => 'Updated Assignment Group',
          'position' => 2,
          'assignments' => assignments_data,
        }
      end

      describe '#initialize' do
        context 'when assignment_group is not provided' do
          it 'creates a new Lms::AssignmentGroup' do
            command = described_class.new(cohort:, attributes:)

            expect(command.cohort).to eq(cohort)
            expect(command.attributes).to eq(attributes)
            expect(command.assignment_group).to be_a(Lms::AssignmentGroup)
            expect(command.assignment_group.cohort).to eq(cohort)
          end
        end

        context 'when assignment_group is provided' do
          let(:existing_group) { create(:lms_assignment_group, cohort:) }

          it 'uses the provided assignment group' do
            command = described_class.new(
              cohort:,
              attributes:,
              assignment_group: existing_group,
            )

            expect(command.assignment_group).to eq(existing_group)
          end
        end
      end

      describe '#call!' do
        let(:assignment_group) { create(:lms_assignment_group, cohort:) }

        it 'updates the assignment group with provided attributes' do
          command = described_class.new(
            cohort:,
            attributes: attributes.except('assignments'),
            assignment_group:,
          )

          command.call!

          expect(assignment_group.reload.name).to eq(attributes['name'])
          expect(assignment_group.position).to eq(attributes['position'])
        end

        context 'attribute filtering' do
          it 'filters out unwanted attributes and keeps only name, position, short_name' do
            attributes_with_extra_fields = {
              'id' => 1224,
              'name' => 'Online Coursework and Assessments',
              'position' => 1,
              'group_weight' => 55.0,
              'sis_source_id' => nil,
              'integration_data' => {},
              'rules' => {},
              'short_name' => 'Custom Short',
            }

            command = described_class.new(
              cohort:,
              attributes: attributes_with_extra_fields,
              assignment_group:,
            )

            command.call!

            assignment_group.reload
            expect(assignment_group.name).to eq('Online Coursework and Assessments')
            expect(assignment_group.position).to eq(1)
            expect(assignment_group.short_name).to eq('Custom Short')
          end
        end

        context 'short_name' do
          context 'when short_name is provided' do
            it 'uses the provided short_name' do
              attributes_with_short_name = attributes.except('assignments').merge('short_name' => 'Custom Short')
              command = described_class.new(
                cohort:,
                attributes: attributes_with_short_name,
                assignment_group:,
              )

              command.call!

              expect(assignment_group.reload.short_name).to eq('Custom Short')
            end
          end

          context 'when short_name is not provided' do
            let!(:setting) do
              create(:setting, name: 'lms_assignment_group_short_name_mapping', value: { 'Updated Assignment Group' => 'Mapped Name' })
            end

            it 'uses the mapped short name from settings' do
              command = described_class.new(attributes: attributes.except('assignments'), assignment_group:)

              command.call!

              expect(assignment_group.reload.short_name).to eq('Mapped Name')
            end
          end
        end

        context 'with assignments in attributes' do
          context 'assignment records are not present' do
            it 'calls ReplaceCommand for each assignment' do
              command = described_class.new(
                cohort:,
                attributes:,
                assignment_group:,
              )

              allow(Lms::Assignment::ReplaceFromCanvasAttributesCommand).to receive(:call!)

              command.call!

              assignments_data.each do |assignment_data|
                expected_attributes = assignment_data.merge('lms_assignment_group' => assignment_group)
                expect(Lms::Assignment::ReplaceFromCanvasAttributesCommand).to have_received(:call!).with(
                  cohort:,
                  canvas_attributes: expected_attributes,
                ).once
              end
            end
          end
        end

        context 'with blank assignments' do
          it 'does not call ReplaceCommand for assignments' do
            attributes_without_assignments = attributes.except('assignments')
            command = described_class.new(
              cohort:,
              attributes: attributes_without_assignments,
              assignment_group:,
            )

            allow(Lms::Assignment::ReplaceFromCanvasAttributesCommand).to receive(:call!)

            command.call!

            expect(Lms::Assignment::ReplaceFromCanvasAttributesCommand).not_to have_received(:call!)
          end
        end
      end
    end
  end
end
