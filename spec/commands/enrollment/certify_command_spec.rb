# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Enrollment::CertifyCommand, type: :command do
  around do |example|
    Time.use_zone('UTC') { example.run }
  end

  let(:enrollment) { create(:enrollment) }
  let(:certificate_url) { 'http://example.com/certificate.png' }
  let(:certificate_issued_at) { Date.current }
  let(:command) { described_class.new(enrollment:, certificate_issued_at:, certificate_url:) }

  before do
    allow(Enrollment::ReplaceCommand).to receive(:call!)
  end

  describe '#call!' do
    context 'when certificate_issued_at is the current date in UTC' do
      it 'calls Enrollment::ReplaceCommand with the current date converted to a timestamp' do
        command.call!
        expect(Enrollment::ReplaceCommand).to have_received(:call!).with(
          enrollment:,
          attributes: {
            status: :certificate_issued,
            certificate_url:,
            certificate_issued_at: certificate_issued_at.all_day,
          },
          reason: String,
          notes: String,
        )
      end
    end

    context 'when certificate_issued_at is a date other than current date in UTC' do
      let(:certificate_issued_at) { Date.current - 1.day }

      it 'calls Enrollment::ReplaceCommand with the certificate_issued_at unchanged' do
        command.call!
        expect(Enrollment::ReplaceCommand).to have_received(:call!).with(
          enrollment:,
          attributes: {
            status: :certificate_issued,
            certificate_url:,
            certificate_issued_at: certificate_issued_at.all_day,
          },
          reason: String,
          notes: String,
        )
      end
    end
  end
end
