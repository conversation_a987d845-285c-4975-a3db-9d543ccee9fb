# frozen_string_literal: true

require 'rails_helper'

class Enrollment
  describe AlertOnDuplicateCommand do
    describe '#call!' do
      let(:enrollment) { create(:enrollment) }
      let!(:duplicate_enrollment) { create(:enrollment, learner: enrollment.learner, section_args: { cohort: enrollment.cohort }) }

      subject(:call!) do
        described_class.call!(enrollment:)
      end

      context 'when a duplicate primary enrollment exists for the same learner and cohort' do
        it 'creates a duplicate enrollment task' do
          allow(LearningDelivery::Task::CreateResolveDuplicateEnrollmentTaskCommand).to receive(:call!).and_return(true)

          call!

          expect(LearningDelivery::Task::CreateResolveDuplicateEnrollmentTaskCommand).to have_received(:call!).with(
            enrollment:,
            duplicate_enrollment:,
          ).once
        end
      end

      context 'when no duplicate enrollment exists' do
        before do
          duplicate_enrollment.update!(learner: create(:learner))
        end

        it 'does not create a duplicate enrollment task' do
          allow(LearningDelivery::Task::CreateResolveDuplicateEnrollmentTaskCommand).to receive(:call!).and_return(true)

          call!

          expect(LearningDelivery::Task::CreateResolveDuplicateEnrollmentTaskCommand).not_to have_received(:call!)
        end
      end

      context 'when the enrollment is not primary' do
        before do
          allow(enrollment).to receive(:primary?).and_return(false)
        end

        it 'does not create a duplicate enrollment task' do
          allow(LearningDelivery::Task::CreateResolveDuplicateEnrollmentTaskCommand).to receive(:call!).and_return(true)

          call!

          expect(LearningDelivery::Task::CreateResolveDuplicateEnrollmentTaskCommand).not_to have_received(:call!)
        end
      end

      context 'when the enrollment is a transfer' do
        before do
          enrollment.update!(transferred_from: create(:enrollment))
        end

        it 'does not create a duplicate enrollment task' do
          allow(LearningDelivery::Task::CreateResolveDuplicateEnrollmentTaskCommand).to receive(:call!).and_return(true)

          call!

          expect(LearningDelivery::Task::CreateResolveDuplicateEnrollmentTaskCommand).not_to have_received(:call!)
        end
      end
    end
  end
end
