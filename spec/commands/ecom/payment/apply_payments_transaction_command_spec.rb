# frozen_string_literal: true

require 'rails_helper'

describe Ecom::Payment::ApplyPaymentsTransactionCommand do
  describe '#call!' do
    let(:payment_method_kind) { 'installments' }
    let(:scenario) { PaidLearnerScenario.create(ecom_payment_method_kind: payment_method_kind) }
    let(:payment) { scenario.ecom_payment }
    let(:transaction_amount) { payment.total_cents }
    let(:status) { payment.status }
    let!(:charge_transaction) do
      create(:payments_transaction, :charge, ecom_payment: payment, amount_cents: transaction_amount)
    end
    let(:payments_transaction) { charge_transaction }

    let(:ecom_order_item) { payment.order.order_items.first }
    let!(:process_revenue_command_class) { class_spy(Ecom::OrderItem::ProcessRevenueCommand).as_stubbed_const }
    let!(:fulfill_command_class) { class_spy(Ecom::Order::ConditionalFulfillCommand, call!: nil).as_stubbed_const }
    let!(:booking) { create(:finance_booking, :revenue, ecom_order_item:, ecom_payment: payment) }
    let(:order_fulfillable) { true }
    let(:command) { described_class.new(payment:, payments_transaction:) }

    before do
      allow(process_revenue_command_class).to receive(:call!) do |ecom_order_item:, payments_transaction:|
        if ecom_order_item.order.paid? && ecom_order_item.registration&.confirmed_status?
          Finance::Receipt::CreateCommand.call!(ecom_order_item:, payments_transaction:)
        end
      end
      allow_any_instance_of(Ecom::Order).to receive(:fulfillable?).and_return(order_fulfillable)
      payment.update!(status:)
      payment.payment_method.update!(kind: payment_method_kind)
      ecom_order_item.registration&.update!(status: :confirmed)
    end

    subject(:call!) { command.call! }

    context 'with unpaid pending payment' do
      def assert_valid_call(payment_status:, order_status:)
        expect(process_revenue_command_class).to have_received(:call!).with(ecom_order_item:, payments_transaction:)
        expect(fulfill_command_class).to have_received(:call!)

        expect(payment.reload.status).to eq(payment_status)
        expect(payment.order.status).to eq(order_status)

        expect(payments_transaction.finance_receipt).to have_attributes(
          booking: payment.finance_revenue_booking,
          total_cents: payments_transaction.amount_cents,
          received_at: payments_transaction.created_at,
        )
      end

      context 'with balance fully paid' do
        let(:transaction_amount) { payment.unpaid_balance_cents }

        it 'updates the payment status to paid and fulfills order' do
          call!

          assert_valid_call(payment_status: 'paid', order_status: 'paid')
        end

        context 'with refund' do
          let(:refund_cents) { raise }
          let(:payments_transaction) do
            create(
              :ecom_refund,
              :partial,
              :with_payments_transaction_refund,
              refund_cents:, payments_transaction_charge: charge_transaction,
              payments_transaction_refund_args: { amount_cents: -refund_cents, ecom_payment: payment },
            ).payments_transaction_refund
          end

          context 'with a partial refund' do
            let(:refund_cents) { 1 }

            it 'updates the payment status to partial' do
              call!

              assert_valid_call(payment_status: 'partial', order_status: 'paid')
            end
          end

          context 'with a full refund' do
            let(:refund_cents) { transaction_amount }

            it 'updates the payment status to partial' do
              call!

              assert_valid_call(payment_status: 'refunded', order_status: 'paid')
            end
          end
        end
      end

      context 'with balance not fully paid' do
        let(:transaction_amount) { payment.unpaid_balance_cents - 1 }

        it 'updates the payment status to paid and fulfills order' do
          call!

          assert_valid_call(payment_status: 'partial', order_status: 'paid')
        end
      end

      context 'with balance overpaid' do
        let(:transaction_amount) { payment.unpaid_balance_cents + 1 }

        it 'updates the payment status to paid and fulfills order' do
          call!

          assert_valid_call(payment_status: 'paid', order_status: 'paid')
        end
      end
    end

    context 'with partner_invoice payment method' do
      let(:payment_method_kind) { 'partner_invoice' }
      let(:registration) { payment.order.order_items.first.reload.registration }
      let(:internal_notification_mailer) { instance_double(ActionMailer::MessageDelivery, deliver_later: true) }

      before do
        allow(PartnerSourcedRegistrationMailer).to receive(:with).with(registration:).and_return(PartnerSourcedRegistrationMailer)
        allow(PartnerSourcedRegistrationMailer).to receive(:partner_invoice_payment_confirmation).and_return(internal_notification_mailer)
      end

      it 'calls PartnerSourcedRegistrationMailer' do
        call!

        expect(PartnerSourcedRegistrationMailer).to have_received(:with).with(registration:)
        expect(PartnerSourcedRegistrationMailer).to have_received(:partner_invoice_payment_confirmation)
        expect(internal_notification_mailer).to have_received(:deliver_later).with(wait: 5.seconds)
      end
    end
  end
end
