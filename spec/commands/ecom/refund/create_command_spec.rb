# frozen_string_literal: true

require 'rails_helper'

describe Ecom::Refund::CreateCommand do
  describe '#call!' do
    let(:refund_status) { 'pending' }
    let(:refund_cents) { raise }
    let(:charge_cents) { rand(999_00..999_99) }
    let(:order_status) { 'paid' }
    let(:payments_transaction_charge) do
      scenario = PaidLearnerScenario.create
      scenario.registration.update!(status: :confirmed)
      create(
        :payments_transaction,
        :charge,
        amount_cents: charge_cents,
        ecom_payment: scenario.ecom_payment,
      ).tap do |_transaction|
        scenario.ecom_payment.order.update!(status: order_status)
      end
    end

    let(:payment) { payments_transaction_charge.ecom_payment }
    let(:order) { payment.order }
    let(:affects_total_price) { true }
    let(:command) do
      described_class.new(
        payments_transaction_charge:,
        payment:,
        status: refund_status,
        refund_cents:,
        affects_total_price:,
      )
    end
    let!(:book_refund_command) do
      class_spy(Finance::Booking::Refund::BookCommand, call!: nil).as_stubbed_const
    end
    let!(:publish_job) { class_spy(Deal::PublishToHubspotJob, perform_async: nil).as_stubbed_const }
    let!(:stripe_refund_create_command_class_spy) do
      class_spy_const(Payments::Stripe::Refund::CreateCommand, call!: Struct.new(:id, :transacted_at).new(rand.to_s, Time.zone.now))
    end
    let!(:error_reporter_class_spy) do
      class_spy(ErrorReporter, report: nil).as_stubbed_const
    end

    subject(:call!) { command.call! }

    # @return [Ecom::Refund]
    def expect_valid_call(refund_status:, payment_status:)
      payment.payments_transactions

      refund = call!

      refund.reload
      expect(refund.status).to eq(refund_status)
      expect(refund.order.payment.status).to eq(payment_status)

      expect(publish_job).to have_received(:perform_async).at_least(:once)

      refund
    end

    context 'with a full refund' do
      def expect_valid_full_refund
        expect_valid_call(refund_status: 'refunded', payment_status: 'refunded')
      end

      let(:refund_cents) { charge_cents }

      it 'creates a refund transaction and updates refund and payment status' do
        call!

        expect_valid_full_refund
      end

      context 'with affects_total_price true' do
        let(:affects_total_price) { true }

        it 'creates a booking' do
          refund = expect_valid_full_refund

          expect(book_refund_command).to have_received(:call!).with(
            ecom_order_item: order.order_items.first,
            ecom_refund: refund,
          )
        end
      end

      context 'with affects_total_price false' do
        let(:affects_total_price) { false }

        it 'creates a refund but does not create a booking' do
          expect_valid_full_refund

          expect(book_refund_command).not_to have_received(:call!)
        end
      end

      context 'with error raised while creating transaction' do
        let(:error) { raise }

        def expect_handles_error
          allow(stripe_refund_create_command_class_spy).to receive(:call!).and_raise(error)

          refund = nil

          expect do
            call!
          rescue => e
            expect(e).to be_a(described_class::Error)
            refund = e.refund
            raise e
          end.to raise_error(described_class::Error)

          expect(refund.status).to eq('failed')
          expect(refund.new_record?).to be(true)
        end

        context 'with Payments::Transaction::Refund::CreateCommand::Error type' do
          let(:error) { Payments::Transaction::Refund::CreateCommand::Error.new('test message') }

          it 'handles error and does not report error' do
            expect_handles_error

            expect(error_reporter_class_spy).not_to have_received(:report)
          end
        end

        context 'with unknown error type' do
          let(:error) { StandardError.new('test message') }

          it 'handles error and does not report error' do
            expect_handles_error

            expect(error_reporter_class_spy).to have_received(:report)
          end
        end
      end

      context 'with order in invalid status' do
        let(:order_status) { (Ecom::Order.statuses.keys - %w[paid fulfilled]).sample }

        it 'raises ArgumentError' do
          expect { call! }.to raise_error("Order must be in paid or fulfilled status for order #{order.id}}")
        end
      end
    end

    context 'with a partial refund' do
      let(:affects_total_price) { false }
      let(:refund_cents) { rand(1..(charge_cents - 1)) }

      it 'creates a refund transaction and updates refund status' do
        call!

        expect_valid_call(refund_status: 'partial', payment_status: 'partial')
      end
    end

    context 'with refund not pending' do
      let(:refund_cents) { 1 }
      let(:refund_status) { (Ecom::Refund.statuses.keys - %w[pending]).sample }

      it 'raises ArgumentError' do
        expect { call! }.to raise_error(ArgumentError, "Refund must be pending: #{refund_status}")
      end
    end
  end
end
