# frozen_string_literal: true

require 'rails_helper'

describe Ecom::OrderItem::ProcessRevenueCommand do
  describe '#call!' do
    let!(:scenario) { PaidLearnerScenario.create }
    let(:order_item) { scenario.ecom_order_item }
    let(:payments_transaction) { create(:payments_transaction, :charge, ecom_payment: order_item.payment) }
    let!(:booking) { create(:finance_booking, :revenue, ecom_order_item: order_item, ecom_payment: order_item.payment) }
    let(:command) { described_class.new(ecom_order_item: order_item, payments_transaction:) }
    let!(:revenue_book_command_class) { class_spy(Finance::Booking::Revenue::BookCommand).as_stubbed_const }
    let!(:receipt_create_command_class) { class_spy(Finance::Receipt::CreateCommand).as_stubbed_const }

    subject(:call!) { command.call! }

    context 'when order is paid and registration is confirmed' do
      before do
        order_item.order.update!(status: :paid)
        order_item.registration.update!(status: :confirmed)
      end

      it 'calls the revenue booking command' do
        call!

        expect(revenue_book_command_class).to have_received(:call!).with(
          ecom_order_item: order_item,
        )
      end

      it 'calls the receipt creation command' do
        call!

        expect(receipt_create_command_class).to have_received(:call!).with(
          ecom_order_item: order_item,
          payments_transaction:,
        )
      end

      it 'calls both commands in sequence' do
        call!

        expect(revenue_book_command_class).to have_received(:call!).with(
          ecom_order_item: order_item,
        )
        expect(receipt_create_command_class).to have_received(:call!).with(
          ecom_order_item: order_item,
          payments_transaction:,
        )
      end
    end

    context 'when order is not paid' do
      before do
        order_item.order.update!(status: :cart)
        order_item.registration.update!(status: :confirmed)
      end

      it 'does not call the revenue booking command' do
        call!

        expect(revenue_book_command_class).not_to have_received(:call!)
      end

      it 'does not call the receipt creation command' do
        call!

        expect(receipt_create_command_class).not_to have_received(:call!)
      end
    end

    context 'when registration is not confirmed' do
      before do
        order_item.order.update!(status: :paid)
        order_item.registration.update!(status: :created)
      end

      it 'does not call the revenue booking command' do
        call!

        expect(revenue_book_command_class).not_to have_received(:call!)
      end

      it 'does not call the receipt creation command' do
        call!

        expect(receipt_create_command_class).not_to have_received(:call!)
      end
    end

    context 'when registration is cancelled' do
      before do
        order_item.order.update!(status: :paid)
        order_item.registration.update!(status: :cancelled)
      end

      it 'does not call the revenue booking command' do
        call!

        expect(revenue_book_command_class).not_to have_received(:call!)
      end

      it 'does not call the receipt creation command' do
        call!

        expect(receipt_create_command_class).not_to have_received(:call!)
      end
    end

    context 'when registration is nil' do
      before do
        order_item.order.update!(status: :paid)
        order_item.update!(registration: nil)
      end

      it 'raises an error when trying to access registration' do
        expect { call! }.to raise_error(NoMethodError)
      end
    end

    context 'when order is fulfilled' do
      before do
        order_item.order.update!(status: :paid)
        order_item.registration.update!(status: :confirmed)
      end

      it 'calls the revenue booking command' do
        call!

        expect(revenue_book_command_class).to have_received(:call!).with(
          ecom_order_item: order_item,
        )
      end

      it 'calls the receipt creation command' do
        call!

        expect(receipt_create_command_class).to have_received(:call!).with(
          ecom_order_item: order_item,
          payments_transaction:,
        )
      end
    end
  end
end
