# frozen_string_literal: true

require 'rails_helper'

class Alert
  class OpenCommand
    describe InactiveReview do
      let!(:admin_user) { create(:admin_user, email: described_class::DEFAULT_OWNER_EMAIL) }
      let(:review) { create(:lms_submission_review, state: :submitted, updated_at: 5.hours.ago) }

      subject(:command) { described_class.new(review:) }

      describe '#call!' do
        it 'calls OpenCommand with correct parameters' do
          allow(OpenCommand).to receive(:call!)

          command.call!

          expect(OpenCommand).to have_received(:call!).with(
            key: command.send(:key),
            title: command.send(:title),
            description: command.send(:description),
            resource: review,
            owner: admin_user,
          )
        end
      end

      describe '#key' do
        it 'returns the correct key' do
          expect(command.send(:key)).to eq("inactive_review_#{review.id}")
        end
      end

      describe '#title' do
        it 'returns the correct title' do
          expect(command.send(:title)).to eq("Inactive Submission Review Alert: #{review.uid}")
        end
      end

      describe '#description' do
        it 'includes review details and timing information' do
          description = command.send(:description)

          expect(description).to include("Review #{command.send(:admin_review_link)}")
          expect(description).to include("has been in #{review.state_name} state")
          expect(description).to include("for over #{command.send(:format_duration)}")
          expect(description).to include("Last updated: #{review.updated_at.to_fs(:long)}")
          expect(description).to include("Time inactive: #{command.send(:time_inactive)}")
        end
      end

      describe '#admin_review_link' do
        it 'returns the admin URL for the review' do
          expected_url = UrlBuilder::Admin.admin_lms_submission_review_url(review)
          expect(command.send(:admin_review_link)).to eq(expected_url)
        end
      end

      describe '#format_duration' do
        it 'returns the formatted threshold duration' do
          expected_duration = ActiveSupport::Duration.build(described_class::INACTIVE_DURATION_THRESHOLD.to_i).inspect
          expect(command.send(:format_duration)).to eq(expected_duration)
        end
      end

      describe '#time_inactive' do
        it 'returns the formatted time since last update' do
          freeze_time do
            duration = Time.current - review.updated_at
            expected_duration = ActiveSupport::Duration.build(duration.to_i).inspect
            expect(command.send(:time_inactive)).to eq(expected_duration)
          end
        end
      end

      describe '#owner' do
        it 'returns the admin user with the default email' do
          expect(command.send(:owner)).to eq(admin_user)
        end
      end
    end
  end
end
