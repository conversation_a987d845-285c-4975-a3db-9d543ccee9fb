# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ZendeskClient do
  let(:client) { described_class.new }

  describe 'initialization' do
    it 'inherits from HttpClient' do
      expect(client).to be_a(HttpClient)
    end

    it 'defines custom error class' do
      expect(described_class::Error).to be < HttpClient::Error
    end
  end

  describe 'configuration' do
    it 'uses correct origin from remote_resources config' do
      expect(client.send(:origin)).to eq(Rails.application.config.remote_resources[:zendesk][:api_origin])
    end

    it 'uses correct base path' do
      expect(client.send(:base_path)).to eq('/api/v2')
    end

    it 'implements api_key method' do
      expect(client.send(:api_key)).to be_present
    end
  end

  describe 'headers' do
    let(:headers) { client.send(:headers) }

    it 'includes required headers' do
      expect(headers).to include(
        'Accept' => 'application/json',
        'Content-Type' => 'application/json',
      )
    end

    it 'includes Basic authentication header' do
      expect(headers['Authorization']).to start_with('Basic ')
    end

    it 'uses correct email and token format' do
      # Mock the environment variable to match what the implementation expects
      allow(ENV).to receive(:fetch).with("ZENDESK_AUTH_EMAIL", nil).and_return('<EMAIL>')
      email = '<EMAIL>'
      token = Rails.application.credentials.ZENDESK_API_KEY
      expected_auth_string = Base64.strict_encode64("#{email}/token:#{token}")

      expect(headers['Authorization']).to eq("Basic #{expected_auth_string}")
    end
  end

  describe 'API requests', :vcr do
    context 'with valid credentials' do
      it 'can make GET requests to articles endpoint', vcr: 'api_clients/zendesk_client/can_make_get_requests_to_articles_endpoint' do
        response = client.get('/help_center/articles', params: { per_page: 1 })

        expect(response).to be_a(Hash)
        expect(response).to have_key('articles')
      end

      it 'can search articles', vcr: 'api_clients/zendesk_client/can_search_articles' do
        response = client.get('/help_center/articles/search', params: { query: 'test', per_page: 1 })

        expect(response).to be_a(Hash)
        expect(response).to have_key('results')
      end
    end

    context 'with invalid endpoint' do
      it 'raises ZendeskClient::Error for non-existent endpoints', vcr: 'api_clients/zendesk_client/raises_error_for_non_existent_endpoints' do
        expect do
          client.get('/help_center/non-existent-endpoint')
        end.to raise_error(described_class::Error)
      end
    end
  end

  describe 'error handling' do
    context 'when API is unavailable' do
      before do
        # Mock the error properly by creating a mock response with embedded request
        mock_request = instance_double(HTTPX::Request, uri: URI('https://example.com/test'), query: nil)
        mock_response = instance_double(HTTPX::Response, status: 500, body: 'Server Error')
        allow(mock_response).to receive(:instance_variable_get).with(:@request).and_return(mock_request)
        allow(mock_response).to receive(:try).with(:body).and_return('Server Error')

        allow(client).to receive(:make_request).and_raise(
          described_class::Error.new("Connection failed", response: mock_response),
        )
      end

      it 'raises ZendeskClient::Error' do
        expect do
          client.get('/articles')
        end.to raise_error(described_class::Error, /Connection failed/)
      end
    end

    context 'when credentials are invalid' do
      before do
        # Mock invalid credentials response with embedded request
        mock_request = instance_double(HTTPX::Request, uri: URI('https://example.com/test'), query: nil)
        mock_response = instance_double(HTTPX::Response, status: 401, body: 'Unauthorized')
        allow(mock_response).to receive(:instance_variable_get).with(:@request).and_return(mock_request)
        allow(mock_response).to receive(:try).with(:body).and_return('Unauthorized')

        allow(client).to receive(:make_request).and_raise(
          described_class::Error.new("Unauthorized", response: mock_response),
        )
      end

      it 'raises ZendeskClient::Error for authentication failures' do
        expect do
          client.get('/articles')
        end.to raise_error(described_class::Error, /Unauthorized/)
      end
    end
  end

  describe '#paginated_get' do
    context 'when there are multiple pages', :vcr do
      it 'accumulates results from all pages', vcr: 'api_clients/zendesk_client/paginated_get_multiple_pages_accumulates_results' do
        # Use /help_center/articles endpoint instead of search to ensure multiple results
        results = client.paginated_get(
          '/help_center/articles',
          params: { per_page: 1 },
        )

        expect(results).to be_an(Array)
        expect(results.length).to eq(3) # Based on our VCR cassette with 3 pages
        expect(results.first).to have_key('id')
        expect(results.first).to have_key('title')

        # Verify all articles from all pages are included (based on the VCR cassette)
        expect(results.pluck('id')).to eq([39431299473691, 39261851772443, 39259121494171])
      end

      it 'handles pagination correctly', vcr: 'api_clients/zendesk_client/paginated_get_multiple_pages_handles_pagination' do
        # This test uses the search endpoint that returns results in 'results' key
        results = client.paginated_get(
          '/help_center/articles/search',
          params: { query: 'test', per_page: 1 },
        )

        expect(results).to be_an(Array)
        # Based on the VCR cassette, this search returns 1 result with no pagination
        # This tests the 'results' key handling even though it's single page
        expect(results.length).to eq(1)
        expect(results.first).to have_key('id')
        expect(results.first).to have_key('title')
      end
    end

    context 'when there is only one page', :vcr do
      it 'returns all results from single page', vcr: 'api_clients/zendesk_client/paginated_get_single_page_returns_results' do
        results = client.paginated_get(
          '/help_center/articles/search',
          params: { query: 'test pagination single page', per_page: 50 },
        )

        expect(results).to be_an(Array)
        # This specific query should return few enough results to fit on one page
        expect(results.length).to be <= 50
        expect(results).to all(have_key('id').and(have_key('title')))
      end
    end

    context 'when response has no results', :vcr do
      it 'returns empty array', vcr: 'api_clients/zendesk_client/paginated_get_empty_results' do
        results = client.paginated_get(
          '/help_center/articles/search',
          params: { query: 'nonexistent query that should return no results xyz123' },
        )
        expect(results).to eq([])
      end
    end

    context 'when response has no results key' do
      # This test case is harder to create with real API calls since ZenDesk API
      # consistently returns a 'results' key. We'll keep this as a unit test
      # but simplify it to just test the method behavior without external calls.
      let(:no_results_key_response) do
        {
          'count' => 0,
          'next_page' => nil,
          'page' => 1,
          'page_count' => 1,
          'per_page' => 50,
          'previous_page' => nil,
        }
      end

      before do
        allow(client).to receive(:get).and_return(no_results_key_response)
      end

      it 'handles missing results key gracefully' do
        results = client.paginated_get('/help_center/articles/search')
        expect(results).to eq([])
      end
    end

    context 'when a block is provided', :vcr do
      it 'yields the response to the block', vcr: 'api_clients/zendesk_client/paginated_get_yields_response_to_block' do
        yielded_responses = []

        client.paginated_get(
          '/help_center/articles/search',
          params: { query: 'test', per_page: 1 },
        ) do |response|
          yielded_responses << response
        end

        expect(yielded_responses).not_to be_empty
        expect(yielded_responses).to all(respond_to(:json))
      end
    end

    context 'with custom headers and params', :vcr do
      it 'accepts custom parameters', vcr: 'api_clients/zendesk_client/paginated_get_with_custom_params' do
        custom_params = { query: 'test custom params', per_page: 2 }

        results = client.paginated_get(
          '/help_center/articles/search',
          params: custom_params,
        )

        expect(results).to be_an(Array)
        # The results should reflect our custom search query
      end
    end
  end

  describe 'integration with Rails config' do
    it 'uses zendesk configuration from remote_resources.yml' do
      expect(Rails.application.config.remote_resources[:zendesk]).to include(
        api_origin: 'https://ziplineseducation.zendesk.com',
      )
    end

    it 'accesses API key from Rails credentials' do
      expect(Rails.application.credentials.ZENDESK_API_KEY).to be_present
    end
  end
end
