# frozen_string_literal: true

require 'rails_helper'

module Remote
  module <PERSON><PERSON><PERSON>
    class Deal
      RSpec.describe EnrollmentPresenter do
        around do |example|
          Time.use_zone(Deal::CRM_TIME_ZONE) { example.run }
        end

        let(:ecom_payment_method_kind) { 'upfront' }
        let(:scenario_args) { {} }
        let(:scenario) { EnrolledLearnerScenario.create(ecom_payment_method_kind:, **scenario_args) }
        let(:enrollment) { scenario.enrollment }
        let(:charge) { scenario.payments_transaction }
        let(:ecom_payment) { enrollment.ecom_order.payment }
        let(:presenter) { described_class.new(enrollment:) }

        describe '#properties' do
          let(:properties) { presenter.properties }

          it 'returns a fixed list of properties' do
            expect(properties).to include(
              lms_learner_access_reinstatement_date: '',
              partner_brand_fee__: presenter.send(:finance_contract).fee_rate,
              became_paid_enrollee: presenter.send(:became_paid_enrollee_at).to_date,
              amount: anything,
              small_business_target: false,
              cc_processing_fee: anything,
              enrollee_type: 'stand',
              lms_type: 'student',
              learner_payment_type: 'Full Payment',
              learner_payment_process: 'greenfig_online',
              learner_status: '',
              extended_until: anything,
              financial_revenue_reverse: anything,
              financial_cash_reversal: anything,
              refund____: anything,
              payment_refund_reason: anything,
              learner_payment_collected: anything,
              bookings____: anything,
              finance_relationship: 'Standard',
              third_party_entity: nil,
              drop_withdraw_reason: anything,
              learner_drop_withdraw_date: anything,
              lms_learner_access_restriction_date: anything,
              certificate_update_date: anything,
              certificate_url: anything,
              is_transfer: anything,
              learner_transfer_original_cohort: anything,
              learner_payment_: 2250.0,
              payment_overdue_since_date: anything,
            )
          end

          context 'deal is closed won by CLAS' do
            before do
              enrollment.update!(clas_cohort_admission_key: 1)
            end

            it 'returns nil for specific properties' do
              expect(properties).to include(
                lms_learner_access_reinstatement_date: '',
                partner_brand_fee__: nil,
                became_paid_enrollee: presenter.send(:became_paid_enrollee_at).to_date,
                amount: anything,
                small_business_target: false,
                cc_processing_fee: nil,
                enrollee_type: nil,
                lms_type: 'student',
                learner_payment_type: nil,
                learner_payment_process: nil,
                learner_status: '',
                extended_until: anything,
                financial_revenue_reverse: anything,
                financial_cash_reversal: anything,
                refund____: anything,
                payment_refund_reason: anything,
                learner_payment_collected: anything,
                bookings____: nil,
                drop_withdraw_reason: anything,
                learner_drop_withdraw_date: anything,
                lms_learner_access_restriction_date: anything,
                certificate_update_date: anything,
                certificate_url: anything,
                is_transfer: anything,
                learner_transfer_original_cohort: anything,
                learner_payment_: nil,
                payment_overdue_since_date: anything,
                used_promo_code: nil,
              )
            end
          end

          context 'with installments' do
            let(:ecom_payment_method_kind) { 'installments' }

            it 'returns course_price of upfront_price' do
              expect(presenter.properties[:learner_payment_]).to eq(2250.0)
            end
          end

          context 'with installments with eva' do
            let(:ecom_payment_method_kind) { 'installments' }
            let(:discount) { create(:promos_discount, :percent_off, rate_off: 0.1) }
            let(:discount_code) { create(:promos_discount_code, discount:) }
            let(:scenario_args) do
              {
                ecom_order_args: {
                  promos_discount_applications: [
                    build(:promos_discount_application, discount_code:),
                  ],
                },
              }
            end

            it 'returns course_price of upfront_price - discounts' do
              expect(presenter.properties[:learner_payment_]).to eq(2025.0)
            end
          end

          context 'transfer properties' do
            context 'when no limited transfers exist for the registration' do
              it 'sets is_transfer to false' do
                expect(properties[:is_transfer]).to be(false)
              end
            end

            context 'when limited transfers exist for the registration' do
              let(:original_cohort) { instance_double(Cohort, starts_on: Date.new(2025, 1, 1)) }
              let(:original_enrollment) { instance_double(Enrollment, cohort: original_cohort) }
              let(:limited_transfer) { instance_double(Enrollment::Transfer, limited: true, transferred_from: original_enrollment) }

              before do
                # Directly mock the first_limited_transfer method
                allow(presenter).to receive(:first_limited_transfer).and_return(limited_transfer)
              end

              it 'sets is_transfer to true' do
                expect(properties[:is_transfer]).to be(true)
              end

              it 'sets learner_transfer_original_cohort to the original cohort month and year' do
                expect(properties[:learner_transfer_original_cohort]).to eq('january_2025')
              end
            end
          end

          context 'when promo codes are present' do
            let(:discount_one) { create(:promos_discount, :percent_off, rate_off: 0.1) }
            let(:discount_two) { create(:promos_discount, :percent_off, rate_off: 0.2) }
            let(:discount_code_one) { create(:promos_discount_code, discount: discount_one, code: 'SUMMER21', humanized_code: 'SUMMER-21') }
            let(:discount_code_two) { create(:promos_discount_code, discount: discount_two, code: 'FALL22', humanized_code: 'FALL-22') }
            let(:scenario_args) do
              {
                ecom_order_args: {
                  promos_discount_applications: [
                    build(:promos_discount_application, discount_code: discount_code_one),
                    build(:promos_discount_application, discount_code: discount_code_two),
                  ],
                },
              }
            end

            it 'returns a string of humanized promo codes joined by semicolon' do
              expect(presenter.properties[:used_promo_code]).to eq('SUMMER-21; FALL-22')
            end
          end
        end

        describe '#bookings' do
          let(:bookings) { presenter.send(:bookings) }

          it 'returns payment total amount' do
            expect(bookings).to eq(presenter.payment.total_amount)
          end
        end

        describe '#became_paid_enrollee_at' do
          let(:became_paid_enrollee_at) { presenter.send(:became_paid_enrollee_at) }

          it 'returns the date' do
            expect(became_paid_enrollee_at).to eq(enrollment.deal.closed_at)
          end
        end

        describe '#reversal_month_range' do
          let(:reversal_month_range) { presenter.send(:reversal_month_range) }

          it 'returns the month range' do
            expect(reversal_month_range).to cover(presenter.send(:became_paid_enrollee_at))
          end
        end

        describe '#non_reversal_period' do
          let(:non_reversal_period) { presenter.send(:non_reversal_period) }

          it 'returns the month range' do
            expect(non_reversal_period).not_to cover(presenter.send(:became_paid_enrollee_at))
            expect(non_reversal_period.first).to be_after(presenter.send(:became_paid_enrollee_at))
          end
        end

        describe '#cash_reversal' do
          let!(:refund) { create(:payments_transaction, type: 'Payments::Transaction::Refund', ecom_payment:, amount_cents: -2345) }
          let(:cash_reversal) { presenter.send(:cash_reversal) }

          it 'returns the refunded amount' do
            expect(cash_reversal).to eq(-refund.amount_amount)
            expect(cash_reversal).to be_positive
            expect(presenter.send(:cash_non_reversal)).to eq(0)
          end
        end

        describe '#cash_non_reversal' do
          let!(:refund) do
            transacted_at = created_at = Time.use_zone(CRM_TIME_ZONE) { 1.month.after }
            create(:payments_transaction, type: 'Payments::Transaction::Refund', ecom_payment:, amount_cents: -2345, created_at:, transacted_at:)
          end
          let(:cash_non_reversal) { presenter.send(:cash_non_reversal) }

          it 'returns the refunded amount' do
            expect(cash_non_reversal).to eq(-refund.amount_amount)
            expect(cash_non_reversal).to be_positive
            expect(presenter.send(:cash_reversal)).to eq(0)
          end
        end

        describe '#bookings_reversal' do
          let(:reason) { 'drop_unenroll_withdrawal' }

          let(:bookings_reversal) { presenter.send(:bookings_reversal) }

          context 'with a 100% refund in the same month as enrollment' do
            let!(:refund) do
              create(:payments_transaction, type: 'Payments::Transaction::Refund', ecom_payment:, amount_cents: charge.amount_cents * -1)
            end
            let!(:ecom_refund) do
              refund.create_ecom_refund!(
                reason: :drop_unenroll_withdrawal,
                refund_cents: -refund.amount_cents,
                payments_transaction_charge: charge,
                payment: ecom_payment,
              )
            end

            it 'reverses the entire bookings' do
              expect(bookings_reversal).to eq(presenter.send(:bookings))
              expect(bookings_reversal).to be_positive
            end
          end

          context 'with a post transaction adjustment in the same month as enrollment' do
            let!(:refund) do
              create(:payments_transaction, type: 'Payments::Transaction::Refund', ecom_payment:, amount_cents: -1000)
            end
            let!(:ecom_refund) do
              refund.create_ecom_refund!(
                reason: :post_transaction_adjustment,
                refund_cents: -refund.amount_cents,
                payments_transaction_charge: charge,
                payment: ecom_payment,
              )
            end

            it 'reverses the bookings partially' do
              expect(bookings_reversal).not_to eq(presenter.send(:bookings))
              expect(bookings_reversal).to be_positive
              expect(bookings_reversal).to eq(presenter.send(:cash_reversal))
            end
          end
        end

        describe '#enrollee_type' do
          let(:enrollee_type) { presenter.send(:enrollee_type) }

          context 'with a reenrolled registration' do
            let(:registration) { create(:registration, reenrolled_from: create(:enrollment)) }

            before do
              allow(enrollment).to receive(:registration).and_return(registration)
            end

            it 'returns the reenrolled type' do
              expect(enrollee_type).to eq('re-enroll')
            end
          end

          context 'with a non reenrolled registration' do
            it 'returns the standard type' do
              expect(enrollee_type).to eq('stand')
            end
          end
        end

        describe '#payment_overdue_since_date' do
          context 'when payment method is not installments' do
            let(:ecom_payment_method_kind) { 'upfront' }

            it 'returns an empty string' do
              expect(presenter.send(:payment_overdue_since_date)).to eq("")
            end
          end

          context 'when payment method is installments' do
            let(:ecom_payment_method_kind) { 'installments' }

            context 'when there is no past due installments' do
              let!(:installment_plan) do
                create(:payments_installment_plan, status: :active, ecom_payment: presenter.payment)
              end

              it 'returns an empty string' do
                expect(presenter.send(:payment_overdue_since_date)).to eq("")
              end
            end

            context 'when installment plan is past due' do
              let(:installment_plan) do
                create(:payments_installment_plan, status: :past_due, ecom_payment: presenter.payment)
              end
              let!(:installment) do
                create(:payments_installment, status: :active, due_at: 1.day.ago, installment_plan:, transaction_charge: charge)
              end

              it 'returns the installment due date in Pacific Time' do
                expect(presenter.send(:payment_overdue_since_date)).to eq(installment.due_at.in_time_zone('Pacific Time (US & Canada)').to_date)
              end
            end

            context 'when installment plan is unpaid' do
              let(:installment_plan) do
                create(:payments_installment_plan, status: :unpaid, ecom_payment: presenter.payment)
              end
              let!(:installment) do
                create(:payments_installment, status: :active, due_at: 1.day.ago, installment_plan:, transaction_charge: charge)
              end

              it 'returns the installment due date in Pacific Time' do
                expect(presenter.send(:payment_overdue_since_date)).to eq(installment.due_at.in_time_zone('Pacific Time (US & Canada)').to_date)
              end
            end
          end
        end

        describe '#learner_drop_withdraw_date' do
          before do
            Timecop.travel(Time.utc(2025, 1, 25, 6, 0, 0))
          end

          after do
            Timecop.return
          end

          let(:learner_drop_withdraw_date) { presenter.send(:learner_drop_withdraw_date) }

          context 'when enrollment is not paused or exited' do
            let(:enrollment) { create(:enrollment, :active_status) }

            it 'returns an empty string' do
              expect(learner_drop_withdraw_date).to eq('')
            end
          end

          context 'when enrollment is paused' do
            let(:enrollment) { create(:enrollment, :paused_status) }

            before do
              allow(presenter).to receive(:latest_status_change_date).and_return(Time.zone.today)
            end

            it 'returns the latest status change date' do
              expect(learner_drop_withdraw_date).to eq(Time.zone.today)
            end
          end

          context 'when enrollment is exited' do
            let(:enrollment) { create(:enrollment, :withdrew_status, exit_requested_on: Time.zone.yesterday) }

            it 'returns the exit requested date' do
              expect(learner_drop_withdraw_date).to eq(Time.zone.yesterday)
            end
          end
        end

        describe '#deal_closed_won_by_clas?' do
          let(:deal_closed_won_by_clas) { presenter.send(:deal_closed_won_by_clas?) }

          context 'when deal is closed won by CLAS' do
            before do
              enrollment.update!(clas_cohort_admission_key: 1)
            end

            it 'returns true' do
              expect(deal_closed_won_by_clas).to be true
            end
          end

          context 'when deal is not closed won by CLAS' do
            it 'returns false' do
              expect(deal_closed_won_by_clas).to be false
            end
          end
        end

        describe '#cc_processing_fee' do
          subject(:cc_processing_fee) { presenter.send(:cc_processing_fee) }

          context 'when deal is closed won by CLAS' do
            before do
              enrollment.update!(clas_cohort_admission_key: 1)
            end

            it 'returns nil' do
              expect(cc_processing_fee).to be_nil
            end
          end

          context 'when payment method is offer_on_site' do
            before do
              allow(presenter.payment_method).to receive_messages(offer_on_site?: true, learner_manual?: false)
              allow(presenter.send(:finance_contract)).to receive(:processing_fee_carve_out_rate).and_return(0.03)
            end

            it 'returns the processing fee carve out rate' do
              expect(cc_processing_fee).to eq(0.03)
            end
          end

          context 'when payment method is learner_manual' do
            before do
              allow(presenter.payment_method).to receive_messages(offer_on_site?: false, learner_manual?: true)
              allow(presenter.send(:finance_contract)).to receive(:processing_fee_carve_out_rate).and_return(0.025)
            end

            it 'returns the processing fee carve out rate' do
              expect(cc_processing_fee).to eq(0.025)
            end
          end

          context 'when payment method is neither offer_on_site nor learner_manual' do
            before do
              allow(presenter.payment_method).to receive_messages(offer_on_site?: false, learner_manual?: false)
            end

            it 'returns 0' do
              expect(cc_processing_fee).to eq(0)
            end
          end
        end

        describe '#learner_activation_status' do
          context 'when there is a high-risk not-activated assessment' do
            let!(:risk_assessment) { create(:learning_delivery_risk_assessment, risk_type: 'not_activated', risk_level: 'high_risk', enrollment:) }

            it 'returns not_activated' do
              expect(presenter.send(:learner_activation_status)).to eq('not_activated')
            end
          end

          context 'when there is an on-track assessment' do
            let!(:risk_assessment) { create(:learning_delivery_risk_assessment, risk_type: 'not_activated', risk_level: 'on_track', enrollment:) }

            it 'returns activated' do
              expect(presenter.send(:learner_activation_status)).to eq('activated')
            end
          end

          context 'when there is no not-activated risk assessment' do
            it 'returns nil' do
              expect(presenter.send(:learner_activation_status)).to be_nil
            end
          end
        end
      end
    end
  end
end
