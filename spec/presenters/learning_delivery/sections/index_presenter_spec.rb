# frozen_string_literal: true

require 'rails_helper'

module LearningDelivery
  module Sections
    RSpec.describe IndexPresenter, type: :presenter do
      let(:admin_user) { create(:admin_user) }
      let(:params) { {} }

      let!(:cohort_1) { create(:cohort, starts_on: Date.current - 2, ends_on: Date.current - 1) }
      let!(:cohort_2) { create(:cohort, status: "started", starts_on: Date.current) }
      let!(:cohort_3) { create(:cohort, starts_on: Date.current + 1) }

      let!(:section_1) { create(:section, cohort_id: cohort_1.id) }
      let!(:section_2) { create(:section, cohort_id: cohort_2.id) }
      let!(:section_3) { create(:section, cohort_id: cohort_3.id) }

      describe '#section_item_presenters' do
        context 'when admin user is an employee' do
          let(:employee) { create(:learning_delivery_employee, personal_email: admin_user.email, admin_user:) }
          subject(:presenter) { described_class.new(employee:, params:) }

          before do
            [section_1, section_2, section_3].each do |section|
              section.update!(advocate_id: employee.id, grader_id: employee.id, instructor_id: employee.id)
            end
          end

          context 'when all section filter is selected' do
            it 'returns only past sections' do
              expect(presenter.section_item_presenters.map(&:section)).to include(section_1, section_2, section_3)
            end
          end

          context 'when past section filter is selected' do
            let(:params) { { section_filter: 'past' } }

            it 'returns only past sections' do
              expect(presenter.section_item_presenters.map(&:section)).to contain_exactly(section_1)
            end
          end

          context 'when active section filter is selected' do
            let(:params) { { section_filter: 'active' } }

            it 'returns active sections only' do
              expect(presenter.section_item_presenters.map(&:section)).to contain_exactly(section_2)
            end
          end

          context 'when upcoming section filter is selected' do
            let(:params) { { section_filter: 'upcoming' } }

            it 'returns upcoming sections only' do
              expect(presenter.section_item_presenters.map(&:section)).to contain_exactly(section_3)
            end
          end
        end

        context "When admin user is not employee" do
          let(:employee) { nil }
          subject(:presenter) { described_class.new(employee:, params:) }

          context 'when all section filter is selected' do
            it 'returns only past sections' do
              expect(presenter.section_item_presenters.map(&:section)).to include(section_1, section_2, section_3)
            end
          end

          context 'when past section filter is selected' do
            let(:params) { { section_filter: 'past' } }

            it 'returns only past sections' do
              expect(presenter.section_item_presenters.map(&:section)).to contain_exactly(section_1)
            end
          end

          context 'when active section filter is selected' do
            let(:params) { { section_filter: 'active' } }

            it 'returns active sections only' do
              expect(presenter.section_item_presenters.map(&:section)).to contain_exactly(section_2)
            end
          end

          context 'when upcoming section filter is selected' do
            let(:params) { { section_filter: 'upcoming' } }

            it 'returns upcoming sections only' do
              expect(presenter.section_item_presenters.map(&:section)).to contain_exactly(section_3)
            end
          end
        end
      end
    end
  end
end
