# frozen_string_literal: true

require 'rails_helper'

module LearningDelivery
  class Task
    describe ResolveDuplicateEnrollmentPresenter do
      let(:enrollment) { create(:enrollment) }
      let(:duplicate_enrollment) { create(:enrollment, learner: enrollment.learner, section_args: { cohort: enrollment.cohort }) }
      let!(:task_template) do
        LearningDelivery::TaskTemplate.find_or_initialize_by(task_type: :resolve_duplicate_enrollment, sub_type: nil).tap do |template|
          template.title = 'Resolve Duplicate Enrollment: {LEARNER_NAME}'
          template.description = 'Learner {LEARNER_NAME} ({LEARNER_EMAIL}) has a duplicate enrollment for cohort {COHORT_NAME}.'
          template.recommendation = 'Please review both enrollments. <PERSON><PERSON> may have wanted to enroll ' \
                                    'another person. Contact the learner to resolve the issue.'
          template.reason = 'Duplicate enrollment detected during checkout process.'
          template.save!
        end
      end

      subject(:presenter) do
        described_class.new(
          resource: enrollment,
          duplicate_enrollment:,
        )
      end

      describe '#placeholder_values' do
        it 'returns a hash with the correct placeholder keys' do
          expect(presenter.placeholder_values.keys).to include(*ResolveDuplicateEnrollmentPresenter::VARIABLES)
        end
      end

      describe '#task_template' do
        it 'returns the correct task template' do
          expect(presenter.task_template).to eq(task_template)
        end
      end

      describe '#title' do
        it 'returns the expected title' do
          expect(task_template.title).to eq('Resolve Duplicate Enrollment: {LEARNER_NAME}')
          expect(presenter.title).to eq("Resolve Duplicate Enrollment: #{enrollment.learner.full_name}")
        end
      end

      describe '#description' do
        it 'returns the expected description' do
          learner_name = enrollment.learner.full_name
          learner_email = enrollment.learner.primary_email_address
          cohort_name = enrollment.cohort.name
          expected = "Learner #{learner_name} (#{learner_email}) has a duplicate enrollment for cohort #{cohort_name}."
          expect(presenter.description).to eq(expected)
        end
      end

      describe '#recommendation' do
        it 'returns the expected recommendation' do
          expected = 'Please review both enrollments. Learner may have wanted to enroll another person. Contact the learner to resolve the issue.'
          expect(presenter.recommendation).to eq(expected)
        end
      end

      describe '#reason' do
        it 'returns the expected reason' do
          expect(presenter.reason).to eq('Duplicate enrollment detected during checkout process.')
        end
      end

      describe '#due_at' do
        it 'returns a date 2 days from now' do
          allow(Time).to receive(:current).and_return(Time.zone.parse('2025-05-05 12:00:00'))
          expect(presenter.due_at).to be_within(1.second).of(Time.zone.parse('2025-05-07 12:00:00'))
        end
      end

      describe '#learner_name' do
        it 'returns the learner full name' do
          expect(presenter.learner_name).to eq(enrollment.learner.full_name)
        end
      end

      describe '#learner_email' do
        it 'returns the learner primary email address' do
          expect(presenter.learner_email).to eq(enrollment.learner.primary_email_address)
        end
      end

      describe '#cohort_name' do
        it 'returns the cohort name' do
          expect(presenter.cohort_name).to eq(enrollment.cohort.name)
        end
      end

      describe '#duplicate_enrollment_id' do
        it 'returns the duplicate enrollment uid' do
          expect(presenter.duplicate_enrollment_id).to eq(duplicate_enrollment.uid)
        end
      end
    end
  end
end
