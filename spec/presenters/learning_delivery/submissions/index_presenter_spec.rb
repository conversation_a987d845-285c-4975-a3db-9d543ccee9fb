# frozen_string_literal: true

require 'rails_helper'

module LearningDelivery
  module Submissions
    RSpec.describe IndexPresenter, type: :presenter do
      subject(:presenter) { described_class.new(params:) }

      let(:params) { {} }
      let!(:submission_one) { create(:lms_submission, :submitted) }
      let!(:submission_two) { create(:lms_submission, :submitted) }
      let!(:review_one) { create(:lms_submission_review, :manual_review_needed, submission: submission_one) }
      let!(:review_two) { create(:lms_submission_review, :manual_review_needed, submission: submission_two) }

      describe '#line_item_presenters' do
        it 'returns an array of LineItemPresenters' do
          expect(presenter.line_item_presenters).to all(be_a(IndexPresenter::LineItemPresenter))
        end

        it 'includes presenters for all submissions with reviews' do
          expect(presenter.line_item_presenters.map(&:submission)).to include(submission_one, submission_two)
        end

        it 'passes the correct params to each LineItemPresenter' do
          line_item_presenters = presenter.line_item_presenters
          expect(line_item_presenters.first.params).to eq(params)
          expect(line_item_presenters.last.params).to eq(params)
        end
      end

      describe '#submissions' do
        context 'with section_id filter' do
          let(:section) { create(:section) }
          let!(:submission_with_section) { create(:lms_submission, :submitted, section:) }
          let!(:review_with_section) { create(:lms_submission_review, :manual_review_needed, submission: submission_with_section) }
          let(:params) { { section_id: section.id } }

          it 'filters submissions by section_id' do
            expect(presenter.send(:submissions)).to eq([submission_with_section])
          end
        end

        context 'with assignment_template_id filter' do
          let(:assignment_template) { create(:lms_assignment_template) }
          let(:assignment) { create(:lms_assignment, assignment_template:) }
          let(:enrollment) { create(:enrollment) }
          let!(:submission_with_assignment) { create(:lms_submission, :submitted, assignment:, enrollment:) }
          let!(:review_with_assignment) { create(:lms_submission_review, :manual_review_needed, submission: submission_with_assignment) }
          let(:params) { { assignment_template_id: assignment_template.id } }

          it 'filters submissions by assignment_template_id' do
            expect(presenter.send(:submissions)).to eq([submission_with_assignment])
          end
        end

        context 'with current_employee as grader' do
          let(:grader) { create(:learning_delivery_employee_grader) }
          let(:section) { create(:section, grader:) }
          let!(:submission_in_grader_section) { create(:lms_submission, :submitted, section:) }
          let!(:review_in_grader_section) { create(:lms_submission_review, :manual_review_needed, submission: submission_in_grader_section) }
          subject(:presenter) { described_class.new(params: {}, current_employee: grader) }

          it 'filters submissions to only those in grader sections' do
            expect(presenter.send(:submissions)).to include(submission_in_grader_section)
            expect(presenter.send(:submissions)).not_to include(submission_two)
          end

          it 'filters submissions with current_review that is not in training' do
            submission_in_grader_section.current_review.update(training: true)
            expect(presenter.send(:submissions)).not_to include(submission_in_grader_section)
          end
        end

        context 'with filter: new_comments' do
          before do
            allow(Lms::Submission::Comment).to receive(:unread_from_learner).and_return(
              instance_double(ActiveRecord::Relation, pluck: [submission_two.id]),
            )
          end

          let(:params) { { filter: 'new_comments' } }

          it 'filters submissions to only those with new comments' do
            expect(presenter.send(:submissions)).to eq([submission_two])
          end
        end
      end

      describe '#submissions_list' do
        it 'returns the list of submission IDs' do
          expect(presenter.submissions_list).to contain_exactly(submission_one.id, submission_two.id)
        end
      end

      describe '#filters_presenter' do
        it 'returns a FiltersPresenter' do
          expect(presenter.filters_presenter).to be_a(IndexPresenter::FiltersPresenter)
        end

        it 'passes the selected filter to the presenter' do
          params[:filter] = 'all'
          expect(presenter.filters_presenter.filter_type).to eq('all')
        end

        it 'passes all relevant arguments to the presenter' do
          params[:section_id] = 123
          params[:assignment_template_id] = 456
          params[:filter] = 'needs_review'
          fake_employee = instance_double(LearningDelivery::Employee, grader_sections: instance_double(ActiveRecord::Relation, pluck: []))
          presenter_with_employee = described_class.new(params:, current_employee: fake_employee)
          filters_presenter = presenter_with_employee.filters_presenter
          expect(filters_presenter.selected_section_id).to eq(123)
          expect(filters_presenter.selected_assignment_template_id).to eq(456)
          expect(filters_presenter.filter_type).to eq('needs_review')
          expect(filters_presenter.current_employee).to eq(fake_employee)
        end
      end
    end
  end
end
