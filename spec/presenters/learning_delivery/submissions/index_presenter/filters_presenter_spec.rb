# frozen_string_literal: true

require 'rails_helper'

module LearningDelivery
  module Submissions
    class IndexPresenter
      RSpec.describe FiltersPresenter, type: :presenter do
        let(:submissions) { Lms::Submission.none }
        let(:submission_ids_with_unread_comments) { [] }
        let(:selected_section_id) { nil }
        let(:selected_assignment_template_id) { nil }
        let(:filter_type) { nil }
        let(:current_employee) { nil }

        subject(:presenter) do
          described_class.new(
            submissions:,
            submission_ids_with_unread_comments:,
            selected_section_id:,
            selected_assignment_template_id:,
            filter_type:,
            current_employee:,
            selected_per_page: nil,
          )
        end

        describe '#review_status_options' do
          it 'returns an array of status options' do
            expect(presenter.review_status_options).to be_an(Array)
            expect(presenter.review_status_options).not_to be_empty
          end

          it 'includes all review states' do
            state_names = Lms::Submission::Review.state_alt_name_to_ids.keys
            state_ids = Lms::Submission::Review.state_alt_name_to_ids.values
            option_names = presenter.review_status_options.map(&:first)
            option_ids = presenter.review_status_options.map(&:last)
            expect(option_names).to match_array(state_names)
            expect(option_ids).to match_array(state_ids)
          end
        end

        describe '#sections and #section_options' do
          let!(:active_section) { create(:section, cohort: create(:cohort, :opened)) }
          let!(:inactive_section) { create(:section, cohort: create(:cohort, status: :inactive)) }
          let!(:grader) { create(:learning_delivery_employee_grader) }
          let!(:grader_section) { create(:section, grader:, cohort: create(:cohort, :opened)) }

          context 'when current_employee is a grader with grader sections' do
            let(:current_employee) { grader }
            it 'returns only the grader sections' do
              expect(presenter.send(:sections)).to match_array(grader.grader_sections.active)
            end

            it 'section_options maps section names and ids' do
              expect(presenter.section_options).to eq(grader.grader_sections.active.order(:name).map { |s| [s.program_cohort_name, s.id] })
            end
          end

          context 'when current_employee is a grader with no grader sections' do
            let(:current_employee) { grader_without_sections }
            let(:grader_without_sections) { create(:learning_delivery_employee_grader) }
            it 'returns no sections' do
              expect(presenter.send(:sections)).to be_empty
            end
          end

          context 'when current_employee is nil' do
            let(:current_employee) { nil }
            it 'returns all active sections' do
              expect(presenter.send(:sections)).to include(active_section, grader_section)
              expect(presenter.send(:sections)).not_to include(inactive_section)
            end
          end
        end

        describe '#needs_review_count and #new_comments_count' do
          let!(:submission_one) { create(:lms_submission) }
          let!(:submission_two) { create(:lms_submission) }
          let!(:submission_three) { create(:lms_submission) }

          let!(:review_needed) do
            create(:lms_submission_review, submission: submission_one, state: :manual_review_needed, attempt: submission_one.attempt)
          end
          let!(:review_not_needed) { create(:lms_submission_review, submission: submission_two, state: :graded, attempt: submission_two.attempt) }
          let!(:review_for_submission_three) do
            create(:lms_submission_review, submission: submission_three, state: :graded, attempt: submission_three.attempt)
          end

          let(:submissions) { Lms::Submission.joins(:reviews).where(id: [submission_one.id, submission_two.id, submission_three.id]) }
          let(:submission_ids_with_unread_comments) { [submission_two.id, submission_three.id] }

          it 'returns the correct needs_review_count' do
            expect(presenter.needs_review_count).to eq(1)
          end

          it 'returns the correct new_comments_count' do
            expect(presenter.new_comments_count).to eq(2)
          end
        end

        describe 'filter methods' do
          it 'filter_all? returns true when filter_type is all' do
            presenter = described_class.new(filter_type: 'all', submissions: Lms::Submission.none,
              submission_ids_with_unread_comments: [], selected_section_id: nil, selected_assignment_template_id: nil, selected_per_page: nil,
            )
            expect(presenter.filter_all?).to be true
          end

          it 'filter_needs_review? returns true when filter_type is nil' do
            presenter = described_class.new(filter_type: nil, submissions: Lms::Submission.none,
              submission_ids_with_unread_comments: [], selected_section_id: nil, selected_assignment_template_id: nil, selected_per_page: nil,
            )
            expect(presenter.filter_needs_review?).to be true
            expect(presenter.filter_all?).to be false
          end

          it 'filter_new_comments? returns true when filter_type is new_comments' do
            presenter = described_class.new(filter_type: 'new_comments', submissions: Lms::Submission.none,
              submission_ids_with_unread_comments: [], selected_section_id: nil, selected_assignment_template_id: nil, selected_per_page: nil,
            )
            expect(presenter.filter_new_comments?).to be true
          end

          it 'filter methods return needs_review for other filter_types' do
            presenter = described_class.new(filter_type: 'other', submissions: Lms::Submission.none,
              submission_ids_with_unread_comments: [], selected_section_id: nil, selected_assignment_template_id: nil, selected_per_page: nil,
            )
            expect(presenter.filter_all?).to be false
            expect(presenter.filter_needs_review?).to be true
            expect(presenter.filter_new_comments?).to be false
          end
        end

        describe '#assignment_templates and #assignment_template_options' do
          let!(:section) { create(:section, cohort: create(:cohort, :opened)) }
          let!(:module_template) { create(:lms_module_template, week_number: 1, title: 'Module 1') }
          let!(:assignment_template) { create(:lms_assignment_template, module_template:, name: 'Assignment 1') }
          let!(:assignment) { create(:lms_assignment, assignment_template:) }
          let!(:assignment_group) { create(:lms_assignment_group, :playbook_assignment) }
          let!(:section_assignment) { create(:lms_section_assignment, section:, assignment:) }

          before do
            assignment.update(assignment_group:)
          end

          context 'when a section is selected' do
            let(:selected_section_id) { section.id }

            it 'returns assignment templates for the selected section' do
              expect(presenter.send(:assignment_templates)).to include(assignment_template)
            end

            it 'assignment_template_options maps template full names and ids' do
              expect(presenter.assignment_template_options).to eq([[assignment_template.full_name, assignment_template.id]])
            end
          end

          context 'when no section is selected' do
            let(:selected_section_id) { nil }

            it 'returns assignment templates for all sections' do
              expect(presenter.send(:assignment_templates)).to include(assignment_template)
            end

            it 'assignment_template_options maps template full names and ids' do
              expect(presenter.assignment_template_options).to eq([[assignment_template.full_name, assignment_template.id]])
            end
          end

          context 'when there are multiple assignment templates' do
            let!(:module_template_2) { create(:lms_module_template, week_number: 2, title: 'Module 2') }
            let!(:assignment_template_2) { create(:lms_assignment_template, module_template: module_template_2, name: 'Assignment 2') }
            let!(:assignment_2) { create(:lms_assignment, assignment_template: assignment_template_2, assignment_group:) }
            let!(:section_assignment_2) { create(:lms_section_assignment, section:, assignment: assignment_2) }

            it 'returns assignment templates ordered by week number, module title, and assignment name' do
              templates = presenter.send(:assignment_templates).to_a
              expect(templates.first).to eq(assignment_template)
              expect(templates.second).to eq(assignment_template_2)
            end
          end
        end
      end
    end
  end
end
