# frozen_string_literal: true

require 'rails_helper'

module Site
  describe Support::ShowPresenter do
    let(:scenario) { LandingScenario.create }
    let(:partner_program) { scenario.partner_program }
    let(:presenter) { described_class.new(partner_program:) }

    describe '#initialize' do
      it 'initializes with partner_program' do
        expect(presenter.partner_program).to eq(partner_program)
      end

      it 'initializes with optional section_id' do
        presenter_with_section = described_class.new(partner_program:, section_id: 'test-section')
        expect(presenter_with_section.section_id).to eq('test-section')
      end

      it 'initializes with optional content_tags' do
        tags = %w[tag1 tag2]
        presenter_with_tags = described_class.new(partner_program:, content_tags: tags)
        expect(presenter_with_tags.content_tags).to eq(tags)
      end
    end

    describe '#canonical_url' do
      it 'returns the support page canonical URL' do
        expect(presenter.canonical_url).to be_a(String)
        expect(presenter.canonical_url).to include(partner_program.partner.slug)
      end
    end

    describe '#cache_key' do
      it 'returns cache key' do
        expect(presenter.cache_key).to be_a(String)
      end

      it 'returns the same cache key when cache key components are unchanged' do
        expect(Array.new(2) { described_class.new(partner_program:).cache_key('header') }.uniq.length).to eq(1)
      end

      it 'returns different cache keys when cache key components change' do
        expect { partner_program.reload.update!(updated_at: (partner_program.updated_at + 1.minute)) }
          .to(change { described_class.new(partner_program:).cache_key('header') })
      end
    end

    describe '#syllabus_url' do
      it 'returns the syllabus URL' do
        expect(presenter.syllabus_url).to be_a(String)
        expect(presenter.syllabus_url).to include(partner_program.partner.slug)
      end
    end

    describe '#enroll_url' do
      it 'returns the enrollment URL' do
        expect(presenter.enroll_url).to be_a(String)
        expect(presenter.enroll_url).to include(partner_program.partner.slug)
      end
    end

    describe '#contact_email' do
      it 'returns the support email' do
        expect(presenter.contact_email).to eq('<EMAIL>')
      end
    end

    describe '#support_options' do
      it 'returns array of support options' do
        options = presenter.support_options
        expect(options).to be_an(Array)
        expect(options.size).to eq(3)
      end

      it 'includes chat option' do
        chat_option = presenter.support_options.find { |opt| opt[:id] == 'chat' }
        expect(chat_option).to be_present
        expect(chat_option[:title]).to eq('Chat live')
        expect(chat_option[:controller]).to eq('hubspot-chat')
      end

      it 'includes meeting option' do
        meeting_option = presenter.support_options.find { |opt| opt[:id] == 'meeting' }
        expect(meeting_option).to be_present
        expect(meeting_option[:title]).to eq('Book a meeting')
        expect(meeting_option[:controller]).to eq('calendly')
      end

      it 'includes email option' do
        email_option = presenter.support_options.find { |opt| opt[:id] == 'email' }
        expect(email_option).to be_present
        expect(email_option[:title]).to eq('Email us')
        expect(email_option[:url]).to eq('mailto:<EMAIL>')
      end
    end

    describe '#sanitized_article_body' do
      let(:article) { { body: '<p>Test content with <script>alert("xss")</script> and {REIMBURSEMENT_URL}</p>' } }

      before do
        allow(PartnerProgram::InterpolateCommand).to receive(:call!).and_return('<p>Test content with  and https://example.com/reimbursement</p>')
      end

      it 'interpolates variables before sanitization' do
        allow(PartnerProgram::InterpolateCommand).to receive(:call!).and_return('<p>Interpolated content</p>')

        presenter.sanitized_article_body(article)

        expect(PartnerProgram::InterpolateCommand).to have_received(:call!).with(
          partner_program:,
          string: article[:body],
        )
      end

      it 'sanitizes HTML content' do
        result = presenter.sanitized_article_body(article)
        expect(result).not_to include('<script>')
        expect(result).not_to include('alert("xss")')
      end

      it 'preserves safe HTML tags' do
        safe_article = { body: '<p>Safe <strong>content</strong> with <a href="https://example.com">link</a></p>' }
        allow(PartnerProgram::InterpolateCommand).to receive(:call!).and_return(safe_article[:body])

        result = presenter.sanitized_article_body(safe_article)
        expect(result).to include('<p>')
        expect(result).to include('<strong>')
        expect(result).to include('href="https://example.com')
      end

      it 'adds security attributes to external links' do
        external_article = { body: '<a href="https://external.com">External link</a>' }
        allow(PartnerProgram::InterpolateCommand).to receive(:call!).and_return(external_article[:body])

        result = presenter.sanitized_article_body(external_article)
        expect(result).to include('href="https://external.com" target="_blank" rel="noopener"')
      end
    end

    describe '#articles?' do
      context 'when articles are present' do
        before do
          allow(presenter).to receive(:articles).and_return([{ title: 'Test Article' }])
        end

        it 'returns true' do
          expect(presenter.articles?).to be true
        end
      end

      context 'when no articles are present' do
        before do
          allow(presenter).to receive(:articles).and_return([])
        end

        it 'returns false' do
          expect(presenter.articles?).to be false
        end
      end
    end

    describe '#total_articles_count' do
      before do
        allow(presenter).to receive(:articles).and_return([{ title: 'Article 1' }, { title: 'Article 2' }])
      end

      it 'returns count of articles' do
        expect(presenter.total_articles_count).to eq(2)
      end
    end

    describe '#article_tags' do
      let(:article) { { content_tag_names: %w[tag1 tag2] } }

      it 'returns article content tag names' do
        expect(presenter.article_tags(article)).to eq(%w[tag1 tag2])
      end

      it 'returns empty array when no tags' do
        article_without_tags = {}
        expect(presenter.article_tags(article_without_tags)).to eq([])
      end
    end

    describe '#available_content_tags' do
      before do
        filter_command = instance_double(Zendesk::FilterAndFormatArticlesCommand)
        allow(Zendesk::FilterAndFormatArticlesCommand).to receive(:new).and_return(filter_command)
        allow(filter_command).to receive(:content_tag_mappings).and_return(
          'general' => 'General',
          'payments' => 'Payments',
        )
      end

      it 'returns sorted array of display names and keys' do
        tags = presenter.available_content_tags
        expect(tags).to be_an(Array)
        expect(tags).to include(['General', 'general'])
        expect(tags).to include(['Payments', 'payments'])
      end
    end

    describe 'delegate methods' do
      it 'delegates partner to partner_program' do
        expect(presenter.partner).to eq(partner_program.partner)
      end

      it 'delegates program to partner_program' do
        expect(presenter.program).to eq(partner_program.program)
      end

      it 'delegates site_partner_dataset to partner' do
        expect(presenter.site_partner_dataset).to eq(partner_program.partner.site_partner_dataset)
      end
    end

    describe 'presenter composition' do
      it 'creates shared setup presenter' do
        expect(presenter.shared_setup_presenter).to be_a(Site::Components::PartnerProgram::SharedSetupPresenter)
      end

      it 'creates meta tags presenter' do
        expect(presenter.meta_tags_presenter).to be_a(Site::Components::MetaTagsPresenter)
      end

      it 'creates header banner presenter' do
        expect(presenter.header_banner_presenter).to be_a(Site::Components::HeaderBannerPresenter)
      end

      it 'creates footer presenter' do
        expect(presenter.footer_presenter).to be_a(Site::Components::FooterPresenter)
      end

      it 'creates disclaimer presenter' do
        expect(presenter.disclaimer_presenter).to be_a(Site::Components::CustomDisclaimerPresenter)
      end

      it 'creates custom footer presenter' do
        expect(presenter.custom_footer_presenter).to be_a(Site::Components::FooterPresenter)
      end
    end

    describe 'custom content methods' do
      let(:partner) { partner_program.partner }
      let(:site_partner_dataset) { partner.site_partner_dataset }

      describe '#show_custom_disclaimer?' do
        context 'when custom disclaimer is present' do
          before do
            allow(site_partner_dataset).to receive(:custom_disclaimer).and_return('Custom disclaimer text')
          end

          it 'returns true' do
            expect(presenter.show_custom_disclaimer?).to be true
          end
        end

        context 'when custom disclaimer is blank' do
          before do
            allow(site_partner_dataset).to receive(:custom_disclaimer).and_return('')
          end

          it 'returns false' do
            expect(presenter.show_custom_disclaimer?).to be false
          end
        end

        context 'when site_partner_dataset is nil' do
          before do
            allow(partner).to receive(:site_partner_dataset).and_return(nil)
          end

          it 'returns false' do
            expect(presenter.show_custom_disclaimer?).to be false
          end
        end
      end


      describe '#logo_css' do
        it 'delegates to site_partner_dataset' do
          expect(presenter.logo_css).to eq(site_partner_dataset.logo_css)
        end
      end

      describe '#cobranding_css' do
        it 'delegates to site_partner_dataset' do
          expect(presenter.cobranding_css).to eq(site_partner_dataset.cobranding_css)
        end
      end

      describe '#logo_on_primary_background' do
        it 'delegates to partner theme' do
          expect(presenter.logo_on_primary_background).to eq(partner.theme.logo_on_primary_background)
        end
      end

      describe '#cobranding_text' do
        it 'delegates to partner theme' do
          expect(presenter.cobranding_text).to eq(partner.theme.cobranding_text)
        end
      end
    end
  end
end
