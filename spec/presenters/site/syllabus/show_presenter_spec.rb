# frozen_string_literal: true

require 'rails_helper'

describe Site::Syllabus::ShowPresenter do
  let(:scenario) { LandingScenario.create }
  let(:partner_program) { scenario.partner_program }
  let(:discount_code_code) { nil }
  let(:presenter) { described_class.new(partner_program:, discount_code_code:) }
  let(:default_payment_method) { scenario.ecom_payment_method }
  let(:default_variant) { scenario.ecom_variant }
  let(:upfront_pricing) do
    Ecom::Pricing::BuildFromVariantCommand.new(
      variant: default_variant,
      payment_method: default_payment_method,
    ).call!
  end

  describe '#show_our_course_attracts_block?' do
    it 'matches Site::PartnerDataset.show_our_course_attracts_block' do
      partner_program.partner.site_partner_dataset.update!(show_our_course_attracts_block: [true, false].sample)
      expect(presenter.show_our_course_attracts_block?).to eq(partner_program.partner.site_partner_dataset.show_our_course_attracts_block)
    end
  end

  describe '#list_amount' do
    it 'returns the list amount' do
      expect(presenter.list_amount).to eq(upfront_pricing.list_amount)
    end
  end

  context 'with discount_code_code' do
    let(:promos_discount_code) { create(:promos_discount_code, :enabled, discount_args: { cents_off: 10_00 }) }
    let(:discount_code_code) { promos_discount_code.code }

    it 'returns the actual amount minus the discount' do
      expect(presenter.promos_discount_cents).to eq(10_00)
    end
  end
end
