# frozen_string_literal: true

require 'rails_helper'

describe Site::Orders::ConfirmPresenter do
  let(:partner) { create(:partner, :eva_enabled, finance_relationship_count: 1) }
  let(:scenario) { ConfiguredPartnerProgramScenario.create(partner:) }
  let(:partner_program) { scenario.partner_program }
  let(:cohort) { scenario.cohort }
  let(:section) { scenario.section }
  let(:registration) { create(:registration, partner_program:, cohort:, section:, eva_type: :veteran, aspiration: :begin, experience_level: :newbie) }
  let(:order) { create(:ecom_order, partner:) }
  let(:order_item) { create(:ecom_order_item, order:, registration:) }
  let(:presenter) { described_class.new(order_item:) }

  describe '#initialize' do
    it 'sets the order, order_item, and registration' do
      expect(presenter.order).to eq(order)
      expect(presenter.order_item).to eq(order_item)
      expect(presenter.registration).to eq(registration)
    end

    it 'initializes a new_registrations_presenter with correct parameters' do
      expect(presenter.new_registrations_presenter).to be_a(Site::Registrations::NewPresenter)
      expect(presenter.new_registrations_presenter.partner_program).to eq(registration.partner_program)
    end
  end

  describe '#review_mode?' do
    it 'returns true' do
      expect(presenter.review_mode?).to be true
    end
  end

  describe '#email' do
    it 'returns the email of the registration' do
      expect(presenter.email).to eq(registration.email.address)
    end
  end

  describe '#first_name' do
    it 'returns the first name of the learner' do
      expect(presenter.first_name).to eq(registration.learner.first_name)
    end
  end

  describe '#last_name' do
    it 'returns the last name of the learner' do
      expect(presenter.last_name).to eq(registration.learner.last_name)
    end
  end

  describe '#phone' do
    context 'when learner has a phone' do
      it 'returns the phone number of the learner' do
        expect(presenter.phone).to eq(registration.learner.phone.local_number)
      end
    end

    context 'when learner does not have a phone' do
      before do
        allow(registration.learner).to receive(:phone).and_return(nil)
      end

      it 'returns nil' do
        expect(presenter.phone).to be_nil
      end
    end
  end

  describe '#pre_select_cohort?' do
    it 'returns true when cohort key matches registration cohort key' do
      expect(presenter.pre_select_cohort?(cohort_key: registration.cohort.key)).to be true
    end

    it 'returns false when cohort key does not match registration cohort key' do
      other_cohort = create(:cohort)
      expect(presenter.pre_select_cohort?(cohort_key: other_cohort.key)).to be false
    end
  end

  describe '#pre_select_section?' do
    it 'returns true when section uid matches registration section uid' do
      expect(presenter.pre_select_section?(section_uid: registration.section.uid)).to be true
    end

    it 'returns false when section uid does not match registration section uid' do
      other_section = create(:section)
      expect(presenter.pre_select_section?(section_uid: other_section.uid)).to be false
    end
  end

  describe '#cohort_readonly?' do
    context 'when registration has cohort_picked_status' do
      before { allow(registration).to receive(:cohort_picked_status?).and_return(true) }

      it 'returns true' do
        expect(presenter.cohort_readonly?).to be true
      end
    end

    context 'when registration has section_picked_status' do
      before { allow(registration).to receive(:section_picked_status?).and_return(true) }

      it 'returns true' do
        expect(presenter.cohort_readonly?).to be true
      end
    end

    context 'when registration has neither status' do
      before do
        allow(registration).to receive_messages(
          cohort_picked_status?: false,
          section_picked_status?: false,
        )
      end

      it 'returns false' do
        expect(presenter.cohort_readonly?).to be false
      end
    end
  end

  describe '#section_readonly?' do
    context 'when registration has section_picked_status' do
      before { allow(registration).to receive(:section_picked_status?).and_return(true) }

      it 'returns true' do
        expect(presenter.section_readonly?).to be true
      end
    end

    context 'when registration does not have section_picked_status' do
      before { allow(registration).to receive(:section_picked_status?).and_return(false) }

      it 'returns false' do
        expect(presenter.section_readonly?).to be false
      end
    end
  end

  describe '#selected_cohort_name' do
    context 'when cohort is readonly' do
      before { allow(presenter).to receive(:cohort_readonly?).and_return(true) }

      it 'returns the name of the selected cohort' do
        expect(presenter.selected_cohort_name).to eq(cohort.starts_on.strftime('%B (Starts %-m/%-d)'))
      end
    end

    context 'when cohort is not readonly' do
      before { allow(presenter).to receive(:cohort_readonly?).and_return(false) }

      it 'returns nil' do
        expect(presenter.selected_cohort_name).to be_nil
      end
    end
  end

  describe '#selected_section_name' do
    context 'when section is readonly' do
      before { allow(presenter).to receive(:section_readonly?).and_return(true) }

      it 'returns the name of the selected section' do
        expect(presenter.selected_section_name).to eq(Section::LiveDayTimeFormatter.new(section:).to_fs)
      end
    end

    context 'when section is not readonly' do
      before { allow(presenter).to receive(:section_readonly?).and_return(false) }

      it 'returns nil' do
        expect(presenter.selected_section_name).to be_nil
      end
    end
  end

  describe '#selected_cohort_key' do
    context 'when cohort is readonly' do
      before { allow(presenter).to receive(:cohort_readonly?).and_return(true) }

      it 'returns the key of the selected cohort' do
        expect(presenter.selected_cohort_key).to eq(cohort.key)
      end
    end

    context 'when cohort is not readonly' do
      before { allow(presenter).to receive(:cohort_readonly?).and_return(false) }

      it 'returns nil' do
        expect(presenter.selected_cohort_key).to be_nil
      end
    end
  end

  describe '#selected_section_uid' do
    context 'when section is readonly' do
      before { allow(presenter).to receive(:section_readonly?).and_return(true) }

      it 'returns the uid of the selected section' do
        expect(presenter.selected_section_uid).to eq(section.uid)
      end
    end

    context 'when section is not readonly' do
      before { allow(presenter).to receive(:section_readonly?).and_return(false) }

      it 'returns nil' do
        expect(presenter.selected_section_uid).to be_nil
      end
    end
  end

  describe 'inherited methods from NewPresenter' do
    describe '#cohort_options' do
      context 'when cohort is readonly' do
        before { allow(presenter).to receive(:cohort_readonly?).and_return(true) }

        it 'returns only the selected cohort' do
          options = presenter.cohort_options
          expect(options.size).to eq(1)
          expect(options.first).to eq([cohort.key, cohort.starts_on.strftime('%B (Starts %-m/%-d)')])
        end
      end

      context 'when cohort is not readonly' do
        before { allow(presenter).to receive(:cohort_readonly?).and_return(false) }

        it 'returns all cohort options' do
          expect(presenter.cohort_options).to include([cohort.key, cohort.starts_on.strftime('%B (Starts %-m/%-d)')])
        end
      end
    end

    describe '#section_options' do
      context 'when section is readonly' do
        before { allow(presenter).to receive(:section_readonly?).and_return(true) }

        it 'returns only the selected section' do
          options = presenter.section_options(cohort:)
          expect(options.size).to eq(1)
          expect(options.first).to eq([section.uid, Section::LiveDayTimeFormatter.new(section:).to_fs])
        end
      end

      context 'when section is not readonly' do
        before { allow(presenter).to receive(:section_readonly?).and_return(false) }

        it 'returns all section options for a given cohort' do
          expect(presenter.section_options(cohort:)).to include([section.uid, Section::LiveDayTimeFormatter.new(section:).to_fs])
        end
      end
    end

    describe '#experience_level_options' do
      it 'returns the experience level options' do
        expect(presenter.experience_level_options)
          .to include(
            [:newbie, "I'm a newbie to #{partner_program.program.short_name.downcase_with_inflections}"],
            [:some, "I have some #{partner_program.program.short_name.downcase_with_inflections} experience"],
            [:current, presenter.current_experience_level_text],
          )
      end
    end

    describe '#aspiration_options' do
      it 'returns the aspiration options' do
        expect(presenter.aspiration_options).to include(
          [:begin, "Begin my professional career"],
          [:advance, "Advance in my current career"],
          [:change, "Change or re-enter my career"],
          [:other, "Other"],
        )
      end
    end
  end
end
