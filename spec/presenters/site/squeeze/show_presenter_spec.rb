# frozen_string_literal: true

require 'rails_helper'

describe Site::Squeeze::ShowPresenter do
  let(:scenario) { SqueezeScenario.create }
  let(:partner_program) { scenario.partner_program }
  let(:presenter) { described_class.new(partner_program:, learner_cookie_info: {}) }

  def site_program_dataset
    @site_program_dataset ||= create(:site_program_dataset, program: create(:program))
  end

  describe '#show_our_course_attracts_block?' do
    it 'matches Site::PartnerDataset.show_our_course_attracts_block' do
      partner_program.partner.site_partner_dataset.update!(show_our_course_attracts_block: [true, false].sample)
      expect(presenter.show_our_course_attracts_block?).to eq(partner_program.partner.site_partner_dataset.show_our_course_attracts_block)
    end
  end

  describe "#sneak_peek_heading" do
    context "when program dataset squeeze overrides are not present" do
      it "returns the default squeeze overrides" do
        expect(presenter.sneak_peek_heading)
          .to include("Get a sneak peek")
      end
    end

    context "when squeeze overrides are present" do
      before do
        presenter.site_program_dataset.update(overrides: site_program_dataset.overrides
              .merge(squeeze: {
                sneak_peek_heading: "Get a sneak peek into AI-powered marketing",
              },
                    ),
                                             )
      end

      it 'returns the custom program dataset squeeze overrides' do
        expect(presenter.sneak_peek_heading)
          .to include("Get a sneak peek into AI-powered marketing")
      end
    end
  end
end
