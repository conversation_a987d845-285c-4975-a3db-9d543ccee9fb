# frozen_string_literal: true

VCR.configure do |config|
  config.cassette_library_dir = "fixtures/vcr_cassettes"
  config.hook_into :webmock
  config.configure_rspec_metadata!
  config.filter_sensitive_data('<BEARER_TOKEN>') do |interaction|
    auths = interaction.request.headers['Authorization'].first
    if (match = auths.match(/^Bearer\s+([^,\s]+)/))
      match.captures.first
    end
  end
  config.filter_sensitive_data('<BASIC_AUTH_TOKEN>') do |interaction|
    auths = interaction.request.headers['Authorization']&.first
    if auths&.match(/^Basic\s+(.+)$/)
      Regexp.last_match(1) # Filter the base64 encoded credentials
    end
  end
  config.filter_sensitive_data('<STRIPE_USER_AGENT>') do |interaction|
    interaction.request.headers['X-Stripe-Client-User-Agent']&.first
  end

  # filter multiple '"secure_params":"<some JWT token>", from response body
  config.before_record do |interaction|
    loop do
      break unless (match = interaction.response.body.match(/("secure_params":"[^,]+")/))

      # we are changing the key to "secure_params_redacted" to avoid the filtered key meeting the match requirements again
      interaction.filter!(match.captures.first, '"secure_params_redacted":"<SECURE_PARAMS>"')
    end
  end
end
