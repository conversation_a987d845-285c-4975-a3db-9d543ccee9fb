# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LearningDelivery::TasksController do
  let(:admin_user) { create(:admin_user) }
  let(:task) { create(:learning_delivery_task, :created) }

  before do
    sign_in admin_user
    allow(controller).to receive(:authorize!).and_return(true)
  end

  describe 'GET #index' do
    let!(:tasks) { create_list(:learning_delivery_task, 3) }

    it 'returns http success' do
      get :index
      expect(response).to have_http_status(:success)
    end

    it 'assigns @presenter' do
      get :index
      expect(assigns(:presenter)).to be_a(LearningDelivery::Tasks::IndexPresenter)
    end

    it 'renders the index template' do
      get :index
      expect(response).to render_template(:index)
    end

    context 'with filtering parameters' do
      it 'passes parameters to presenter' do
        allow(LearningDelivery::Tasks::IndexPresenter).to receive(:new).and_call_original

        get :index, params: { status: 'created' }

        expect(LearningDelivery::Tasks::IndexPresenter).to have_received(:new)
          .with(hash_including(params: hash_including('status' => 'created')))
      end
    end

    context 'authorization' do
      it 'checks authorization for Learning Delivery resource group' do
        allow(controller).to receive(:authorize!)

        get :index

        expect(controller).to have_received(:authorize!).with(:read, 'Learning Delivery')
      end
    end
  end

  describe 'GET #show' do
    context 'for Turbo Stream requests' do
      before do
        request.accept = 'text/vnd.turbo-stream.html'
      end

      it 'returns http success' do
        get :show, params: { id: task.uid }, format: :turbo_stream
        expect(response).to have_http_status(:success)
      end

      it 'assigns @show_presenter' do
        get :show, params: { id: task.uid }, format: :turbo_stream
        expect(assigns(:show_presenter)).to be_a(LearningDelivery::Tasks::TaskModalPresenter)
        expect(assigns(:show_presenter).task).to eq(task)
      end

      it 'renders turbo stream response' do
        get :show, params: { id: task.uid }, format: :turbo_stream
        expect(response.content_type).to include('text/vnd.turbo-stream.html')
      end

      it 'updates the task status to viewed' do
        get :show, params: { id: task.uid }, format: :turbo_stream
        expect(task.reload.status).to eq('viewed')
      end
    end

    context 'for direct navigation' do
      it 'returns http success' do
        get :show, params: { id: task.uid }
        expect(response).to have_http_status(:success)
      end

      it 'assigns both @show_presenter and @presenter' do
        get :show, params: { id: task.uid }
        expect(assigns(:show_presenter)).to be_a(LearningDelivery::Tasks::TaskModalPresenter)
        expect(assigns(:presenter)).to be_a(LearningDelivery::Tasks::IndexPresenter)
      end

      it 'renders the index template' do
        get :show, params: { id: task.uid }
        expect(response).to render_template(:index)
      end
    end


    context 'authorization' do
      it 'checks authorization for Learning Delivery resource group' do
        allow(controller).to receive(:authorize!)

        get :show, params: { id: task.uid }

        expect(controller).to have_received(:authorize!).with(:read, 'Learning Delivery')
      end
    end
  end

  describe 'PATCH #update' do
    let(:update_params) { { status: 'completed' } }

    context 'when update is successful' do
      before do
        allow_any_instance_of(LearningDelivery::Task::UpdateStatusCommand)
          .to receive(:call).and_return(true)
      end

      it 'returns http success' do
        patch :update, params: { id: task.uid, status: 'completed' }
        expect(response).to have_http_status(:success)
      end

      it 'returns JSON success response' do
        patch :update, params: { id: task.uid, status: 'completed' }
        json_response = response.parsed_body
        expect(json_response['status']).to eq('success')
        expect(json_response['message']).to eq('Task status updated to completed')
      end

      it 'calls UpdateStatusCommand with correct parameters' do
        allow(LearningDelivery::Task::UpdateStatusCommand).to receive(:new).and_call_original

        patch :update, params: { id: task.uid, status: 'completed' }

        expect(LearningDelivery::Task::UpdateStatusCommand).to have_received(:new)
          .with(hash_including(
            task:,
            new_status: 'completed',
          ),
               )
      end
    end

    context 'when update fails' do
      let(:command_double) { instance_double(LearningDelivery::Task::UpdateStatusCommand) }

      before do
        allow(LearningDelivery::Task::UpdateStatusCommand).to receive(:new).and_return(command_double)
        allow(command_double).to receive(:call).and_return(false)
      end

      it 'returns unprocessable entity status' do
        patch :update, params: { id: task.uid, status: 'completed' }
        expect(response).to have_http_status(:unprocessable_content)
      end

      it 'returns JSON error response' do
        patch :update, params: { id: task.uid, status: 'completed' }
        json_response = response.parsed_body
        expect(json_response['status']).to eq('error')
        expect(json_response['errors']).to include('Failed to update task status')
      end
    end

    context 'authorization' do
      it 'checks authorization for Learning Delivery resource group' do
        allow(controller).to receive(:authorize!)

        patch :update, params: { id: task.uid, status: 'completed' }

        expect(controller).to have_received(:authorize!).with(:write, 'Learning Delivery')
      end
    end

    context 'when task is not found' do
      it 'raises ActiveRecord::RecordNotFound' do
        expect do
          patch :update, params: { id: 'invalid-uid', status: 'completed' }
        end.to raise_error(ActiveRecord::RecordNotFound)
      end
    end

    context 'with invalid parameters' do
      it 'handles missing status parameter' do
        patch :update, params: { id: task.uid }
        expect(response).to have_http_status(:unprocessable_content)
        json_response = response.parsed_body
        expect(json_response['status']).to eq('error')
        expect(json_response['errors']).to include('Status parameter is required')
      end
    end
  end
end
