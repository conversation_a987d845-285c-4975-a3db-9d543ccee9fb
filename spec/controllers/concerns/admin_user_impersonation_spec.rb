# frozen_string_literal: true

require 'rails_helper'

describe 'AdminUserImpersonation' do
  controller(ApplicationController) do
    include AdminUserImpersonation
  end

  before do
    routes.draw do
      get 'start' => 'anonymous#start'
      get 'end' => 'anonymous#end'
    end
  end

  let(:subdomain) { 'admin' }
  let(:authenticated_admin_user) { create(:admin_user) }
  let(:impersonated_admin_user) { create(:admin_user) }
  let(:resource_group) { 'Admin - Impersonation' }
  let(:permission_level) { :rw }
  let(:digest_param) { AdminUserImpersonation.send(:build_digest, authenticated_admin_user:, impersonated_admin_user:) }
  let(:redirect_to_param) { 'http://example.com' }

  before do
    controller.bypass_sign_in(authenticated_admin_user)
    allow_any_instance_of(ActionController::TestRequest).to receive(:subdomain).and_return(subdomain)
    authenticated_admin_user.permissions << create(:permission, resource_group:, level: permission_level)
  end

  def expect_current_admin_user(admin_user)
    expect(session['warden.user.admin_user.key']&.first&.first).to eq(admin_user.id)
  end

  describe 'GET start' do
    subject(:get_start) { get :start, params: { admin_user_id: impersonated_admin_user.id, redirect_to: redirect_to_param, digest: digest_param } }

    it 'authenticates impersonated user and redirect' do
      get_start
      expect_current_admin_user(impersonated_admin_user)
      expect(session[:authenticated_admin_user_id]).to eq(authenticated_admin_user.id)
      expect(response).to redirect_to(redirect_to_param)
    end

    context 'when already impersonating another user' do
      before do
        session[:authenticated_admin_user_id] = authenticated_admin_user.id
        controller.bypass_sign_in(create(:admin_user))
      end

      it 'returns to the authenticated admin user' do
        get_start
        expect_current_admin_user(impersonated_admin_user)
        expect(session[:authenticated_admin_user_id]).to eq(authenticated_admin_user.id)
        expect(response).to redirect_to(redirect_to_param)
      end
    end

    context 'with no permission' do
      let(:resource_group) { 'invalid' }

      it 'raises an error' do
        expect { get_start }.to raise_error('You must be signed in as an admin user to impersonate')
      end
    end

    context 'without write permission' do
      let(:permission_level) { :r }

      it 'raises an error' do
        expect { get_start }.to raise_error('You must be signed in as an admin user to impersonate')
      end
    end

    context 'when not impersonating an admin user' do
      let(:impersonated_admin_user) { create(:admin_user, :with_learning_delivery_employee) }

      it 'raises an error' do
        expect { get_start }.to raise_error('You must be signed in as an admin user to impersonate')
      end
    end

    context 'with invalid digest' do
      let(:digest_param) { 'invalid_digest' }

      it 'raises an error' do
        expect { get_start }.to raise_error('Invalid digest')
      end
    end
  end

  describe 'GET end' do
    before do
      controller.bypass_sign_in(impersonated_admin_user)
      session[:authenticated_admin_user_id] = authenticated_admin_user.id
    end

    subject(:get_end) { get :end, params: { admin_user_id: impersonated_admin_user.id, redirect_to: redirect_to_param, digest: digest_param } }

    it 'authenticates with authenticated admin user in session and redirect' do
      get_end
      expect_current_admin_user(authenticated_admin_user)
      expect(session[:authenticated_admin_user_id]).to be_nil
      expect(response).to redirect_to(redirect_to_param)
    end

    context 'when not impersonating any user' do
      before do
        session.delete(:authenticated_admin_user_id)
      end

      it 'raises an error' do
        expect { get_end }.to raise_error('Not impersonating any user')
      end
    end

    context 'with no permission' do
      let(:resource_group) { 'invalid' }

      it 'raises an error' do
        expect { get_end }.to raise_error('You must be signed in as an admin user to impersonate')
      end
    end

    context 'with invalid digest' do
      let(:digest_param) { 'invalid_digest' }

      it 'raises an error' do
        expect { get_end }.to raise_error('Invalid digest')
      end
    end
  end

  describe '.allow_subdomain?' do
    let(:subdomain) { raise }

    subject(:allow_subdomain?) { AdminUserImpersonation.allow_subdomain?(authenticated_admin_user:, impersonated_admin_user:, subdomain:) }

    context 'when subdomain is admin' do
      let(:subdomain) { 'admin' }

      it 'returns true' do
        expect(allow_subdomain?).to be true
      end

      context 'with no permission' do
        let(:resource_group) { 'invalid' }

        it 'returns false' do
          expect(allow_subdomain?).to be false
        end
      end

      context 'without write permission' do
        let(:permission_level) { :r }

        it 'returns false' do
          expect(allow_subdomain?).to be false
        end
      end

      context 'with impersonated user not an admin user' do
        let(:impersonated_admin_user) { create(:admin_user, :with_learning_delivery_employee) }

        it 'returns false' do
          expect(allow_subdomain?).to be false
        end
      end
    end

    context 'when subdomain is learning-delivery' do
      let(:subdomain) { 'learning-delivery' }
      let(:resource_group) { 'Learning Delivery - Impersonation' }
      let(:impersonated_admin_user) { create(:admin_user, :with_learning_delivery_employee) }

      it 'returns true' do
        expect(allow_subdomain?).to be true
      end

      context 'with no permission' do
        let(:resource_group) { 'invalid' }

        it 'returns false' do
          expect(allow_subdomain?).to be false
        end
      end

      context 'without write permission' do
        let(:permission_level) { :r }

        it 'returns false' do
          expect(allow_subdomain?).to be false
        end
      end

      context 'with impersonated user not a learning delivery employee' do
        let(:impersonated_admin_user) { create(:admin_user) }

        it 'returns false' do
          expect(allow_subdomain?).to be false
        end
      end
    end
  end

  describe '.build_start_url' do
    let(:subdomains) { 'admin' }

    subject(:build_start_url) do
      AdminUserImpersonation.build_start_url(
        authenticated_admin_user:,
        impersonated_admin_user:,
        redirect_to: redirect_to_param,
        subdomains:,
      )
    end

    it 'returns the correct URL for admin subdomain' do
      expect(build_start_url).to include("admin_users/start")
      expect(build_start_url).to include("redirect_to=#{CGI.escape(redirect_to_param)}")
      expect(build_start_url).to include("digest=#{CGI.escape(digest_param)}")
    end
  end

  describe '.build_end_url' do
    let(:subdomains) { 'admin' }

    subject(:build_end_url) do
      AdminUserImpersonation.build_end_url(
        authenticated_admin_user:,
        impersonated_admin_user:,
        redirect_to: redirect_to_param,
        subdomains:,
      )
    end

    it 'returns the correct URL for admin subdomain' do
      expect(build_end_url).to include("admin_users/end")
      expect(build_end_url).to include("redirect_to=#{CGI.escape(redirect_to_param)}")
      expect(build_end_url).to include("digest=#{CGI.escape(digest_param)}")
    end
  end
end
