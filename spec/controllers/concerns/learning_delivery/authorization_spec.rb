# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ApplicationController do # rubocop:disable RSpec/SpecFilePathFormat
  include Devise::Test::ControllerHelpers

  controller do
    include LearningDelivery::Authorization

    # Skip the authenticate_admin_user! before_action
    skip_before_action :authenticate_admin_user!

    def test_authorize_for_target_employee
      employee, permission_level = authorize_for_target_employee!(params[:employee_id])
      render json: { employee_id: employee&.id, permission_level: }
    end
  end

  # Set up routes for the test action
  before do
    routes.draw { get "/test_authorize_for_target_employee" => "anonymous#test_authorize_for_target_employee" }
  end

  let(:admin_user) { create(:admin_user) }
  let(:current_employee) { create(:learning_delivery_employee, admin_user:) }
  let(:target_employee) { create(:learning_delivery_employee) }

  before do
    sign_in admin_user
    allow(controller).to receive_messages(current_admin_user: admin_user, current_employee:)
  end

  describe '#authorize_for_target_employee!' do
    context 'when employee_id is present and current employee is different from target employee' do
      context 'with manager permissions' do
        before do
          Permission::UpdatePermissionsCommand.call!(record: current_employee.admin_user,
            permissions: [{ resource_group: 'Learning Delivery - Management', level: :r }],
          )
        end

        it 'authorizes for management and returns target employee with manager permission level' do
          get :test_authorize_for_target_employee, params: { employee_id: target_employee.humanized_uid }

          expect(response).to have_http_status(:ok)
          expect(response.parsed_body).to eq(
            {
              'employee_id' => target_employee.id,
              'permission_level' => 'manager',
            },
          )
        end
      end

      context 'with employee permissions' do
        before do
          Permission::UpdatePermissionsCommand.call!(record: current_employee.admin_user,
            permissions: [{ resource_group: 'Learning Delivery', level: :r }],
          )
        end

        it 'raises ForbiddenError when user does not have management permission' do
          expect do
            get :test_authorize_for_target_employee, params: { employee_id: target_employee.uid }
          end.to raise_error(LearningDelivery::Authorization::ForbiddenError)
        end
      end
    end

    context 'when employee_id is blank' do
      context 'with employee permissions' do
        before do
          Permission::UpdatePermissionsCommand.call!(record: current_employee.admin_user,
            permissions: [{ resource_group: 'Learning Delivery', level: :r }],
          )
        end

        it 'authorizes for regular access and returns current employee with employee permission level' do
          get :test_authorize_for_target_employee, params: { employee_id: nil }

          expect(response).to have_http_status(:ok)
          expect(response.parsed_body).to eq(
            {
              'employee_id' => current_employee.id,
              'permission_level' => 'employee',
            },
          )
        end
      end

      context 'with no permissions' do
        it 'raises ForbiddenError when user does not have regular permission' do
          expect do
            get :test_authorize_for_target_employee, params: { employee_id: nil }
          end.to raise_error(LearningDelivery::Authorization::ForbiddenError)
        end
      end
    end

    context 'when employee_id is present but current employee is the same as target employee' do
      context 'with employee permissions' do
        before do
          Permission::UpdatePermissionsCommand.call!(record: current_employee.admin_user,
            permissions: [{ resource_group: 'Learning Delivery', level: :r }],
          )
        end

        it 'authorizes for regular access and returns current employee with employee permission level' do
          get :test_authorize_for_target_employee, params: { employee_id: current_employee.uid }

          expect(response).to have_http_status(:ok)
          expect(response.parsed_body).to eq(
            {
              'employee_id' => current_employee.id,
              'permission_level' => 'employee',
            },
          )
        end
      end
    end

    context 'when employee is nil' do
      context 'with maanger permissions' do
        before do
          Permission::UpdatePermissionsCommand.call!(record: current_employee.admin_user,
            permissions: [{ resource_group: 'Learning Delivery - Management', level: :r }],
          )
        end

        it 'raises RecordNotFound' do
          expect do
            get :test_authorize_for_target_employee, params: { employee_id: 'non-existent-uid' }
          end.to raise_error(ActiveRecord::RecordNotFound)
        end
      end

      context 'with employee permissions' do
        before do
          Permission::UpdatePermissionsCommand.call!(record: current_employee.admin_user,
            permissions: [{ resource_group: 'Learning Delivery', level: :r }],
          )
        end

        it 'raises RecordNotFound' do
          expect do
            get :test_authorize_for_target_employee, params: { employee_id: 'non-existent-uid' }
          end.to raise_error(LearningDelivery::Authorization::ForbiddenError)
        end
      end
    end
  end
end
