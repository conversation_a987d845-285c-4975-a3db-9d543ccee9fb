# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Site::PartnerProgramRouting do
  controller(ActionController::Base) do
    include Site::PartnerProgramRouting # rubocop:disable RSpec/DescribedClass

    def index
      partner_program = partner_program_from_routing_params(scope: PartnerProgram.all)
      render plain: partner_program.id
    end
  end

  let(:proxy_host) { 'example.com' }
  let(:partner_slug) { 'test-partner' }
  let(:program_slug) { 'test-program' }

  let(:partner) do
    create(:partner,
      name: 'Test Partner',
      slug: partner_slug,
      proxy_host:,
      theme_count: 1,
      site_partner_dataset_count: 1,
    )
  end

  let(:program) do
    create(:program,
      name: 'Test Program',
      slug: program_slug,
      site_program_dataset_count: 1,
      cohort_count: 1,
    )
  end

  let!(:partner_program) do
    create(:partner_program,
      partner:,
      program:,
      site_partner_program_dataset_count: 1,
    )
  end

  before do
    allow_any_instance_of(PartnerProgram).to receive(:configured_for_site?).and_return(true)
  end

  describe '#partner_program_from_routing_params' do
    context 'when finding by proxy_host' do
      before do
        allow(controller).to receive_messages(proxy_host:, partner_slug: nil, program_slug:)
      end

      it 'finds the partner program' do
        get :index
        expect(response.body).to eq(partner_program.id.to_s)
      end
    end

    context 'when finding by partner slug' do
      before do
        allow(controller).to receive_messages(proxy_host: nil, partner_slug:, program_slug:)
      end

      it 'finds the partner program' do
        get :index
        expect(response.body).to eq(partner_program.id.to_s)
      end
    end

    context 'when using default program' do
      let!(:default_program) { create(:program, name: 'Default Program', site_program_dataset_count: 1, cohort_count: 1) }
      let!(:default_partner_program) do
        create(:partner_program,
          partner:,
          program: default_program,
          default: true,
          site_partner_program_dataset_count: 1,
        )
      end

      before do
        allow(controller).to receive_messages(proxy_host:, partner_slug: nil, program_slug: nil)
      end

      it 'finds the default partner program' do
        get :index
        expect(response.body).to eq(default_partner_program.id.to_s)
      end
    end

    context 'when partner program cannot be shown' do
      before do
        allow(controller).to receive_messages(proxy_host:, partner_slug: nil, program_slug:)
        allow_any_instance_of(PartnerProgram).to receive(:configured_for_site?).and_return(false)
      end

      it 'raises ViewablePartnerProgramNotFound' do
        expect do
          get :index
        end.to raise_error(Site::PartnerProgramRouting::ViewablePartnerProgramNotFound)
      end
    end

    context 'when partner is not found' do
      before do
        # Use a non-existent proxy_host and partner_slug
        allow(controller).to receive_messages(proxy_host: 'nonexistent-domain.com', partner_slug: 'nonexistent', program_slug:)
      end

      it 'raises ViewablePartnerProgramNotFound' do
        expect do
          get :index
        end.to raise_error(Site::PartnerProgramRouting::ViewablePartnerProgramNotFound)
      end
    end

    context 'when partner is inactive but has configured alternatives' do
      let(:inactive_partner) do
        create(:partner,
          name: 'Inactive Partner',
          slug: 'inactive-partner',
          proxy_host: 'inactive.example.com',
          theme_count: 1,
          site_partner_dataset_count: 1,
          active: false,
        )
      end

      let(:alternative_partner) do
        create(:partner,
          name: 'Alternative Partner',
          slug: 'alternative-partner',
          proxy_host: 'alternative.example.com',
          theme_count: 1,
          site_partner_dataset_count: 1,
        )
      end

      let!(:alt_partner_program) do
        create(:partner_program,
          partner: alternative_partner,
          program:,
          site_partner_program_dataset_count: 1,
        )
      end

      before do
        allow(controller).to receive_messages(proxy_host: 'inactive.example.com', partner_slug: nil, program_slug:)
        allow(Partner::AlternativesForInactivePartnerCommand).to receive(:call!).and_return([alternative_partner])
      end

      it 'finds the alternative partner program' do
        get :index
        expect(response.body).to eq(alt_partner_program.id.to_s)
      end
    end

    context 'when partner is inactive and has no configured alternatives' do
      let(:inactive_partner) do
        create(:partner,
          name: 'Inactive Partner',
          slug: 'inactive-partner',
          proxy_host: 'inactive.example.com',
          theme_count: 1,
          site_partner_dataset_count: 1,
          active: false,
        )
      end

      before do
        allow(controller).to receive_messages(proxy_host: 'inactive.example.com', partner_slug: nil, program_slug:)
        allow(Partner::AlternativesForInactivePartnerCommand).to receive(:call!).and_return([])
      end

      it 'raises ViewablePartnerProgramNotFound' do
        expect do
          get :index
        end.to raise_error(Site::PartnerProgramRouting::ViewablePartnerProgramNotFound)
      end
    end

    context 'when program is not found' do
      before do
        allow(controller).to receive_messages(proxy_host:, partner_slug: nil, program_slug: 'nonexistent')
        partners = controller.send(:partners_from_routing_params)
        PartnerProgram.where(partner: partners).update_all(default: false) # rubocop:disable Rails/SkipsModelValidations
      end

      it 'raises ViewablePartnerProgramNotFound' do
        expect do
          get :index
        end.to raise_error(Site::PartnerProgramRouting::ViewablePartnerProgramNotFound)
      end
    end
  end

  describe 'multiple partners with same subdomain' do
    let(:shared_proxy_host) { 'advance.georgiacenter.edu' }

    let(:partner_one) do
      create(:partner,
        name: 'University of Georgia 1',
        proxy_host: shared_proxy_host,
        theme_count: 1,
        site_partner_dataset_count: 1,
      )
    end

    let(:partner_two) do
      create(:partner,
        name: 'University of Georgia 2',
        proxy_host: shared_proxy_host,
        theme_count: 1,
        site_partner_dataset_count: 1,
      )
    end

    let(:program_one) do
      create(:program,
        name: 'Program 1',
        slug: 'program-1',
        site_program_dataset_count: 1,
        cohort_count: 1,
      )
    end

    let(:program_two) do
      create(:program,
        name: 'Program 2',
        slug: 'program-2',
        site_program_dataset_count: 1,
        cohort_count: 1,
      )
    end

    let!(:partner_program_one) do
      create(:partner_program,
        partner: partner_one,
        program: program_one,
        site_partner_program_dataset_count: 1,
      )
    end

    let!(:partner_program_two) do
      create(:partner_program,
        partner: partner_two,
        program: program_two,
        site_partner_program_dataset_count: 1,
      )
    end

    before do
      allow_any_instance_of(PartnerProgram).to receive(:configured_for_site?).and_return(true)
    end

    context 'when finding partner program by proxy_host and program slug' do
      before do
        allow(controller).to receive_messages(proxy_host: shared_proxy_host, partner_slug: nil, program_slug: 'program-1')
      end

      it 'finds the correct partner program for the first partner' do
        get :index
        expect(response.body).to eq(partner_program_one.id.to_s)
      end
    end

    context 'when finding partner program by proxy_host and a different program slug' do
      before do
        allow(controller).to receive_messages(proxy_host: shared_proxy_host, partner_slug: nil, program_slug: 'program-2')
      end

      it 'finds the correct partner program for the second partner' do
        get :index
        expect(response.body).to eq(partner_program_two.id.to_s)
      end
    end
  end
end
