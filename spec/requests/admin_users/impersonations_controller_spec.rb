# frozen_string_literal: true

require 'rails_helper'

module AdminUsers
  RSpec.describe ImpersonationsController do
    let(:authenticated_admin_user) do
      create(:admin_user).tap do |admin_user|
        admin_user.permissions << create(:permission, resource_group: 'Admin - Impersonation', level: :rw)
      end
    end
    let(:impersonated_admin_user) { create(:admin_user) }
    let(:redirect_to_param) { 'http://example.com' }
    let(:digest_param) { AdminUserImpersonation.send(:build_digest, authenticated_admin_user:, impersonated_admin_user:) }

    describe 'GET #start' do
      before do
        sign_in authenticated_admin_user
      end

      subject(:get_start) do
        get admin_users_start_impersonation_path, params: {
          admin_user_id: impersonated_admin_user.id,
          redirect_to: redirect_to_param,
          digest: digest_param,
        }
      end

      it 'redirects' do
        get_start
        expect(response).to redirect_to(redirect_to_param)
      end
    end

    describe 'GET #end' do
      before do
        sign_in authenticated_admin_user

        get admin_users_start_impersonation_path, params: {
          admin_user_id: impersonated_admin_user.id,
          redirect_to: redirect_to_param,
          digest: digest_param,
        }
      end

      subject(:get_end) do
        get admin_users_end_impersonation_path, params: {
          admin_user_id: impersonated_admin_user.id,
          redirect_to: redirect_to_param,
          digest: digest_param,
        }
      end

      context 'when user has Admin - Impersonation read permission' do
        before do
          authenticated_admin_user.permissions << create(:permission, resource_group: 'Admin - Impersonation', level: :rw)
        end

        it 'redirects' do
          get_end
          expect(response).to redirect_to(redirect_to_param)
        end
      end
    end
  end
end
