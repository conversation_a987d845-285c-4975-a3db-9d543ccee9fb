# frozen_string_literal: true

require 'rails_helper'

describe 'LearningDelivery::ApplicationController', type: :controller do
  controller(LearningDelivery::ApplicationController) do
    def index
      add_breadcrumb("Index", "/index")
      render plain: "ok"
    end
  end

  describe "#breadcrumbs" do
    it "allows adding and accessing breadcrumbs directly" do
      controller.send(:breadcrumbs) << Breadcrumb.new("Test", "/test")

      breadcrumbs = controller.send(:breadcrumbs)

      expect(breadcrumbs.size).to eq(1)
      expect(breadcrumbs.first.name).to eq("Test")
      expect(breadcrumbs.first.path).to eq("/test")
    end
  end

  describe "#top_navbar_presenter" do
    it "returns a TopNavbarPresenter instance" do
      presenter = controller.send(:top_navbar_presenter)
      expect(presenter).to be_a(LearningDelivery::TopNavbarPresenter)
    end
  end

  describe "#sidebar_presenter" do
    it "returns a SidebarPresenter instance" do
      presenter = controller.send(:sidebar_presenter)
      expect(presenter).to be_a(LearningDelivery::SidebarPresenter)
    end
  end

  describe "#feature_enabled?" do
    it "returns false for an unknown feature" do
      allow(FeatureFlag).to receive(:enabled?).and_return(false)
      controller.send(:feature_enabled?, :some_non_existent_feature)
      expect(FeatureFlag).to have_received(:enabled?).with(:some_non_existent_feature)
    end
  end
end
