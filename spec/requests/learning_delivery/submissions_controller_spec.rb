# frozen_string_literal: true

require 'rails_helper'

module LearningDelivery
  describe SubmissionsController do
    let(:admin_user) do
      create(:admin_user).tap do |u|
        u.permissions << create(:permission, resource_group: 'LMS', level: :rwd)
      end
    end

    before do
      host!(RequestHelper.new.host(:learning_delivery))
      sign_in admin_user
    end

    describe 'GET #index' do
      it 'returns a success response' do
        get '/submissions'
        expect(response).to be_successful
      end

      it 'assigns an index presenter' do
        get '/submissions'
        expect(assigns(:presenter)).to be_a(Submissions::IndexPresenter)
      end

      it 'initializes the presenter with current_employee' do
        allow(Submissions::IndexPresenter).to receive(:new).and_call_original
        get '/submissions'
        expect(Submissions::IndexPresenter).to have_received(:new).with(
          params: instance_of(ActionController::Parameters),
          current_employee: nil,
        )
      end

      context 'when admin_user has an associated learning_delivery_employee' do
        let(:learning_delivery_employee) { create(:learning_delivery_employee) }

        before do
          admin_user.update!(learning_delivery_employee:)
        end

        it 'initializes the presenter with the current_employee' do
          allow(Submissions::IndexPresenter).to receive(:new).and_call_original
          get '/submissions'
          expect(Submissions::IndexPresenter).to have_received(:new).with(
            params: instance_of(ActionController::Parameters),
            current_employee: learning_delivery_employee,
          )
        end
      end

      context 'with review_status parameter' do
        it 'passes the parameter to the presenter' do
          get '/submissions?review_status=graded'
          expect(assigns(:presenter).params[:review_status]).to eq('graded')
        end
      end

      context 'with section_id parameter' do
        it 'passes the parameter to the presenter' do
          get '/submissions?section_id=123'
          expect(assigns(:presenter).params[:section_id]).to eq('123')
        end
      end

      context 'with assignment_template_id parameter' do
        it 'passes the parameter to the presenter' do
          get '/submissions?assignment_template_id=456'
          expect(assigns(:presenter).params[:assignment_template_id]).to eq('456')
        end
      end

      context 'with filter parameter' do
        it 'passes the parameter to the presenter' do
          get '/submissions?filter=needs_review'
          expect(assigns(:presenter).params[:filter]).to eq('needs_review')
        end
      end
    end

    describe 'GET #show' do
      let(:submission) { create(:lms_submission) }
      let(:review_status) { Lms::Submission::Review.state_names.keys.sample }
      let(:params) { { review_status: } }

      it 'returns a success response' do
        get learning_delivery_submission_path(submission)
        expect(response).to be_successful
      end

      it 'assigns a show presenter' do
        get learning_delivery_submission_path(submission)
        expect(assigns(:presenter)).to be_a(Submissions::ShowPresenter)
      end

      it 'initializes the presenter with the correct parameters' do
        allow(Submissions::ShowPresenter).to receive(:new).and_call_original

        get learning_delivery_submission_path(submission, params)

        expect(Submissions::ShowPresenter).to have_received(:new).with(
          submission:,
          params: instance_of(ActionController::Parameters),
          current_employee: nil,
        )
      end

      context 'when admin_user has an associated learning_delivery_employee' do
        let(:learning_delivery_employee) { create(:learning_delivery_employee) }

        before do
          admin_user.update!(learning_delivery_employee:)
        end

        it 'initializes the presenter with the current_employee' do
          allow(Submissions::ShowPresenter).to receive(:new).and_call_original
          get learning_delivery_submission_path(submission)
          expect(Submissions::ShowPresenter).to have_received(:new).with(
            submission:,
            params: instance_of(ActionController::Parameters),
            current_employee: learning_delivery_employee,
          )
        end
      end

      context 'with parameters' do
        it 'passes permitted parameters to the presenter' do
          get learning_delivery_submission_path(submission, params)
          expect(assigns(:presenter).params[:review_status]).to eq(review_status)
        end
      end
    end

    describe 'POST #publish' do
      let(:submission) { create(:lms_submission) }
      let(:mark_as_reviewed_command) { instance_double(Lms::Submission::MarkAsManuallyGradedCommand) }

      before do
        allow(Lms::Submission::MarkAsManuallyGradedCommand).to receive(:new).and_return(mark_as_reviewed_command)
        allow(mark_as_reviewed_command).to receive(:call!)
      end

      context 'when the job is successfully enqueued' do
        before do
          allow(Lms::Submission::PublishJob).to receive(:perform_async).and_return(true)
        end

        it 'enqueues the publish job with the correct parameters' do
          post publish_learning_delivery_submission_path(submission)

          expect(Lms::Submission::MarkAsManuallyGradedCommand).to have_received(:new).with(
            submission:,
            current_user: admin_user,
          )
          expect(mark_as_reviewed_command).to have_received(:call!)
          expect(Lms::Submission::PublishJob).to have_received(:perform_async).with(
            submission.id.to_s,
          )
        end

        it 'returns a success response' do
          post publish_learning_delivery_submission_path(submission)
          expect(response).to be_successful
        end

        it 'returns a JSON response with success status' do
          post publish_learning_delivery_submission_path(submission)
          json_response = response.parsed_body
          expect(json_response['status']).to eq('success')
          expect(json_response['message']).to eq('Successfully published to Canvas')
        end
      end

      context 'when an error occurs during job enqueuing' do
        let(:error_message) { 'Error publishing to remote' }

        before do
          allow(Lms::Submission::PublishJob).to receive(:perform_async).and_raise(StandardError, error_message)
        end

        it 'returns an unprocessable entity status' do
          post publish_learning_delivery_submission_path(submission)
          expect(response).to have_http_status(:unprocessable_content)
        end

        it 'returns a JSON response with error status and messages' do
          post publish_learning_delivery_submission_path(submission)
          json_response = response.parsed_body
          expect(json_response['status']).to eq('error')
          expect(json_response['errors']).to eq([error_message])
        end
      end
    end
  end
end
