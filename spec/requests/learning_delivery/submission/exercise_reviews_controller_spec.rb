# frozen_string_literal: true

require 'rails_helper'

module LearningDelivery
  module Submission
    describe ExerciseReviewsController do
      let(:admin_user) do
        create(:admin_user).tap do |u|
          u.permissions << create(:permission, resource_group: 'LMS', level: :rwd)
        end
      end

      let(:exercise_review) { create(:lms_submission_exercise_review) }
      let(:submission_id) { exercise_review.submission.id }
      let(:command) { instance_double(Lms::Submission::ExerciseReview::UpdateCommand) }
      let(:grade) { '3' }
      let(:comment) { 'Great work!' }
      let(:state) { 'graded' }
      let(:exercise_review_params) do
        {
          grade:,
          comment:,
          state:,
          mark_as_reviewed: true,
        }
      end

      before do
        host!(RequestHelper.new.host(:learning_delivery))
        sign_in admin_user
        allow(Lms::Submission::ExerciseReview::UpdateCommand).to receive(:new).and_return(command)
      end

      describe 'PATCH #update' do
        context 'when the command succeeds' do
          before do
            allow(command).to receive(:call!)
            allow(command).to receive(:exercise_review).and_return(
              exercise_review.tap do |er|
                er.grade = grade.to_i
                er.comment = comment
                er.state = state
                er.manually_reviewed_at = Time.current
                er.manually_reviewed_by_id = admin_user.id
              end,
            )
          end

          it 'returns a success response' do
            patch learning_delivery_submission_submission_exercise_review_path(submission_id:, id: exercise_review.id),
              params: { exercise_review: exercise_review_params }, as: :turbo_stream

            expect(response).to be_successful
          end

          it 'initializes the command with the correct parameters' do
            called_args = nil
            allow(Lms::Submission::ExerciseReview::UpdateCommand).to receive(:new) do |arg|
              called_args = arg
              command
            end

            patch learning_delivery_submission_submission_exercise_review_path(submission_id:, id: exercise_review.id),
              params: { exercise_review: exercise_review_params }, as: :turbo_stream

            expect(called_args[:exercise_review]).to eq(exercise_review)
            expect(called_args[:current_user]).to eq(admin_user)
            expect(called_args[:attributes]).to be_a(ActionController::Parameters)
            expect(called_args[:attributes].to_h).to include(
              "grade" => grade.to_i,
              "comment" => comment,
              "mark_as_reviewed" => "true",
            )
          end
        end

        context 'when the command fails' do
          let(:error_message) { 'Error updating exercise review' }

          before do
            allow(command).to receive(:call!).and_raise(StandardError, error_message)
          end

          it 'returns an unprocessable entity status' do
            patch learning_delivery_submission_submission_exercise_review_path(submission_id:, id: exercise_review.id),
              params: { exercise_review: exercise_review_params }, as: :turbo_stream
            expect(response).to have_http_status(:unprocessable_content)
          end

          it 'returns a JSON response with error status and messages' do
            patch learning_delivery_submission_submission_exercise_review_path(submission_id:, id: exercise_review.id),
              params: { exercise_review: exercise_review_params }, as: :turbo_stream

            json_response = response.parsed_body
            expect(json_response['status']).to eq('error')
            expect(json_response['errors']).to eq([error_message])
          end
        end
      end
    end
  end
end
