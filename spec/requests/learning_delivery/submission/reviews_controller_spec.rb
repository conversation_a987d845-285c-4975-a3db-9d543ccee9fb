# frozen_string_literal: true

require 'rails_helper'

module LearningDelivery
  module Submission
    describe ReviewsController do
      let(:admin_user) do
        create(:admin_user).tap do |u|
          u.permissions << create(:permission, resource_group: 'LMS', level: :rwd)
        end
      end

      let(:review) { create(:lms_submission_review) }
      let(:submission_id) { review.submission.id }
      let(:comment) { 'Great work!' }
      let(:command) { instance_double(Lms::Submission::Review::ReplaceCommand) }
      let(:review_params) do
        {
          grade: '1',
          comment:,
        }
      end


      before do
        host!(RequestHelper.new.host(:learning_delivery))
        sign_in admin_user
        allow(Lms::Submission::Review::ReplaceCommand).to receive(:new).and_return(command)
      end

      describe 'PATCH #update' do
        let(:update_path) { learning_delivery_submission_submission_review_path(id: review.id, submission_id:) }

        context 'when the command succeeds' do
          before do
            allow(command).to receive(:call!)
            allow(command).to receive(:review).and_return(
              review.tap do |r|
                r.grade = 'complete'
                r.comment = 'Excellent submission!'
                r.state = 'graded'
              end,
            )
          end

          it 'returns a success response' do
            patch update_path, params: { review: review_params }, as: :turbo_stream
            expect(response).to be_successful
          end

          it 'initializes the command with the correct parameters' do
            processed_review_params = {
              grade: 1,
              comment:,
            }
            patch update_path, params: { review: review_params }, as: :turbo_stream

            expect(Lms::Submission::Review::ReplaceCommand).to have_received(:new).with(
              review:,
              attributes: hash_including(processed_review_params.stringify_keys),
              current_user: admin_user,
            )
          end
        end

        context 'when the command fails' do
          let(:error_message) { 'Error updating review' }

          before do
            allow(command).to receive(:call!).and_raise(StandardError, error_message)
          end

          it 'returns an unprocessable entity status' do
            patch update_path, params: { review: review_params }, as: :turbo_stream
            expect(response).to have_http_status(:unprocessable_content)
          end
        end
      end
    end
  end
end
