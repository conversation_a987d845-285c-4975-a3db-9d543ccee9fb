# frozen_string_literal: true

require 'rails_helper'

module Site
  describe SyllabusRequestsController do
    describe 'POST #create' do
      before do
        host!(RequestHelper.new.host(:site))
        class_spy(SyllabusRequest::SubmitHubspotFormJob, perform_async: nil).as_stubbed_const
      end

      let!(:scenario) { LandingScenario.create }
      let(:partner_program) { scenario.partner_program }

      context 'with valid params' do
        let(:params) do
          {
            syllabus_request: {
              first_name: '<PERSON>',
              phone: '************',
              site_ad_tracking_attributes: {
                fbclid: '123',
              },
            },
            email_address: '<EMAIL>',
            partner_program_uid: partner_program.uid,
          }
        end

        it 'creates a new syllabus request and queues Hubspot jobs' do
          expect { post('/syllabus_requests', params:) }.to change { SyllabusRequest.count }.by(1)

          syllabus_request = SyllabusRequest.last
          expect(syllabus_request).to have_attributes(first_name: '<PERSON>')
          expect(syllabus_request.email.address).to eq('<EMAIL>')
          expect(syllabus_request.phone.local_number).to eq('(*************')
          expect(syllabus_request.site_ad_tracking.fbclid).to eq('123')
          expect(response).to redirect_to(site_syllabus_path(partner_identifier: partner_program.partner, program_slug: partner_program.program))

          # Verify the background job is queued with correct parameters
          expect(SyllabusRequest::SubmitHubspotFormJob).to have_received(:perform_async).with(
            syllabus_request.id,
            {
              "hutk" => nil,
              "ipAddress" => '127.0.0.1',
              "pageName" => 'Syllabus Request',
              "pageUri" => 'http://upskill.local.ziplines.dev/syllabus_requests?fbclid=123',
            },
            true,
          )
        end

        context 'with referrer cookie' do
          let!(:referrer) { create(:promos_referrer) }

          before do
            allow_any_instance_of(described_class).to receive(:captured_referrer).and_return(referrer)
          end

          it 'associates the syllabus request with the referrer' do
            expect { post('/syllabus_requests', params:) }.to change { SyllabusRequest.count }.by(1)

            syllabus_request = SyllabusRequest.last
            expect(syllabus_request.promos_referrer).to eq(referrer)
          end
        end

        context 'when an invalid authenticity token failure occurs' do
          before do
            allow_any_instance_of(described_class).to receive(:verified_request?).and_return(false)
          end

          it 'redirects back with flash error message' do
            post('/syllabus_requests', params:)

            expect(response).to redirect_to(root_path)
            expect(flash[:error]).to eq('There was an issue with your submission, please try again.')
          end
        end
      end

      context 'when a bot is detected' do
        let(:params) do
          {
            syllabus_request: {
              first_name: 'John',
              phone: '************',
              site_ad_tracking_attributes: {
                fbclid: '123',
              },
              hp_key: 'fake',
            },
            email_address: '<EMAIL>',
            partner_program_uid: partner_program.uid,
          }
        end

        it 'does not create a syllabus request' do
          expect { post('/syllabus_requests', params:) }.not_to(change { SyllabusRequest.count })

          expect(response).to redirect_to(site_syllabus_path(partner_identifier: partner_program.partner, program_slug: partner_program.program))
        end
      end
    end
  end
end
