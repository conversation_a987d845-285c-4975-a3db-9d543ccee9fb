# frozen_string_literal: true

require 'rails_helper'

module Site
  describe SupportController do
    before do
      host!(RequestHelper.new.host(:site))
    end

    let(:scenario) do
      SqueezeScenario.create(
        site_partner_program_dataset_count:,
        site_partner_dataset_count:,
        partner_theme_count:,
        program_theme_count:,
        site_squeeze_page_template_count:,
        site_program_dataset_count:,
      )
    end
    let(:site_partner_program_dataset_count) { 1 }
    let(:site_partner_dataset_count) { 1 }
    let(:partner_theme_count) { 1 }
    let(:program_theme_count) { 1 }
    let(:site_squeeze_page_template_count) { 1 }
    let(:site_program_dataset_count) { 1 }
    let!(:partner) { scenario.partner }
    let!(:program) { scenario.program }
    let!(:partner_program) { scenario.partner_program }

    let(:mock_support_articles) do
      [
        {
          id: 123,
          title: 'Test Support Article 1',
          body: '<p>Test content 1 with {REIMBURSEMENT_URL}</p>',
          position: 1,
          url: 'https://example.zendesk.com/article/123',
          updated_at: '2024-01-01T12:00:00Z',
        },
        {
          id: 124,
          title: 'Test Support Article 2',
          body: '<p>Test content 2</p>',
          position: 2,
          url: 'https://example.zendesk.com/article/124',
          updated_at: '2024-01-01T13:00:00Z',
        },
      ]
    end

    def setup_valid_partner_program_routing
      allow_any_instance_of(described_class).to receive(:partner_program_from_routing_params)
        .and_return(partner_program)
      allow_any_instance_of(described_class).to receive(:set_time_zone)
      allow(partner_program).to receive_messages(
        configured_for_site?: true,
        time_zone: 'UTC',
      )
      allow_any_instance_of(Zendesk::FilterAndFormatArticlesCommand)
        .to receive(:content_tag_mappings).and_return({})
    end

    shared_examples 'successful support page response' do
      it 'returns successful response with presenter' do
        get "/#{partner.slug}/#{program.slug}/support"

        expect(response).to have_http_status(:ok)
        expect(response.content_type).to include('text/html')
        expect(assigns(:presenter)).to be_a(Support::ShowPresenter)
        expect(assigns(:presenter).partner_program).to eq(partner_program)
      end
    end

    describe 'GET #show' do
      before do
        allow(Setting).to receive(:value_for).and_return('enabled')
      end

      context 'with valid partner/program routing' do
        before do
          setup_valid_partner_program_routing
          allow(Zendesk::FetchArticlesCommand).to receive(:call!).and_return(mock_support_articles)
        end

        it_behaves_like 'successful support page response'

        it 'calls FetchArticlesCommand with correct parameters' do
          get "/#{partner.slug}/#{program.slug}/support"

          assigns(:presenter).articles

          expect(Zendesk::FetchArticlesCommand).to have_received(:call!).with(
            partner_program:,
          )
        end

        it 'handles content_tags parameter' do
          get "/#{partner.slug}/#{program.slug}/support", params: { content_tags: 'pricing,certifications' }

          presenter = assigns(:presenter)
          expect(presenter.content_tags).to eq('pricing,certifications')
        end

        it 'handles custom section_id parameter' do
          get "/#{partner.slug}/#{program.slug}/support", params: { section_id: '12345' }

          presenter = assigns(:presenter)
          expect(presenter.section_id).to eq('12345')
        end

        it 'passes parameters to presenter correctly' do
          get "/#{partner.slug}/#{program.slug}/support",
            params: { section_id: '12345', content_tags: 'pricing,certifications' }

          presenter = assigns(:presenter)
          expect(presenter.section_id).to eq('12345')
          expect(presenter.content_tags).to eq('pricing,certifications')
        end
      end

      context 'with partner program not found' do
        before do
          allow_any_instance_of(described_class).to receive(:partner_program_from_routing_params)
            .and_raise(Site::PartnerProgramRouting::ViewablePartnerProgramNotFound)
        end

        it 'redirects with alert for invalid partner' do
          get "/invalid-partner/invalid-program/support"

          expect(response).to have_http_status(:found)
          expect(response).to redirect_to(root_path)
        end
      end

      context 'with Zendesk API error' do
        before do
          setup_valid_partner_program_routing
          allow(Zendesk::FetchArticlesCommand).to receive(:call!)
            .and_raise(StandardError.new("API Connection Failed"))
        end

        it 'redirects with alert for API errors' do
          allow(Rails.error).to receive(:report)

          allow(Support::ShowPresenter).to receive(:new).and_raise(StandardError.new("API Connection Failed"))

          get "/#{partner.slug}/#{program.slug}/support"

          expect(Rails.error).to have_received(:report).with(
            kind_of(StandardError),
            context: { action: 'support_fetch' },
          )
          expect(response).to have_http_status(:found)
          expect(response).to redirect_to(root_path)
        end
      end

      context 'with empty support results' do
        before do
          setup_valid_partner_program_routing
          allow(Zendesk::FetchArticlesCommand).to receive(:call!).and_return([])
        end

        it 'renders page with empty articles' do
          get "/#{partner.slug}/#{program.slug}/support"

          expect(response).to have_http_status(:ok)
          expect(assigns(:presenter).articles).to eq([])
          expect(assigns(:presenter).total_articles_count).to eq(0)
        end
      end

      context 'presenter validation' do
        before do
          setup_valid_partner_program_routing
          allow(Zendesk::FetchArticlesCommand).to receive(:call!).and_return(mock_support_articles)
        end

        it 'creates presenter with proper data structure' do
          get "/#{partner.slug}/#{program.slug}/support"

          presenter = assigns(:presenter)
          expect(presenter).to be_a(Support::ShowPresenter)
          expect(presenter.articles).to be_an(Array)
          expect(presenter.articles.size).to eq(2)
          expect(presenter.partner_program).to eq(partner_program)
          expect(presenter.total_articles_count).to eq(2)
          expect(presenter.articles?).to be(true)
        end
      end
    end
  end
end
