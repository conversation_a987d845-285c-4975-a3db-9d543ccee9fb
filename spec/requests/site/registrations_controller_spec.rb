# frozen_string_literal: true

require 'rails_helper'

module Site
  describe RegistrationsController do
    describe 'POST #create' do
      before do
        host!(RequestHelper.new.host(:site))
        class_spy(Registration::ReplaceCommand::SubmitHubspotFormJob, perform_async: nil).as_stubbed_const
        class_spy(Registration::ReplaceCommand::AssignDealJob, perform_async: nil).as_stubbed_const
      end

      let(:scenario) { LandingScenario.create }
      let(:partner) { scenario.partner }
      let(:partner_program) { scenario.partner_program }
      let(:program) { scenario.program }
      let!(:section) { scenario.section }

      let(:valid_params) do
        {
          registration: {
            first_name: "<PERSON>",
            last_name: "<PERSON><PERSON><PERSON><PERSON>",
            email_address: "<EMAIL>",
            phone: "(*************",
            experience_level: "newbie",
            aspiration: "begin",
            site_ad_tracking_attributes: {
              fbclid: '123',
            },
          },
          partner_program_uid: partner_program.uid,
          section_uid: section.uid,
        }
      end

      context 'with valid params' do
        it 'creates a new registration and order' do
          expect { post('/registrations', params: valid_params) }.to change { Registration.count }.by(1)

          expect(response.location).to be_starts_with(new_site_payments_url(order_id: ''))

          registration = Registration.last
          expect(registration).to have_attributes(
            experience_level: "newbie",
            aspiration: "begin",
            partner_program:,
            section:,
            status: "confirmed",
          )
          expect(registration.learner).to have_attributes(
            first_name: "Philippe",
            last_name: "Huibonhoa",
          )

          expect(registration.email).to have_attributes(
            address: "<EMAIL>",
          )

          expect(registration.site_ad_tracking.fbclid).to eq('123')

          expect(registration.learner.phone.local_number).to eq("(*************")

          order = registration.ecom_order_item.order
          expect(order).to have_attributes(
            status: 'cart',
            learner: registration.learner,
            partner:,
          )

          # Verify the background job is queued with correct parameters
          expect(Registration::ReplaceCommand::SubmitHubspotFormJob).to have_received(:perform_async).with(
            registration.id,
            {
              "hutk" => nil,
              "ipAddress" => "127.0.0.1",
              "pageName" => "Registration",
              "pageUri" => "http://upskill.local.ziplines.dev/registrations?fbclid=123",
            },
          )
          expect(Registration::ReplaceCommand::AssignDealJob).to have_received(:perform_async).with(registration.id, true)
        end

        context 'with referrer cookie' do
          let!(:referrer) { create(:promos_referrer) }

          before do
            allow_any_instance_of(described_class).to receive(:captured_referrer).and_return(referrer)
          end

          it 'associates the registration with the referrer' do
            expect { post('/registrations', params: valid_params) }.to change { Registration.count }.by(1)

            registration = Registration.last
            expect(registration.promos_referrer).to eq(referrer)
          end

          context 'when there is a Setting for :referral_discount_code for a valid discount' do
            let(:code) { 'TESTCODE' }
            let!(:promo_code) { create(:promos_discount_code, :enabled, humanized_code: code) }
            let!(:setting) { create(:setting, name: 'Referral Discount Code', value: { code: }) }

            it 'applies the discount code to the registration order' do
              expect { post('/registrations', params: valid_params) }.to change { Registration.count }.by(1)

              registration = Registration.last
              expect(registration.promos_referrer).to eq(referrer)
              expect(registration.ecom_order.promos_discount_codes).to include(promo_code)
            end
          end

          context 'when there is a Setting for :referral_discount_code for an invalid discount' do
            let!(:setting) { create(:setting, name: 'Referral Discount Code', value: { code: 'INVALID' }) }

            it 'still creates the registration order' do
              expect { post('/registrations', params: valid_params) }.to change { Registration.count }.by(1)

              registration = Registration.last
              expect(registration.promos_referrer).to eq(referrer)
              expect(registration.ecom_order.promos_discount_codes).to be_empty
            end
          end
        end

        context 'with a proxy_host' do
          before do
            partner.update!(proxy_host: 'example.com', dns_status: :connected)
          end

          it 'creates a new registration and order' do
            expect { post('/registrations', params: valid_params) }.to change { Registration.count }.by(1)

            expect(response.location).to be_starts_with("http://example.com#{new_site_payments_path(order_id: '')}")

            registration = Registration.last
            expect(registration).to have_attributes(
              experience_level: "newbie",
              aspiration: "begin",
              partner_program:,
              section:,
              status: "confirmed",
            )
            expect(registration.learner).to have_attributes(
              first_name: "Philippe",
              last_name: "Huibonhoa",
            )

            expect(registration.email).to have_attributes(
              address: "<EMAIL>",
            )

            expect(registration.site_ad_tracking.fbclid).to eq('123')

            expect(registration.learner.phone.local_number).to eq("(*************")

            order = registration.ecom_order_item.order
            expect(order).to have_attributes(
              status: 'cart',
              learner: registration.learner,
              partner:,
            )

            expect(Registration::ReplaceCommand::SubmitHubspotFormJob).to have_received(:perform_async).with(
              registration.id,
              {
                "hutk" => nil,
                "ipAddress" => "127.0.0.1",
                "pageName" => "Registration",
                "pageUri" => "http://upskill.local.ziplines.dev/registrations?fbclid=123",
              },
            )
            expect(Registration::ReplaceCommand::AssignDealJob).to have_received(:perform_async).with(registration.id, true)
          end
        end
      end

      context 'if discount code was previously saved' do
        context 'with valid code' do
          let!(:discount_code) { create(:promos_discount_code) }

          it 'creates a new registration and order with discount applied' do
            get("/#{program.slug}/#{partner.slug}?key=#{discount_code.humanized_code}")

            expect { post('/registrations', params: valid_params) }.to change { Registration.count }.by(1)

            expect(response.location).to be_starts_with(new_site_payments_url(order_id: ''))

            registration = Registration.last
            expect(registration).to have_attributes(
              experience_level: "newbie",
              aspiration: "begin",
              partner_program:,
              section:,
              status: "confirmed",
            )

            order = registration.ecom_order_item.order
            expect(order).to have_attributes(
              status: 'cart',
              learner: registration.learner,
              partner:,
            )

            order = registration.ecom_order
            expect(order.promos_discount_codes.first).to eq(discount_code)

            # Verify the background job is queued with correct parameters
            expect(Registration::ReplaceCommand::SubmitHubspotFormJob).to have_received(:perform_async).with(
              registration.id,
              {
                "hutk" => nil,
                "ipAddress" => "127.0.0.1",
                "pageName" => "Registration",
                "pageUri" => "http://upskill.local.ziplines.dev/registrations?fbclid=123",
              },
            )
            expect(Registration::ReplaceCommand::AssignDealJob).to have_received(:perform_async).with(registration.id, true)

            # it deletes the discount code from the session
            expect(session[:discount_code_code]).to be_nil
          end
        end

        context 'with an invalid code' do
          let!(:discount_code) do
            create(:promos_discount_code,
              discount_conditions: [create(:promos_discount_condition, :date_range, starts_on: 10.days.ago, ends_on: 5.days.ago)],
            )
          end

          let!(:error_reporter) { class_spy(ErrorReporter, report: nil).as_stubbed_const }

          it 'creates a new registration and order without discount applied, but reports an warning' do
            get("/#{program.slug}/#{partner.slug}?key=#{discount_code.humanized_code}")

            expect { post('/registrations', params: valid_params) }.to change { Registration.count }.by(1)

            expect(response.location).to be_starts_with(new_site_payments_url(order_id: ''))

            registration = Registration.last
            expect(registration).to have_attributes(
              experience_level: "newbie",
              aspiration: "begin",
              partner_program:,
              section:,
              status: "confirmed",
            )

            order = registration.ecom_order_item.order
            expect(order).to have_attributes(
              status: 'cart',
              learner: registration.learner,
              partner:,
            )

            # Verify the background job is queued with correct parameters
            expect(Registration::ReplaceCommand::SubmitHubspotFormJob).to have_received(:perform_async).with(
              registration.id,
              {
                "hutk" => nil,
                "ipAddress" => "127.0.0.1",
                "pageName" => "Registration",
                "pageUri" => "http://upskill.local.ziplines.dev/registrations?fbclid=123",
              },
            )
            expect(Registration::ReplaceCommand::AssignDealJob).to have_received(:perform_async).with(registration.id, true)

            expect(session[:discount_code_code]).to be_nil


            order = registration.ecom_order
            expect(order.promos_discount_codes).to be_empty

            expect(error_reporter).to have_received(:report)
          end
        end
      end

      context 'relationship passed' do
        let!(:relationship) { create(:finance_relationship, partner:) }
        let(:params) { valid_params.merge(finance_relationship_id: relationship.id) }

        it 'creates a new registration tied to the given relationship' do
          expect { post('/registrations', params:) }.to change { Registration.count }.by(1)

          expect(response.location).to be_starts_with(new_site_payments_url(order_id: ''))

          registration = Registration.last
          expect(registration.finance_relationship).to eq(relationship)
        end
      end

      context 'with eva_type passed in' do
        context 'with a valid value' do
          let(:scenario) { LandingScenario.create(partner_args: [:eva_enabled]) }

          let(:eva_type) { Registration.eva_types.keys.first }
          let(:params) { valid_params.deep_merge(registration: { eva_type: }) }

          it 'creates a new registration with the given eva_type' do
            expect { post('/registrations', params:) }.to change { Registration.count }.by(1)

            expect(response.location).to be_starts_with(new_site_payments_url(order_id: ''))

            registration = Registration.last
            expect(registration).to have_attributes(
              experience_level: "newbie",
              aspiration: "begin",
              partner_program:,
              section:,
              eva_type:,
              status: "confirmed",
            )
          end
        end

        context "with a blank value" do
          let(:scenario) { LandingScenario.create(partner_args: [:eva_enabled]) }

          let(:params) { valid_params.deep_merge(registration: { eva_type: '' }) }

          it 'creates a new registration with a nil eva_type' do
            expect { post('/registrations', params:) }.to change { Registration.count }.by(1)

            expect(response.location).to be_starts_with(new_site_payments_url(order_id: ''))

            registration = Registration.last
            expect(registration).to have_attributes(
              experience_level: "newbie",
              aspiration: "begin",
              partner_program:,
              section:,
              eva_type: nil,
              status: "confirmed",
            )
          end
        end

        context 'when partner does not have eva_enabled' do
          let(:eva_type) { Registration.eva_types.keys.first }
          let(:params) { valid_params.deep_merge(registration: { eva_type: }) }

          it 'raises' do
            expect { post('/registrations', params:) }.not_to(change { Registration.count })

            expect(response).to have_http_status(:unprocessable_content)
          end
        end
      end

      context 'when an invalid authenticity token failure occurs' do
        before do
          allow_any_instance_of(described_class).to receive(:verified_request?).and_return(false)
        end

        context 'with referrer from same host' do
          let(:referrer_url) { 'http://upskill.local.ziplines.dev/some-page' }

          it 'redirects back to referrer with flash error message' do
            post(
              '/registrations',
              params: valid_params,
              headers: { 'HTTP_REFERER' => referrer_url },
            )

            expect(response).to redirect_to(referrer_url)
            expect(flash[:error]).to eq('There was an issue with your submission, please try again.')
          end
        end

        context 'without referrer' do
          before do
            allow_any_instance_of(ActionDispatch::Request).to receive(:referer).and_return(nil)
          end

          context 'with partner program params' do
            it 'redirects to registration page built by UrlBuilder with flash error message' do
              post('/registrations', params: valid_params)

              expect(response).to redirect_to(
                new_site_registrations_path(
                  partner_identifier: partner.slug,
                  program_slug: program.slug,
                ),
              )
              expect(flash[:error]).to eq('There was an issue with your submission, please try again.')
            end
          end

          context 'when partner_program_from_routing_params fails' do
            before do
              allow_any_instance_of(described_class).to receive(:partner_program_from_routing_params).and_raise(
                Site::PartnerProgramRouting::ViewablePartnerProgramNotFound,
              )
            end

            it 'redirects to root path with flash error message' do
              post('/registrations', params: valid_params)

              expect(response).to redirect_to(root_path)
              expect(flash[:error]).to eq('There was an issue with your submission, please try again.')
            end
          end
        end
      end
    end

    describe 'GET #redirect' do
      before do
        host!(RequestHelper.new.host(:site))
      end

      let(:scenario) { LandingScenario.create }
      let(:partner) { scenario.partner }
      let(:program) { scenario.program }
      let(:partner_program) { scenario.partner_program }

      context 'with valid params' do
        it 'redirects to the registration page' do
          get "/#{partner.slug}/#{program.slug}-enrollment"

          expect(response).to have_http_status(:moved_permanently)
          expect(response).to redirect_to(
            UrlBuilder::Site.new(partner_program:).call!(template: :registration),
          )
        end

        it 'forwards URL parameters to the redirect destination' do
          get "/#{partner.slug}/#{program.slug}-enrollment?utm_source=test&utm_medium=email&utm_campaign=campaign"

          expect(response).to have_http_status(:moved_permanently)
          expect(response).to redirect_to(
            UrlBuilder::Site.new(partner_program:).call!(
              template: :registration,
              utm_source: 'test',
              utm_medium: 'email',
              utm_campaign: 'campaign',
            ),
          )
        end

        it 'forwards multiple URL parameters to the redirect destination' do
          get "/#{partner.slug}/#{program.slug}-enrollment?utm_source=test&referral_code=ABC123&discount=SAVE10"

          expect(response).to have_http_status(:moved_permanently)
          expect(response).to redirect_to(
            UrlBuilder::Site.new(partner_program:).call!(
              template: :registration,
              utm_source: 'test',
              referral_code: 'ABC123',
              discount: 'SAVE10',
            ),
          )
        end
      end

      context 'with a proxy_host' do
        before do
          partner.update!(proxy_host: 'example.com', dns_status: :connected)
        end

        let(:headers) { { 'HTTP_PARTNER_PROXY_HOST' => partner.proxy_host } }

        it 'redirects to the registration page with the proxy host' do
          get("/#{program.slug}-enrollment", headers:)

          expect(response).to have_http_status(:moved_permanently)
          expect(response).to redirect_to(
            UrlBuilder::Site.new(partner_program:).call!(template: :registration),
          )
        end

        it 'forwards URL parameters to the redirect destination with proxy host' do
          get("/#{program.slug}-enrollment?utm_source=test&utm_medium=email", headers:)

          expect(response).to have_http_status(:moved_permanently)
          expect(response).to redirect_to(
            UrlBuilder::Site.new(partner_program:).call!(
              template: :registration,
              utm_source: 'test',
              utm_medium: 'email',
            ),
          )
        end

        context 'when the partner is inactive but has configured alternatives' do
          let(:inactive_partner) do
            create(:partner,
              name: 'Inactive Partner',
              slug: 'inactive-partner',
              proxy_host: 'inactive.example.com',
              theme_count: 1,
              site_partner_dataset_count: 1,
              status: :inactive,
            )
          end

          let(:alternative_partner) do
            create(:partner,
              name: 'Alternative Partner',
              slug: 'alternative-partner',
              proxy_host: 'alternative.example.com',
              theme_count: 1,
              site_partner_dataset_count: 1,
            )
          end

          let!(:alt_program) { create(:program, slug: 'alt-program', site_program_dataset_count: 1, cohort_count: 1) }

          let!(:alt_partner_program) do
            create(:partner_program,
              partner: alternative_partner,
              program: alt_program,
              site_partner_program_dataset_count: 1,
            )
          end

          let(:inactive_headers) { { 'HTTP_PARTNER_PROXY_HOST' => inactive_partner.proxy_host } }

          let(:expected_url) { 'http://upskill.local.ziplines.dev/alternative-partner/alt-program/registrations/new' }
          let(:expected_url_with_params) { 'http://upskill.local.ziplines.dev/alternative-partner/alt-program/registrations/new?utm_medium=email&utm_source=test' }

          before do
            allow_any_instance_of(described_class).to receive(:partners_from_routing_params).and_return([])
            allow(Partner::AlternativesForInactivePartnerCommand).to receive(:call!).with(any_args).and_return([alternative_partner])
            allow_any_instance_of(described_class).to receive(:partner_program_from_routing_params).and_return(alt_partner_program)
          end

          it 'redirects to the alternative partner program registration page' do
            get proxy_host_new_site_registrations_path(program_slug: program.slug), headers: inactive_headers

            expect(response).to have_http_status(:moved_permanently)
            expect(response).to redirect_to(expected_url)
          end

          it 'forwards URL parameters to the alternative partner registration page' do
            params = { program_slug: program.slug, utm_source: 'test', utm_medium: 'email' }
            get proxy_host_new_site_registrations_path(params), headers: inactive_headers

            expect(response).to have_http_status(:moved_permanently)
            expect(response).to redirect_to(expected_url_with_params)
          end
        end
      end
    end
  end
end
