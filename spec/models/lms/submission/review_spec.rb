# frozen_string_literal: true

# == Schema Information
#
# Table name: lms_submission_reviews
#
#  id                                                 :bigint           not null, primary key
#  attempt                                            :integer          default(0)
#  auto_graded_at                                     :datetime
#  auto_grader_grade(Grade assigned by the AI Grader) :integer
#  comment                                            :text
#  grade                                              :integer
#  graded_at                                          :datetime
#  manually_reviewed_at                               :datetime
#  pdf_generated_at                                   :datetime
#  published_at                                       :datetime
#  score                                              :integer
#  state                                              :integer          default("submitted"), not null
#  training                                           :boolean          default(FALSE), not null
#  uid                                                :string           not null
#  created_at                                         :datetime         not null
#  updated_at                                         :datetime         not null
#  manually_reviewed_by_id(References admin_users)    :bigint
#  submission_id                                      :bigint           not null
#
# Indexes
#
#  index_lms_submission_reviews_on_manually_reviewed_by_id    (manually_reviewed_by_id)
#  index_lms_submission_reviews_on_state                      (state)
#  index_lms_submission_reviews_on_submission_id_and_attempt  (submission_id,attempt) UNIQUE
#  index_lms_submission_reviews_on_training                   (training)
#  index_lms_submission_reviews_on_uid                        (uid) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (manually_reviewed_by_id => admin_users.id)
#  fk_rails_...  (submission_id => lms_submissions.id)
#
require 'rails_helper'

RSpec.describe Lms::Submission::Review do
  it_behaves_like :a_factorified_class

  describe 'state scopes' do
    let!(:submitted_review) { create(:lms_submission_review, :submitted) }
    let!(:pdf_generated_review) { create(:lms_submission_review, :pdf_generated) }
    let!(:auto_graded_review) { create(:lms_submission_review, :auto_graded) }
    let!(:manual_review_needed_review) { create(:lms_submission_review, :manual_review_needed) }
    let!(:graded_review) { create(:lms_submission_review, :graded) }
    let!(:published_review) { create(:lms_submission_review, :published) }
    let!(:stale_review) { create(:lms_submission_review, :stale) }

    describe '.graded' do
      it 'returns only graded reviews' do
        expect(described_class.graded).to contain_exactly(graded_review)
      end
    end

    describe '.pdf_generated' do
      it 'returns only pdf generated reviews' do
        expect(described_class.pdf_generated).to contain_exactly(pdf_generated_review)
      end
    end

    describe '.auto_graded' do
      it 'returns only auto graded reviews' do
        expect(described_class.auto_graded).to contain_exactly(auto_graded_review)
      end
    end

    describe '.manual_review_needed' do
      it 'returns only manuel review needed reviews' do
        expect(described_class.manual_review_needed).to contain_exactly(manual_review_needed_review)
      end
    end

    describe '.submitted' do
      it 'returns only submitted reviews' do
        expect(described_class.submitted).to contain_exactly(submitted_review)
      end
    end

    describe '.published' do
      it 'returns only published reviews' do
        expect(described_class.published).to contain_exactly(published_review)
      end
    end

    describe '.stale' do
      it 'returns only stale reviews' do
        expect(described_class.stale).to contain_exactly(stale_review)
      end
    end
  end

  describe '.mismatched_grades (scope)' do
    let!(:matching_incomplete) do
      submission = create(:lms_submission, grade: 'incomplete')
      create(:lms_submission_review, :auto_graded, submission:, auto_grader_grade: :incomplete)
    end

    let!(:mismatched_incomplete) do
      submission = create(:lms_submission, grade: 'complete')
      create(:lms_submission_review, :auto_graded, submission:, auto_grader_grade: :incomplete)
    end

    let!(:matching_complete) do
      submission = create(:lms_submission, grade: 'complete')
      create(:lms_submission_review, :auto_graded, submission:, auto_grader_grade: :complete)
    end

    let!(:mismatched_complete) do
      submission = create(:lms_submission, grade: 'incomplete')
      create(:lms_submission_review, :auto_graded, submission:, auto_grader_grade: :complete)
    end

    it 'returns only reviews where submission grade does not match auto_grader_grade' do
      expect(described_class.mismatched_grades).to contain_exactly(mismatched_incomplete, mismatched_complete)
    end
  end

  describe '.auto_grader_overridden (scope)' do
    let!(:review_with_same_grades) { create(:lms_submission_review, :auto_graded, grade: :complete, auto_grader_grade: :complete) }
    let!(:review_with_nil_auto_grade) { create(:lms_submission_review, :auto_graded, grade: :complete, auto_grader_grade: nil) }
    let!(:review_with_nil_grade) { create(:lms_submission_review, :auto_graded, grade: nil, auto_grader_grade: :complete) }
    let!(:review_with_different_grades) { create(:lms_submission_review, :auto_grader_overridden) }

    it 'returns only reviews where grade differs from auto_grader_grade' do
      results = described_class.auto_grader_overridden

      expect(results).to include(review_with_different_grades)
      expect(results).not_to include(review_with_same_grades)
      expect(results).not_to include(review_with_nil_auto_grade)
      expect(results).not_to include(review_with_nil_grade)
    end

    it 'returns an empty collection when no reviews have different grades' do
      review_with_different_grades.destroy

      expect(described_class.auto_grader_overridden).to be_empty
    end
  end

  describe '.auto_grader_accepted (scope)' do
    let!(:review_with_same_grades) { create(:lms_submission_review, :auto_graded, grade: :complete, auto_grader_grade: :complete) }
    let!(:review_with_nil_auto_grade) { create(:lms_submission_review, :auto_graded, grade: :complete, auto_grader_grade: nil) }
    let!(:review_with_nil_grade) { create(:lms_submission_review, :auto_graded, grade: nil, auto_grader_grade: :complete) }
    let!(:review_with_different_grades) { create(:lms_submission_review, :auto_grader_overridden) }

    it 'returns reviews where grade matches auto_grader_grade or auto_grader_grade is nil' do
      results = described_class.auto_grader_accepted

      expect(results).to include(review_with_same_grades)
      expect(results).not_to include(review_with_nil_auto_grade)
      expect(results).not_to include(review_with_nil_grade)
      expect(results).not_to include(review_with_different_grades)
    end
  end

  describe '.auto_grader_blank (scope)' do
    let!(:review_with_auto_grade) { create(:lms_submission_review, :auto_graded, grade: :complete, auto_grader_grade: :complete) }
    let!(:review_with_nil_auto_grade) { create(:lms_submission_review, :auto_graded, grade: :complete, auto_grader_grade: nil) }

    it 'returns only reviews where auto_grader_grade is nil' do
      results = described_class.auto_grader_blank

      expect(results).to include(review_with_nil_auto_grade)
      expect(results).not_to include(review_with_auto_grade)
    end
  end

  describe '.training and .live (scopes)' do
    let!(:training_review) { create(:lms_submission_review, training: true) }
    let!(:live_review) { create(:lms_submission_review, training: false) }

    describe '.training' do
      it 'returns only reviews where training is true' do
        expect(described_class.training).to contain_exactly(training_review)
      end
    end

    describe '.live' do
      it 'returns only reviews where training is false' do
        expect(described_class.live).to contain_exactly(live_review)
      end
    end
  end

  describe '.current (scope)' do
    let!(:submission_attempt_1) { create(:lms_submission, attempt: 1) }
    let!(:submission_attempt_2) { create(:lms_submission, attempt: 2) }
    let!(:current_review_1) { create(:lms_submission_review, submission: submission_attempt_1, attempt: 1) }
    let!(:outdated_review_1) { create(:lms_submission_review, submission: submission_attempt_2, attempt: 1) }
    let!(:current_review_2) { create(:lms_submission_review, submission: submission_attempt_2, attempt: 2) }

    it 'returns only reviews where attempt matches submission attempt' do
      expect(described_class.current).to contain_exactly(current_review_1, current_review_2)
    end
  end

  describe '#submission_export' do
    let(:review) { create(:lms_submission_review, :pdf_generated) }

    it 'has a submission_export attached' do
      expect(review.submission_export).to be_attached
    end

    it 'validates the content type of submission_export' do
      invalid_pdf_review = build(:lms_submission_review)
      invalid_pdf_review.submission_export.attach(
        io: StringIO.new('Invalid content'),
        filename: 'invalid.txt',
        content_type: 'text/plain',
      )
      expect(invalid_pdf_review).not_to be_valid
      expect(invalid_pdf_review.errors[:submission_export]).to include('has an invalid content type (authorized content type is PDF)')
    end
  end

  describe '#comments_to_publish' do
    let(:review_comment) { "I'm the prettiest review comment" }
    let(:review) { create(:lms_submission_review, comment: review_comment) }
    let(:combine_comments_command) { instance_double(Lms::Submission::Review::CombineCommentsCommand) }
    let(:combined_comments) { "Combined comments result" }

    before do
      allow(Lms::Submission::Review::CombineCommentsCommand).to receive(:new).with(review:).and_return(combine_comments_command)
      allow(combine_comments_command).to receive(:call!).and_return(combined_comments)
    end

    it 'delegates to CombineCommentsCommand' do
      expect(review.comments_to_publish).to eq(combined_comments)
      expect(Lms::Submission::Review::CombineCommentsCommand).to have_received(:new).with(review:)
      expect(combine_comments_command).to have_received(:call!)
    end
  end

  describe '#current?' do
    context 'when review attempt matches submission attempt' do
      let(:submission) { create(:lms_submission, attempt: 2) }
      let(:review) { create(:lms_submission_review, submission:, attempt: 2) }

      it 'returns true' do
        expect(review.current?).to be true
      end
    end

    context 'when review attempt does not match submission attempt' do
      let(:submission) { create(:lms_submission, attempt: 3) }
      let(:review) { create(:lms_submission_review, submission:, attempt: 2) }

      it 'returns false' do
        expect(review.current?).to be false
      end
    end
  end
end
