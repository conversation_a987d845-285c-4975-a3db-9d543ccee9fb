# frozen_string_literal: true

# == Schema Information
#
# Table name: lms_assignments
#
#  id                      :bigint           not null, primary key
#  due_at                  :datetime
#  grading_type            :integer          not null
#  html_url                :text
#  lock_at                 :datetime
#  name                    :string           not null
#  published               :boolean          default(FALSE), not null
#  required                :boolean          default(FALSE), not null
#  status                  :integer
#  unlock_at               :datetime
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  assignment_template_id  :bigint
#  lms_assignment_group_id :bigint
#  lms_module_id           :bigint
#
# Indexes
#
#  idx_assignments_sorting                           (due_at,id)
#  index_lms_assignments_on_assignment_template_id   (assignment_template_id)
#  index_lms_assignments_on_lms_assignment_group_id  (lms_assignment_group_id)
#  index_lms_assignments_on_lms_module_id            (lms_module_id)
#
# Foreign Keys
#
#  fk_rails_...  (lms_assignment_group_id => lms_assignment_groups.id)
#  fk_rails_...  (lms_module_id => lms_modules.id)
#
require 'rails_helper'

describe Lms::Assignment do
  it_behaves_like :a_factorified_class
end
