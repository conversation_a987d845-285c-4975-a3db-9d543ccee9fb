# frozen_string_literal: true

# == Schema Information
#
# Table name: lms_submissions
#
#  id                :bigint           not null, primary key
#  attempt           :integer
#  grade             :string
#  graded_at         :datetime
#  score             :decimal(, )
#  seconds_late      :integer
#  state             :integer          default("unsubmitted"), not null
#  submission_type   :integer
#  submitted_at      :datetime
#  url               :text
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  enrollment_id     :bigint           not null
#  graded_by_id      :bigint
#  lms_assignment_id :bigint           not null
#
# Indexes
#
#  index_lms_submissions_on_assignment_and_enrollment  (enrollment_id,lms_assignment_id) UNIQUE
#  index_lms_submissions_on_graded_by_id               (graded_by_id)
#  index_lms_submissions_on_lms_assignment_id          (lms_assignment_id)
#
# Foreign Keys
#
#  fk_rails_...  (enrollment_id => enrollments.id)
#  fk_rails_...  (graded_by_id => learning_delivery_employees.id)
#  fk_rails_...  (lms_assignment_id => lms_assignments.id)
#
require 'rails_helper'

module Lms
  describe Submission do
    it_behaves_like :a_factorified_class

    describe 'validations' do
      let(:submission) { build(:lms_submission) }

      it 'is valid with valid attributes' do
        expect(submission).to be_valid
      end

      it 'is invalid without a state' do
        submission.state = nil
        expect(submission).not_to be_valid
        expect(submission.errors[:state]).to include("can't be blank")
      end

      it 'is invalid without a submission_type' do
        submission.submission_type = nil
        expect(submission).not_to be_valid
        expect(submission.errors[:submission_type]).to include("can't be blank")
      end

      it 'is invalid with duplicate assignment and enrollment combination' do
        existing_submission = create(:lms_submission)
        duplicate_submission = build(:lms_submission,
          enrollment: existing_submission.enrollment,
          assignment: existing_submission.assignment,
        )

        expect(duplicate_submission).not_to be_valid
        expect(duplicate_submission.errors[:lms_assignment_id]).to include('has already been taken')
      end
    end

    describe 'scopes' do
      let!(:unsubmitted_submission) { create(:lms_submission) }
      let!(:submitted_submission) { create(:lms_submission, :submitted) }
      let!(:pending_review_submission) { create(:lms_submission, :pending_review) }
      let!(:graded_submission) { create(:lms_submission, :graded) }

      describe '.graded' do
        it 'returns only graded submissions' do
          expect(described_class.graded).to contain_exactly(graded_submission)
        end
      end

      describe '.pending_review' do
        it 'returns only pending review submissions' do
          expect(described_class.pending_review).to contain_exactly(pending_review_submission)
        end
      end

      describe '.submitted' do
        it 'returns only submitted submissions' do
          expect(described_class.submitted).to contain_exactly(submitted_submission)
        end
      end

      describe '.unsubmitted' do
        it 'returns only unsubmitted submissions' do
          expect(described_class.unsubmitted).to contain_exactly(unsubmitted_submission)
        end
      end

      describe '.active' do
        it 'returns submissions that are submitted, pending review, or graded' do
          expect(described_class.active).to contain_exactly(
            submitted_submission,
            pending_review_submission,
            graded_submission,
          )
        end
      end

      describe '.inactive' do
        it 'returns only unsubmitted submissions' do
          expect(described_class.inactive).to contain_exactly(unsubmitted_submission)
        end
      end
    end

    describe '#current_review' do
      let(:current_attempt) { rand(2..5) }
      let!(:submission) { create(:lms_submission, attempt: current_attempt) }
      let!(:review_1) { create(:lms_submission_review, attempt: 1) }
      let!(:current_review) { create(:lms_submission_review, attempt: current_attempt, submission:) }

      it 'returns only the current review' do
        expect(submission.current_review).to eq(current_review)
      end
    end


    describe '#submitted?' do
      subject(:submission) { build(:lms_submission, state:) }

      context 'when state is submitted' do
        let(:state) { :submitted }

        it { is_expected.to be_submitted }
      end

      context 'when state is pending_review' do
        let(:state) { :pending_review }

        it { is_expected.to be_submitted }
      end

      context 'when state is graded' do
        let(:state) { :graded }

        it { is_expected.to be_submitted }
      end

      context 'when state is unsubmitted' do
        let(:state) { :unsubmitted }

        it { is_expected.not_to be_submitted }
      end
    end

    describe "#publishable?" do
      let(:submission) { review.submission }
      let(:review) { create(:lms_submission_review) }

      context "when there is no current_review" do
        let(:submission) { create(:lms_submission, :submitted) }

        it { expect(submission).not_to be_publishable }
      end

      context "when current_review is published" do
        let(:review) { create(:lms_submission_review, :published) }

        it { expect(submission).not_to be_publishable }
      end

      context "when current_review is training" do
        let(:review) { create(:lms_submission_review, training: true) }

        it { expect(submission).not_to be_publishable }
      end

      context "when current_review is not published and not training" do
        let(:review) { create(:lms_submission_review, :graded) }

        it { expect(submission).to be_publishable }
      end
    end
  end
end
