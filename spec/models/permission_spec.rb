# frozen_string_literal: true

# == Schema Information
#
# Table name: permissions
#
#  id             :bigint           not null, primary key
#  level          :integer          not null
#  name           :string           not null
#  resource_group :string           not null
#  resource_type  :string
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  resource_id    :integer
#
# Indexes
#
#  idx_on_resource_group_resource_type_resource_id_70b222d81b  (resource_group,resource_type,resource_id) UNIQUE
#
require 'rails_helper'

describe Permission do
  it_behaves_like :a_factorified_class

  {
    read?: {
      r: true,
      rw: true,
      rwd: true,
    },
    write?: {
      r: false,
      rw: true,
      rwd: true,
    },
    delete?: {
      r: false,
      rw: false,
      rwd: true,
    },
  }.each do |method, levels|
    describe "##{method}" do
      levels.each do |level, expected|
        context "when level is #{level}" do
          let(:permission) { build(:permission, level:) }

          it "returns #{expected}" do
            expect(permission.public_send(method)).to be expected
          end
        end
      end
    end
  end
end
