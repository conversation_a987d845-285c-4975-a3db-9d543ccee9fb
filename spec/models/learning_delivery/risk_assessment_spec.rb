# frozen_string_literal: true

# == Schema Information
#
# Table name: learning_delivery_risk_assessments
#
#  id            :bigint           not null, primary key
#  assessed_at   :datetime         not null
#  risk_details  :jsonb            not null
#  risk_level    :integer          default("on_track"), not null
#  risk_type     :integer          not null
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  enrollment_id :bigint           not null
#
# Indexes
#
#  idx_learning_delivery_risk_assessments_on_enrollment_and_type  (enrollment_id,risk_type) UNIQUE
#  index_learning_delivery_risk_assessments_on_enrollment_id      (enrollment_id)
#
# Foreign Keys
#
#  fk_rails_...  (enrollment_id => enrollments.id) ON DELETE => cascade
#
require 'rails_helper'

RSpec.describe LearningDelivery::RiskAssessment do
  it_behaves_like :a_factorified_class

  describe 'associations' do
    it 'belongs to an enrollment' do
      assessment = build(:learning_delivery_risk_assessment)
      expect(assessment.enrollment).to be_present
    end
  end

  describe 'validations' do
    it 'requires a risk_type' do
      assessment = build(:learning_delivery_risk_assessment, risk_type: nil)
      expect(assessment).not_to be_valid
      expect(assessment.errors[:risk_type]).to include("can't be blank")
    end

    it 'requires a risk_level' do
      assessment = build(:learning_delivery_risk_assessment, risk_level: nil)
      expect(assessment).not_to be_valid
      expect(assessment.errors[:risk_level]).to include("can't be blank")
    end

    it 'validates uniqueness of enrollment_id scoped to risk_type' do
      existing = create(:learning_delivery_risk_assessment)
      duplicate = build(:learning_delivery_risk_assessment, enrollment: existing.enrollment, risk_type: existing.risk_type)
      expect(duplicate).not_to be_valid
      expect(duplicate.errors[:risk_type]).to include('has already been taken')
    end

    it 'requires assessed_at when risk_level changes' do
      assessment = build(:learning_delivery_risk_assessment, risk_level: :low_risk, assessed_at: nil)
      expect(assessment).not_to be_valid
      expect(assessment.errors[:assessed_at]).to include("can't be blank")
    end
  end

  describe 'scopes' do
    let!(:missing_assignments) { create(:learning_delivery_risk_assessment, :missing_assignments) }
    let!(:not_activated) { create(:learning_delivery_risk_assessment, :not_activated) }

    describe 'risk_type scopes' do
      it 'returns risk assessments of the specified type' do
        expect(described_class.missing_assignments).to include(missing_assignments)
        expect(described_class.missing_assignments).not_to include(not_activated)

        expect(described_class.not_activated).to include(not_activated)
        expect(described_class.not_activated).not_to include(missing_assignments)
      end
    end
  end

  describe 'instance methods' do
    describe '#risk_type_name' do
      it 'returns the human-readable risk type name' do
        assessment = build(:learning_delivery_risk_assessment, risk_type: :missing_assignments)
        expect(assessment.risk_type_name).to eq('Missing assignments')
      end
    end
  end
end
