# frozen_string_literal: true

# == Schema Information
#
# Table name: partners
#
#  id                          :bigint           not null, primary key
#  abbreviation                :string
#  certifier                   :integer          default("none"), not null
#  currency_code               :string           default("USD"), not null
#  dns_status                  :integer          default("disabled"), not null
#  ecom_payment_providers      :string           default(["APPLE_PAY", "GOOGLE_PAY"]), not null, is an Array
#  formal_name                 :string           not null
#  name                        :string           not null
#  proxy_host                  :string
#  section_style               :integer          default("commingled"), not null
#  short_name                  :string           not null
#  slug                        :string           not null
#  status                      :integer          default("active"), not null
#  time_zone                   :string           default("UTC"), not null
#  uid                         :string           not null
#  created_at                  :datetime         not null
#  updated_at                  :datetime         not null
#  address_id                  :bigint
#  promos_eva_discount_code_id :bigint
#
# Indexes
#
#  index_partners_on_abbreviation                 (abbreviation) UNIQUE WHERE (abbreviation IS NOT NULL)
#  index_partners_on_address_id                   (address_id)
#  index_partners_on_name                         (name) UNIQUE
#  index_partners_on_promos_eva_discount_code_id  (promos_eva_discount_code_id)
#  index_partners_on_proxy_host                   (proxy_host)
#  index_partners_on_slug                         (slug) UNIQUE
#  index_partners_on_uid                          (uid) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (address_id => addresses.id)
#  fk_rails_...  (promos_eva_discount_code_id => promos_discount_codes.id)
#
require 'rails_helper'

describe Partner do
  it_behaves_like :a_factorified_class

  describe '#validations' do
    describe '#matching_promos_eva_discount_code_currency_code' do
      let(:currency_code) { 'USD' }
      let(:promos_eva_discount_code) { nil }
      let(:partner) { build(:partner, currency_code:, promos_eva_discount_code:) }

      subject(:valid?) { partner.valid? }

      context 'when promos_eva_discount_code is nil' do
        it 'is valid' do
          expect(partner).to be_valid
        end
      end

      context 'when promos_eva_discount_code is present' do
        let(:promos_eva_discount_currency_code) { 'USD' }
        let(:promos_eva_discount_type) { :percent_off }
        let(:promos_eva_discount_code) do
          create(
            :promos_discount_code,
            discount_args: [ promos_eva_discount_type, currency_code: promos_eva_discount_currency_code ],
          )
        end

        it 'is valid if currency codes match' do
          expect(partner).to be_valid
        end

        context 'when currency codes do not match' do
          let(:currency_code) { 'CAD' }

          it 'is valid with percent_off' do
            expect(partner).to be_valid
          end

          context 'with amount_off' do
            let(:promos_eva_discount_type) { :amount_off }

            it 'is invalid with amount off' do
              expect(partner).not_to be_valid
              expect(partner.errors[:promos_eva_discount_code_id]).to match_array(
                'discount currency code for amount off must match the currency code',
              )
            end
          end
        end
      end
    end

    describe '#ecom_payment_providers_has_valid_keys' do
      let(:partner) { build(:partner, ecom_payment_providers:) }

      subject(:valid?) { partner.valid? }

      context 'when ecom_payment_providers is empty' do
        let(:ecom_payment_providers) { [] }

        it 'is valid' do
          expect(partner).to be_valid
        end
      end

      context 'when ecom_payment_providers contains valid keys' do
        let(:ecom_payment_providers) { %w[APPLE_PAY GOOGLE_PAY] }

        it 'is valid' do
          expect(partner).to be_valid
        end
      end

      context 'when ecom_payment_providers contains invalid keys' do
        let(:ecom_payment_providers) { %w[INVALID_KEY APPLE_PAY] }

        it 'is invalid' do
          expect(partner).not_to be_valid
          expect(partner.errors[:ecom_payment_providers]).to include(
            'contains invalid payment provider keys: INVALID_KEY',
          )
        end
      end

      context 'when ecom_payment_providers contains empty key' do
        let(:ecom_payment_providers) { ['', 'APPLE_PAY'] }

        it 'is valid with removed empty key' do
          expect(partner).to be_valid
          expect(partner.ecom_payment_providers).to eq(['APPLE_PAY'])
        end
      end
    end
  end

  describe '#set_dns_status' do
    let(:partner) { create(:partner, dns_status:, proxy_host:) }

    context 'when proxy_host is nil' do
      let(:proxy_host) { nil }
      let(:dns_status) { :connected }

      it 'sets dns_status to disabled' do
        partner.valid?
        expect(partner.dns_status).to eq('disabled')
      end
    end

    context 'when proxy_host is not nil' do
      let(:proxy_host) { 'http://example.com' }

      context 'and dns_status is disabled' do
        let(:dns_status) { :disabled }

        it 'sets dns_status to pending' do
          partner.valid?
          expect(partner.dns_status).to eq('pending')
        end
      end

      context 'and dns_status is connected' do
        let(:dns_status) { :connected }

        it 'does nothing' do
          partner.valid?
          expect(partner.dns_status).to eq('connected')
        end
      end
    end
  end

  describe '#sanitize_proxy_host' do
    let(:partner) { create(:partner, proxy_host:) }

    context 'when proxy_host is nil' do
      let(:proxy_host) { nil }

      it 'does nothing' do
        partner.valid?
        expect(partner.proxy_host).to be_nil
      end
    end

    context 'when proxy_host starts with https' do
      let(:proxy_host) { 'https://example.com' }

      it 'removes the protocol from the proxy_host' do
        partner.valid?
        expect(partner.proxy_host).to eq('example.com')
      end
    end

    context 'when proxy_host starts with http' do
      let(:proxy_host) { 'http://example.com' }

      it 'removes the protocol from the proxy_host' do
        partner.valid?
        expect(partner.proxy_host).to eq('example.com')
      end
    end

    context 'when proxy_host starts has a trailing slash' do
      let(:proxy_host) { 'example.com/' }

      it 'removes the protocol from the proxy_host' do
        partner.valid?
        expect(partner.proxy_host).to eq('example.com')
      end
    end

    context 'when proxy_host starts has a protocol and trailing slash' do
      let(:proxy_host) { 'http://example.com/' }

      it 'removes the protocol from the proxy_host' do
        partner.valid?
        expect(partner.proxy_host).to eq('example.com')
      end
    end

    context 'when proxy_host needs no changes' do
      let(:proxy_host) { 'example.com' }

      it 'removes the protocol from the proxy_host' do
        partner.valid?
        expect(partner.proxy_host).to eq('example.com')
      end
    end
  end
end
