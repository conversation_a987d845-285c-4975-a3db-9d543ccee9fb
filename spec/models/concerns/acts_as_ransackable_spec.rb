# frozen_string_literal: true

require 'rails_helper'

describe ActsAsRansackable do
  let(:args) { [] }

  def create_klass(name, &)
    expose_args = args
    Temping.create(name) do
      include ActsAsRansackable

      instance_exec(*expose_args, &)
    end
  end

  let(:klass) do
    create_klass(:klass) do |*expose_args|
      expose_all_attributes_to_ransack(*expose_args)
      expose_all_scopes_to_ransack(*expose_args)
      expose_all_associations_to_ransack(*expose_args)

      with_columns do |t|
        t.integer :field_1
        t.integer :field_2
      end

      ransacker :field_3
    end
  end

  let(:klass_with_scopes) do
    create_klass(:klass_with_scopes) do |*expose_args|
      expose_all_scopes_to_ransack(*expose_args)

      with_columns do |t|
        t.integer :field_1
        t.integer :field_2
      end

      ransacker :field_3

      scope :scope_1, -> { where(field_1: 1) }
      scope :scope_2, -> { where(field_2: 2) }
    end
  end

  let(:parent_1_class) { Temping.create(:parent_1) }
  let(:parent_2_class) { Temping.create(:parent_2) }

  let(:klass_with_associations) do
    create_klass(:klass_with_associations) do |*expose_args|
      expose_all_associations_to_ransack(*expose_args)

      belongs_to :parent_1
      belongs_to :parent_2

      with_columns do |t|
        t.integer :parent_1_id
        t.integer :parent_2_id
      end
    end
  end

  describe ".expose_all_attributes_to_ransack" do
    let(:all_attributes) { klass.column_names + klass._ransackers.keys }

    context "when no arguments are passed" do
      it "exposes all attributes to ransack" do
        expect(klass.ransackable_attributes).to eq(all_attributes)
      end
    end

    context "when an except argument is passed" do
      let(:except) { :field_1 }
      let(:args) { [{ except: }] }

      it "exposes all attributes except the ones passed" do
        expect(klass.ransackable_attributes).to eq(all_attributes - Array(except).map(&:to_s))
      end
    end

    context 'when an array of except arguments is passed' do
      let(:except) { %i[field_1 field_2] }
      let(:args) { [{ except: }] }

      it 'exposes all attributes except the ones passed' do
        expect(klass.ransackable_attributes).to eq(all_attributes - except.map(&:to_s))
      end
    end
  end

  describe '.expose_all_scopes_to_ransack' do
    let(:all_scopes) { klass_with_scopes.scope_names.map(&:to_s) }

    context 'when no arguments are passed' do
      it 'exposes all scopes to ransack' do
        expect(klass_with_scopes.ransackable_scopes).to eq(all_scopes)
      end
    end

    context 'when there are no scopes defined' do
      it 'exposes no scopes to ransack' do
        expect(klass.ransackable_scopes).to eq([])
      end
    end

    context 'when an except argument is passed' do
      let(:except) { :scope_1 }
      let(:args) { [{ except: }] }

      it 'exposes all scopes except the ones passed' do
        expect(klass_with_scopes.ransackable_scopes).to eq(all_scopes - Array(except).map(&:to_s))
      end
    end
  end

  describe '.expose_all_associations_to_ransack' do
    context 'when there are no associations defined' do
      it 'exposes no associations to ransack' do
        expect(klass.ransackable_associations).to eq([])
      end
    end

    context 'when there are associations defined' do
      let(:all_associations) { klass_with_associations.reflect_on_all_associations.map { |a| a.name.to_s } }

      it 'exposes all associations to ransack' do
        expect(klass_with_associations.ransackable_associations).to eq(all_associations)
      end

      context 'when an except argument is passed' do
        let(:except) { :parent_1 }
        let(:args) { [{ except: }] }

        it 'exposes all associations except the ones passed' do
          expect(klass_with_associations.ransackable_associations).to eq(all_associations - Array(except).map(&:to_s))
        end
      end
    end
  end
end
