# frozen_string_literal: true

# == Schema Information
#
# Table name: sections
#
#  id                   :bigint           not null, primary key
#  chat_join_url        :string
#  chat_workspace_key   :string
#  conferencing_url     :string
#  live_day_of_the_week :integer          not null
#  live_end_time        :time             not null
#  live_start_time      :time             not null
#  suffix               :string           not null
#  uid                  :string           not null
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  advocate_id          :bigint
#  cohort_id            :bigint           not null
#  grader_id            :bigint
#  instructor_id        :bigint
#  partner_id           :bigint
#
# Indexes
#
#  index_sections_on_advocate_id         (advocate_id)
#  index_sections_on_chat_workspace_key  (chat_workspace_key)
#  index_sections_on_cohort_id           (cohort_id)
#  index_sections_on_grader_id           (grader_id)
#  index_sections_on_instructor_id       (instructor_id)
#  index_sections_on_partner_id          (partner_id)
#  index_sections_on_uid                 (uid) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (advocate_id => learning_delivery_employees.id)
#  fk_rails_...  (cohort_id => cohorts.id) ON DELETE => cascade
#  fk_rails_...  (grader_id => learning_delivery_employees.id)
#  fk_rails_...  (instructor_id => learning_delivery_employees.id)
#  fk_rails_...  (partner_id => partners.id)
#
require 'rails_helper'

describe Section do
  describe 'factories' do
    it_behaves_like :a_factorified_class

    it_behaves_like :a_factorified_class, factory_name: :section, factory_args: :configured
  end

  describe '#ensure_at_least_one_section (before_destroy)' do
    context 'when there are multiple sections' do
      let!(:cohort) { create(:cohort, section_count: 2) }

      it 'does not raise an error' do
        expect { cohort.sections.first.destroy }.to change { described_class.count }.by(-1)
      end
    end

    context 'when there is only one section' do
      let!(:cohort) { create(:cohort, section_count: 1) }

      it 'raises an error' do
        expect { cohort.sections.first.destroy }.not_to(change { described_class.count })
        expect(cohort.sections.first.destroy).to be(false)
      end
    end
  end

  describe '.dedicated_to' do
    let(:partner) { create(:partner) }
    let!(:section_with_partner) { create(:section, partner:) }
    let!(:section_without_partner) { create(:section, partner: nil) }

    it 'returns sections dedicated to the given partner' do
      expect(described_class.dedicated_to(partner)).to include(section_with_partner)
      expect(described_class.dedicated_to(partner)).not_to include(section_without_partner)
    end
  end

  describe '#live_name' do
    let(:section) { create(:section, live_start_time: '10:00', live_end_time: '12:00', live_day_of_the_week: :monday) }
    let(:format) { :default }

    subject(:live_name) { section.live_name(format:) }

    context 'when format is :default' do
      it 'returns the live session time in a readable format' do
        expect(live_name).to eq(Section::LiveDayTimeFormatter.new(section:).to_fs(format:))
      end
    end

    context 'when format is :short' do
      let(:format) { :short }

      it 'returns the live session time in a short format' do
        expect(live_name).to eq(Section::LiveDayTimeFormatter.new(section:).to_fs(format:))
      end
    end

    context 'when format is not specified' do
      subject(:live_name) { section.live_name }

      it 'defaults to :short format' do
        expect(live_name).to eq(Section::LiveDayTimeFormatter.new(section:).to_fs(format: :short))
      end
    end
  end

  describe '#commingled?' do
    context 'when the section has no partner' do
      let(:section) { create(:section, partner: nil) }

      it 'returns true' do
        expect(section.commingled?).to be(true)
      end
    end

    context 'when the section has a partner' do
      let(:partner) { create(:partner) }
      let(:section) { create(:section, partner:) }

      it 'returns false' do
        expect(section.commingled?).to be(false)
      end
    end
  end


  describe '#with_ids' do
    let!(:section_1) { create(:section) }
    let!(:section_2) { create(:section) }
    let!(:section_3) { create(:section) }
    context 'when IDs are present' do
      it 'returns sections matching the given IDs' do
        result = described_class.with_ids([section_1.id, section_3.id])
        expect(result).to contain_exactly(section_1, section_3)
      end
    end

    context 'when IDs are nil' do
      it 'returns all sections' do
        result = described_class.with_ids(nil)
        expect(result).to contain_exactly(section_1, section_2, section_3)
      end
    end

    context 'when IDs are an empty array' do
      it 'returns all sections' do
        result = described_class.with_ids([])
        expect(result).to contain_exactly(section_1, section_2, section_3)
      end
    end
  end

  describe '.program_cohort_suffix_cont' do
    let!(:program) { create(:program, name: 'Software Engineering', abbreviation: 'SWE') }
    let!(:cohort) { create(:cohort, program:, name: 'Software Engineering - Jan 2025') }

    let!(:section_1) { create(:section, cohort:, suffix: '1') }
    let!(:section_2) { create(:section, cohort:, suffix: '2') }

    let!(:other_program) { create(:program, name: 'Data Science', abbreviation: 'DS') }
    let!(:other_cohort) { create(:cohort, program: other_program, name: 'Data Science - Jan 2025') }
    let!(:other_section) { create(:section, cohort: other_cohort, suffix: '1') }

    context 'with single word search' do
      it 'matches on program name' do
        results = described_class.program_cohort_suffix_cont('software')
        expect(results).to eq([section_1, section_2])
      end

      it 'matches on program abbreviation' do
        results = described_class.program_cohort_suffix_cont('swe')
        expect(results).to eq([section_1, section_2])
      end

      it 'matches on cohort name' do
        results = described_class.program_cohort_suffix_cont('jan')
        expect(results).to eq([section_1, section_2, other_section])
      end

      it 'matches on section suffix' do
        results = described_class.program_cohort_suffix_cont('swe jan 1')
        expect(results).to eq([section_1])
      end
    end

    context 'with multiple word search' do
      it 'requires all words to match' do
        results = described_class.program_cohort_suffix_cont('software jan')
        expect(results).to eq([section_1, section_2])

        results = described_class.program_cohort_suffix_cont('data jan')
        expect(results).to eq([other_section])
      end

      it 'orders by number of matches' do
        # Create a section that matches in more places
        matching_program = create(:program, name: 'Software Testing', abbreviation: 'SWT')
        matching_cohort = create(:cohort, program: matching_program, name: 'Software QA - Feb 2025')
        matching_section = create(:section, cohort: matching_cohort, suffix: 'software')

        results = described_class.program_cohort_suffix_cont('software')
        expect(results.first).to eq(matching_section) # Matches in program name and suffix (2 matches)
        expect(results[1..]).to contain_exactly(section_1, section_2) # Matches only in program name (1 match)
      end
    end

    context 'with case insensitive matching' do
      it 'matches regardless of case' do
        results = described_class.program_cohort_suffix_cont('SOFTWARE')
        expect(results).to contain_exactly(section_1, section_2)

        results = described_class.program_cohort_suffix_cont('sWe')
        expect(results).to contain_exactly(section_1, section_2)
      end
    end

    context 'with partial word matching' do
      it 'matches parts of words' do
        results = described_class.program_cohort_suffix_cont('soft eng')
        expect(results).to contain_exactly(section_1, section_2)
      end
    end
  end
end
