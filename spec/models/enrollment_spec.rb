# frozen_string_literal: true

# == Schema Information
#
# Table name: enrollments
#
#  id                        :bigint           not null, primary key
#  certificate_issued_at     :datetime
#  certificate_url           :string
#  certifier                 :integer          default("none"), not null
#  clas_cohort_admission_key :integer
#  course_risk               :integer
#  course_risk_reviewed_at   :datetime
#  exit_requested_on         :date
#  extended_until            :date
#  extension_reason          :string
#  last_lms_activity_at      :datetime
#  primary                   :boolean          default(TRUE), not null
#  risk_level                :integer          default("on_track"), not null
#  status                    :integer          default("pending"), not null
#  uid                       :string           not null
#  created_at                :datetime         not null
#  updated_at                :datetime         not null
#  deal_id                   :bigint
#  ecom_order_item_id        :bigint           not null
#  extended_by_id            :bigint
#  learner_id                :bigint           not null
#  partner_program_id        :bigint           not null
#  registration_id           :bigint           not null
#  section_id                :bigint           not null
#
# Indexes
#
#  idx_enrollments_section_join                    (section_id,id)
#  index_enrollments_on_clas_cohort_admission_key  (clas_cohort_admission_key) UNIQUE
#  index_enrollments_on_deal_id                    (deal_id)
#  index_enrollments_on_ecom_order_item_id         (ecom_order_item_id)
#  index_enrollments_on_extended_by_id             (extended_by_id)
#  index_enrollments_on_learner_id                 (learner_id)
#  index_enrollments_on_partner_program_id         (partner_program_id)
#  index_enrollments_on_registration_id            (registration_id)
#  index_enrollments_on_status                     (status)
#  index_enrollments_on_uid                        (uid) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (deal_id => deals.id)
#  fk_rails_...  (ecom_order_item_id => ecom_order_items.id)
#  fk_rails_...  (extended_by_id => admin_users.id)
#  fk_rails_...  (learner_id => learners.id)
#  fk_rails_...  (partner_program_id => partner_programs.id)
#  fk_rails_...  (registration_id => registrations.id)
#  fk_rails_...  (section_id => sections.id)
#
require 'rails_helper'

describe Enrollment do
  it_behaves_like :a_factorified_class

  describe '.post_learner (scope)' do
    let!(:enrollments) do
      described_class.statuses.symbolize_keys.keys.index_with do |status|
        create(:enrollment, "#{status}_status")
      end
    end

    it 'returns enrollments with statuses post learner' do
      expect(described_class.post_learner).to match_array(enrollments.values_at(:pass, :no_pass, :certificate_issued))
    end
  end

  describe '.exited (scope)' do
    let!(:enrollments) do
      described_class.statuses.symbolize_keys.keys.index_with do |status|
        create(:enrollment, "#{status}_status")
      end
    end

    it 'returns enrollments with statuses exited' do
      expect(described_class.exited).to match_array(enrollments.values_at(:unenrolled, :dropped, :withdrew, :transferred))
    end
  end

  describe '#transfer?' do
    context 'when called from a transferred enrollment' do
      let(:enrollment) { create(:enrollment, :transfer) }

      it 'returns true' do
        expect(enrollment.transfer?).to be true
      end
    end

    context 'when called from an enrollment that is not transferred' do
      let(:enrollment) { build(:enrollment) }

      it 'returns false' do
        expect(enrollment.transfer?).to be false
      end
    end
  end

  describe '#extended?' do
    context 'when called from an extended enrollment' do
      let(:enrollment) { build(:enrollment, :extended) }

      it 'returns true' do
        expect(enrollment.extended?).to be true
      end
    end

    context 'when called from an enrollment that is not extended' do
      let(:enrollment) { build(:enrollment) }

      it 'returns false' do
        expect(enrollment.extended?).to be false
      end
    end
  end

  describe '#latest_status_change_for' do
    let(:enrollment) { build(:enrollment) }
    subject(:result) { enrollment.latest_status_change_for(:pass) }

    context 'with no status_changes' do
      it 'returns nil' do
        expect(result).to be_nil
      end
    end

    context 'with status changes' do
      let!(:status_change_1) { create(:enrollment_status_change, :status_became_active, enrollment:) }
      let!(:status_change_2) { create(:enrollment_status_change, :status_became_pass, enrollment:) }
      let!(:status_change_3) { create(:enrollment_status_change, :status_became_paused, enrollment:) }
      let!(:status_change_4) { create(:enrollment_status_change, :status_became_pass, enrollment:) }
      let!(:status_change_5) { create(:enrollment_status_change, :status_became_certificate_issued, enrollment:) }

      it 'returns the most recent change where the status became the given status' do
        expect(result).to eq(status_change_4)
      end
    end
  end

  describe '.primary' do
    let(:ecom_order_item) { create(:ecom_order_item) }
    let!(:primary_enrollment) { create(:enrollment, ecom_order_item:, primary: true) }
    let!(:non_primary_enrollment) { create(:enrollment, ecom_order_item:, primary: false) }

    it 'returns enrollments with primary status' do
      expect(described_class.primary).to eq([primary_enrollment])
    end
  end

  describe '.retained (scope)' do
    let!(:status_enrollment_mapping) do
      described_class.statuses.symbolize_keys.keys.index_with do |status|
        create(:enrollment, "#{status}_status")
      end
    end

    it 'returns enrollments that are still in the section' do
      expect(described_class.where(id: status_enrollment_mapping.values).retained).to match_array(
        status_enrollment_mapping.values_at(:pending, :active, :no_pass, :pass, :certificate_issued),
      )
    end
  end

  describe '#highest_risk_assessment' do
    let(:enrollment) { create(:enrollment) }

    context 'when there are no risk assessments' do
      it 'returns nil' do
        expect(enrollment.highest_risk_assessment).to be_nil
      end
    end

    context 'when there are multiple risk assessments with different risk levels' do
      let!(:on_track_assessment) { create(:learning_delivery_risk_assessment, :on_track, :not_activated, enrollment:) }
      let!(:low_risk_assessment) { create(:learning_delivery_risk_assessment, :low_risk, :missing_assignments, enrollment:) }
      let!(:high_risk_assessment) { create(:learning_delivery_risk_assessment, :high_risk, :late_assignments, enrollment:) }

      it 'returns the risk assessment with the highest risk level' do
        expect(enrollment.highest_risk_assessment).to eq(high_risk_assessment)
      end
    end

    context 'when there are multiple risk assessments with the same highest risk level' do
      let!(:high_risk_assessment_1) { create(:learning_delivery_risk_assessment, :high_risk, :late_assignments, enrollment:) }
      let!(:high_risk_assessment_2) { create(:learning_delivery_risk_assessment, :high_risk, :no_recent_activity, enrollment:) }

      it 'returns one of the highest risk assessments' do
        expect(enrollment.highest_risk_assessment).to be_in([high_risk_assessment_1, high_risk_assessment_2])
      end
    end

    context 'when there is only one risk assessment' do
      let!(:single_risk_assessment) { create(:learning_delivery_risk_assessment, :low_risk, enrollment:) }

      it 'returns that risk assessment' do
        expect(enrollment.highest_risk_assessment).to eq(single_risk_assessment)
      end
    end
  end
end
