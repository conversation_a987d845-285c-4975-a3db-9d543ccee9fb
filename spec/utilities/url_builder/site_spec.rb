# frozen_string_literal: true

require 'rails_helper'

class UrlBuilder
  describe Site do
    describe '#call!' do
      subject(:call!) { described_class.new(partner_program:, bypass_proxy:).call!(template:, **args) }

      let(:partner_program) { create(:partner_program, partner:, program:) }
      let(:program) { create(:program, slug: program_slug) }
      let(:program_slug) { 'spec-writing' }
      let(:partner_slug) { 'uc-zip' }
      let(:bypass_proxy) { false }
      let(:args) { {} }

      context 'when proxy_host is setup' do
        let(:partner) { create(:partner, slug: partner_slug, proxy_host: 'zip.com', dns_status: :connected) }

        context 'template: :landing' do
          let(:template) { :landing }

          it 'returns the url with the proxy_host' do
            expect(call!).to eq('http://zip.com/spec-writing')
          end

          context 'and bypass_proxy is set' do
            let(:bypass_proxy) { true }

            it 'returns the url with the partner as a param' do
              expect(call!).to eq('http://upskill.local.ziplines.dev/uc-zip/spec-writing')
            end
          end
        end

        context 'template: :syllabus' do
          let(:template) { :syllabus }

          it 'returns the url with the proxy_host' do
            expect(call!).to eq('http://zip.com/spec-writing/syllabus')
          end
        end

        context 'template: :squeeze' do
          let(:template) { :squeeze }

          it 'returns the url with the proxy_host' do
            expect(call!).to eq('http://zip.com/spec-writing/course')
          end
        end

        context 'template: :registration' do
          let(:template) { :registration }

          it 'returns the url with the proxy_host' do
            expect(call!).to eq('http://zip.com/spec-writing/registrations/new')
          end
        end

        context 'template: :get_to_know_you' do
          let(:template) { :get_to_know_you }

          it 'returns the url with the proxy_host' do
            expect(call!).to eq('http://zip.com/get-to-know-you')
          end
        end

        context 'template: :reimbursement' do
          let(:template) { :reimbursement }

          it 'returns the url with the proxy_host' do
            expect(call!).to eq('http://zip.com/spec-writing/reimbursement')
          end
        end

        context 'template: :support' do
          let(:template) { :support }

          it 'returns the url with the proxy_host' do
            expect(call!).to eq('http://zip.com/spec-writing/support')
          end
        end

        context 'template: :payment' do
          let(:template) { :payment }
          let(:args) { { order_id: 'O-123-123-123' } }

          it 'returns the url with the proxy_host' do
            expect(call!).to eq('http://zip.com/payments/new?order_id=O-123-123-123')
          end
        end

        context 'template: :receipt' do
          let(:template) { :receipt }
          let(:args) { { order_id: 'O-123-123-123' } }

          it 'returns the url with the proxy_host' do
            expect(call!).to eq('http://zip.com/payment/O-123-123-123/confirmation')
          end
        end

        context 'template: :order_confirm' do
          let(:template) { :order_confirm }
          let(:args) { { order_id: 'O-123-123-123', order_item_uid: 'OI-456-456-456' } }

          it 'returns the url with the proxy_host' do
            expect(call!).to eq('http://zip.com/order/O-123-123-123/confirm?order_item_uid=OI-456-456-456')
          end
        end
      end

      context 'when proxy_host is not setup' do
        let(:partner) { create(:partner, :dns_disabled, slug: partner_slug) }

        context 'template: :landing' do
          let(:template) { :landing }

          it 'returns the url with the partner as a param' do
            expect(call!).to eq('http://upskill.local.ziplines.dev/uc-zip/spec-writing')
          end
        end

        context 'template: :syllabus' do
          let(:template) { :syllabus }

          it 'returns the url with the proxy_host' do
            expect(call!).to eq('http://upskill.local.ziplines.dev/uc-zip/spec-writing/syllabus')
          end
        end

        context 'template: :squeeze' do
          let(:template) { :squeeze }

          it 'returns the url with the proxy_host' do
            expect(call!).to eq('http://upskill.local.ziplines.dev/uc-zip/spec-writing/course')
          end
        end

        context 'template: :registration' do
          let(:template) { :registration }

          it 'returns the url with the proxy_host' do
            expect(call!).to eq('http://upskill.local.ziplines.dev/uc-zip/spec-writing/registrations/new')
          end
        end

        context 'template: :get_to_know_you' do
          let(:template) { :get_to_know_you }

          it 'returns the url with the proxy_host' do
            expect(call!).to eq('http://upskill.local.ziplines.dev/uc-zip/get-to-know-you')
          end
        end

        context 'template: :reimbursement' do
          let(:template) { :reimbursement }

          it 'returns the url with the proxy_host' do
            expect(call!).to eq('http://upskill.local.ziplines.dev/uc-zip/spec-writing/reimbursement')
          end
        end

        context 'template: :support' do
          let(:template) { :support }

          it 'returns the url with the proxy_host' do
            expect(call!).to eq('http://upskill.local.ziplines.dev/uc-zip/spec-writing/support')
          end
        end

        context 'template: :payment' do
          let(:template) { :payment }
          let(:args) { { order_id: 'O-123-123-123' } }

          it 'returns the url with the proxy_host' do
            expect(call!).to eq('http://upskill.local.ziplines.dev/payments/new?order_id=O-123-123-123')
          end
        end

        context 'template: :receipt' do
          let(:template) { :receipt }
          let(:args) { { order_id: 'O-123-123-123' } }

          it 'returns the url with the proxy_host' do
            expect(call!).to eq('http://upskill.local.ziplines.dev/payment/O-123-123-123/confirmation')
          end
        end

        context 'template: :order_confirm' do
          let(:template) { :order_confirm }
          let(:args) { { order_id: 'O-123-123-123', order_item_uid: 'OI-456-456-456' } }

          it 'returns the url with the proxy_host' do
            expect(call!).to eq('http://upskill.local.ziplines.dev/order/O-123-123-123/confirm?order_item_uid=OI-456-456-456')
          end
        end
      end
    end
  end
end
