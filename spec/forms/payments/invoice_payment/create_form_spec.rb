# frozen_string_literal: true

require 'rails_helper'

describe Payments::InvoicePayment::CreateForm do
  let(:currency_code) { 'CAD' }
  let(:scenario) { ConfiguredPartnerProgramScenario.create(partner_args: { currency_code: }) }
  let(:partner) { scenario.partner }
  let(:partner_program) { scenario.partner_program }
  let(:section) { scenario.section }
  let(:manual_enrollment_form_payment_price_cents) { 100_00 }
  let(:payment_amount_cents) { manual_enrollment_form_payment_price_cents }
  let(:manual_enrollment_form) do
    # We usually get to this form from the manual enrollment form, so let's start there
    Registration::CreateManualForm.new(
      email_address: FFaker::Internet.email,
      first_name: FFaker::Name.first_name,
      last_name: FFaker::Name.last_name,
      phone: FFaker::PhoneNumber.phone_number,
      partner_id: scenario.partner.id,
      partner_program_id: partner_program.id,
      finance_relationship_id: scenario.finance_relationship.id,
      third_party_entity: nil,
      price_cents: manual_enrollment_form_payment_price_cents,
      payment_method_id: scenario.enabled_payment_methods.map(&:ecom_payment_method).detect(&:partner_invoice?).id,
      admin_user: scenario.admin_user,
    ).tap(&:save!)
  end
  let(:registration) { manual_enrollment_form.registration }
  let(:ecom_payment) { manual_enrollment_form.order.payment }
  let(:payer_name) { FFaker::Name.name }
  let(:payer_email) { FFaker::Internet.email }
  let(:invoice_number) { FFaker::UUID.uuidv4 }
  let(:admin_user) { create(:admin_user) }
  let(:return_to) { FFaker::Internet.http_url }
  let(:form) do
    described_class.new(
      ecom_payment_id: ecom_payment.id,
      payer_name:,
      payer_email:,
      amount_cents_param: payment_amount_cents.fdiv(100).to_s,
      invoice_number:,
      admin_user_id: admin_user.id,
      return_to:,
    )
  end

  before do
    # Mock the command calls for automatic cohort selection
    allow(Program::AddableCohortsCommand).to receive(:call!).with(program: partner_program.program, partner:).and_return([scenario.cohort])
  end

  describe 'validations' do
    subject(:valid?) { form.valid? }

    context 'with valid attributes' do
      it 'is valid' do
        expect(valid?).to be(true)
      end
    end

    context 'with invalid email' do
      let(:payer_email) { 'invalid-email' }

      it 'is invalid' do
        expect(valid?).to be(false)
        expect(form.errors.messages[:payer_email]).to include('is invalid')
      end
    end

    context 'with invalid payment_amount_cents' do
      let(:payment_amount_cents) { -1 }

      it 'is invalid' do
        expect(valid?).to be(false)
        expect(form.errors.messages[:amount_cents]).to include(/must be greater than/)
      end
    end

    context 'with invalid payer_name' do
      let(:payer_name) { '' }

      it 'is invalid' do
        expect(valid?).to be(false)
        expect(form.errors.messages[:payer_name]).to include("can't be blank")
      end
    end
  end

  describe '#save!' do
    let(:invoice_payment) { build(:payments_invoice_payment) }

    before do
      allow_any_instance_of(ActionMailer::Parameterized::MessageDelivery).to receive(:deliver_later)
    end

    subject(:save!) { form.save! }

    def assert_valid_save!
      save!

      expect(form.invoice_payment).to have_attributes(
        amount_cents: payment_amount_cents,
        invoice_number:,
        admin_user:,
        ecom_payment:,
      )
      expect(form.payer).to have_attributes(
        email: payer_email,
        name: payer_name,
      )

      order = Ecom::Order.last
      expect(order).to have_attributes(
        status: 'paid',
      )

      expect(order.order_items.size).to eq(1)
    end

    it 'creates an invoice payment with paid order' do
      assert_valid_save!
    end

    context 'when payment_amount_cents is less than manual_enrollment_form_payment_price_cents' do
      let(:payment_amount_cents) { manual_enrollment_form_payment_price_cents - 1 }

      it 'saves as we allow creating free orders' do
        assert_valid_save!
      end
    end

    context 'when manual_enrollment_form_payment_price_cents is 0' do
      let(:manual_enrollment_form_payment_price_cents) { 0 }

      it 'saves as we allow creating free orders' do
        assert_valid_save!
      end
    end

    context 'when registration status is created' do
      before do
        registration.update!(status: :created)
      end

      it 'does not create an enrollment' do
        save!

        order_item = Ecom::Order.last.order_items.first
        expect(order_item.primary_enrollment).to be_nil
      end
    end

    context 'when registration status is confirmed' do
      before do
        registration.update!(status: :confirmed)
      end

      it 'creates an enrollment' do
        save!

        order_item = Ecom::Order.last.order_items.first
        enrollment = order_item.primary_enrollment
        expect(enrollment).to have_attributes(
          primary: true,
          status: 'pending',
          learner: registration.learner,
          partner_program:,
          registration:,
          section:,
        )
      end
    end
  end
end
