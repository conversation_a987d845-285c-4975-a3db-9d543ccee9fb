# frozen_string_literal: true

# == Schema Information
#
# Table name: learning_delivery_activities
#
#  id            :bigint           not null, primary key
#  activity_type :integer          not null
#  description   :text
#  metadata      :json
#  target_type   :string
#  title         :string           not null
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  employee_id   :bigint           not null
#  target_id     :bigint
#
# Indexes
#
#  idx_on_activity_type_employee_id_783bac81e2        (activity_type,employee_id)
#  idx_on_employee_id_created_at_b1ebf93a5d           (employee_id,created_at)
#  index_learning_delivery_activities_on_employee_id  (employee_id)
#  index_learning_delivery_activities_on_target       (target_type,target_id)
#
# Foreign Keys
#
#  fk_rails_...  (employee_id => learning_delivery_employees.id)
#
FactoryBot.define do
  factory :learning_delivery_activity, class: 'LearningDelivery::Activity' do
    parent_with_nested_args :employee, factory: :learning_delivery_employee

    activity_type { :task }
    title { FFaker::Lorem.sentence }
    description { FFaker::Lorem.sentence }
    metadata { { notes: FFaker::Lorem.word } }

    trait :grade do
      activity_type { :grade }
      title { 'Grading completed' }
      description { 'Employee completed grading assignments' }
      metadata { { submissions_count: rand(1..10), duration_minutes: rand(15..60) } }
    end

    trait :evaluate do
      activity_type { :evaluate }
      title { 'Evaluation completed' }
      description { 'Employee completed student evaluations' }
      metadata { { evaluations_count: rand(1..5), duration_minutes: rand(30..90) } }
    end

    trait :task do
      activity_type { :task }
      title { 'Task completed' }
      description { 'Employee completed assigned task' }
      metadata { { task_category: FFaker::Lorem.word, duration_minutes: rand(10..45) } }
    end

    trait :advocate do
      employee { association :learning_delivery_employee, :advocate }
    end

    trait :grader do
      employee { association :learning_delivery_employee, :grader }
    end

    trait :instructor do
      employee { association :learning_delivery_employee, :instructor }
    end

    # Named factories for convenience
    factory :learning_delivery_activity_grade, traits: [:grade]
    factory :learning_delivery_activity_evaluate, traits: [:evaluate]
    factory :learning_delivery_activity_task, traits: [:task]
  end
end
