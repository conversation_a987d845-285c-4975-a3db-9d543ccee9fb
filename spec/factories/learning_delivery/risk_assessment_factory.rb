# frozen_string_literal: true

# == Schema Information
#
# Table name: learning_delivery_risk_assessments
#
#  id            :bigint           not null, primary key
#  assessed_at   :datetime         not null
#  risk_details  :jsonb            not null
#  risk_level    :integer          default("on_track"), not null
#  risk_type     :integer          not null
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  enrollment_id :bigint           not null
#
# Indexes
#
#  idx_learning_delivery_risk_assessments_on_enrollment_and_type  (enrollment_id,risk_type) UNIQUE
#  index_learning_delivery_risk_assessments_on_enrollment_id      (enrollment_id)
#
# Foreign Keys
#
#  fk_rails_...  (enrollment_id => enrollments.id) ON DELETE => cascade
#
FactoryBot.define do
  factory :learning_delivery_risk_assessment, class: 'LearningDelivery::RiskAssessment' do
    parent_with_nested_args :enrollment

    risk_type { :no_recent_activity }
    risk_level { :low_risk }
    risk_details { { last_login: '2025-03-17', days_since_login: 5 } }
    assessed_at { Time.current }

    trait :high_risk do
      risk_level { :high_risk }
    end

    trait :low_risk do
      risk_level { :low_risk }
    end

    trait :on_track do
      risk_level { :on_track }
    end

    trait :not_activated do
      risk_type { :not_activated }
      risk_details { { last_login: '2025-03-17' } }
    end

    trait :missing_assignments do
      risk_type { :missing_assignments }
      risk_details { { missing_assignment_ids: [1, 2, 3] } }
    end

    trait :late_assignments do
      risk_type { :late_assignments }
      risk_details { { late_assignment_ids: [1, 2, 3] } }
    end

    trait :no_recent_activity do
      risk_type { :no_recent_activity }
      risk_details { { last_login: '2025-03-17', days_since_login: 5 } }
    end
  end
end
