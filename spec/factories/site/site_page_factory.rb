# frozen_string_literal: true

FactoryBot.define do
  factory :site_page, class: 'Site::Page' do
    parent_with_nested_args :partner_program

    template { :landing }
    url { UrlBuilder::Site.new(partner_program:).call!(template:) }
    short_url {}

    trait :landing_template do
    end

    trait :syllabus_template do
      template { :syllabus }
    end

    trait :squeeze_template do
      template { :squeeze }
    end

    trait :registration_template do
      template { :registration }
    end

    trait :get_to_know_you_template do
      template { :get_to_know_you }
    end

    trait :reimbursement_template do
      template { :reimbursement }
    end

    trait :curriculum_template do
      template { :curriculum }
    end

    trait :testimonials_template do
      template { :testimonials }
    end

    trait :support_template do
      template { :support }
    end
  end
end
