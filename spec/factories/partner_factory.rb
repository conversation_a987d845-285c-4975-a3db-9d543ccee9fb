# frozen_string_literal: true

# == Schema Information
#
# Table name: partners
#
#  id                          :bigint           not null, primary key
#  abbreviation                :string
#  certifier                   :integer          default("none"), not null
#  currency_code               :string           default("USD"), not null
#  dns_status                  :integer          default("disabled"), not null
#  ecom_payment_providers      :string           default(["APPLE_PAY", "GOOGLE_PAY"]), not null, is an Array
#  formal_name                 :string           not null
#  name                        :string           not null
#  proxy_host                  :string
#  section_style               :integer          default("commingled"), not null
#  short_name                  :string           not null
#  slug                        :string           not null
#  status                      :integer          default("active"), not null
#  time_zone                   :string           default("UTC"), not null
#  uid                         :string           not null
#  created_at                  :datetime         not null
#  updated_at                  :datetime         not null
#  address_id                  :bigint
#  promos_eva_discount_code_id :bigint
#
# Indexes
#
#  index_partners_on_abbreviation                 (abbreviation) UNIQUE WHERE (abbreviation IS NOT NULL)
#  index_partners_on_address_id                   (address_id)
#  index_partners_on_name                         (name) UNIQUE
#  index_partners_on_promos_eva_discount_code_id  (promos_eva_discount_code_id)
#  index_partners_on_proxy_host                   (proxy_host)
#  index_partners_on_slug                         (slug) UNIQUE
#  index_partners_on_uid                          (uid) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (address_id => addresses.id)
#  fk_rails_...  (promos_eva_discount_code_id => promos_discount_codes.id)
#
FactoryBot.define do
  factory :partner do
    name { "#{FFaker::Education.unique.school} (#{rand(999_999)})" }
    proxy_host { "#{FFaker::Internet.unique.domain_word}#{rand(999_999)}.com" }

    short_name { name }
    formal_name { name }

    parent_with_nested_args :promos_eva_discount_code, factory: :promos_discount_code, optional: true
    parent_with_nested_args :address, factory: :address, optional: true

    child_with_nested_args :theme, factory: :partner_theme, has: :one
    child_with_nested_args :certificate_theme, has: :one
    child_with_nested_args :site_partner_dataset, has: :one
    child_with_nested_args :ecom_order
    child_with_nested_args :partner_program
    child_with_nested_args :enabled_payment_method
    child_with_nested_args :finance_relationship
    child_with_nested_args :remote_hubspot_contact_partner_channel, has: :one, as: :core_record
    child_with_nested_args :section

    trait :active do
      status { :active }
    end

    trait :inactive do
      status { :inactive }
    end

    trait :certifier_none do
    end

    trait :certifier_partner do
      certifier { :partner }
    end

    trait :certifier_third_party do
      certifier { :third_party }
    end

    trait :with_enabled_payment_methods do
      transient do
        mapped_enabled_payment_method_args do
          [
            [:upfront, partner: instance],
            [:installments, partner: instance],
            [:buy_now_pay_later, partner: instance],
            [:partner_invoice, partner: instance],
            [:learner_invoice, partner: instance],
          ]
        end
      end
    end

    trait :configured do
      with_enabled_payment_methods
      finance_relationship_count { 1 }
      common_finance_relationship_args { [:standard, :with_active_contract] }
      theme_count { 1 }
    end

    trait :remote_publishable do
      configured
      remote_hubspot_contact_partner_channel_count { 1 }
    end

    trait :eva_enabled do
      with_promos_eva_discount_code
    end

    trait :dns_disabled do
      proxy_host { nil }
      dns_status { :disabled }
    end

    trait :dns_pending do
      dns_status { :pending }
    end

    trait :dns_connected do
      dns_status { :connected }
    end

    trait :dns_failed do
      dns_status { :failed }
    end

    trait :dedicated_sections do
      section_style { :dedicated }
    end

    trait :commingled_sections do
      section_style { :commingled }
    end
  end
end
