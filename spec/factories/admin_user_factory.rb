# frozen_string_literal: true

# == Schema Information
#
# Table name: admin_users
#
#  id                  :bigint           not null, primary key
#  email               :string           not null
#  first_name          :string           not null
#  last_name           :string           not null
#  provider            :string
#  remember_created_at :datetime
#  status              :integer          default("active"), not null
#  time_zone           :string           default("UTC"), not null
#  uid                 :string
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  slack_member_id     :string
#
# Indexes
#
#  index_admin_users_on_email   (email) UNIQUE
#  index_admin_users_on_status  (status)
#
FactoryBot.define do
  factory :admin_user do
    email { FFaker::Internet.email }
    first_name { FFaker::Name.first_name }
    last_name { FFaker::Name.last_name }
    status { :active }

    child_with_nested_args :learning_delivery_employee, has: :one

    trait :active do
    end

    trait :deactivated do
      status { :deactivated }
    end

    trait :super_admin do
      after(:create) do |admin_user|
        Permission.resource_groups.each do |resource_group|
          permission = Permission.find_or_create_by(resource_group:, level: :rwd)
          create(:admin_user_permission, admin_user:, permission:)
        end
      end
    end

    trait :with_learning_delivery_employee do
      learning_delivery_employee_count { 1 }
    end
  end
end
