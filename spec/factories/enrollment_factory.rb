# frozen_string_literal: true

# == Schema Information
#
# Table name: enrollments
#
#  id                        :bigint           not null, primary key
#  certificate_issued_at     :datetime
#  certificate_url           :string
#  certifier                 :integer          default("none"), not null
#  clas_cohort_admission_key :integer
#  course_risk               :integer
#  course_risk_reviewed_at   :datetime
#  exit_requested_on         :date
#  extended_until            :date
#  extension_reason          :string
#  last_lms_activity_at      :datetime
#  primary                   :boolean          default(TRUE), not null
#  risk_level                :integer          default("on_track"), not null
#  status                    :integer          default("pending"), not null
#  uid                       :string           not null
#  created_at                :datetime         not null
#  updated_at                :datetime         not null
#  deal_id                   :bigint
#  ecom_order_item_id        :bigint           not null
#  extended_by_id            :bigint
#  learner_id                :bigint           not null
#  partner_program_id        :bigint           not null
#  registration_id           :bigint           not null
#  section_id                :bigint           not null
#
# Indexes
#
#  idx_enrollments_section_join                    (section_id,id)
#  index_enrollments_on_clas_cohort_admission_key  (clas_cohort_admission_key) UNIQUE
#  index_enrollments_on_deal_id                    (deal_id)
#  index_enrollments_on_ecom_order_item_id         (ecom_order_item_id)
#  index_enrollments_on_extended_by_id             (extended_by_id)
#  index_enrollments_on_learner_id                 (learner_id)
#  index_enrollments_on_partner_program_id         (partner_program_id)
#  index_enrollments_on_registration_id            (registration_id)
#  index_enrollments_on_status                     (status)
#  index_enrollments_on_uid                        (uid) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (deal_id => deals.id)
#  fk_rails_...  (ecom_order_item_id => ecom_order_items.id)
#  fk_rails_...  (extended_by_id => admin_users.id)
#  fk_rails_...  (learner_id => learners.id)
#  fk_rails_...  (partner_program_id => partner_programs.id)
#  fk_rails_...  (registration_id => registrations.id)
#  fk_rails_...  (section_id => sections.id)
#
FactoryBot.define do
  factory :enrollment do
    parent_with_nested_args :ecom_order_item
    parent_with_nested_args :learner
    parent_with_nested_args :partner_program
    parent_with_nested_args :deal
    parent_with_nested_args :registration
    parent_with_nested_args :section
    parent_with_nested_args :extended_by, factory: :admin_user, optional: true

    child_with_nested_args :status_change, factory: :enrollment_status_change
    child_with_nested_args :to_transfer, factory: :enrollment_transfer, has: :one, as: :transferred_from
    child_with_nested_args :from_transfer, factory: :enrollment_transfer, has: :one, as: :transferred_to
    child_with_nested_args :reenrolled_to, factory: :registration, has: :one, as: :reenrolled_from
    child_with_nested_args :remote_canvas_enrollment, has: :one, as: :core_record

    transient do
      default_partner_program_args { :configured }
      default_deal_args { [:enrollee, { partner_program:, email: learner.primary_email }] }
      default_registration_args { { partner_program:, section: } }
      default_section_args { { ecom_variant_count: 1, default_cohort_args: { program: partner_program.program } } }
      default_learner_args { { primary_email: registration.email } }
      default_ecom_order_item_args { { variant: section.ecom_variant, registration: } }
    end

    trait :extended do
      extended_until { 2.weeks.from_now }
      with_extended_by
      extension_reason { FFaker::Lorem.sentence }
    end

    trait :transfer do
      after(:create) do |model, evaluator|
        model.from_transfer ||=
          create(
            :enrollment_transfer,
            transferred_to: model,
            transferred_from: evaluator.transferred_from ||
              create(
                :enrollment,
                learner: model.learner,
                partner_program: model.partner_program,
                ecom_order_item: model.ecom_order_item,
                registration: model.registration,
                section_args: {
                  ecom_variant: model.section.ecom_variant,
                  cohort: model.cohort,
                },
                primary: false,
              ),
          )
        model.save!
        model.reload # for some reason, enrollment.to_tranfer == enrollment.from_transfer until it is reloaded
      end
    end

    trait :transferred do
      status { :transferred }

      after(:create) do |model, evaluator|
        model.to_transfer ||=
          create(
            :enrollment_transfer,
            transferred_from: model,
            transferred_to: evaluator.transferred_to ||
              create(
                :enrollment,
                learner: model.learner,
                partner_program: model.partner_program,
                ecom_order_item: model.ecom_order_item,
                registration: model.registration,
                section_args: {
                  ecom_variant: model.section.ecom_variant,
                  cohort: model.cohort,
                },
                primary: false,
              ),
          )
        model.save!
        model.reload # for some reason, enrollment.to_tranfer == enrollment.from_transfer until it is reloaded
      end
    end

    trait :primary do
      primary { true }
    end

    trait :certificate_issued do
      certificate_issued_at { Time.zone.now }
      certificate_url { FFaker::Internet.http_url }
    end

    trait :certifier_none do
    end

    trait :certifier_partner do
      certifier { :partner }
    end

    trait :certifier_third_party do
      certifier { :third_party }
    end

    trait :pending_status do
      status { :pending }
    end

    trait :active_status do
      status { :active }
    end

    trait :no_pass_status do
      status { :no_pass }
    end

    trait :pass_status do
      status { :pass }
    end

    trait :certificate_issued_status do
      status { :certificate_issued }
    end

    trait :unenrolled_status do
      status { :unenrolled }
    end

    trait :dropped_status do
      status { :dropped }
    end

    trait :withdrew_status do
      status { :withdrew }
    end

    trait :transferred_status do
      transferred
    end

    trait :paused_status do
      status { :paused }
    end

    trait :remote_publishable do
      remote_canvas_enrollment_count { 1 }
    end
  end
end
