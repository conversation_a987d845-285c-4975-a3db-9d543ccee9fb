# frozen_string_literal: true

# == Schema Information
#
# Table name: lms_submissions
#
#  id                :bigint           not null, primary key
#  attempt           :integer
#  grade             :string
#  graded_at         :datetime
#  score             :decimal(, )
#  seconds_late      :integer
#  state             :integer          default("unsubmitted"), not null
#  submission_type   :integer
#  submitted_at      :datetime
#  url               :text
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  enrollment_id     :bigint           not null
#  graded_by_id      :bigint
#  lms_assignment_id :bigint           not null
#
# Indexes
#
#  index_lms_submissions_on_assignment_and_enrollment  (enrollment_id,lms_assignment_id) UNIQUE
#  index_lms_submissions_on_graded_by_id               (graded_by_id)
#  index_lms_submissions_on_lms_assignment_id          (lms_assignment_id)
#
# Foreign Keys
#
#  fk_rails_...  (enrollment_id => enrollments.id)
#  fk_rails_...  (graded_by_id => learning_delivery_employees.id)
#  fk_rails_...  (lms_assignment_id => lms_assignments.id)
#
FactoryBot.define do
  factory :lms_submission, class: 'Lms::Submission' do
    parent_with_nested_args :enrollment
    parent_with_nested_args :assignment, factory: :lms_assignment
    parent_with_nested_args :graded_by, factory: :learning_delivery_employee, optional: true

    state { :unsubmitted }
    submission_type { :online_text_entry }
    score { rand(0.0..100.0).round(2) }
    grade { score.to_s }
    seconds_late { 0 }

    trait :unsubmitted do
      state { :unsubmitted }
      grade { nil }
    end

    trait :submitted do
      state { :submitted }
      attempt { 1 }
      submitted_at { Time.current }
    end

    trait :submitted_on_time do
      submitted
      transient do
        days_early { 1 }
      end

      after(:build) do |submission, evaluator|
        submission.submitted_at = submission.assignment.due_at - evaluator.days_early.days if submission.assignment.due_at.present?
      end
    end

    trait :submitted_late do
      submitted
      transient do
        days_late { 1 }
      end

      after(:build) do |submission, evaluator|
        submission.submitted_at = submission.assignment.due_at + evaluator.days_late.days if submission.assignment.due_at.present?
      end
    end

    trait :pending_review do
      submitted
      state { :pending_review }
    end

    trait :graded do
      submitted
      url { FFaker::Internet.http_url }
      state { :graded }
      graded_at { Time.current }
      graded_by { association :learning_delivery_employee }
    end

    trait :online_url do
      submission_type { :online_url }
      url { FFaker::Internet.http_url }
    end

    trait :online_upload do
      submission_type { :online_upload }
    end

    trait :media_recording do
      submission_type { :media_recording }
    end

    trait :basic_lti_launch do
      submission_type { :basic_lti_launch }
    end

    trait :student_annotation do
      submission_type { :student_annotation }
    end

    trait :late do
      seconds_late { 3600 }
    end

    trait :with_grading_config do
      transient do
        default_assignment_args do
          {
            assignment_template_args: :with_grading_config,
          }
        end
      end
    end

    trait :active do
      submitted
    end

    trait :inactive do
      unsubmitted
    end
  end
end
