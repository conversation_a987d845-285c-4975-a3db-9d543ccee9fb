# frozen_string_literal: true

# == Schema Information
#
# Table name: lms_assignments
#
#  id                      :bigint           not null, primary key
#  due_at                  :datetime
#  grading_type            :integer          not null
#  html_url                :text
#  lock_at                 :datetime
#  name                    :string           not null
#  published               :boolean          default(FALSE), not null
#  required                :boolean          default(FALSE), not null
#  status                  :integer
#  unlock_at               :datetime
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  assignment_template_id  :bigint
#  lms_assignment_group_id :bigint
#  lms_module_id           :bigint
#
# Indexes
#
#  idx_assignments_sorting                           (due_at,id)
#  index_lms_assignments_on_assignment_template_id   (assignment_template_id)
#  index_lms_assignments_on_lms_assignment_group_id  (lms_assignment_group_id)
#  index_lms_assignments_on_lms_module_id            (lms_module_id)
#
# Foreign Keys
#
#  fk_rails_...  (lms_assignment_group_id => lms_assignment_groups.id)
#  fk_rails_...  (lms_module_id => lms_modules.id)
#
FactoryBot.define do
  factory :lms_assignment, class: 'Lms::Assignment' do
    parent_with_nested_args :lms_module
    parent_with_nested_args :assignment_group, factory: :lms_assignment_group
    parent_with_nested_args :section, optional: true
    parent_with_nested_args :assignment_template, factory: :lms_assignment_template

    name { FFaker::Lorem.word }
    grading_type { :pass_fail }

    # Trait for assignments that impact outcomes (requires grading + due date)
    # By default due in the future (not yet overdue)
    trait :impacts_outcome do
      published { true }
      required { true }
      due_at { 1.day.from_now }
      grading_type { :pass_fail }
    end

    trait :overdue do
      due_at { 1.day.ago }
    end

    trait :not_required do
      required { false }
    end

    trait :no_due_date do
      due_at { nil }
    end

    trait :not_graded do
      grading_type { :not_graded }
    end
  end
end
