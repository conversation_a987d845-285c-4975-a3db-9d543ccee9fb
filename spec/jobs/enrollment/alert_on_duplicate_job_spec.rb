# frozen_string_literal: true

require 'rails_helper'

describe Enrollment::AlertOnDuplicateJob do
  describe '#perform' do
    let(:enrollment) { create(:enrollment) }

    it 'calls the AlertOnDuplicateCommand' do
      allow(Enrollment::AlertOnDuplicateCommand).to receive(:call!).and_return(true)

      described_class.new.perform(enrollment.id)

      expect(Enrollment::AlertOnDuplicateCommand).to have_received(:call!).with(enrollment:).once
    end
  end
end
