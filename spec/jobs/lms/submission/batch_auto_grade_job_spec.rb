# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Lms::Submission::BatchAutoGradeJob do
  describe '#perform' do
    let!(:grader) { create(:learning_delivery_employee) }

    let!(:section_with_grader) { create(:section, :active, grader:) }
    let!(:section_without_grader) { create(:section, :active, grader: nil) }
    let!(:inactive_section) { create(:section, :inactive, grader:) }

    let!(:submitted_submission) do
      create(:lms_submission,
        :submitted,
        enrollment_args: { section: section_with_grader },
      )
    end

    let!(:unsubmitted_submission) do
      create(:lms_submission,
        :unsubmitted,
        enrollment_args: { section: section_with_grader },
      )
    end

    let!(:graded_submission) do
      create(:lms_submission,
        :graded,
        enrollment_args: { section: section_with_grader },
      )
    end

    let!(:submitted_submission_with_complete_grade) do
      create(:lms_submission,
        :submitted,
        grade: Lms::Submission::COMPLETE,
        enrollment_args: { section: section_with_grader },
      )
    end

    let!(:submission_in_section_without_grader) do
      create(:lms_submission,
        :submitted,
        enrollment_args: { section: section_without_grader },
      )
    end

    let!(:submission_in_inactive_section) do
      create(:lms_submission,
        :submitted,
        enrollment_args: { section: inactive_section },
      )
    end

    subject(:job) { described_class.new }

    before do
      allow(Lms::Submission::AutoGradeJob).to receive(:perform_async)
    end

    it 'enqueues auto grade jobs for submitted submissions in active sections with graders' do
      job.perform

      expect(Lms::Submission::AutoGradeJob).to have_received(:perform_async).with(submitted_submission.id, false).once
      expect(Lms::Submission::AutoGradeJob).not_to have_received(:perform_async).with(submitted_submission_with_complete_grade.id, false)
      expect(Lms::Submission::AutoGradeJob).not_to have_received(:perform_async).with(unsubmitted_submission.id, false)
      expect(Lms::Submission::AutoGradeJob).not_to have_received(:perform_async).with(graded_submission.id, false)
      expect(Lms::Submission::AutoGradeJob).not_to have_received(:perform_async).with(submission_in_section_without_grader.id, false)
      expect(Lms::Submission::AutoGradeJob).not_to have_received(:perform_async).with(submission_in_inactive_section.id, false)
    end

    it 'does not enqueue any jobs when there are no active sections with graders' do
      section_with_grader.update!(grader: nil)

      job.perform
      expect(Lms::Submission::AutoGradeJob).not_to have_received(:perform_async)
    end

    context 'when there are multiple submissions in the same section and multiple sections' do
      let!(:another_section) { create(:section, :active, grader:) }

      let!(:another_submission) { create(:lms_submission, :submitted, enrollment_args: { section: another_section }) }

      let!(:another_submitted_submission) do
        create(:lms_submission, :submitted, enrollment_args: { section: section_with_grader })
      end

      it 'handles multiple sections and submissions correctly' do
        job.perform

        expect(Lms::Submission::AutoGradeJob).to have_received(:perform_async).with(submitted_submission.id, false).once
        expect(Lms::Submission::AutoGradeJob).to have_received(:perform_async).with(another_submission.id, false).once
        expect(Lms::Submission::AutoGradeJob).to have_received(:perform_async).with(another_submitted_submission.id, false).once
        expect(Lms::Submission::AutoGradeJob).to have_received(:perform_async).exactly(3).times
      end
    end

    context 'when there are submissions with reviews' do
      let(:submission_with_current_review) { create(:lms_submission, :submitted, enrollment_args: { section: section_with_grader }) }
      let!(:review) { create(:lms_submission_review, submission: submission_with_current_review) }
      let(:submission_with_outdated_review) { create(:lms_submission, :submitted, attempt: 2, enrollment_args: { section: section_with_grader }) }
      let!(:outdated_review) { create(:lms_submission_review, submission: submission_with_outdated_review, attempt: 1) }

      it 'does not enqueue auto grade jobs for submissions with current reviews' do
        job.perform

        expect(Lms::Submission::AutoGradeJob).not_to have_received(:perform_async).with(submission_with_current_review.id, false)
        expect(Lms::Submission::AutoGradeJob).to have_received(:perform_async).with(submission_with_outdated_review.id, false)
      end
    end


    describe 'private #find_submitted_submissions' do
      it 'returns only submitted submissions for the given section' do
        result = job.send(:find_submitted_submissions, section_with_grader)

        expect(result).to include(submitted_submission)
        expect(result).not_to include(unsubmitted_submission)
        expect(result).not_to include(graded_submission)
        expect(result).not_to include(submission_in_section_without_grader)
        expect(result).not_to include(submission_in_inactive_section)
      end
    end
  end
end
