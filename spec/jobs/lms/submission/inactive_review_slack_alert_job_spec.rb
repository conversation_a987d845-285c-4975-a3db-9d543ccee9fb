# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Lms::Submission::InactiveReviewSlackAlertJob do
  subject(:job) { described_class.new }

  before do
    allow(FeatureFlag).to receive(:enabled?).with(:alert_inactive_review).and_return(feature_flag_enabled)
    allow(Alert::OpenCommand::InactiveReview).to receive(:call!)
  end

  describe '#perform' do
    context 'when feature flag is disabled' do
      let(:feature_flag_enabled) { false }

      it 'does not create any alerts' do
        job.perform
        expect(Alert::OpenCommand::InactiveReview).not_to have_received(:call!)
      end
    end

    context 'when feature flag is enabled' do
      let(:feature_flag_enabled) { true }

      let!(:old_training_review) { create(:lms_submission_review, :submitted, :training, updated_at: 5.hours.ago) }

      let!(:old_submitted_review) { create(:lms_submission_review, state: :submitted, updated_at: 5.hours.ago) }

      let!(:old_pdf_generated_review) { create(:lms_submission_review, state: :pdf_generated, updated_at: 6.hours.ago) }

      let!(:old_auto_graded_review) do
        create(:lms_submission_review, state: :auto_graded, updated_at: 5.hours.ago)
      end

      let!(:old_graded_review) do
        create(:lms_submission_review, state: :graded, updated_at: 10.hours.ago)
      end

      let!(:recent_submitted_review) do
        create(:lms_submission_review, state: :submitted, updated_at: 2.hours.ago)
      end

      let!(:old_published_review) do
        create(:lms_submission_review, state: :published, updated_at: 6.hours.ago)
      end

      let!(:old_manual_review_needed_review) do
        create(:lms_submission_review, state: :manual_review_needed, updated_at: 6.hours.ago)
      end

      let!(:old_stale_review) do
        create(:lms_submission_review, state: :stale, updated_at: 6.hours.ago)
      end

      it 'creates alerts for reviews not updated for more than 4 hours in non-excluded states' do
        job.perform

        expect(Alert::OpenCommand::InactiveReview).to have_received(:call!).exactly(4).times
      end

      it 'calls Alert::OpenCommand::InactiveReview with correct reviews' do
        job.perform

        inactive_reviews = [old_submitted_review, old_pdf_generated_review, old_auto_graded_review, old_graded_review]

        inactive_reviews.each do |review|
          expect(Alert::OpenCommand::InactiveReview).to have_received(:call!).with(review:)
        end
      end

      it 'does not create alerts for reviews in excluded states or recently updated reviews' do
        job.perform

        excluded_reviews = [old_published_review, old_manual_review_needed_review, old_stale_review, recent_submitted_review]

        excluded_reviews.each do |review|
          expect(Alert::OpenCommand::InactiveReview).not_to have_received(:call!).with(review:)
        end
      end
    end
  end
end
