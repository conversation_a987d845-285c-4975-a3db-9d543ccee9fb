# frozen_string_literal: true

require 'rails_helper'

module Lms
  class Submission
    describe PublishJob do
      describe '#perform' do
        let(:submission) { create(:lms_submission, :submitted) }
        let(:job) { described_class.new.perform(submission.id) }
        let(:publish_command) { instance_double(Lms::Submission::PublishCommand, call!: true) }

        before do
          allow(Lms::Submission::PublishCommand).to receive(:new).and_return(publish_command)
        end

        it 'creates and calls the PublishCommand with the submission' do
          job
          expect(Lms::Submission::PublishCommand).to have_received(:new).with(
            submission:,
          )
          expect(publish_command).to have_received(:call!)
        end
      end

      describe 'job execution' do
        it 'enqueues the job with the correct parameters' do
          submission = create(:lms_submission)

          expect do
            described_class.perform_async(submission.id.to_s)
          end.to change { described_class.jobs.size }.by(1)
        end
      end
    end
  end
end
