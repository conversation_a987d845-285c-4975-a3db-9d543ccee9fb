# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Lms::Submission::Comment::FetchBySectionJob do
  describe '#perform' do
    let(:section_id) { 42 }
    let(:section) { instance_double(Section) }

    before do
      allow(Section).to receive(:find).with(section_id).and_return(section)
    end

    context 'when updated_since is not provided' do
      it 'calls the command with section and default lookback time' do
        freeze_time do
          expected_time = 4.hours.ago

          allow(Lms::Submission::Comment::FetchBySectionCommand).to receive(:call!)

          described_class.new.perform(section_id)

          expect(Lms::Submission::Comment::FetchBySectionCommand).to have_received(:call!).with(
            section:,
            updated_since: be_within(1.second).of(expected_time),
          )
        end
      end
    end

    context 'when updated_since is provided as ISO8601' do
      it 'parses the time and calls the command with it' do
        iso = '2025-08-12T10:00:00Z'
        parsed = Time.zone.parse(iso)

        allow(Lms::Submission::Comment::FetchBySectionCommand).to receive(:call!)

        described_class.new.perform(section_id, iso)

        expect(Lms::Submission::Comment::FetchBySectionCommand).to have_received(:call!).with(
          section:,
          updated_since: be_within(1.second).of(parsed),
        )
      end
    end
  end
end
