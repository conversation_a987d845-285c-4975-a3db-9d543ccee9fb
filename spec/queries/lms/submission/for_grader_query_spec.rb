# frozen_string_literal: true

require 'rails_helper'

module Lms
  class Submission
    RSpec.describe ForGraderQuery do
      let(:current_employee) { create(:learning_delivery_employee, :grader) }
      let(:section) { create(:section, :configured, grader: current_employee) }
      let(:params) { {} }
      let(:query) { described_class.new(params:, current_employee:) }

      describe '#initialize' do
        it 'sets params and current_employee' do
          expect(query.params).to eq(params)
          expect(query.current_employee).to eq(current_employee)
        end
      end

      describe '#relation' do
        let(:overdue_assignment) { create(:lms_assignment, :overdue) }
        let!(:submission) { create(:lms_submission, :submitted, section:, assignment: overdue_assignment) }
        let!(:submission_review) { create(:lms_submission_review, :manual_review_needed, submission:) }
        let!(:other_submission) { create(:lms_submission, :submitted) }
        let!(:other_review) { create(:lms_submission_review, :manual_review_needed, submission: other_submission) }

        it 'returns submissions for grader sections' do
          result = query.relation
          expect(result).to include(submission)
          expect(result).not_to include(other_submission)
        end

        it 'includes necessary associations' do
          expect(query.relation.includes_values).to include(:reviews, :learner, :comments, :module_template)
        end

        it 'orders by assignment due at ASC' do
          not_due_assignment = create(:lms_assignment, due_at: 1.day.from_now)
          second_submission = create(:lms_submission, :submitted, section:, assignment: not_due_assignment)
          create(:lms_submission_review, :manual_review_needed, submission: second_submission)
          results = query.relation.to_a
          expect(results.first).to eq(submission)
          expect(results.last).to eq(second_submission)
        end
      end

      describe '#base_filtered_relation' do
        let!(:submission) { create(:lms_submission, :submitted, section:) }
        let!(:submission_review) { create(:lms_submission_review, :manual_review_needed, submission:) }

        context 'with section_id filter' do
          let(:other_section) { create(:section, grader: current_employee) }
          let!(:other_submission) { create(:lms_submission, :submitted, section: other_section) }
          let!(:other_review) { create(:lms_submission_review, :manual_review_needed, submission: other_submission) }
          let(:params) { { section_id: other_section.id } }

          it 'filters by section' do
            result = query.base_filtered_relation
            expect(result.joins_values).to include(:enrollment)
            expect(result).to include(other_submission)
            expect(result).not_to include(submission)
          end
        end

        context 'with assignment_template_id filter' do
          let(:assignment_template) { create(:lms_assignment_template) }
          let!(:assignment) { create(:lms_assignment, assignment_template:) }
          let!(:template_submission) { create(:lms_submission, :submitted, assignment:, section:) }
          let!(:template_review) { create(:lms_submission_review, :manual_review_needed, submission: template_submission) }
          let(:params) { { assignment_template_id: assignment_template.id } }

          it 'filters by assignment template' do
            result = query.base_filtered_relation
            expect(result.joins_values).to include(:assignment)
            expect(result).to include(template_submission)
            expect(result).not_to include(submission)
          end
        end

        context 'with learner_id filter' do
          let(:learner) { create(:learner) }
          let(:other_learner) { create(:learner) }
          let(:enrollment) { create(:enrollment, learner:, section:) }
          let(:other_enrollment) { create(:enrollment, learner: other_learner, section:) }
          let!(:learner_submission) { create(:lms_submission, :submitted, enrollment:, section:) }
          let!(:other_learner_submission) { create(:lms_submission, :submitted, enrollment: other_enrollment, section:) }
          let!(:learner_review) { create(:lms_submission_review, :manual_review_needed, submission: learner_submission) }
          let!(:other_review) { create(:lms_submission_review, :manual_review_needed, submission: other_learner_submission) }
          let(:params) { { learner_id: learner.id } }

          it 'filters by learner' do
            result = query.base_filtered_relation
            expect(result.joins_values).to include(:enrollment)
            expect(result).to include(learner_submission)
            expect(result).not_to include(other_learner_submission)
          end
        end
      end

      describe 'quick filters' do
        let!(:submission) { create(:lms_submission, :submitted, section:) }
        let!(:submission_review) { create(:lms_submission_review, :manual_review_needed, submission:) }

        context 'all filter' do
          let(:params) { { filter: 'all' } }

          it 'filters submissions needing manual review' do
            submission_review.update!(state: :graded)

            result = query.relation
            expect(result).to include(submission)
          end
        end

        context 'new_comments filter' do
          let(:params) { { filter: 'new_comments' } }

          it 'filters submissions with unread comments' do
            create(:lms_submission_comment, :from_learner, :unread, submission:)

            result = query.relation
            expect(result).to include(submission)
          end
        end

        context 'processing filter' do
          let(:params) { { filter: 'processing' } }
          let!(:submitted_submission) { create(:lms_submission, :submitted, section:) }
          let!(:pdf_generated_submission) { create(:lms_submission, :submitted, section:) }
          let!(:auto_graded_submission) { create(:lms_submission, :submitted, section:) }
          let!(:graded_submission) { create(:lms_submission, :submitted, section:) }
          let!(:published_submission) { create(:lms_submission, :submitted, section:) }

          let!(:submitted_review) { create(:lms_submission_review, :submitted, submission: submitted_submission) }
          let!(:pdf_generated_review) { create(:lms_submission_review, :pdf_generated, submission: pdf_generated_submission) }
          let!(:auto_graded_review) { create(:lms_submission_review, :auto_graded, submission: auto_graded_submission) }
          let!(:graded_review) { create(:lms_submission_review, :graded, submission: graded_submission) }
          let!(:published_review) { create(:lms_submission_review, :published, submission: published_submission) }

          it 'filters submissions in autograding progress states' do
            result = query.relation
            expect(result).to include(submitted_submission, pdf_generated_submission, auto_graded_submission, graded_submission)
            expect(result).not_to include(published_submission)
          end
        end

        context 'no filter' do
          let(:params) { {} }

          it 'returns all submissions for grader' do
            result = query.relation
            expect(result).to include(submission)
          end
        end
      end

      describe 'without current_employee' do
        let(:current_employee) { nil }
        let!(:submission) { create(:lms_submission, :submitted) }
        let!(:submission_review) { create(:lms_submission_review, :manual_review_needed, submission:) }
        let!(:training_submission) { training_review.submission }
        let(:training_review) { create(:lms_submission_review, :manual_review_needed, :training) }

        it 'returns all submissions without grader filter' do
          result = query.relation
          expect(result).to include(submission)
        end

        it 'excludes training submissions' do
          result = query.relation
          expect(result).not_to include(training_submission)
        end
      end

      describe 'training submissions exclusion' do
        let!(:training_submission) { create(:lms_submission, :submitted, section:) }
        let!(:training_review) { create(:lms_submission_review, :manual_review_needed, :training, submission: training_submission) }

        it 'excludes training submissions when current_employee is present' do
          result = query.relation
          expect(result).not_to include(training_submission)
        end
      end
    end
  end
end
