# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LearningDelivery::SectionsController, type: :controller do
  let(:admin_user) { create(:admin_user) }
  let(:current_employee) { create(:learning_delivery_employee, personal_email: admin_user.email, admin_user:) }
  before do
    sign_in(admin_user)
  end

  describe 'GET #index' do
    it 'assigns a LearningDelivery::Sections::IndexPresenter' do
      get :index
      expect(assigns(:presenter)).to be_a(LearningDelivery::Sections::IndexPresenter)
    end

    it 'responds successfully' do
      get :index
      expect(response).to have_http_status(:ok)
    end

    it 'assigns filtered params to presenter' do
      get :index, params: { page: 2, per_page: 10, section_filter: 'active' }

      expected_params = { 'page' => '2', 'per_page' => '10', 'section_filter' => 'active' }
      actual_params = assigns(:presenter).params.to_h.slice(*expected_params.keys)
      expect(actual_params).to eq(expected_params)
    end
  end
end
