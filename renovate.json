{
  "$schema": "https://docs.renovatebot.com/renovate-schema.json",
  "extends": [
    "config:recommended"
  ],
  "dependencyDashboardApproval": true,
  "commitMessagePrefix": "chore(deps): ",
  "packageRules": [
    {
      "matchPackageNames": [
        "valkey/valkey",
        "postgres",
      ],
      "enabled": false
    },
    {
      "matchManagers": [
        "github-actions"
      ],
      "groupName": "GitHub Actions dependencies"
    }
  ],
  "hostRules": [
    {
      "hostType": "rubygems",
      "matchHost": "https://github.com/zip-learning",
      "token": "{{ secrets.GITHUB_COM_TOKEN }}"
    }
  ]
}