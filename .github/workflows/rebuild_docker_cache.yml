# This workflow rebuilds all intermediate docker stages and pushes them to GHCR.
# It is triggered weekly on cron and on workflow_dispatch from the tests workflow on
# ruby version or Dockerfile changes.
name: "Rebuild docker caches"
on:
  workflow_dispatch:
  workflow_call:
    outputs:
      base_cache_image_uri:
        description: 'The base cache image URI'
        value: ${{ jobs.docker_build.outputs.base_cache_image_uri }}
      builder_cache_image_uri:
        description: 'The builder cache image URI'
        value: ${{ jobs.docker_build.outputs.builder_cache_image_uri }}
    secrets:
      BUNDLE_GITHUB__COM:
        required: true
  schedule:
    - cron:  "0 8 * * 1"

permissions:
  id-token: write
  contents: read
  packages: write
  actions: write

concurrency:
  group: rebuild-docker-caches-${{ github.head_ref || github.ref_name }}

jobs:
  docker_build:
    name: "Build and push the base and builder images"
    runs-on: [runs-on,image=ci-arm64,family=c8g.xlarge,ssh=false]
    outputs:
      base_cache_image: ${{ steps.set_outputs.outputs.base_cache_image }}
      builder_cache_image: ${{ steps.set_outputs.outputs.builder_cache_image }}
      base_cache_image_uri: ${{ steps.set_outputs.outputs.base_cache_image_uri }}
      builder_cache_image_uri: ${{ steps.set_outputs.outputs.builder_cache_image_uri }}
    timeout-minutes: 20
    steps:
      - name: Enable Outbound Firewall
        run: |
          ALLOWED_FQDNS=(
            dl-cdn.alpinelinux.org                        # Alpine Linux repository
            rubygems.org                                  # RubyGems repository
            index.rubygems.org                            # RubyGems repository
            ghcr.io                                       # GitHub Container Registry
            pkg-containers.githubusercontent.com          # GitHub Container Registry
            github.com                                    # GitHub checkout
          )

          # Join array elements with commas for the allow-fqdns parameter
          ALLOWED_FQDNS_JOINED=$(IFS=,; echo "${ALLOWED_FQDNS[*]}")

          sudo /usr/local/bin/outbound_firewall.sh --docker --allow-fqdns="$ALLOWED_FQDNS_JOINED"

      - name: Purge cached images
        run: |
          sudo docker system prune -af
          
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0

      - name: Login to GitHub Container Registry
        uses: docker/login-action@184bdaa0721073962dff0199f1fb9940f07167d1 # v3.5.0
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set shared variables
        id: set_vars
        run: |
          .github/workflows/scripts/set_gha_shared_variables.sh

      - name: Set outputs
        id: set_outputs
        run: |
          echo "base_cache_image=${{ env.BASE_CACHE_IMAGE_NAME }}" >> $GITHUB_OUTPUT
          echo "builder_cache_image=${{ env.BUILDER_CACHE_IMAGE_NAME }}" >> $GITHUB_OUTPUT
          echo "base_cache_image_uri=${{ env.BASE_CACHE_IMAGE }}" >> $GITHUB_OUTPUT
          echo "builder_cache_image_uri=${{ env.BUILDER_CACHE_IMAGE }}" >> $GITHUB_OUTPUT

      # docker-container driver is required to use zstd compression
      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@e468171a9de216ec08956ac3ada2f0791b6bd435 # v3.11.1
        with:  
          driver: docker-container
          cleanup: false

      - name: Build and push base and builder images
        env:
          BUILDX_BUILDER: ${{ steps.buildx.outputs.name }}
        run: |
          .github/workflows/scripts/build_and_push_base_builder_images.sh

      - name: Alert On Unexpected Network Activity
        if: always()
        run: |
          /usr/local/bin/alert_on_unexpected_activity.sh
      
  cleanup:
    permissions:
      contents: none
      packages: write
    needs: docker_build
    runs-on: ubuntu-latest # Note: We cant run this on our own runners because it doesn't work on ARM
    timeout-minutes: 5
    steps:
      - name: Cleanup Base and Builder images
        uses: snok/container-retention-policy@4f22ef80902ad409ed55a99dc5133cc1250a0d03 # v3.0.0
        with:
          image-names: |
            ${{ needs.docker_build.outputs.base_cache_image }}
            ${{ needs.docker_build.outputs.builder_cache_image }}
          token: ${{ secrets.GITHUB_TOKEN }}
          cut-off: 4w
          account: ${{ github.repository_owner }}

  build_bundler_images:
    needs: docker_build
    uses: ./.github/workflows/build_bundler_images.yml
    secrets:
      BUNDLE_GITHUB__COM: ${{ secrets.BUNDLE_GITHUB__COM }}
