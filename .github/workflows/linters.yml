name: Linters

on:
  workflow_dispatch:
  pull_request:
  schedule:
    # Run weekly to refresh the caches
    - cron: "5 8 * * 1"

permissions:
  contents: read
  pull-requests: write

jobs:
  rubocop:
    name: Rubocop
    runs-on: ubuntu-latest
    timeout-minutes: 5
    env:
      BUNDLE_ONLY: 'rubocop'
    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0

      - name: Enable Outbound Firewall
        run: |
          ALLOWED_FQDNS=(
            rubygems.org                      # RubyGems repository
            index.rubygems.org                # RubyGems repository
            raw.githubusercontent.com         # ReviewDog action
          )

          # Join array elements with commas for the allow-fqdns parameter
          ALLOWED_FQDNS_JOINED=$(IFS=,; echo "${ALLOWED_FQDNS[*]}")

          sudo .github/workflows/scripts/ci-firewall/github_runner_outbound_firewall.sh --allow-fqdns="$ALLOWED_FQDNS_JOINED"

      - uses: ruby/setup-ruby@829114fc20da43a41d27359103ec7a63020954d4 # v1.255.0
        with:
          bundler-cache: true

      - name: Restore Rubocop cache
        uses: actions/cache/restore@0400d5f644dc74513175e3cd8d07132dd4860809 # v4.2.4
        with:
          path: ~/.cache/rubocop_cache
          key: ${{ runner.os }}-rubocop-cache-${{ hashFiles('.rubocop.yml') }}
          restore-keys: |
            ${{ runner.os }}-rubocop-cache-

      - name: rubocop
        uses: reviewdog/action-rubocop@efcf72af8105d5f0e82b1afa0f3aa36d0fd33793 # v2.21.4
        with:
          skip_install: true
          use_bundler: true
          reporter: github-pr-check
          fail_level: any
          rubocop_flags: --config .rubocop.yml
          filter_mode: nofilter

      - name: Save Rubocop cache
        uses: actions/cache/save@0400d5f644dc74513175e3cd8d07132dd4860809 # v4.2.4
        if: always()
        with:
          path: ~/.cache/rubocop_cache
          key: ${{ runner.os }}-rubocop-cache-${{ hashFiles('.rubocop.yml') }}

      - name: Network activity log
        if: always()
        run: |
          set +e

          #Set the region since the script can't determine the region on github hosted runners
          export AWS_REGION=${{ vars.AWS_REGION }}
          .github/workflows/scripts/ci-firewall/alert_on_unexpected_activity.sh
          cat /tmp/dnsmasq.log
          exit 0

  brakeman:
    name: Brakeman
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0

      - name: Enable Outbound Firewall
        run: |
          ALLOWED_FQDNS=(
            rubygems.org                      # RubyGems repository
            index.rubygems.org                # RubyGems repository
            raw.githubusercontent.com         # ReviewDog action
          )

          # Join array elements with commas for the allow-fqdns parameter
          ALLOWED_FQDNS_JOINED=$(IFS=,; echo "${ALLOWED_FQDNS[*]}")

          sudo .github/workflows/scripts/ci-firewall/github_runner_outbound_firewall.sh --allow-fqdns="$ALLOWED_FQDNS_JOINED"
        
      - uses: ruby/setup-ruby@829114fc20da43a41d27359103ec7a63020954d4 # v1.255.0

      - name: brakeman
        uses: reviewdog/action-brakeman@5083efd49634e26645a0736681b618ccc3fb7f14 # v2.19.2
        with:
          brakeman_flags: -i config/brakeman.ignore
          reporter: github-pr-check
          fail_level: any
          filter_mode: nofilter

      - name: Network activity log
        if: always()
        run: |
          set +e

          #Set the region since the script can't determine the region on github hosted runners
          export AWS_REGION=${{ vars.AWS_REGION }}
          .github/workflows/scripts/ci-firewall/alert_on_unexpected_activity.sh
          cat /tmp/dnsmasq.log
          exit 0

  gitleaks:
    name: Gitleaks
    runs-on: [runs-on,image=ci-arm64,family=c8g.xlarge,ssh=false]
    timeout-minutes: 5
    steps:
      - name: Enable Outbound Firewall
        run: |
          ALLOWED_FQDNS=(
            github.com # GitHub checkout
          )

          # Join array elements with commas for the allow-fqdns parameter
          ALLOWED_FQDNS_JOINED=$(IFS=,; echo "${ALLOWED_FQDNS[*]}")

          sudo /usr/local/bin/outbound_firewall.sh --allow-fqdns="$ALLOWED_FQDNS_JOINED"

      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
        with:
          fetch-depth: 0

      - name: Run Gitleaks
        run: |
          gitleaks dir -v --redact
          
          # Also scan PR commits if this is a pull request
          if [ "${{ github.event_name }}" = "pull_request" ]; then
            echo "Scanning PR commits..."
            gitleaks git -v --redact --log-opts="--no-merges --first-parent ${{ github.event.pull_request.base.sha }}^..${{ github.event.pull_request.head.sha }}"
          fi

      - name: Alert On Unexpected Network Activity
        if: always()
        run: |
          /usr/local/bin/alert_on_unexpected_activity.sh
