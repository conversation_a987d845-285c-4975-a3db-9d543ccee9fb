# This workflow builds the production docker image ahead of time on merges into the dev branch.
# This is done to speed up the production deploy by having the image already built by the time the
# workflow is triggered.
name: Pre-Build Production
on:
  push:
    branches:
      - dev

permissions:
  id-token: write
  contents: write
  packages: read

concurrency:
  group: production-pre-build

jobs:
  build:
    name: Pre Build Production Image
    timeout-minutes: 5
    runs-on: [runs-on,image=ci-arm64,family=c8g.xlarge,ssh=false]
    env:
      BUNDLE_GITHUB__COM: ${{ secrets.BUNDLE_GITHUB__COM }}
    steps:
      - name: Enable Outbound Firewall
        run: |
          ALLOWED_FQDNS=(
            ${{ vars.PRD_BUILD_CACHE_BUCKET_NAME }}.s3.${{vars.AWS_REGION}}.amazonaws.com     # Build cache S3 Bucket
            ${{ vars.PRD_ASSETS_BUCKET_NAME }}.s3.${{vars.AWS_REGION}}.amazonaws.com          # Assets S3 Bucket
            ${{ vars.PRD_ASSETS_BUCKET_NAME }}.s3-accelerate.amazonaws.com                    # Assets S3 Accelerate Bucket
            dl-cdn.alpinelinux.org                                                            # Alpine Linux repository
            rubygems.org                                                                      # RubyGems repository
            index.rubygems.org                                                                # RubyGems repository
            ghcr.io                                                                           # GitHub Container Registry
            pkg-containers.githubusercontent.com                                              # GitHub Container Registry
            github.com                                                                        # GitHub checkout
            api.ecr.${{vars.AWS_REGION}}.amazonaws.com                                        # Amazon Elastic Container Registry
            ${{ vars.PRD_ECR_REPO }}                                                          # Amazon Elastic Container Registry
          )

          # Join array elements with commas for the allow-fqdns parameter
          ALLOWED_FQDNS_JOINED=$(IFS=,; echo "${ALLOWED_FQDNS[*]}")

          sudo /usr/local/bin/outbound_firewall.sh --docker --allow-fqdns="$ALLOWED_FQDNS_JOINED"

      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0

      - name: Write REVISION
        run: |
          export REVISION=$(git rev-parse HEAD)
          echo "REVISION=${REVISION}" >> $GITHUB_ENV
          echo ${REVISION} > REVISION

      - name: Get current date
        id: date
        run: |
          echo "year=$(date +'%Y')" >> $GITHUB_OUTPUT
          echo "month=$(date +'%m')" >> $GITHUB_OUTPUT

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@e468171a9de216ec08956ac3ada2f0791b6bd435 # v3.11.1
        with:  
          driver: docker-container
          cleanup: false

      - name: Set shared variables
        run: |
          export GITHUB_TOKEN=${{ secrets.GITHUB_TOKEN }}
          export REVISION=${{env.REVISION}}
          export LOGIN_TO_GHCR=true
          export BRANCH_NAME=main
          export RAILS_ENV=production
          export SET_DOCKER_CACHE_VARS=true
          .github/workflows/scripts/set_gha_shared_variables.sh
      
      - name: Configure AWS Credentials for Building Image
        uses: aws-actions/configure-aws-credentials@7474bc4690e29a8392af63c5b98e7449536d5c3a # v4.3.1
        with:
          role-to-assume: ${{ vars.AWS_ROLE_BUILD_PRD_IMAGE_ARN }}
          role-duration-seconds: 1800
          aws-region: ${{ vars.AWS_REGION }}
      
      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@062b18b96a7aff071d4dc91bc00c4c1a7945b076 # v2.0.1

      # ECR repo has tag immutability enabled, so we need to remove the existing tag before pushing a new one
      # this can happen if we re-run the workflow
      - name: Remove existing prebuilt image tag if exists
        run: |
          aws ecr batch-delete-image --repository-name ${{ vars.IMAGE_NAME }} --image-ids imageTag=${{ env.PREBUILT_IMAGE_TAG }} || true

      - name: Build Image
        uses: docker/build-push-action@263435318d21b8e681c14492fe198d362a7d2c83 # v6.18.0
        env:
          DOCKER_BUILD_SUMMARY: false
          DOCKER_BUILD_RECORD_UPLOAD: false
        with:
          sbom: false
          provenance: false
          context: ./docker
          target: release
          secrets: |
            aws_access_key_id=${{ env.AWS_ACCESS_KEY_ID }}
            aws_secret_access_key=${{ env.AWS_SECRET_ACCESS_KEY }}
            aws_session_token=${{ env.AWS_SESSION_TOKEN }}
            bundle_github__com=${{ secrets.BUNDLE_GITHUB__COM }}
          build-args: |
            RUBY_VERSION
            DEVOPS_APP_NAME
            RAILS_ENV
            AWS_REGION
            BUILD_CACHE_BUCKET_NAME=${{ vars.PRD_BUILD_CACHE_BUCKET_NAME }}
            BASE_FROM
            BUILDER_FROM
            BUILDER_BUNDLER_FROM
            ASSETS_BUCKET_NAME=${{ vars.PRD_ASSETS_BUCKET_NAME }}
            ASSETS_PREFIX=${{ format(vars.ASSETS_PREFIX_FORMAT, env.BRANCH_NAME_NORMALIZED, steps.date.outputs.year, steps.date.outputs.month) }}
            FALLBACK_ASSETS_PREFIX=${{ format(vars.ASSETS_PREFIX_FORMAT, 'dev', steps.date.outputs.year, steps.date.outputs.month) }}
          build-contexts: |
            app=.
          tags: |
            ${{ vars.PRD_ECR_REPO }}/${{ vars.IMAGE_NAME }}:${{ env.PREBUILT_IMAGE_TAG }}
          outputs: |
            type=image,oci-mediatypes=true,compression=zstd,compression-level=3,force-compression=true,push=true

      - name: Alert On Unexpected Network Activity
        if: always()
        run: |
          /usr/local/bin/alert_on_unexpected_activity.sh \
            --slack-webhook-url "${{ secrets.PRD_SLACK_WEBHOOK_URL }}" \
            --slack-message "During <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|prebuild image run>"
      