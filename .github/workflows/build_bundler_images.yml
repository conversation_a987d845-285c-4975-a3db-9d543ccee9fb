name: "Build bundler images"
on:
  workflow_dispatch:
  workflow_call:
    secrets:
      BUNDLE_GITHUB__COM:
        required: true
  push:
    branches:
      - dev
    paths:
      # Note: These files should match the files used in generating the BUILDER_BUNDLER_IMAGE_SHA in set_gha_shared_variables.sh
      - .ruby-version
      - docker/Dockerfile
      - Gemfile
      - Gemfile.lock

permissions:
  id-token: write
  contents: read
  packages: write

concurrency:
  group: build-bundler-images-${{ github.head_ref || github.ref_name }}

jobs:
  build_bundler_images:
    name: "Build and push builder-bundler images"
    strategy:
      matrix:
        environment:
          - name: test
            role_arn: ${{ vars.AWS_ROLE_BUILD_TEST_IMAGE_ARN }}
            cache_bucket: ${{ vars.TEST_BUILD_CACHE_BUCKET_NAME }}
          - name: staging
            role_arn: ${{ vars.AWS_ROLE_BUILD_STG_IMAGE_ARN }}
            cache_bucket: ${{ vars.STG_BUILD_CACHE_BUCKET_NAME }}
          - name: production
            role_arn: ${{ vars.AWS_ROLE_BUILD_PRD_IMAGE_ARN }}
            cache_bucket: ${{ vars.PRD_BUILD_CACHE_BUCKET_NAME }}
    runs-on: [runs-on,image=ci-arm64,family=c8g.xlarge,ssh=false]
    timeout-minutes: 20
    outputs:
      image_name: ${{ steps.set-image-name.outputs.image_name }}
    steps:
      - name: Enable Outbound Firewall
        run: |
          ALLOWED_FQDNS=(
            ${{ matrix.environment.cache_bucket }}.s3.${{vars.AWS_REGION}}.amazonaws.com  # Build cache S3 Bucket
            dl-cdn.alpinelinux.org                                                        # Alpine Linux repository
            rubygems.org                                                                  # RubyGems repository
            index.rubygems.org                                                            # RubyGems repository
            ghcr.io                                                                       # GitHub Container Registry
            pkg-containers.githubusercontent.com                                          # GitHub Container Registry
            github.com                                                                    # GitHub checkout
          )

          # Join array elements with commas for the allow-fqdns parameter
          ALLOWED_FQDNS_JOINED=$(IFS=,; echo "${ALLOWED_FQDNS[*]}")

          sudo /usr/local/bin/outbound_firewall.sh --docker --allow-fqdns="$ALLOWED_FQDNS_JOINED"

      - name: Purge cached images
        run: |
          sudo docker system prune -af

      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0

      - name: Login to GitHub Container Registry
        uses: docker/login-action@184bdaa0721073962dff0199f1fb9940f07167d1 # v3.5.0
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set shared variables
        run: |
          export RAILS_ENV=${{ matrix.environment.name }}
          .github/workflows/scripts/set_gha_shared_variables.sh
      
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@7474bc4690e29a8392af63c5b98e7449536d5c3a # v4.3.1
        with:
          role-to-assume: ${{ matrix.environment.role_arn }}
          role-duration-seconds: 1800
          aws-region: ${{ vars.AWS_REGION }}

      # docker-container driver is required to use zstd compression
      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@e468171a9de216ec08956ac3ada2f0791b6bd435 # v3.11.1
        with:  
          driver: docker-container
          cleanup: false

      - name: Set image name output
        id: set-image-name
        run: |
          echo "image_name=${{ env.BUILDER_BUNDLER_CACHE_IMAGE_NAME }}" >> $GITHUB_OUTPUT

      - name: Build and push the ${{ matrix.environment.name }} builder-bundler image
        env:
          BUILD_CACHE_BUCKET_NAME: ${{ matrix.environment.cache_bucket }}
          BASE_CACHE_IMAGE: ${{ env.BASE_CACHE_IMAGE }}
          BUILDER_CACHE_IMAGE: ${{ env.BUILDER_CACHE_IMAGE }}
          BUILDER_BUNDLER_CACHE_IMAGE: ${{ env.BUILDER_BUNDLER_CACHE_IMAGE }}
          BUNDLE_GITHUB__COM: ${{ secrets.BUNDLE_GITHUB__COM }}
          BUILDX_BUILDER: ${{ steps.buildx.outputs.name }}
          # Note: we don't push to production since we actually use the same image for staging and production
          # However, we still want to build the image so the production gem cache on s3 is up to date
          # Only push for staging and test environments
          TEST_MODE: ${{ matrix.environment.name == 'production' }}
        run: |
          .github/workflows/scripts/build_and_push_bundler_image.sh

      - name: Alert On Unexpected Network Activity
        if: always()
        run: |
          /usr/local/bin/alert_on_unexpected_activity.sh

  cleanup_builder_bundler:
    name: "Cleanup builder-bundler images"
    permissions:
      contents: none
      packages: write
    needs: build_bundler_images
    # don't run for PRs, only on schedule or manual trigger
    if: github.event_name != 'push' 
    runs-on: ubuntu-latest # Note: We cant run this on our own runners because it doesn't work on ARM
    steps:
      - name: Cleanup Builder Bundler images
        uses: snok/container-retention-policy@4f22ef80902ad409ed55a99dc5133cc1250a0d03 # v3.0.0
        with:
          image-names: |
            ${{ needs.build_bundler_images.outputs.image_name }}
          token: ${{ secrets.GITHUB_TOKEN }}
          cut-off: 4w
          account: ${{ github.repository_owner }}