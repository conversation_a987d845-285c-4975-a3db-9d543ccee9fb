# The reusable workflow for deploying the application to a cluster.
name: "_deploy (template workflow)"
on:
  workflow_call:
    inputs:
      branch:
        required: false
        type: string
        default: ${{ github.head_ref || github.ref_name }}
      cluster_name:
        required: true
        type: string
      refresh_db_snapshot:
        required: false
        type: boolean
        default: false
      aws_region:
        required: true
        type: string
      build_cache_bucket_name:
        required: true
        type: string
      assets_bucket_name:
        required: true
        type: string
      ecr_repo:
        required: true
        type: string
      aws_role_build_image_arn:
        required: true
        type: string
      aws_role_deploy_image_arn:
        required: true
        type: string
      image_name:
        required: true
        type: string
      environment:
        required: true
        type: string
      redeploy_current_revision:
        required: false
        type: boolean
        default: false
        description: 'Re-deploy the currently deployed revision'
      force_rebuild_docker_caches_except:
        required: false
        type: string
        default: ''
        description: 'Force rebuild of docker caches except skip rebuilding if BASE_FROM matches this value'
    secrets:
      BUNDLE_GITHUB__COM:
        required: true
      SCHEMA_MIGRATIONS_API_KEY:
        required: true
      ROLLBAR_ACCESS_TOKEN:
        required: false
      MERGE_MAIN_TO_DEV_TOKEN:
        required: false
      SLACK_WEBHOOK_URL:
        required: true
  
permissions:
  actions: write
  id-token: write
  contents: write
  packages: write

# I recommend adding another concurrency group on the calling workflow
# as I've seen github actions get stuck sometimes when only enforcing the concurrency
# in the reusable workflow.  I'm not sure why this is and it shouldn't be necessary. 
# Probably a bug in the github actions platform.
concurrency:
  group: _deploy-${{ inputs.environment }}-${{ inputs.cluster_name }}

jobs:
  deploy:
    name: Deploy
    runs-on: [runs-on,image=ci-arm64,family=c8g.xlarge,ssh=false]
    timeout-minutes: 45
    environment: 
      name: ${{ inputs.environment == 'production' && 'production' || format('staging ({0})', inputs.cluster_name) }}
      url:  ${{ inputs.environment == 'production' && 'https://admin.ziplines.com' || format('https://admin.{0}.stg.ziplines.dev', inputs.cluster_name) }}
    env:
      BUNDLE_GITHUB__COM: ${{ secrets.BUNDLE_GITHUB__COM }}
    steps:
      - name: Enable Outbound Firewall
        run: |
          ALLOWED_FQDNS=(
            ${{ inputs.build_cache_bucket_name }}.s3.${{vars.AWS_REGION}}.amazonaws.com       # Build cache S3 Bucket
            ${{ inputs.assets_bucket_name }}.s3.${{vars.AWS_REGION}}.amazonaws.com            # Assets S3 Bucket
            ${{ inputs.assets_bucket_name }}.s3-accelerate.amazonaws.com                      # Assets S3 Accelerate Bucket
            dl-cdn.alpinelinux.org                                                            # Alpine Linux repository
            rubygems.org                                                                      # RubyGems repository
            index.rubygems.org                                                                # RubyGems repository
            ghcr.io                                                                           # GitHub Container Registry
            pkg-containers.githubusercontent.com                                              # GitHub Container Registry
            github.com                                                                        # GitHub checkout
            api.ecr.${{vars.AWS_REGION}}.amazonaws.com                                        # Amazon Elastic Container Registry
            ${{inputs.ecr_repo}}                                                              # Amazon Elastic Container Registry
            ecs.${{vars.AWS_REGION}}.amazonaws.com                                            # Amazon Elastic Container Service
            streaming-logs.${{vars.AWS_REGION}}.amazonaws.com                                 # Streaming db:migrate logs
            api.rollbar.com                                                                   # Rollbar
            ${{ inputs.environment == 'production' && 'admin.ziplines.com' || format('admin.{0}.stg.ziplines.dev', inputs.cluster_name) }} # Deployment API
          )

          # Join array elements with commas for the allow-fqdns parameter
          ALLOWED_FQDNS_JOINED=$(IFS=,; echo "${ALLOWED_FQDNS[*]}")

          # Log only for now to avoid breaking deployments
          sudo /usr/local/bin/outbound_firewall.sh --log-only --docker --allow-fqdns="$ALLOWED_FQDNS_JOINED"

      - name: Configure AWS Credentials for Building Image
        uses: aws-actions/configure-aws-credentials@7474bc4690e29a8392af63c5b98e7449536d5c3a # v4.3.1
        with:
          role-to-assume: ${{ inputs.aws_role_build_image_arn }}
          role-duration-seconds: 1800
          aws-region: ${{ inputs.aws_region }}

      - name: Get currently deployed revision and branch
        id: current_deployment
        if: ${{ inputs.redeploy_current_revision }}
        run: |
          if [[ "${{ inputs.environment }}" == "production" ]]; then
            echo "ERROR: redeploy_current_revision cannot be used in production environment."
            echo "This feature is only available for staging environments."
            exit 1
          fi
          
          # Fetch SSM parameter with cluster tasks configuration
          echo "Fetching SSM parameter for cluster: ${{ inputs.cluster_name }}"
          TASKS_JSON=$(aws ssm get-parameters-by-path --path "/meta/clusters/${{ inputs.cluster_name }}" --query 'Parameters[?Name==`/meta/clusters/${{ inputs.cluster_name }}/tasks.json`].Value | [0]' --output text)
          
          # Parse container_name and task_definition_name from the "web" service
          echo "Parsing web service configuration..."
          CONTAINER_NAME=$(echo "$TASKS_JSON" | jq -r '.web.container_name')
          TASK_DEFINITION_NAME=$(echo "$TASKS_JSON" | jq -r '.web.task_definition_name')
          
          echo "Container name: $CONTAINER_NAME"
          echo "Task definition name: $TASK_DEFINITION_NAME"
          
          # Fetch the latest task definition
          echo "Fetching latest task definition..."
          if TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "$TASK_DEFINITION_NAME" --query 'taskDefinition' 2>/dev/null); then
            echo "Found task definition: $TASK_DEFINITION_NAME"
            
            # Extract revision and branch from environment variables
            echo "Extracting revision from APP_REVISION environment variable..."
            REVISION=$(echo "$TASK_DEFINITION" | jq -r ".containerDefinitions[] | select(.name == \"$CONTAINER_NAME\") | .environment[] | select(.name == \"APP_REVISION\") | .value")
            echo "Current revision: $REVISION"
            
            echo "Extracting branch name from APP_BRANCH environment variable..."
            BRANCH_NAME=$(echo "$TASK_DEFINITION" | jq -r ".containerDefinitions[] | select(.name == \"$CONTAINER_NAME\") | .environment[] | select(.name == \"APP_BRANCH\") | .value")
          else
            echo "Task definition not found, falling back to template task definition..."
            
            # Get template task definition name
            TEMPLATE_TASK_DEFINITION_NAME=$(echo "$TASKS_JSON" | jq -r '.web.template_task_definition_name')
            echo "Template task definition name: $TEMPLATE_TASK_DEFINITION_NAME"
            
            # Fetch template task definition
            echo "Fetching template task definition..."
            TEMPLATE_TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "$TEMPLATE_TASK_DEFINITION_NAME" --query 'taskDefinition')
            
            # Extract branch name from APP_BRANCH environment variable
            echo "Extracting branch name from APP_BRANCH environment variable..."
            BRANCH_NAME=$(echo "$TEMPLATE_TASK_DEFINITION" | jq -r ".containerDefinitions[] | select(.name == \"$CONTAINER_NAME\") | .environment[] | select(.name == \"APP_BRANCH\") | .value")
            echo "Branch name: $BRANCH_NAME"
            
            # Extract image URI from template task definition
            echo "Extracting image URI from template task definition..."
            IMAGE_URI=$(echo "$TEMPLATE_TASK_DEFINITION" | jq -r ".containerDefinitions[] | select(.name == \"$CONTAINER_NAME\") | .image")
            echo "Template image URI: $IMAGE_URI"
            
            # Extract repository name from image URI
            REPOSITORY_NAME=$(echo "$IMAGE_URI" | sed 's|.*/||' | sed 's|:.*||')
            echo "Repository name: $REPOSITORY_NAME"
            
            # Get image tags from ECR
            echo "Fetching image tags from ECR..."
            IMAGE_TAGS=$(aws ecr describe-images --repository-name "$REPOSITORY_NAME" --query 'imageDetails[].imageTags[]' --output text)
            
            # Find first tag that starts with 40-character hex string
            echo "Looking for revision tag..."
            REVISION_TAG=$(echo "$IMAGE_TAGS" | tr ' ' '\n' | grep -E '^[0-9a-f]{40}_' | head -n1)
            
            if [[ -n "$REVISION_TAG" ]]; then
              REVISION=$(echo "$REVISION_TAG" | cut -d'_' -f1)
              echo "Found revision from tag: $REVISION"
            else
              echo "ERROR: Could not find revision tag in image tags"
              exit 1
            fi
          fi
          
          # Validate that both revision and branch are set
          if [[ -z "$REVISION" || "$REVISION" == "null" ]]; then
            echo "ERROR: Could not determine revision"
            exit 1
          fi
          
          if [[ -z "$BRANCH_NAME" || "$BRANCH_NAME" == "null" ]]; then
            echo "ERROR: Could not determine branch name"
            exit 1
          fi
          
          # Set outputs for use in subsequent steps
          echo "revision=$REVISION" >> $GITHUB_OUTPUT
          echo "branch=$BRANCH_NAME" >> $GITHUB_OUTPUT
          
          echo "Successfully extracted current deployment info:"
          echo "  Revision: $REVISION"
          echo "  Branch: $BRANCH_NAME"

      - name: Ensure valid branch
        if: ${{ inputs.environment == 'production' }}
        run: |
          if [[ "${GITHUB_REF}" != "refs/heads/main" && "${GITHUB_REF}" != "refs/heads/dev" ]]; then
            echo "Production deployments can only be run on the 'main' or 'dev' branches."
            exit 1
          fi

      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
        with:
          ref: ${{ inputs.redeploy_current_revision && steps.current_deployment.outputs.revision || '' }}
          fetch-depth: ${{ inputs.environment == 'production' && github.ref == 'refs/heads/dev' && '0' || '1' }}

      - name: Merge to main
        if: ${{ inputs.environment == 'production' && github.ref == 'refs/heads/dev' }}
        run: |
          git fetch origin main:main
          git checkout main
          git merge origin/main --ff-only
          git merge dev --ff-only
          git push origin main

      # If multiple PRs are merged at the same time, Github can sometimes run them out of order
      # This job ensures we are only deploying the latest sha (i.e. the HEAD) for the branch
      - name: Ensure we are deploying the latest sha
        id: ensure_valid_branch
        if: ${{ github.event_name != 'workflow_dispatch' && !inputs.redeploy_current_revision }}
        run: |
          git checkout ${{ inputs.branch }}
          git fetch origin ${{ inputs.branch }}
          if [ $(git rev-parse HEAD) != $(git rev-parse origin/${{ inputs.branch }}) ]; then 
            echo 'This is not the latest sha for the branch. Skipping deploy.'
            echo "Debug: $(git rev-parse HEAD) != $(git rev-parse origin/${{ inputs.branch }})"
            exit 1
          fi
        shell: bash

      - name: Set shared variables
        id: shared_vars
        run: |
          export REVISION=$(git rev-parse HEAD)
          echo ${REVISION} > REVISION
          echo "REVISION=${REVISION}" >> $GITHUB_ENV
          export BRANCH_NAME=${{ inputs.redeploy_current_revision && steps.current_deployment.outputs.branch || inputs.branch }}
          export RAILS_ENV=${{ inputs.environment }}
          export GITHUB_TOKEN=${{ secrets.GITHUB_TOKEN }}
          export LOGIN_TO_GHCR=true
          export SET_DOCKER_CACHE_VARS=true
          echo "year=$(date +'%Y')" >> $GITHUB_OUTPUT
          echo "month=$(date +'%m')" >> $GITHUB_OUTPUT
          .github/workflows/scripts/set_gha_shared_variables.sh

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@062b18b96a7aff071d4dc91bc00c4c1a7945b076 # v2.0.1

      - name: Check if prebuilt image exists
        if: ${{ inputs.environment == 'production' }}
        id: prebuilt_image
        run: |
          if aws ecr describe-images --repository-name ${{ inputs.image_name }} --image-ids imageTag=${{ env.PREBUILT_IMAGE_TAG }} &>/dev/null; then
            echo uri="${{ inputs.ecr_repo }}/${{ inputs.image_name }}:${{ env.PREBUILT_IMAGE_TAG }}" >> $GITHUB_OUTPUT
            echo "Using prebuilt image tag: ${{ env.PREBUILT_IMAGE_TAG }}"
            MANIFEST=$(aws ecr batch-get-image --repository-name ${{ inputs.image_name }} --image-ids imageTag=${{ env.PREBUILT_IMAGE_TAG }} --output text --query 'images[].imageManifest')
            aws ecr batch-delete-image --repository-name ${{ inputs.image_name }} --image-ids imageTag=${{ env.BRANCH_NAME_NORMALIZED }}
            aws ecr put-image --repository-name ${{ inputs.image_name }} --image-tag ${{ env.BRANCH_NAME_NORMALIZED }} --image-manifest "$MANIFEST"
          fi

      - name: Set up Docker Buildx
        if: ${{ steps.prebuilt_image.outputs.uri == '' }}
        id: buildx
        uses: docker/setup-buildx-action@e468171a9de216ec08956ac3ada2f0791b6bd435 # v3.11.1
        with:  
          driver: docker-container
          cleanup: false

      - name: Build base and builder images if missing or rebuild requested
        if: ${{ steps.prebuilt_image.outputs.uri == '' && (env.BASE_BUILDER_NEED_BUILD == 'true' || (inputs.force_rebuild_docker_caches_except != '' && env.BASE_FROM != inputs.force_rebuild_docker_caches_except)) }}
        env:
          BUILDX_BUILDER: ${{ steps.buildx.outputs.name }}
        run: |
          echo "Base or builder cache images missing or rebuild requested, building them now..."
          .github/workflows/scripts/build_and_push_base_builder_images.sh
    
      - name: Delete latest tag for branch
        if: ${{ steps.prebuilt_image.outputs.uri == '' && !inputs.redeploy_current_revision }}
        run: |
          aws ecr batch-delete-image --repository-name ${{ inputs.image_name }} --image-ids imageTag=${{ env.BRANCH_NAME_NORMALIZED }}       
      
      - name: Set image uris
        if: ${{ steps.prebuilt_image.outputs.uri == '' }}
        id: image_uris
        run: |
          uri="${{ inputs.ecr_repo }}/${{ inputs.image_name }}:${{ env.IMAGE_TAG }}"
          echo uri=${uri} >> $GITHUB_OUTPUT
          
          if [[ "${{ inputs.redeploy_current_revision }}" != "true" ]]; then
            echo uri_latest="${{ inputs.ecr_repo }}/${{ inputs.image_name }}:${{ env.BRANCH_NAME_NORMALIZED }}" >> $GITHUB_OUTPUT
          fi

      - name: Build Image
        if: ${{ steps.prebuilt_image.outputs.uri == '' }}
        uses: docker/build-push-action@263435318d21b8e681c14492fe198d362a7d2c83 # v6.18.0
        env:
          DOCKER_BUILD_SUMMARY: false
          DOCKER_BUILD_RECORD_UPLOAD: false
        with:
          sbom: false
          provenance: false
          context: ./docker
          target: release
          secrets: |
            aws_access_key_id=${{ env.AWS_ACCESS_KEY_ID }}
            aws_secret_access_key=${{ env.AWS_SECRET_ACCESS_KEY }}
            aws_session_token=${{ env.AWS_SESSION_TOKEN }}
            bundle_github__com=${{ secrets.BUNDLE_GITHUB__COM }}
          build-args: |
            RUBY_VERSION
            DEVOPS_APP_NAME
            APP_REVISION=${{ env.REVISION }}
            APP_BRANCH=${{ env.BRANCH_NAME }}
            RAILS_ENV
            AWS_REGION
            BUILD_CACHE_BUCKET_NAME=${{ inputs.build_cache_bucket_name }}
            BASE_FROM
            BUILDER_FROM
            BUILDER_BUNDLER_FROM
            ASSETS_BUCKET_NAME=${{ inputs.assets_bucket_name }}
            ASSETS_PREFIX=${{ format(vars.ASSETS_PREFIX_FORMAT, env.BRANCH_NAME_NORMALIZED, steps.shared_vars.outputs.year, steps.shared_vars.outputs.month) }}
            FALLBACK_ASSETS_PREFIX=${{ format(vars.ASSETS_PREFIX_FORMAT, 'dev', steps.shared_vars.outputs.year, steps.shared_vars.outputs.month) }}
            SKIP_BUNDLER_FALLBACK_CACHE_UPLOAD=${{ inputs.redeploy_current_revision && 'true' || 'false' }}
          build-contexts: |
            app=.
          tags: |
            ${{ steps.image_uris.outputs.uri }}
            ${{ steps.image_uris.outputs.uri_latest }}
          outputs: |
            type=image,oci-mediatypes=true,compression=zstd,compression-level=3,force-compression=true,push=true
      
      - name: Configure AWS Credentials for Deploying Image
        uses: aws-actions/configure-aws-credentials@7474bc4690e29a8392af63c5b98e7449536d5c3a # v4.3.1
        with:
          role-to-assume: ${{ inputs.aws_role_deploy_image_arn }}
          role-duration-seconds: 1800
          aws-region: ${{ inputs.aws_region }}
      
      - name: Setup Ruby For ci-deployer gem
        uses: ruby/setup-ruby@829114fc20da43a41d27359103ec7a63020954d4 # v1.255.0
        with:
          bundler-cache: true
          working-directory: .github/workflows/scripts/ci-deployer

      - name: Deploy Image
        working-directory: .github/workflows/scripts/ci-deployer
        env:
          SCHEMA_MIGRATIONS_API_KEY: ${{ secrets.SCHEMA_MIGRATIONS_API_KEY }}
          CLUSTER_NAME: ${{ inputs.cluster_name }}
          IMAGE: ${{ steps.prebuilt_image.outputs.uri == '' && steps.image_uris.outputs.uri || steps.prebuilt_image.outputs.uri }}
          APP_ROOT: ../../../../
          REFRESH_DB_SNAPSHOT: ${{ inputs.refresh_db_snapshot && 'true' || 'false' }}
          APP_REVISION: ${{ env.REVISION }}
          APP_BRANCH: ${{ env.BRANCH_NAME }}
        run: |
          bundle exec ci-deployer 
      
      - name: Notify Rollbar of deployment
        continue-on-error: true
        uses: rollbar/github-deploy-action@eaf2a60ea238bd273226eee0ddceecfe5611964d # 2.1.2
        if: ${{ inputs.environment == 'production' }}
        with:
          environment: 'production'
          version: ${{ env.REVISION }}
        env:
          ROLLBAR_ACCESS_TOKEN: ${{ secrets.ROLLBAR_ACCESS_TOKEN }}
          ROLLBAR_USERNAME: ${{ github.actor }}

      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
        if: ${{ inputs.environment == 'production' && github.ref == 'refs/heads/main'}}
        with:
          token: ${{ secrets.MERGE_MAIN_TO_DEV_TOKEN }}
          ref: 'main'
          fetch-depth: 0 

      - name: Merge to dev
        if: ${{ inputs.environment == 'production' && github.ref == 'refs/heads/main'}}
        run: |
          git config --local user.name "Ziplines Bot"
          git config --local user.email "<EMAIL>"
          git fetch origin dev:dev
          git checkout dev
          git merge origin/dev --ff-only
          git merge main
          git push origin dev

      - name: Alert On Unexpected Network Activity
        if: always()
        run: |
          set +e

          if [ "${{ inputs.environment }}" == "production" ]; then
            MESSAGE_TEXT="During deployment of <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|production>"
          else
            MESSAGE_TEXT="During deployment of <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|${{ inputs.cluster_name }}> ${{ inputs.environment }}"
          fi

          /usr/local/bin/alert_on_unexpected_activity.sh \
            --slack-webhook-url "${{ secrets.SLACK_WEBHOOK_URL }}" \
            --slack-message "$MESSAGE_TEXT"

          # Don't fail the workflow
          exit 0

      - name: Alert On Failed Deployment
        # don't generate an alert if the ensure_valid_branch step fails
        # this can happen on staging deploys when a bunch of PRs are merged at once
        if: ${{ failure() && steps.ensure_valid_branch.conclusion != 'failure' }} 
        run: |
          if [ "${{ inputs.environment }}" == "production" ]; then
            MESSAGE_TEXT="Deployment to <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|production> failed!"
          else
            MESSAGE_TEXT="Deployment to <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|${{ inputs.cluster_name }}> ${{ inputs.environment }} failed!"
          fi
          
          # Send the Slack notification
          curl -sS -X POST -H 'Content-type: application/json' --data "{
            \"attachments\": [
              {
                \"title\": \"Failed Deploy\",
                \"color\": \"danger\",
                \"text\": \"$MESSAGE_TEXT\"
              }
            ]
          }" ${{ secrets.SLACK_WEBHOOK_URL }} > /dev/null
        