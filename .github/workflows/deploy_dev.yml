name: Deploy Staging (dev)
on:
  workflow_dispatch:
    inputs:
      refresh_db_snapshot:
        description: Refresh DB snapshot to latest
        type: boolean
        default: false
  schedule:
    - cron:  "30 2 * * *"
  push:
    branches:
      - dev
  
permissions:
  actions: write
  id-token: write
  contents: write
  packages: write

concurrency:
  group: deploy_dev

jobs:
  deploy_staging:
    name: dev
    uses: ./.github/workflows/_deploy.yml
    with:
      environment: staging
      cluster_name: dev
      aws_role_build_image_arn: ${{ vars.AWS_ROLE_BUILD_STG_IMAGE_ARN}}
      aws_role_deploy_image_arn: ${{ vars.AWS_ROLE_DEPLOY_STG_IMAGE_ARN}}
      aws_region: ${{ vars.AWS_REGION }}
      ecr_repo: ${{ vars.STG_ECR_REPO }}
      image_name: ${{ vars.IMAGE_NAME }}
      assets_bucket_name: ${{ vars.STG_ASSETS_BUCKET_NAME }}
      build_cache_bucket_name: ${{ vars.STG_BUILD_CACHE_BUCKET_NAME }}
      refresh_db_snapshot: ${{ github.event.inputs.refresh_db_snapshot == 'true' || github.event_name == 'schedule' }}
    secrets:
      BUNDLE_GITHUB__COM: ${{ secrets.BUNDLE_GITHUB__COM }}
      SCHEMA_MIGRATIONS_API_KEY: ${{ secrets.STG_SCHEMA_MIGRATIONS_API_KEY }}
      SLACK_WEBHOOK_URL: ${{ secrets.STG_SLACK_WEBHOOK_URL }}

  # run_qa_tests:
  #   name: Run QA Tests
  #   needs: deploy_staging
  #   if: ${{ github.event_name == 'push' }}
  #   uses: zip-learning/qa/.github/workflows/specs.yml@main
  #   with:
  #     staging_server_name: dev
  #   secrets:
  #     BUNDLE_GITHUB__COM: ${{ secrets.BUNDLE_GITHUB__COM }}
  #     QA_SVC_CORE_PASSWORD: ${{ secrets.QA_SVC_CORE_PASSWORD }}
  #     QA_SVC_CORE_TOTP_SECRET: ${{ secrets.QA_SVC_CORE_TOTP_SECRET }}

  # alert_on_qa_failure:
  #   name: Alert On QA Test Failure
  #   needs: run_qa_tests
  #   if: cancelled() || failure()
  #   runs-on: ubuntu-latest
  #   steps:
  #     - name: Checkout repository
  #       uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

  #     - name: Alert On Failed QA Tests
  #       run: |
  #         ACTOR="${{ github.actor }}"
          
  #         # Lookup the slack member id from the github actor
  #         if [ -f ".github/workflows/config/slack_developer_notifications.yml" ]; then
  #           SLACK_USER=$(grep "^${ACTOR}:" .github/workflows/config/slack_developer_notifications.yml | cut -d':' -f2 | xargs)
  #           if [ -n "$SLACK_USER" ]; then
  #             USER_MENTION="<@${SLACK_USER}>"
  #           else
  #             USER_MENTION="$ACTOR"
  #           fi
  #         else
  #           USER_MENTION="$ACTOR"
  #         fi
          
  #         MESSAGE_TEXT="❌ QA Tests failed on <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|dev> staging! ${USER_MENTION}"
          
  #         curl -sS -X POST -H 'Content-type: application/json' --data "{
  #           \"attachments\": [
  #             {
  #               \"title\": \"Failed QA Tests\",
  #               \"color\": \"danger\",
  #               \"text\": \"$MESSAGE_TEXT\"
  #             }
  #           ]
  #         }" ${{ secrets.CORE_DEV_SLACK_WEBHOOK_URL }} > /dev/null
