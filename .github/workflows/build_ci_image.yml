# This workflow builds the image (AMI) that will be used for the self-hosted "runs-on" CI environment.
# The purpose of this is to cache frequently used dependencies on the AMI to speed up the CI process.
# It also cleans up old AMIs that are no longer needed.
# Runs weekly and has the option to "build from scratch" which means to build the AMI from the latest base
# "runs-on" AMI.  Currently it will automatically rebuild from scratch once a month, on other weeks it will just
# update the AMI from our last built one (which is much faster).  It's important to rebuild it from the base 
# runs-on AMI from time to time in order to get the updated runs-on dependencies (e.g. the GHA self hosted 
# runner agent)
name: "Build CI Image"
on:
  workflow_dispatch:
    inputs:
      build_from_scratch:
        description: "Build from scratch"
        type: boolean
        default: false
  schedule:
    - cron:  "0 9 * * 1"

permissions:
  id-token: write
  contents: read
  packages: read

jobs:
  build_ci_images:
    name: "Build AMI"
    runs-on: [runs-on,image=ci-arm64,family=c8g.xlarge,ssh=false]
    timeout-minutes: 60
    steps:
      - name: Enable Outbound Firewall
        run: |
          ALLOWED_FQDNS=(
            rubygems.org                                           # RubyGems repository
            index.rubygems.org                                     # RubyGems repository
            github.com                                             # GitHub checkout
            ec2.${{vars.AWS_REGION}}.amazonaws.com                 # Packer, Clean up old AMIs
            iam.amazonaws.com                                      # Packer
            releases.hashicorp.com                                 # Packer releases
            checkpoint-api.hashicorp.com                           # Packer versioning
          )

          # Join array elements with commas for the allow-fqdns parameter
          ALLOWED_FQDNS_JOINED=$(IFS=,; echo "${ALLOWED_FQDNS[*]}")

          sudo /usr/local/bin/outbound_firewall.sh --allow-fqdns="$ALLOWED_FQDNS_JOINED"

      - name: Checkout repository
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
      
      - name: Setup `packer`
        uses: hashicorp/setup-packer@1aa358be5cf73883762b302a3a03abd66e75b232 # v3.1.0
        with:
          version: 1.14.1
      
      - name: Run `packer init`
        working-directory: .github/workflows/packer
        run: "packer init ci_ami.pkr.hcl"
      
      - name: Configure AWS Credentials For Building CI AMI
        uses: aws-actions/configure-aws-credentials@7474bc4690e29a8392af63c5b98e7449536d5c3a # v4.3.1
        with:
          role-to-assume: ${{ vars.AWS_ROLE_BUILD_CI_IMAGES_ARN }}
          role-duration-seconds: 3600
          aws-region: ${{ vars.AWS_REGION }}

      - name: Set shared variables
        id: set_vars
        run: |
          .github/workflows/scripts/set_gha_shared_variables.sh
          echo "POSTGRES_IMAGE=$(yq '.env.POSTGRES_IMAGE' .github/workflows/tests.yml)" >> $GITHUB_ENV
          echo "REDIS_IMAGE=$(yq '.env.REDIS_IMAGE' .github/workflows/tests.yml)" >> $GITHUB_ENV

          # Check if build_from_scratch input is true
          if [ "${{ inputs.build_from_scratch }}" == "true" ]; then
            echo "BUILD_FROM_SCRATCH=true" >> $GITHUB_ENV
          else
            # Check if this is a scheduled run
            if [ "${{ github.event_name }}" == "schedule" ]; then
              # Check if this is the first Monday of the month (day 1-7)
              DAY_OF_MONTH=$(date +%-d)
              if [ "$DAY_OF_MONTH" -le "7" ]; then
                echo "First Monday of the month - building from scratch" >> $GITHUB_STEP_SUMMARY
                echo "BUILD_FROM_SCRATCH=true" >> $GITHUB_ENV
                exit 0
              fi
            fi
            
            # For non-scheduled runs or not the first Monday, use existing logic
            EXISTING_AMIS=$(aws ec2 describe-images --owners self --filters "Name=name,Values=${{vars.CI_IMAGE_BUILDER_AMI_NAME_PREFIX}}-*" --query 'length(Images[])')
            if [ "$EXISTING_AMIS" -gt "0" ]; then
              echo "BUILD_FROM_SCRATCH=false" >> $GITHUB_ENV
            else
              echo "BUILD_FROM_SCRATCH=true" >> $GITHUB_ENV
            fi
          fi
      
      - name: Run `packer build`
        working-directory: .github/workflows/packer
        run: |
          PACKER_CMD="packer build -var 'build_from_scratch=${{env.BUILD_FROM_SCRATCH}}' -var 'ami_name_prefix=${{vars.CI_IMAGE_BUILDER_AMI_NAME_PREFIX}}' -var 'run_tag_prefix=${{vars.CI_IMAGE_BUILDER_RUN_TAG_PREFIX}}' -var 'iam_instance_profile=${{vars.CI_IMAGE_BUILDER_INSTANCE_PROFILE}}' -var 'github_token=${{secrets.GITHUB_TOKEN}}' -var 'github_actor=${{github.actor}}' -var 'postgres_image=${{ env.POSTGRES_IMAGE }}' -var 'redis_image=${{ env.REDIS_IMAGE }}' -var 'base_image=${{ env.BASE_CACHE_IMAGE }}' -var 'builder_image=${{ env.BUILDER_CACHE_IMAGE}}' -var 'builder_bundler_test_image=${{ env.TEST_BUILDER_BUNDLER_CACHE_IMAGE }}' ci_ami.pkr.hcl"
          
          for attempt in 1 2 3; do
            echo "Attempt $attempt of 3"
            
            # Capture both stdout and stderr, and the exit code while streaming
            set +e
            set -o pipefail
            OUTPUT=$(eval $PACKER_CMD 2>&1 | tee /dev/stderr)
            EXIT_CODE=$?
            set +o pipefail
            set -e
            
            if [ $EXIT_CODE -eq 0 ]; then
              echo "Packer build succeeded on attempt $attempt"
              break
            elif [ $attempt -eq 3 ]; then
              echo "Packer build failed after 3 attempts"
              exit 1
            elif echo "$OUTPUT" | grep -qi "capacity"; then
              echo "Capacity-related error detected, retrying in 30 seconds..."
              sleep 30
            else
              echo "Non-capacity error detected, not retrying"
              exit $EXIT_CODE
            fi
          done

      - name: Configure AWS Credentials For Cleanup CI AMIs
        uses: aws-actions/configure-aws-credentials@7474bc4690e29a8392af63c5b98e7449536d5c3a # v4.3.1
        with:
          role-to-assume: ${{ vars.AWS_ROLE_CLEANUP_CI_IMAGES_ARN }}
          role-duration-seconds: 3600
          aws-region: ${{ vars.AWS_REGION }}

      - name: Install Ruby
        uses: ruby/setup-ruby@829114fc20da43a41d27359103ec7a63020954d4 # v1.255.0
        with:
          bundler-cache: true
          working-directory: .github/workflows/scripts/cleanup_amis
      
      - name: Cleanup AMIs
        working-directory: .github/workflows/scripts/cleanup_amis
        run: |
          export AMI_FILTER='${{vars.CI_IMAGE_BUILDER_AMI_NAME_PREFIX}}-*'
          bundle exec ruby cleanup_amis.rb

      - name: Alert On Unexpected Network Activity
        if: always()
        run: |
          /usr/local/bin/alert_on_unexpected_activity.sh