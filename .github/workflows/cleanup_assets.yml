# This workflow cleans up old assets from the assets bucket on S3 in staging and production environments.
name: "Cleanup Old Assets"
on:
  workflow_dispatch:
  schedule:
    - cron: 0 7 1 * *

permissions:
  id-token: write
  contents: read

jobs:
  cleanup_assets:
    name: "Cleanup Old Assets"
    runs-on: [runs-on,image=ci-arm64,family=c8g.large,ssh=false]
    timeout-minutes: 10
    env:
      BUNDLE_WITHOUT: "test"
    steps:
      - name: Enable Outbound Firewall
        run: |
          ALLOWED_FQDNS=(
            ${{ vars.PRD_ASSETS_BUCKET_NAME }}.s3.${{vars.AWS_REGION}}.amazonaws.com          # Prod Assets S3 Bucket
            ${{ vars.STG_ASSETS_BUCKET_NAME }}.s3.${{vars.AWS_REGION}}.amazonaws.com          # Assets S3 Bucket
            rubygems.org                                                                      # RubyGems repository
            index.rubygems.org                                                                # RubyGems repository
            github.com                                                                        # GitHub checkout
          )

          # Join array elements with commas for the allow-fqdns parameter
          ALLOWED_FQDNS_JOINED=$(IFS=,; echo "${ALLOWED_FQDNS[*]}")

          sudo /usr/local/bin/outbound_firewall.sh --allow-fqdns="$ALLOWED_FQDNS_JOINED"

      - name: Checkout repository
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0

      - name: Install Ruby
        uses: ruby/setup-ruby@829114fc20da43a41d27359103ec7a63020954d4 # v1.255.0
        with:
          bundler-cache: true
          working-directory: .github/workflows/scripts/asset_cleaner
      
      - name: Configure AWS Credentials For Staging Cleanup
        uses: aws-actions/configure-aws-credentials@7474bc4690e29a8392af63c5b98e7449536d5c3a # v4.3.1
        with:
          role-to-assume: ${{ vars.AWS_ROLE_CLEANUP_STG_ASSETS_ARN }}
          role-duration-seconds: 3600
          aws-region: ${{ vars.AWS_REGION }}

      - name: Cleanup Staging Assets
        working-directory: .github/workflows/scripts/asset_cleaner
        run: |
          export ENVIRONMENT=staging
          export ASSETS_BUCKET=${{ vars.STG_ASSETS_BUCKET_NAME }}
          bundle exec bin/clean

      - name: Configure AWS Credentials For Production Cleanup
        uses: aws-actions/configure-aws-credentials@7474bc4690e29a8392af63c5b98e7449536d5c3a # v4.3.1
        with:
          role-to-assume: ${{ vars.AWS_ROLE_CLEANUP_PRD_ASSETS_ARN }}
          role-duration-seconds: 3600
          aws-region: ${{ vars.AWS_REGION }}

      - name: Cleanup Production Assets
        working-directory: .github/workflows/scripts/asset_cleaner
        run: |
          export ENVIRONMENT=production
          export ASSETS_BUCKET=${{ vars.PRD_ASSETS_BUCKET_NAME }}
          bundle exec bin/clean

      - name: Alert On Unexpected Network Activity
        if: always()
        run: |
          /usr/local/bin/alert_on_unexpected_activity.sh
      
      # The cleanup scripts are currently running in "dry-run" mode since there isn't enough existing
      # assets to test the cleanup process. Once the scripts are working as intended, we can remove
      # the `exit 1` command below and set the PERFORM_DELETIONS env variable on the clean command.
      # However, I'd like to get a few months of asset folders built up before we start deleting them for real.
      # For now, this failure will generate an email which will remind me to check back in on this in a few months.
      - name: Fail Run
        run: exit 1