name: Run Tests
on:
  workflow_dispatch:
  pull_request:
  push:
    branches:
      - dev

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true
  
permissions:
  actions: write
  id-token: write
  contents: read
  packages: write

env:
  # Note that dip pulls in these references so if you change the name, the dip helpers need to be updated
  POSTGRES_IMAGE: postgres:17.5-alpine3.22
  REDIS_IMAGE: valkey/valkey:8.1-alpine

jobs:
  # This is a check to avoid re-running the tests on pushes to dev/main if the PR branch
  # that was merged in already ran the tests for the same code.
  should_skip_tests:
    name: Check if tests can be skipped
    timeout-minutes: 1
    runs-on: ubuntu-latest
    outputs:
      should_skip: ${{ steps.skip_check.outputs.should_skip }}
    if: github.ref == 'refs/heads/dev' || github.ref == 'refs/heads/main'
    steps:
      - id: skip_check
        uses: fkirc/skip-duplicate-actions@f75f66ce1886f00957d99748a42c724f4330bdcf # v5.3.1
        with:
          concurrent_skipping: 'same_content_newer'
          cancel_others: 'true'
          skip_after_successful_duplicate: 'true'
          do_not_skip: '["workflow_dispatch"]'

  tests:
    needs: should_skip_tests
    if: always() && !failure() && !cancelled() && (needs.should_skip_tests.result == 'skipped' || needs.should_skip_tests.outputs.should_skip != 'true')
    name: RSpec, Schema File Consistency, Sidekiq Boot, Outbound Connection Check
    timeout-minutes: ${{ fromJSON(vars.TESTS_TIMEOUT_MINUTES) }}
    runs-on: [runs-on,image=ci-arm64,family=c8g.16xlarge,ssh=false]
    steps:
      - name: Enable Outbound Firewall
        run: |
          ALLOWED_FQDNS=(
            ${{vars.TEST_BUILD_CACHE_BUCKET_NAME}}.s3.${{vars.AWS_REGION}}.amazonaws.com  # Build cache S3 Bucket
            dl-cdn.alpinelinux.org                                                        # Alpine Linux repository
            rubygems.org                                                                  # RubyGems repository
            index.rubygems.org                                                            # RubyGems repository
            github.com                                                                    # GitHub
            ghcr.io                                                                       # GitHub Container Registry
            pkg-containers.githubusercontent.com                                          # GitHub Container Registry
          )

          # Join array elements with commas for the allow-fqdns parameter
          ALLOWED_FQDNS_JOINED=$(IFS=,; echo "${ALLOWED_FQDNS[*]}")

          sudo /usr/local/bin/outbound_firewall.sh --docker --allow-fqdns="$ALLOWED_FQDNS_JOINED"

      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
        with:
          persist-credentials: false # fixes issue where post step tries to delete files in .git but doesn't have permissions due to chown in tests job
      
      - name: Set shared variables
        run: |
          export SET_DOCKER_CACHE_VARS=true
          export RAILS_ENV=test
          export LOGIN_TO_GHCR=true
          export GITHUB_TOKEN=${{ secrets.GITHUB_TOKEN }}
          .github/workflows/scripts/set_gha_shared_variables.sh

      - name: Configure AWS Credentials for Building Test Image
        uses: aws-actions/configure-aws-credentials@7474bc4690e29a8392af63c5b98e7449536d5c3a # v4.3.1
        with:
          role-to-assume: ${{ vars.AWS_ROLE_BUILD_TEST_IMAGE_ARN }}
          role-duration-seconds: 1800
          aws-region: ${{ vars.AWS_REGION }}
          
      - name: Tests
        run: |
          # RSpec, Schema / Annotations consistency, Sidekiq Worker bootup, and Asset Precompile checks
          
          # Configuration
          PARALLEL_TEST_PROCESSOR_RESERVED=$(( $(nproc) / 4 )) # Reserve 25% of processors for other tasks besides tests
          PARALLEL_TEST_PROCESSORS=$(($(nproc) - $PARALLEL_TEST_PROCESSOR_RESERVED))
          SIDEKIQ_TEST_ENV_NUMBER=$(($PARALLEL_TEST_PROCESSORS + 1))
          TOTAL_DATABASES=$((PARALLEL_TEST_PROCESSORS + 1)) # +1 for sidekiq test
          export BUNDLE_GITHUB__COM=${{ secrets.BUNDLE_GITHUB__COM }}
          DB_SCHEMA_KEY=$(echo -n "$(cat db/schema.rb)${{ env.POSTGRES_IMAGE }}" | sha1sum | awk '{print $1}')
          PGDUMP_DIR="/tmp/pg_dump"
          PGDUMP_CONTAINER_DIR="/pg_dump"
          APP_UID=1005
          POSTGRES_UID=70

          # Common docker configuration variables
          DOCKER_MOUNT_OPTIONS="--tmpfs /tmp:uid=$APP_UID,gid=$APP_UID \
          --tmpfs /home/<USER>/app/tmp/pids:uid=$APP_UID,gid=$APP_UID \
          --tmpfs /home/<USER>/app/tmp:uid=$APP_UID,gid=$APP_UID \
          --tmpfs /home/<USER>/app/.git:uid=$APP_UID,gid=$APP_UID \
          --tmpfs /home/<USER>/app/.aws:uid=$APP_UID,gid=$APP_UID \
          --tmpfs /home/<USER>/app/config/credentials:uid=$APP_UID,gid=$APP_UID \
          --tmpfs /home/<USER>/app/log:uid=$APP_UID,gid=$APP_UID"
          # Read-only configuration
          DOCKER_RO_OPTIONS="--read-only $DOCKER_MOUNT_OPTIONS --mount type=bind,source=.,target=/home/<USER>/app,ro"
          # Read-write configuration
          DOCKER_RW_OPTIONS="$DOCKER_MOUNT_OPTIONS --mount type=bind,source=.,target=/home/<USER>/app"

          # Setup docker environment file
          {
            echo "AWS_ACCESS_KEY_ID=${{ env.AWS_ACCESS_KEY_ID }}"
            echo "AWS_SECRET_ACCESS_KEY=${{ env.AWS_SECRET_ACCESS_KEY }}"
            echo "AWS_SESSION_TOKEN=${{ env.AWS_SESSION_TOKEN }}"
            echo "AWS_REGION=${{ env.AWS_REGION }}"
            echo "PARALLEL_TEST_PROCESSORS=$PARALLEL_TEST_PROCESSORS";
          } > /tmp/docker-env

          # Create docker network
          docker network create gha &> /dev/null || true

          #Look up DNS server for gha network (it is always the gateway address)
          DNS_SERVER=$(docker network inspect -f '{{range .IPAM.Config}}{{.Gateway}}{{end}}' gha)
          
          DOCKER_RUN_OPTIONS="--rm --network gha --dns $DNS_SERVER --env-file /tmp/docker-env ${{ env.BUILDER_BUNDLER_CACHE_IMAGE }} bash -c"

          # Build and push base and builder images if missing
          if [ "${{ env.BASE_BUILDER_NEED_BUILD }}" = "true" ]; then
            echo "Base or builder cache images missing, building them now..."
            { time .github/workflows/scripts/build_and_push_base_builder_images.sh; } &> /tmp/docker_base_builder.log
          fi

          # Build and push the builder-bundler docker image if not in the local or remote registry
          if [ "${{ env.BUILDER_BUNDLER_NEED_BUILD }}" = "true" ]; then
            { time TEST_MODE=true BUILD_CACHE_BUCKET_NAME=${{ vars.TEST_BUILD_CACHE_BUCKET_NAME }} .github/workflows/scripts/build_and_push_bundler_image.sh; } &> /tmp/docker_builder_bundler.log
            { time docker image push ${{ env.BUILDER_BUNDLER_CACHE_IMAGE }}; } &>> /tmp/docker_builder_bundler.log &
            DOCKER_PUSH_PID=$!
          fi

          # Ensure working directory has correct permissions for app container to read/write
          sudo chown -R $APP_UID:$APP_UID . &
          CHOWN_PID=$!
          
          # Start Redis
          docker run -d --rm --name redis --network gha --dns $DNS_SERVER --network-alias redis \
            -e GITHUB_ACTIONS=true -e CI=true \
            -e VALKEY_EXTRA_FLAGS="--databases $TOTAL_DATABASES" \
            -p 6379:6379 ${{env.REDIS_IMAGE}} &> /dev/null &
          REDIS_PID=$!

          # Setup and configure PostgreSQL
          {
            mkdir -p $PGDUMP_DIR
            TEMP_FILE=$(mktemp)

            # Try to restore copy of db from cache (to speed up creation)
            if aws s3 cp --only-show-errors "s3://${{ vars.TEST_BUILD_CACHE_BUCKET_NAME }}/postgres/pg_dump.tar.zst.$DB_SCHEMA_KEY" "$TEMP_FILE" &> /dev/null; then
              zstd -d < "$TEMP_FILE" | tar -xf - -C $PGDUMP_DIR
              echo "DB cache found and extracted" > /tmp/db_setup.log
              sudo cp .github/workflows/scripts/parallel_db_setup.sh $PGDUMP_DIR/
              sudo mv $PGDUMP_DIR/schema_cache.yml db/
              sudo chown -R $APP_UID:$APP_UID db/schema_cache.yml
            else
              touch /tmp/db_cache_miss
              echo "DB cache not found" > /tmp/db_setup.log
            fi

            sudo chown -R $POSTGRES_UID:$POSTGRES_UID $PGDUMP_DIR

            # Start PostgreSQL
            docker run -d --rm --name postgres --network gha --dns $DNS_SERVER --network-alias postgres \
              -e POSTGRES_HOST_AUTH_METHOD=trust -e POSTGRES_PASSWORD=postgres \
              -e GITHUB_ACTIONS=true -e CI=true --tmpfs /var/lib/postgresql/data \
              -v $PGDUMP_DIR:$PGDUMP_CONTAINER_DIR -v pg_socket:/var/run/postgresql \
              -p 5432:5432 ${{env.POSTGRES_IMAGE}} \
              -c fsync=off -c full_page_writes=off -c synchronous_commit=off \
              -c shared_buffers=2GB -c work_mem=64MB -c maintenance_work_mem=512MB \
              -c random_page_cost=1.1 -c wal_level=minimal -c max_wal_senders=0 \
              -c max_connections=1000 &> /dev/null

            # Setup databases based on cache status
            if [ -f /tmp/db_cache_miss ]; then
              { time docker run --name db_setup $DOCKER_RW_OPTIONS $DOCKER_RUN_OPTIONS \
                "bundle exec rake parallel:faster_setup[$TOTAL_DATABASES]"; } &>> /tmp/db_setup.log
            else
              { time docker exec --user postgres postgres bash -c \
                "/usr/bin/env NUM_DBS=$TOTAL_DATABASES DB_NAME=${{vars.TEST_DB_NAME}} $PGDUMP_CONTAINER_DIR/parallel_db_setup.sh"; } &>> /tmp/db_setup.log
            fi;
          } &
          DB_SETUP_PID=$!

          # Build tailwind assets (sprockets assets do not need to be built ahead of time)
          wait $CHOWN_PID
          { time docker run --name build_tailwind $DOCKER_RW_OPTIONS $DOCKER_RUN_OPTIONS \
            'bundle exec tailwindcss -i app/assets/stylesheets/application.tailwind.css \
            -o app/assets/builds/tailwind.css -c config/tailwind.config.js --minify'; } &> /tmp/build_tailwind.log &
          TAILWIND_PID=$!

          # Download previous parallel runtime log
          aws s3 cp --quiet \
            s3://${{ vars.TEST_BUILD_CACHE_BUCKET_NAME }}/parallel_tests/parallel_runtime_rspec.log.${{env.BRANCH_NAME_NORMALIZED}} \
            /tmp/parallel_runtime_rspec.log || \
            aws s3 cp --only-show-errors \
            s3://${{ vars.TEST_BUILD_CACHE_BUCKET_NAME }}/parallel_tests/parallel_runtime_rspec.log.dev \
            /tmp/parallel_runtime_rspec.log || \
            touch /tmp/parallel_runtime_rspec.log
          chmod 777 /tmp/parallel_runtime_rspec.log
          
          # Wait for background setup to complete
          wait $REDIS_PID $DB_SETUP_PID $TAILWIND_PID

          # Upload a cache DB dump with rails schema cache if it doesn't exist
          { if [ -f /tmp/db_cache_miss ]; then
            echo "Uploading new db cache dump" >> /tmp/db_setup.log
            { time docker exec --user postgres postgres bash -c \
              "pg_dump -Fd -j 10 -Z 0 -d ${{ vars.TEST_DB_NAME }} -f $PGDUMP_CONTAINER_DIR"; } &>> /tmp/db_setup.log
            sudo cp db/schema_cache.yml $PGDUMP_DIR
            { time { sudo tar -cpf - -C "$PGDUMP_DIR" . | zstd -z | \
              aws s3 cp --only-show-errors - \
              s3://${{ vars.TEST_BUILD_CACHE_BUCKET_NAME }}/postgres/pg_dump.tar.zst.$DB_SCHEMA_KEY; }; } &>> /tmp/db_setup.log
          fi; } &

          # Run RSpec
          # Note: Write out dummy local secret otherwise you can hit race conditions under parallel tests due to bug:
          # https://github.com/rails/rails/issues/53661
          # Note: Put the rspec time in a separate file to avoid it appearing in the rspec output
          # rspec already prints a summary of the time taken so the full docker run time is only
          # useful for debugging
          { time docker run --name rspec -v pg_socket:/var/run/postgresql \
            -v /tmp/parallel_runtime_rspec.log:/home/<USER>/app/tmp/parallel_runtime_rspec.log \
            $DOCKER_RO_OPTIONS $DOCKER_RUN_OPTIONS \
            "/usr/bin/env DB_HOSTNAME=/var/run/postgresql bundle exec parameter-store-env sh -c \
            'echo dummy > tmp/local_secret.txt && parallel_rspec --serialize-stdout --combine-stderr --verbose-process-command'" \
            &> /tmp/rspec.log;} &> /tmp/rspec_time.log &
          RSPEC_PID=$!

          # Check for uncommitted schema / annotation changes
          # Note in Rails 8, it uses the schema to create the db in place of actually running the migrations
          # While this is great for speed, it means we can't catch out of sync schema changes
          # So we delete the db/schema.rb file which causes it to use the migrations instead
          { time docker run --user=root --name annotations $DOCKER_RW_OPTIONS $DOCKER_RUN_OPTIONS '
            apk add git > /dev/null &&
            su app -c "
              git init &> /dev/null
              echo '/tmp/' >> .gitignore
              echo '/log/' >> .gitignore
              echo '/config/credentials/' >> .gitignore
              git add -A > /dev/null
              git config user.email \"<EMAIL>\" > /dev/null
              git config user.name \"GHA\" > /dev/null
              git commit -a --allow-empty-message -m \"\" > /dev/null
              rm db/schema.rb
              export TEST_ENV_NUMBER=999
              CMD=\"bundle exec rake db:create db:migrate ENABLE_ANNOTATE=true\"
              \$CMD > /tmp/output.log 2>&1
              if [ \$? -ne 0 ]; then
                echo \$CMD failed!
                echo Last 100 lines of command output:
                echo
                tail -n 100 /tmp/output.log
                exit 2
              fi
              bundle exec annotaterb models > /dev/null
              git diff --exit-code"'; } &> /tmp/annotations.log &
          ANNOTATIONS_PID=$!
        
          # Check for Sidekiq boot
          { time docker run --name sidekiq -v /home/<USER>/bundle $DOCKER_RO_OPTIONS $DOCKER_RUN_OPTIONS "
            bundle config set --local without 'development test' &&
            /usr/bin/env TEST_ENV_NUMBER=$SIDEKIQ_TEST_ENV_NUMBER bundle exec bin/test_sidekiq_boots_in_ci &&
            cat /home/<USER>/app/log/*.log"; } &> /tmp/sidekiq.log &
          SIDEKIQ_PID=$!

          # Check for asset precompile
          # Set DB_HOSTNAME to 127.0.0.1 to cause any DB queries during asset compilation to raise an error 
          { time docker run --name asset_precompile \
            --tmpfs /home/<USER>/app/public/assets:uid=$APP_UID,gid=$APP_UID \
            $DOCKER_RW_OPTIONS $DOCKER_RUN_OPTIONS \
            "/usr/bin/env DB_HOSTNAME=127.0.0.1 bundle exec rails assets:precompile"; } &> /tmp/assets_precompile.log &
          ASSETS_PRECOMPILE_PID=$!

          # Wait for all processes to complete and capture their exit codes
          # Note: without setting +e, wait will cause the script to exit if either command fails
          set +e
          wait $RSPEC_PID
          RSPEC_EXIT=$?
          wait $ANNOTATIONS_PID
          ANNOTATIONS_EXIT=$?
          wait $SIDEKIQ_PID
          SIDEKIQ_EXIT=$?
          wait $ASSETS_PRECOMPILE_PID
          ASSETS_PRECOMPILE_EXIT=$?

          ALERT_ON_UNEXPECTED_ACTIVITY_OUTPUT=`/usr/local/bin/alert_on_unexpected_activity.sh --slack-webhook-url "${{ secrets.STG_SLACK_WEBHOOK_URL }}" --slack-message "During <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|test run>"`
          ALERT_ON_UNEXPECTED_ACTIVITY_EXIT=$?
          set -e

          # Print check results
          echo -e "\nSummary:"
          
          GREEN="\033[32m"
          RED="\033[31m"
          RESET="\033[0m"
          FAILED=false
          
          # Define ordered check names
          check_names=(
            "RSpec tests"
            "Schema/annotations check"
            "Sidekiq boot check"
            "Assets precompile check"
            "No unexpected network activity"
          )
          
          # Define check results
          declare -A check_results=(
            ["RSpec tests"]=$RSPEC_EXIT
            ["Schema/annotations check"]=$ANNOTATIONS_EXIT
            ["Sidekiq boot check"]=$SIDEKIQ_EXIT
            ["Assets precompile check"]=$ASSETS_PRECOMPILE_EXIT
            ["No unexpected network activity"]=$ALERT_ON_UNEXPECTED_ACTIVITY_EXIT
          )
          
          # Print results in specified order
          for check in "${check_names[@]}"; do
            if [ ${check_results[$check]} -eq 0 ]; then
              echo -e "${GREEN}✓ $check succeeded${RESET}"
            else
              FAILED=true
              echo -e "${RED}✗ $check failed${RESET}"
            fi
          done

          echo " "
 
          # Output RSpec results if they failed
          if [ $RSPEC_EXIT -ne 0 ]; then
            cat /tmp/rspec.log | .github/workflows/scripts/parse_rspec_failures.rb
          fi

          # Output Uncommitted Schema/Annotations results if they failed
          if [ $ANNOTATIONS_EXIT -eq 1 ]; then
            echo -e "\033[31mSchema/annotations check failed:\033[0m"
            echo -e "Uncommitted changes detected after running migrations and model annotations"
            echo "(This can indicate that the schema.rb or model annotations do not match the latest db state)"
            cat /tmp/annotations.log
          elif [ $ANNOTATIONS_EXIT -eq 2 ]; then
            echo -e "\033[31mSchema/annotations check failed:\033[0m"
            echo -e "Migrations failed to apply during the uncommitted schema/model annotations check"
            cat /tmp/annotations.log
          elif [ $ANNOTATIONS_EXIT -ne 0 ]; then
            echo -e "\033[31mSchema/annotations check failed:\033[0m"
            cat /tmp/annotations.log
          fi

          # Output Sidekiq boot results if they failed
          if [ $SIDEKIQ_EXIT -ne 0 ]; then
            echo -e "\033[31mSidekiq failed to boot:\033[0m"
            cat /tmp/sidekiq.log
          fi

          # Output assets precompile results if they failed
          if [ $ASSETS_PRECOMPILE_EXIT -ne 0 ]; then
            echo -e "\033[31mAsset precompile failed:\033[0m"
            cat /tmp/assets_precompile.log
          fi

          # Output unexpected network activity results
          if [ $ALERT_ON_UNEXPECTED_ACTIVITY_EXIT -ne 0 ]; then
            echo -e "\033[31mUnexpected network activity detected:\033[0m"
            echo "$ALERT_ON_UNEXPECTED_ACTIVITY_OUTPUT"
            echo " "
          fi

          echo "::group::Full RSpec log"
          cat /tmp/rspec.log
          echo "::endgroup::"

          # Wait for the docker push to finish
          if [ -n "${DOCKER_PUSH_PID}" ]; then
            wait $DOCKER_PUSH_PID
          fi

          # Upload the latest parallel runtime log
          if [ "$RSPEC_EXIT" -eq 0 ]; then
            aws s3 cp --only-show-errors /tmp/parallel_runtime_rspec.log \
              s3://${{ vars.TEST_BUILD_CACHE_BUCKET_NAME }}/parallel_tests/parallel_runtime_rspec.log.${{env.BRANCH_NAME_NORMALIZED}}
          fi

          if [ "$FAILED" = true ]; then
            exit 1
          fi

      - name: Upload build logs
        if: always()
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: logs
          path: |
            /tmp/*.log        
          retention-days: 5