#!/bin/bash

# Script to be called from our self-hosted runners to setup an outbound firewall
# This script is automatically copied to the /usr/local/bin directory when the CI image 
# is built.
# Usage:
#   outbound_firewall.sh [--docker] [--allow-fqdns=comma,separated,list] [--allow-all-fqdns=comma,separated,list] [--log-only] [--proxy-servers]
# Options:
#   --docker                Enable Docker support (automatically allows Docker pull FQDNS)
#   --allow-fqdns=LIST      Comma-separated list of FQDNs to allow outbound http/https access
#   --allow-all-fqdns=LIST  Comma-separated list of FQDNs to allow all outbound traffic to
#   --log-only              Only log the outbound firewall rules and do not reject any traffic
#   --proxy-servers         Allow special rules for running the partner proxy test suite
set -E

trap 'echo "Error on or near line $LINENO"; exit 1' ERR

DOCKER_SUPPORT=false
ALLOW_DOCKER_PULL=false
HTTP_HTTPS_FQDNS=""
ALL_TRAFFIC_FQDNS=""
LOG_ONLY_MODE=false
PROXY_SERVERS=false
AWS_REGION=$(ec2metadata --availability-zone | sed 's/.$//')

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    --docker)
      DOCKER_SUPPORT=true
      shift
      ;;
    --allow-fqdns=*)
      HTTP_HTTPS_FQDNS="${1#*=}"
      shift
      ;;
    --allow-all-fqdns=*)
      ALL_TRAFFIC_FQDNS="${1#*=}"
      shift
      ;;
    --log-only)
      LOG_ONLY_MODE=true
      shift
      ;;
    --proxy-servers)
      PROXY_SERVERS=true
      shift
      ;;
    *)
      echo "Unknown option: $1"
      echo "Usage: $0 [--docker] [--allow-fqdns=comma,separated,list] [--allow-all-fqdns=comma,separated,list] [--log-only] [--proxy-servers]"
      exit 1
      ;;
  esac
done

# Docker pull FQDNS
DOCKER_PULL_FQDNS=(
  "docker.io"                                                                    # docker pull
  "registry-1.docker.io"                                                         # docker pull
  "auth.docker.io"                                                               # docker pull
  "production.cloudflare.docker.com"                                             # docker pull
  "docker-images-prod.6aa30f8b08e16409b46e0173d6de2f56.r2.cloudflarestorage.com" # docker pull
)

HTTP_HTTPS_FQDNS_ARRAY=()
ALL_TRAFFIC_FQDNS_ARRAY=()

# Add Docker pull fqdns if requested
if [ "$DOCKER_SUPPORT" = true ]; then
  HTTP_HTTPS_FQDNS_ARRAY+=("${DOCKER_PULL_FQDNS[@]}")
fi

# Add HTTP/HTTPS FQDNs if provided
if [ -n "$HTTP_HTTPS_FQDNS" ]; then
  IFS=',' read -ra HTTP_HTTPS_FQDNS <<< "$HTTP_HTTPS_FQDNS"
  for fqdn in "${HTTP_HTTPS_FQDNS[@]}"; do
    HTTP_HTTPS_FQDNS_ARRAY+=("$fqdn")
  done
fi

# Add all-traffic FQDNs if provided
if [ -n "$ALL_TRAFFIC_FQDNS" ]; then
  IFS=',' read -ra ALL_TRAFFIC_FQDNS <<< "$ALL_TRAFFIC_FQDNS"
  for fqdn in "${ALL_TRAFFIC_FQDNS[@]}"; do
    ALL_TRAFFIC_FQDNS_ARRAY+=("$fqdn")
  done
fi

# Setup dnsmasq
HTTP_HTTPS_FQDNS_STRING=$(IFS=/; echo "${HTTP_HTTPS_FQDNS_ARRAY[*]}")
ALL_TRAFFIC_FQDNS_STRING=$(IFS=/; echo "${ALL_TRAFFIC_FQDNS_ARRAY[*]}")

tee /etc/dnsmasq.d/ipset > /dev/null <<EOF
ipset=/$HTTP_HTTPS_FQDNS_STRING/outbound_firewall_v4,outbound_firewall_v6
EOF

# Add all-traffic FQDNs to separate ipset if any are specified
if [ ${#ALL_TRAFFIC_FQDNS_ARRAY[@]} -gt 0 ]; then
  tee -a /etc/dnsmasq.d/ipset > /dev/null <<EOF
ipset=/$ALL_TRAFFIC_FQDNS_STRING/outbound_firewall_all_v4,outbound_firewall_all_v6
EOF
fi

systemctl restart dnsmasq

# Create a custom chain for common rules
iptables -N OUTBOUND_RULES 2>/dev/null || iptables -F OUTBOUND_RULES
ip6tables -N OUTBOUND_RULES 2>/dev/null || ip6tables -F OUTBOUND_RULES

# Allow established and related connections
iptables -A OUTBOUND_RULES -m state --state RELATED,ESTABLISHED -j ACCEPT
ip6tables -A OUTBOUND_RULES -m state --state RELATED,ESTABLISHED -j ACCEPT
# Allow https traffic to any of the allowed fqdns
iptables -A OUTBOUND_RULES -p tcp --dport 443 -m set --match-set outbound_firewall_v4 dst -j ACCEPT
ip6tables -A OUTBOUND_RULES -p tcp --dport 443 -m set --match-set outbound_firewall_v6 dst -j ACCEPT
# Allow all traffic to all-traffic FQDNs
if [ ${#ALL_TRAFFIC_FQDNS_ARRAY[@]} -gt 0 ]; then
  iptables -A OUTBOUND_RULES -m set --match-set outbound_firewall_all_v4 dst -j ACCEPT
  ip6tables -A OUTBOUND_RULES -m set --match-set outbound_firewall_all_v6 dst -j ACCEPT
fi
# Allow EC2 metadata server
iptables -A OUTBOUND_RULES -d *************** -p tcp --dport 80 -j ACCEPT
# Allow dhcpv4 client traffic
iptables -A OUTPUT -p udp -d ******** --sport 68 --dport 67 -j ACCEPT
# Allow dhcpv6 client traffic
ip6tables -A OUTBOUND_RULES -d ff02::1:2 -p udp --sport 546 --dport 547 -j ACCEPT
# Allow all ICMPv6 traffic - essential for proper IPv6 functionality
# This includes Neighbor Discovery, Router Discovery, Multicast Listener Discovery,
# Path MTU Discovery, and other critical IPv6 operations
ip6tables -A OUTBOUND_RULES -p ipv6-icmp -j ACCEPT

# Allow IGMP (protocol 2) traffic to multicast addresses
iptables -A OUTBOUND_RULES -p 2 -d *********/4 -j ACCEPT

iptables -A OUTBOUND_RULES -d *************** -p udp --dport 53 -j ACCEPT
iptables -A OUTBOUND_RULES -d *************** -p tcp --dport 53 -j ACCEPT
iptables -A OUTBOUND_RULES -d *************** -p udp --dport 123 -j ACCEPT

# Allow traffic to localhost
iptables -A OUTBOUND_RULES -o lo -j ACCEPT
ip6tables -A OUTBOUND_RULES -o lo -j ACCEPT

# Add proxy server rules if enabled
if [ "$PROXY_SERVERS" = true ]; then
  # Allow connecting to nginx proxy server for specs
  iptables -A OUTBOUND_RULES -d ********** -p tcp --dport 80 -j ACCEPT
  iptables -A OUTBOUND_RULES -d ********** -p tcp --dport 443 -j ACCEPT
fi

iptables -A OUTBOUND_RULES -j LOG --log-prefix "IPTABLES: "
ip6tables -A OUTBOUND_RULES -j LOG --log-prefix "IPTABLES: "

# Reject all other traffic (unless in log-only mode)
if [ "$LOG_ONLY_MODE" = false ]; then
  iptables -A OUTBOUND_RULES -j REJECT
  ip6tables -A OUTBOUND_RULES -j REJECT
else
  echo "LOG-ONLY MODE: Unexpected traffic will be logged but not rejected"
fi

# Then apply the custom chain
iptables -A OUTPUT -j OUTBOUND_RULES
ip6tables -A OUTPUT -j OUTBOUND_RULES

# Apply rules to DOCKER-USER chain if Docker support is enabled
if [ "$DOCKER_SUPPORT" = true ]; then
  # Run a docker command to start Docker and create the DOCKER-USER chain
  docker &> /dev/null

  # Wait for DOCKER-USER chain to be created
  while ! iptables -L DOCKER-USER -n &>/dev/null; do
    sleep 1
  done

  # Insert the OUTBOUND_RULES at the beginning of the chain
  # This ensures our rules are processed before the default docker RETURN rule
  iptables -I DOCKER-USER -j OUTBOUND_RULES
  
  # Allow Redis traffic (port 6379) between Docker containers
  iptables -I DOCKER-USER -s **********/12 -d **********/12 -p tcp --dport 6379 -j ACCEPT  # Redis
  
  # Allow PostgreSQL traffic (port 5432) between Docker containers
  iptables -I DOCKER-USER -s **********/12 -d **********/12 -p tcp --dport 5432 -j ACCEPT  # PostgreSQL
fi
