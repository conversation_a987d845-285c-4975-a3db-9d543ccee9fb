GEM
  remote: https://rubygems.org/
  specs:
    ast (2.4.3)
    aws-eventstream (1.4.0)
    aws-partitions (1.1145.0)
    aws-sdk-core (3.229.0)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      bigdecimal
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-kms (1.110.0)
      aws-sdk-core (~> 3, >= 3.228.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.196.1)
      aws-sdk-core (~> 3, >= 3.228.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sdk-ssm (1.201.0)
      aws-sdk-core (~> 3, >= 3.228.0)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.12.1)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.3.0)
    bigdecimal (3.2.2)
    diff-lcs (1.6.2)
    docile (1.4.1)
    jmespath (1.6.2)
    json (2.12.0)
    language_server-protocol (3.17.0.5)
    lint_roller (1.1.0)
    logger (1.7.0)
    parallel (1.27.0)
    parser (3.3.8.0)
      ast (~> 2.4.1)
      racc
    prism (1.4.0)
    racc (1.8.1)
    rainbow (3.1.1)
    regexp_parser (2.10.0)
    rexml (3.4.1)
    rspec (3.13.1)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.5)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-github (3.0.0)
      rspec-core (~> 3.0)
    rspec-mocks (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-support (3.13.4)
    rubocop (1.75.6)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.44.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.44.1)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-rspec (3.6.0)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    ruby-progressbar (1.13.0)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.13.1)
    simplecov-json (0.2.3)
      json
      simplecov
    simplecov_json_formatter (0.1.4)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)

PLATFORMS
  arm64-darwin-24
  ruby

DEPENDENCIES
  aws-sdk-s3
  aws-sdk-ssm
  base64
  rexml
  rspec
  rspec-github
  rubocop-rspec
  simplecov
  simplecov-json

RUBY VERSION
   ruby 3.4.5p51

BUNDLED WITH
   2.6.9
