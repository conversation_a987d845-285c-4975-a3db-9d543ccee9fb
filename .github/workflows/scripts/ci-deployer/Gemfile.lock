GIT
  remote: https://github.com/zip-learning/ci-deployer.git
  revision: e8cd17c7fc6d5cbf72cf100aa44d0ad49ccfb488
  tag: v0.0.14
  specs:
    ci-deployer (0.0.14)
      aws-sdk-cloudwatchlogs (~> 1)
      aws-sdk-ecs (~> 1)
      aws-sdk-ssm (~> 1)
      base64
      bigdecimal
      concurrent-ruby (~> 1)
      logger
      ostruct
      rexml

GEM
  remote: https://rubygems.org/
  specs:
    aws-eventstream (1.4.0)
    aws-partitions (1.1135.0)
    aws-sdk-cloudwatchlogs (1.122.0)
      aws-sdk-core (~> 3, >= 3.227.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-core (3.227.0)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-ecs (1.199.0)
      aws-sdk-core (~> 3, >= 3.227.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-ssm (1.199.0)
      aws-sdk-core (~> 3, >= 3.227.0)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.12.1)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.3.0)
    bigdecimal (3.2.2)
    concurrent-ruby (1.3.5)
    jmespath (1.6.2)
    logger (1.7.0)
    ostruct (0.6.3)
    rexml (3.4.1)

PLATFORMS
  aarch64-linux
  arm64-darwin-23
  ruby

DEPENDENCIES
  ci-deployer!

RUBY VERSION
   ruby 3.4.5p51

BUNDLED WITH
   2.7.1
