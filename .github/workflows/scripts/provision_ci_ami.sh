#!/bin/bash
set -e

# This script is used to provision the base CI image
# At a high level, it sets up the necessary packages and DNS configuration to use dnsmasq
# which is used for the outbound firewall.  It also caches frequently used dependencies
# on the AMI to speed up the CI process.

# Configuration Variables
UPSTREAM_NAMESERVER="***************"
AWS_REGION=$(ec2metadata --availability-zone | sed 's/.$//')
REQUIRED_PACKAGES="dnsmasq ipset wget apt-transport-https gnupg lsb-release curl"

# Detect system architecture and return appropriate string for different tools
get_architecture() {
    local format="${1:-standard}"  # Format: "standard", "gitleaks", or "sam"
    local machine_arch=$(uname -m)
    
    case "$machine_arch" in
        x86_64)
            case "$format" in
                gitleaks) echo "x64" ;;
                sam) echo "x86_64" ;;
                *) echo "x86_64" ;;
            esac
            ;;
        aarch64|arm64)
            echo "arm64"
            ;;
        *)
            echo "Unsupported architecture: $machine_arch" >&2
            return 1
            ;;
    esac
}

# Base FQDNs that are always allowed through the firewall
BASE_FQDNS=(
  ec2.$AWS_REGION.amazonaws.com                                         # AWS
  ec2messages.$AWS_REGION.amazonaws.com                                 # AWS
  ec2-instance-connect.$AWS_REGION.amazonaws.com                        # AWS
  ssm.$AWS_REGION.amazonaws.com                                         # AWS
  ssmmessages.$AWS_REGION.amazonaws.com                                 # AWS
  aws-ssm-document-attachments-$AWS_REGION.s3.$AWS_REGION.amazonaws.com # AWS
  logs.$AWS_REGION.amazonaws.com                                        # AWS
  sts.$AWS_REGION.amazonaws.com                                         # AWS
  cdn.fwupd.org                                                         # Ubuntu fwupd service
  $AWS_REGION.ec2.ports.ubuntu.com                                      # Ubuntu ports
  changelogs.ubuntu.com                                                 # Ubuntu changelogs
  runs-on-s3bucket-2qc0yanzo9vm.s3.$AWS_REGION.amazonaws.com            # RunsOn Service
  runs-on-s3bucketcache-ctph58aizusz.s3.$AWS_REGION.amazonaws.com       # RunsOn Service
  runs-on.com                                                           # RunsOn Service
  actions.githubusercontent.com                                         # GitHub Actions
  api.github.com                                                        # GitHub Actions
  codeload.github.com                                                   # GitHub Actions
  blob.core.windows.net                                                 # GitHub Actions
  actions-results-receiver-production.githubapp.com                     # GitHub Actions
  objects.githubusercontent.com                                         # GitHub Actions
  release-assets.githubusercontent.com                                  # GitHub Actions
  hooks.slack.com                                                       # Slack Alerts for GitHub Actions
)

# Update system packages
update_system_packages() {
    echo "=== Updating system packages ==="
    apt-get update
    apt-get install -y $REQUIRED_PACKAGES
    apt upgrade -y
    echo "System packages updated successfully"
}

# Install or update trivy security scanner
install_trivy() {
    echo "=== Installing/updating trivy ==="
    if ! command -v trivy &> /dev/null; then
        echo "Installing trivy security scanner..."
        wget -qO - https://aquasecurity.github.io/trivy-repo/deb/public.key | gpg --dearmor -o /etc/apt/trusted.gpg.d/trivy.gpg
        echo deb https://aquasecurity.github.io/trivy-repo/deb $(lsb_release -sc) main | tee -a /etc/apt/sources.list.d/trivy.list
        apt-get update
        apt-get install -y trivy
        echo "trivy installed successfully"
    else
        echo "trivy is already installed, checking for updates..."
        apt-get update
        apt-get install -y trivy
    fi
    
    # Update trivy's vulnerability database
    echo "Updating trivy vulnerability database..."
    trivy image --download-db-only
    echo "trivy setup completed"
}

# Install or update gitleaks to the latest version
install_gitleaks() {
    echo "=== Installing/updating gitleaks ==="
    local current_version=""
    local latest_version=""
    
    # Check if gitleaks is already installed and get its version
    if command -v gitleaks &> /dev/null; then
        current_version=$(gitleaks version 2>/dev/null | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1)
        if [[ -n "$current_version" ]]; then
            echo "Current gitleaks version: $current_version"
        else
            echo "Could not determine current gitleaks version"
        fi
    else
        echo "gitleaks is not installed"
    fi
    
    # GitHub repository
    local github_repo="gitleaks/gitleaks"
    
    # Get the latest version from GitHub API
    latest_version=$(curl -s "https://api.github.com/repos/${github_repo}/releases/latest" | grep '"tag_name"' | cut -d '"' -f 4)
    
    if [[ -z "$latest_version" ]]; then
        echo "Failed to fetch latest gitleaks version from GitHub API"
        return 1
    fi

    # Normalize version (remove 'v' prefix if present)
    local latest_version_normalized="${latest_version#v}"
    
    echo "Latest gitleaks version: $latest_version_normalized"
    
    # Compare versions if gitleaks is already installed
    if [[ -n "$current_version" ]]; then
        if [[ "$current_version" == "$latest_version_normalized" ]]; then
            echo "gitleaks is already up to date ($current_version)"
            return 0
        else
            echo "gitleaks needs to be updated from $current_version to $latest_version_normalized"
        fi
    fi
    
    # Determine platform and architecture
    local platform="linux"
    local arch=$(get_architecture "gitleaks")
    if [[ -z "$arch" ]]; then
        return 1
    fi
    
    # Construct download URL
    local download_url="https://github.com/${github_repo}/releases/download/${latest_version}/gitleaks_${latest_version_normalized}_${platform}_${arch}.tar.gz"
    
    echo "Downloading gitleaks from: $download_url"
    
    # Create temporary directory for download
    local temp_dir=$(mktemp -d)
    cd "$temp_dir"
    
    # Download and extract
    if wget -q "$download_url" -O gitleaks.tar.gz; then
        if tar -xzf gitleaks.tar.gz; then
            # Move binary to /usr/local/bin and make executable
            if [[ -f gitleaks ]]; then
                sudo mv gitleaks /usr/local/bin/gitleaks
                sudo chmod +x /usr/local/bin/gitleaks
                echo "gitleaks $latest_version installed successfully"
            else
                echo "Failed to find gitleaks binary in extracted archive"
                return 1
            fi
        else
            echo "Failed to extract gitleaks archive"
            return 1
        fi
    else
        echo "Failed to download gitleaks"
        return 1
    fi
    
    # Cleanup
    cd /
    rm -rf "$temp_dir"
    
    # Verify installation
    if command -v gitleaks &> /dev/null; then
        local installed_version=$(gitleaks version 2>/dev/null | head -1)
        if [[ "$installed_version" == "$latest_version_normalized" ]]; then
            echo "gitleaks $installed_version installed successfully"
        else
            echo "WARNING: gitleaks installation version mismatch"
            echo "Expected: $latest_version_normalized"
            echo "Installed: $installed_version"
            return 1
        fi
    else
        echo "gitleaks installation failed - binary not found"
        return 1
    fi
}

# Configure ipset for firewall rules
configure_ipset() {
    echo "=== Configuring ipset ==="
    
    # Create ipset creation script
    tee /usr/local/bin/create_ipset.sh > /dev/null <<'EOF'
#!/bin/bash
ipset -exist create outbound_firewall_v4 hash:ip
ipset -exist create outbound_firewall_v6 hash:ip family inet6
ipset -exist create outbound_firewall_all_v4 hash:ip
ipset -exist create outbound_firewall_all_v6 hash:ip family inet6
EOF
    
    chmod +x /usr/local/bin/create_ipset.sh
    
    # Create systemd service for ipset
    tee /etc/systemd/system/ipset.service > /dev/null <<'EOF'
[Unit]
Description=Create ipset for dnsmasq
Before=dnsmasq.service
After=network.target

[Service]
Type=oneshot
ExecStart=/usr/local/bin/create_ipset.sh

[Install]
WantedBy=multi-user.target
EOF
    
    systemctl daemon-reload
    systemctl enable ipset.service
    
    # Create the ipsets
    /usr/local/bin/create_ipset.sh
    
    echo "ipset configuration completed"
}

# Configure dnsmasq for DNS resolution and firewall
configure_dnsmasq() {
    echo "=== Configuring dnsmasq ==="
    
    # Configure upstream nameserver
    echo "nameserver $UPSTREAM_NAMESERVER" > /etc/resolv.dnsmasq
    
    # Disable systemd-resolved
    systemctl disable systemd-resolved &> /dev/null || true
    systemctl stop systemd-resolved &> /dev/null || true
    
    # Update resolv.conf
    sed 's/^nameserver.*/nameserver 127.0.0.1/' /etc/resolv.conf > /tmp/resolv.conf
    # Delete comments and empty lines
    sed -i -e '/^#/d' -e '/^$/d' /tmp/resolv.conf
    mv /tmp/resolv.conf /etc/resolv.conf
    
    # Configure dnsmasq
    tee /etc/dnsmasq.d/config > /dev/null <<EOF
log-queries
listen-address=0.0.0.0
resolv-file=/etc/resolv.dnsmasq
EOF
    
    # Configure base FQDNs for ipset
    local BASE_FQDNS_STRING=$(IFS=/; echo "${BASE_FQDNS[*]}")
    tee /etc/dnsmasq.d/base_ipset > /dev/null <<EOF
ipset=/$BASE_FQDNS_STRING/outbound_firewall_v4,outbound_firewall_v6
EOF
    
    # Restart dnsmasq
    systemctl restart dnsmasq
    
    echo "dnsmasq configuration completed"
}

# Configure Docker and cache images
configure_docker() {
    echo "=== Configuring Docker ==="
    
    # Have docker containers use dnsmasq
    echo "{ \"dns\": [\"**********\"] }" > /etc/docker/daemon.json
    
    # Login to GitHub Container Registry
    if [[ -n "$GITHUB_TOKEN" ]] && [[ -n "$GITHUB_ACTOR" ]]; then
        echo "Logging into GitHub Container Registry..."
        echo "$GITHUB_TOKEN" | docker login ghcr.io -u "$GITHUB_ACTOR" --password-stdin &> /dev/null
    else
        echo "Skipping GitHub Container Registry login (credentials not provided)"
    fi
    
    # Cache docker images used for Rails CI
    echo "Caching Docker images..."
    
    # Remove all existing Docker images to ensure a clean state
    echo "Removing all existing Docker images..."
    docker system prune -af
    
    # Pull required images if variables are set
    local images=(
        ${POSTGRES_IMAGE:-}
        ${REDIS_IMAGE:-}
        ${BASE_IMAGE:-}
        ${BUILDER_IMAGE:-}
        ${BUILDER_BUNDLER_TEST_IMAGE:-}
    )
    
    for image in "${images[@]}"; do
        if [[ -n "$image" ]]; then
            echo "Pulling $image..."
            docker pull "$image" || echo "Failed to pull $image, continuing..."
        fi
    done
    
    # Pull SAM docker images
    echo "Pulling SAM build image..."
    docker pull public.ecr.aws/sam/build-ruby3.4:latest-arm64 || echo "Failed to pull SAM image, continuing..."
    
    # Pull nuclei security scanner image
    echo "Pulling nuclei security scanner image..."
    docker pull projectdiscovery/nuclei:latest || echo "Failed to pull nuclei image, continuing..."
    
    # Cleanup docker credentials
    rm -rf ~/.docker/
    
    echo "Docker configuration completed"
}

# Install or update uv Python package manager
install_uv() {
    echo "=== Installing/updating uv ==="
    
    if command -v uv &> /dev/null; then
        echo "uv is already installed, updating..."
    else
        echo "Installing uv..."
    fi
    
    # Use the same installation method for both install and update
    export UV_INSTALL_DIR="/usr/local/bin"
    curl -LsSf https://astral.sh/uv/install.sh | env INSTALLER_NO_MODIFY_PATH=1 sh
    echo "uv installation/update completed"
    
    # Verify installation
    if command -v uv &> /dev/null; then
        local uv_version=$(uv --version 2>/dev/null)
        echo "uv version: $uv_version"
    else
        echo "uv installation verification failed"
        return 1
    fi
}


# Install SAM CLI for lambda projects
install_sam_cli() {
    echo "=== Installing SAM CLI ==="
    
    # Determine architecture
    local arch=$(get_architecture "sam")
    if [[ -z "$arch" ]]; then
        echo "Unsupported architecture for SAM CLI"
        return 1
    fi
    
    local sam_url="https://github.com/aws/aws-sam-cli/releases/latest/download/aws-sam-cli-linux-${arch}.zip"
    
    echo "Downloading SAM CLI from: $sam_url"
    
    if wget -q "$sam_url" -O /tmp/sam.zip; then
        unzip -q /tmp/sam.zip -d /tmp/sam
        /tmp/sam/install --update
        
        # Verify installation
        if sam --version; then
            echo "SAM CLI installed successfully"
        else
            echo "SAM CLI installation verification failed"
            return 1
        fi
        
        # Cleanup
        rm -rf /tmp/sam.zip /tmp/sam
    else
        echo "Failed to download SAM CLI"
        return 1
    fi
}

# Main function to orchestrate all installations
main() {
    echo "========================================"
    echo "Starting AMI provisioning..."
    echo "AWS Region: $AWS_REGION"
    echo "========================================"
    
    # Run all installation steps
    update_system_packages
    install_trivy
    install_gitleaks
    install_uv
    install_sam_cli
    configure_ipset
    configure_dnsmasq
    configure_docker
    
    echo "========================================"
    echo "AMI provisioning completed successfully!"
    echo "========================================"
}

# Execute main function
main