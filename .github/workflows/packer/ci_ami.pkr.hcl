packer {
  required_plugins {
    amazon = {
      source  = "github.com/hashicorp/amazon"
      version = "~> 1"
    }
  }
}

variable "build_from_scratch" {
  type = bool
}

variable "github_actor" {
  type = string
}

variable "github_token" {
  type = string
}

variable "postgres_image" {
  type = string
}

variable "redis_image" {
  type = string
}

variable "base_image" {
  type = string
}

variable "builder_image" {
  type = string
}

variable "builder_bundler_test_image" {
  type = string
}

variable "iam_instance_profile" {
  type = string
}

variable "ami_name_prefix" {
  type = string
}

variable "base_ami_name" {
  type    = string
  default = "runs-on-v2.2-ubuntu24-full-arm64-*"
}

variable "run_tag_prefix" {
  type = string
}

locals {
  ami_name_prefix = "${var.ami_name_prefix}-v2-arm64-"
  ami_name        = "${local.ami_name_prefix}${formatdate("YYYY-MM-DD-hhmmss", timestamp())}"
}

source "amazon-ebs" "build-ebs" {
  ami_name            = local.ami_name
  spot_price          = "auto"
  spot_instance_types = ["c8g.medium", "c7g.medium", "c6g.medium"]
  ssh_username        = "ubuntu"

  source_ami_filter {
    filters = {
      "virtualization-type" = "hvm"
      "name"                = var.build_from_scratch ? var.base_ami_name : "${local.ami_name_prefix}*"
      "root-device-type"    = "ebs"
      "state"               = "available"
    }
    owners      = var.build_from_scratch ? ["135269210855"] : ["self"]
    most_recent = true
  }

  metadata_options {
    http_endpoint = "enabled"
    http_tokens   = "required"
  }

  security_group_filter {
    filters = {
      "group-name" : "EC2-Runson"
    }
  }

  subnet_filter {
    filters = {
      "tag:Name" : "private-*"
    }
    random = true
  }

  iam_instance_profile = var.iam_instance_profile
  ssh_interface        = "session_manager"

  run_tags = {
    Name = "${var.run_tag_prefix}-arm64"
  }

  tags = {
    Name = local.ami_name
  }
}

build {
  sources = ["source.amazon-ebs.build-ebs"]

  provisioner "shell" {
    script          = "../scripts/provision_ci_ami.sh"
    execute_command = "sudo -S sh -c '{{.Vars}} {{.Path}}'"

    environment_vars = [
      "GITHUB_TOKEN=${var.github_token}",
      "GITHUB_ACTOR=${var.github_actor}",
      "POSTGRES_IMAGE=${var.postgres_image}",
      "REDIS_IMAGE=${var.redis_image}",
      "BASE_IMAGE=${var.base_image}",
      "BUILDER_IMAGE=${var.builder_image}",
      "BUILDER_BUNDLER_TEST_IMAGE=${var.builder_bundler_test_image}"
    ]
  }

  provisioner "file" {
    source      = "../scripts/ci-firewall/outbound_firewall.sh"
    destination = "/tmp/outbound_firewall.sh"
  }
  provisioner "file" {
    source      = "../scripts/ci-firewall/alert_on_unexpected_activity.sh"
    destination = "/tmp/alert_on_unexpected_activity.sh"
  }
  provisioner "shell" {
    inline = [
      "sudo mv /tmp/outbound_firewall.sh /usr/local/bin/outbound_firewall.sh",
      "sudo mv /tmp/alert_on_unexpected_activity.sh /usr/local/bin/alert_on_unexpected_activity.sh"
    ]
  }
}